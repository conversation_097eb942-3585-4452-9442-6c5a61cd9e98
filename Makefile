# ASI System Advanced Testing Framework Makefile
# Comprehensive testing automation for all ASI modules

.PHONY: help test test-unit test-integration test-e2e test-performance
.PHONY: test-go test-rust test-python test-cpp
.PHONY: benchmark coverage lint security-scan
.PHONY: ci-test-local setup-test-env clean-test-env

# Default target
help:
	@echo "ASI System Advanced Testing Framework"
	@echo "====================================="
	@echo ""
	@echo "Available targets:"
	@echo "  help                 - Show this help message"
	@echo ""
	@echo "Unit Tests:"
	@echo "  test-unit-all        - Run all unit tests across languages"
	@echo "  test-python          - Run Python unit tests"
	@echo "  test-go              - Run Go unit tests"
	@echo "  test-rust            - Run Rust unit tests"
	@echo "  test-cpp             - Run C++ unit tests"
	@echo ""
	@echo "Integration Tests:"
	@echo "  test-integration     - Run integration tests"
	@echo "  test-integration-ingestion - Run ingestion integration tests"
	@echo "  test-integration-learning  - Run learning integration tests"
	@echo "  test-integration-decision  - Run decision integration tests"
	@echo "  test-integration-security  - Run security integration tests"
	@echo ""
	@echo "End-to-End Tests:"
	@echo "  test-e2e-full        - Run complete E2E test suite"
	@echo "  test-e2e-ingestion-to-learning - Run ingestion→learning workflow"
	@echo "  test-e2e-learning-to-decision  - Run learning→decision workflow"
	@echo "  test-e2e-decision-to-action    - Run decision→action workflow"
	@echo "  test-e2e-performance - Run E2E performance tests"
	@echo ""
	@echo "Performance & Benchmarks:"
	@echo "  benchmark-go         - Run Go benchmarks"
	@echo "  benchmark-rust       - Run Rust benchmarks"
	@echo "  benchmark-python     - Run Python benchmarks"
	@echo "  benchmark-cpp        - Run C++ benchmarks"
	@echo "  benchmark-throughput - Run throughput benchmarks"
	@echo "  benchmark-latency    - Run latency benchmarks"
	@echo "  benchmark-memory     - Run memory usage benchmarks"
	@echo ""
	@echo "Coverage & Quality:"
	@echo "  coverage-report      - Generate comprehensive coverage report"
	@echo "  coverage-html        - Generate HTML coverage report"
	@echo "  lint-all             - Run all linting checks"
	@echo "  security-scan        - Run security vulnerability scanning"
	@echo ""
	@echo "CI/CD:"
	@echo "  ci-test-local        - Run full CI test suite locally"
	@echo "  test-report          - Generate comprehensive test report"
	@echo ""
	@echo "Environment:"
	@echo "  setup-test-env       - Setup test environment"
	@echo "  clean-test-env       - Clean test environment"
	@echo "  check-deps           - Check test dependencies"

# Variables
PYTHON := python3
GO := go
RUST := cargo
CMAKE := cmake
PYTEST_ARGS := -v --tb=short
COVERAGE_MIN := 80

# Test environment setup
setup-test-env:
	@echo "Setting up test environment..."
	@echo "Installing Python dependencies..."
	$(PYTHON) -m pip install --upgrade pip
	$(PYTHON) -m pip install -r tests/requirements.txt
	@echo "Installing Go dependencies..."
	cd tests/unit/go && $(GO) mod download
	@echo "Installing Rust dependencies..."
	cd tests/unit/rust && $(RUST) fetch
	@echo "Test environment setup complete"

check-deps:
	@echo "Checking test dependencies..."
	@command -v $(PYTHON) >/dev/null 2>&1 || { echo "Python not found"; exit 1; }
	@command -v $(GO) >/dev/null 2>&1 || { echo "Go not found"; exit 1; }
	@command -v $(RUST) >/dev/null 2>&1 || { echo "Rust/Cargo not found"; exit 1; }
	@command -v $(CMAKE) >/dev/null 2>&1 || { echo "CMake not found"; exit 1; }
	@echo "All dependencies found"

# Unit Tests
test-unit-all: test-python test-go test-rust test-cpp
	@echo "All unit tests completed"

test-python:
	@echo "Running Python unit tests..."
	cd tests/unit/python && \
	$(PYTHON) -m pytest . \
		$(PYTEST_ARGS) \
		--cov=../../../ \
		--cov-report=term-missing \
		--cov-report=html:htmlcov-python \
		--cov-report=xml:coverage-python.xml \
		--junit-xml=test-results-python.xml \
		-m "unit and not slow"

test-go:
	@echo "Running Go unit tests..."
	cd tests/unit/go && \
	$(GO) test -v -race -coverprofile=coverage.out -covermode=atomic ./... && \
	$(GO) tool cover -html=coverage.out -o coverage.html

test-rust:
	@echo "Running Rust unit tests..."
	cd tests/unit/rust && \
	$(RUST) test --verbose --all-features && \
	$(RUST) tarpaulin --out html --output-dir coverage

test-cpp:
	@echo "Running C++ unit tests..."
	cd tests/unit/cpp && \
	mkdir -p build && \
	cd build && \
	$(CMAKE) .. -DCMAKE_BUILD_TYPE=Debug -DENABLE_COVERAGE=ON && \
	make -j$$(nproc) && \
	ctest --output-on-failure --verbose && \
	lcov --capture --directory . --output-file coverage.info && \
	genhtml coverage.info --output-directory coverage-html

# Integration Tests
test-integration:
	@echo "Running integration tests..."
	$(PYTHON) -m pytest tests/integration/ \
		$(PYTEST_ARGS) \
		--junit-xml=test-results-integration.xml \
		-m "integration and not slow" \
		--timeout=300

test-integration-ingestion:
	@echo "Running ingestion integration tests..."
	$(PYTHON) -m pytest tests/integration/ \
		$(PYTEST_ARGS) \
		-k "ingestion" \
		--timeout=120

test-integration-learning:
	@echo "Running learning integration tests..."
	$(PYTHON) -m pytest tests/integration/ \
		$(PYTEST_ARGS) \
		-k "learning" \
		--timeout=180

test-integration-decision:
	@echo "Running decision integration tests..."
	$(PYTHON) -m pytest tests/integration/ \
		$(PYTEST_ARGS) \
		-k "decision" \
		--timeout=120

test-integration-security:
	@echo "Running security integration tests..."
	$(PYTHON) -m pytest tests/integration/ \
		$(PYTEST_ARGS) \
		-k "security" \
		--timeout=150

# End-to-End Tests
test-e2e-full:
	@echo "Running complete E2E test suite..."
	$(PYTHON) -m pytest tests/e2e/ \
		$(PYTEST_ARGS) \
		--junit-xml=test-results-e2e.xml \
		-m "e2e and not performance" \
		--timeout=600

test-e2e-ingestion-to-learning:
	@echo "Running ingestion→learning E2E workflow..."
	$(PYTHON) -m pytest tests/e2e/ \
		$(PYTEST_ARGS) \
		-k "ingestion_to_learning" \
		--timeout=300

test-e2e-learning-to-decision:
	@echo "Running learning→decision E2E workflow..."
	$(PYTHON) -m pytest tests/e2e/ \
		$(PYTEST_ARGS) \
		-k "learning_to_decision" \
		--timeout=300

test-e2e-decision-to-action:
	@echo "Running decision→action E2E workflow..."
	$(PYTHON) -m pytest tests/e2e/ \
		$(PYTEST_ARGS) \
		-k "decision_to_action" \
		--timeout=300

test-e2e-performance:
	@echo "Running E2E performance tests..."
	$(PYTHON) -m pytest tests/e2e/ \
		$(PYTEST_ARGS) \
		-m "e2e and performance" \
		--timeout=900

# Performance Benchmarks
benchmark-go:
	@echo "Running Go benchmarks..."
	cd tests/unit/go && \
	$(GO) test -bench=. -benchmem -cpuprofile=cpu.prof -memprofile=mem.prof

benchmark-rust:
	@echo "Running Rust benchmarks..."
	cd tests/unit/rust && \
	$(RUST) bench

benchmark-python:
	@echo "Running Python benchmarks..."
	$(PYTHON) -m pytest tests/performance/ \
		--benchmark-only \
		--benchmark-json=benchmark-results.json \
		--benchmark-sort=mean

benchmark-cpp:
	@echo "Running C++ benchmarks..."
	cd tests/unit/cpp/build && \
	./benchmark_tests --benchmark_format=json --benchmark_out=benchmark-results.json

benchmark-throughput:
	@echo "Running throughput benchmarks..."
	$(PYTHON) -m pytest tests/performance/ \
		-k "throughput" \
		--benchmark-only

benchmark-latency:
	@echo "Running latency benchmarks..."
	$(PYTHON) -m pytest tests/performance/ \
		-k "latency" \
		--benchmark-only

benchmark-memory:
	@echo "Running memory benchmarks..."
	$(PYTHON) -m pytest tests/performance/ \
		-k "memory" \
		--benchmark-only

# Coverage Reports
coverage-report: test-unit-all
	@echo "Generating comprehensive coverage report..."
	@echo "Python Coverage:"
	@cd tests/unit/python && $(PYTHON) -m coverage report
	@echo "Go Coverage:"
	@cd tests/unit/go && $(GO) tool cover -func=coverage.out
	@echo "Rust Coverage:"
	@cd tests/unit/rust && $(RUST) tarpaulin --out stdout
	@echo "C++ Coverage:"
	@cd tests/unit/cpp/build && lcov --list coverage.info

coverage-html: test-unit-all
	@echo "Generating HTML coverage reports..."
	@echo "Coverage reports generated in:"
	@echo "  Python: tests/unit/python/htmlcov-python/index.html"
	@echo "  Go: tests/unit/go/coverage.html"
	@echo "  Rust: tests/unit/rust/coverage/index.html"
	@echo "  C++: tests/unit/cpp/build/coverage-html/index.html"

# Linting and Code Quality
lint-all: lint-python lint-go lint-rust lint-cpp
	@echo "All linting checks completed"

lint-python:
	@echo "Running Python linting..."
	black --check --diff .
	isort --check-only --diff .
	flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
	mypy . --ignore-missing-imports || true

lint-go:
	@echo "Running Go linting..."
	cd tests/unit/go && \
	gofmt -l . && \
	golint ./... && \
	go vet ./...

lint-rust:
	@echo "Running Rust linting..."
	cd tests/unit/rust && \
	$(RUST) fmt --all -- --check && \
	$(RUST) clippy --all-targets --all-features -- -D warnings

lint-cpp:
	@echo "Running C++ linting..."
	find tests/unit/cpp -name "*.cpp" -o -name "*.h" | xargs clang-format --dry-run --Werror

# Security Scanning
security-scan:
	@echo "Running security scans..."
	@echo "Python security scan..."
	bandit -r . -f json -o bandit-results.json || true
	@echo "Dependency vulnerability scan..."
	safety check --json --output safety-results.json || true
	@echo "Container security scan..."
	trivy fs . --format json --output trivy-results.json || true

# CI/CD Local Testing
ci-test-local: check-deps lint-all test-unit-all test-integration security-scan
	@echo "Local CI test suite completed successfully"

# Test Reporting
test-report:
	@echo "Generating comprehensive test report..."
	@echo "# ASI System Test Report" > test-report.md
	@echo "" >> test-report.md
	@echo "Generated on: $$(date)" >> test-report.md
	@echo "" >> test-report.md
	@echo "## Test Results Summary" >> test-report.md
	@echo "" >> test-report.md
	@find . -name "test-results-*.xml" -exec echo "- {}" \; >> test-report.md
	@echo "" >> test-report.md
	@echo "## Coverage Summary" >> test-report.md
	@echo "" >> test-report.md
	@echo "Coverage reports available in respective language directories" >> test-report.md
	@echo "Test report generated: test-report.md"

# Environment Cleanup
clean-test-env:
	@echo "Cleaning test environment..."
	find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
	find . -name "*.pyc" -delete 2>/dev/null || true
	find . -name ".pytest_cache" -type d -exec rm -rf {} + 2>/dev/null || true
	find . -name "htmlcov*" -type d -exec rm -rf {} + 2>/dev/null || true
	find . -name "coverage*.xml" -delete 2>/dev/null || true
	find . -name "test-results-*.xml" -delete 2>/dev/null || true
	find . -name "benchmark-results.json" -delete 2>/dev/null || true
	rm -rf tests/unit/go/coverage.* 2>/dev/null || true
	rm -rf tests/unit/rust/target 2>/dev/null || true
	rm -rf tests/unit/rust/coverage 2>/dev/null || true
	rm -rf tests/unit/cpp/build 2>/dev/null || true
	@echo "Test environment cleaned"

# Quick test targets
test: test-unit-all test-integration
	@echo "Core test suite completed"

test-quick: test-python test-go
	@echo "Quick test suite completed"

test-all: test-unit-all test-integration test-e2e-full
	@echo "Complete test suite finished"
