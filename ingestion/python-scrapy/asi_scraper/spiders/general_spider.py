"""
General purpose spider for web scraping with intelligent content extraction.
"""

import scrapy
import re
from urllib.parse import urljoin, urlparse
from datetime import datetime
from typing import Generator, Optional, List, Dict, Any

from ..items import WebPageItem, NewsArticleItem, ProductItem
from ..utils.content_extractor import ContentExtractor
from ..utils.url_filter import URLFilter


class GeneralSpider(scrapy.Spider):
    """
    General purpose spider that can adapt to different website structures.
    Uses intelligent content extraction and classification.
    """
    
    name = 'general'
    allowed_domains = []
    start_urls = []
    
    # Spider configuration
    custom_settings = {
        'DOWNLOAD_DELAY': 1,
        'RANDOMIZE_DOWNLOAD_DELAY': True,
        'CONCURRENT_REQUESTS_PER_DOMAIN': 2,
        'AUTOTHROTTLE_ENABLED': True,
        'AUTOTHROTTLE_TARGET_CONCURRENCY': 2.0,
    }
    
    def __init__(self, start_urls=None, allowed_domains=None, max_depth=3, 
                 follow_links=True, content_types=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Parse start URLs
        if start_urls:
            if isinstance(start_urls, str):
                self.start_urls = [url.strip() for url in start_urls.split(',')]
            else:
                self.start_urls = start_urls
        
        # Parse allowed domains
        if allowed_domains:
            if isinstance(allowed_domains, str):
                self.allowed_domains = [domain.strip() for domain in allowed_domains.split(',')]
            else:
                self.allowed_domains = allowed_domains
        else:
            # Extract domains from start URLs
            self.allowed_domains = list(set(urlparse(url).netloc for url in self.start_urls))
        
        self.max_depth = int(max_depth)
        self.follow_links = follow_links
        self.content_types = content_types or ['webpage', 'news', 'product']
        
        # Initialize utilities
        self.content_extractor = ContentExtractor()
        self.url_filter = URLFilter(self.allowed_domains)
        
        self.logger.info(f"Initialized GeneralSpider with {len(self.start_urls)} start URLs")
        self.logger.info(f"Allowed domains: {self.allowed_domains}")
    
    def start_requests(self) -> Generator[scrapy.Request, None, None]:
        """Generate initial requests."""
        for url in self.start_urls:
            yield scrapy.Request(
                url=url,
                callback=self.parse,
                meta={'depth': 0},
                errback=self.handle_error
            )
    
    def parse(self, response) -> Generator[Any, None, None]:
        """Main parsing method."""
        try:
            # Extract basic page information
            page_info = self.extract_page_info(response)
            
            # Classify content type
            content_type = self.classify_content(response, page_info)
            
            # Extract content based on type
            if content_type == 'news':
                item = self.extract_news_article(response, page_info)
            elif content_type == 'product':
                item = self.extract_product(response, page_info)
            else:
                item = self.extract_webpage(response, page_info)
            
            if item:
                yield item
            
            # Follow links if enabled and within depth limit
            if self.follow_links and response.meta.get('depth', 0) < self.max_depth:
                yield from self.extract_links(response)
                
        except Exception as e:
            self.logger.error(f"Error parsing {response.url}: {e}")
    
    def extract_page_info(self, response) -> Dict[str, Any]:
        """Extract basic page information."""
        return {
            'url': response.url,
            'status_code': response.status,
            'content_type': response.headers.get('Content-Type', b'').decode('utf-8'),
            'content_length': len(response.body),
            'encoding': response.encoding,
            'scraped_at': datetime.utcnow().isoformat(),
            'title': self.content_extractor.extract_title(response),
            'meta_description': self.content_extractor.extract_meta_description(response),
            'meta_keywords': self.content_extractor.extract_meta_keywords(response),
            'language': self.content_extractor.detect_language(response),
        }
    
    def classify_content(self, response, page_info: Dict[str, Any]) -> str:
        """Classify the content type of the page."""
        url = response.url.lower()
        title = page_info.get('title', '').lower()
        content = response.text.lower()
        
        # News article indicators
        news_indicators = [
            'news', 'article', 'story', 'breaking', 'report',
            'journalist', 'published', 'updated'
        ]
        
        # Product page indicators
        product_indicators = [
            'price', 'buy', 'cart', 'product', 'shop', 'store',
            'purchase', 'order', 'shipping', 'reviews'
        ]
        
        # Check for news indicators
        if any(indicator in url or indicator in title for indicator in news_indicators):
            return 'news'
        
        # Check for product indicators
        if any(indicator in url or indicator in title for indicator in product_indicators):
            return 'product'
        
        # Check content for indicators
        if any(indicator in content for indicator in news_indicators[:5]):
            return 'news'
        
        if any(indicator in content for indicator in product_indicators[:5]):
            return 'product'
        
        return 'webpage'
    
    def extract_webpage(self, response, page_info: Dict[str, Any]) -> Optional[WebPageItem]:
        """Extract general webpage content."""
        try:
            item = WebPageItem()
            
            # Basic fields
            item['url'] = page_info['url']
            item['title'] = page_info['title']
            item['status_code'] = page_info['status_code']
            item['content_type'] = page_info['content_type']
            item['content_length'] = page_info['content_length']
            item['encoding'] = page_info['encoding']
            item['scraped_at'] = page_info['scraped_at']
            item['language'] = page_info['language']
            
            # Extract content
            item['content'] = self.content_extractor.extract_main_content(response)
            item['description'] = page_info['meta_description']
            item['keywords'] = page_info['meta_keywords']
            
            # Extract links and media
            item['links'] = self.content_extractor.extract_links(response)
            item['images'] = self.content_extractor.extract_images(response)
            item['emails'] = self.content_extractor.extract_emails(response)
            item['phone_numbers'] = self.content_extractor.extract_phone_numbers(response)
            
            # Extract meta tags
            item['meta_tags'] = self.content_extractor.extract_meta_tags(response)
            
            # Content analysis
            content_text = item['content']
            if content_text:
                item['word_count'] = len(content_text.split())
                item['quality_score'] = self.calculate_quality_score(item)
            
            return item
            
        except Exception as e:
            self.logger.error(f"Error extracting webpage from {response.url}: {e}")
            return None
    
    def extract_news_article(self, response, page_info: Dict[str, Any]) -> Optional[NewsArticleItem]:
        """Extract news article content."""
        try:
            item = NewsArticleItem()
            
            # Start with webpage extraction
            webpage_item = self.extract_webpage(response, page_info)
            if not webpage_item:
                return None
            
            # Copy webpage fields
            for field in webpage_item.fields:
                if field in webpage_item:
                    item[field] = webpage_item[field]
            
            # Extract news-specific fields
            item['headline'] = self.content_extractor.extract_headline(response)
            item['author'] = self.content_extractor.extract_author(response)
            item['published_date'] = self.content_extractor.extract_published_date(response)
            item['category'] = self.content_extractor.extract_category(response)
            item['tags'] = self.content_extractor.extract_tags(response)
            item['source'] = urlparse(response.url).netloc
            
            return item
            
        except Exception as e:
            self.logger.error(f"Error extracting news article from {response.url}: {e}")
            return None
    
    def extract_product(self, response, page_info: Dict[str, Any]) -> Optional[ProductItem]:
        """Extract product information."""
        try:
            item = ProductItem()
            
            # Start with webpage extraction
            webpage_item = self.extract_webpage(response, page_info)
            if not webpage_item:
                return None
            
            # Copy webpage fields
            for field in webpage_item.fields:
                if field in webpage_item:
                    item[field] = webpage_item[field]
            
            # Extract product-specific fields
            item['product_name'] = self.content_extractor.extract_product_name(response)
            item['price'] = self.content_extractor.extract_price(response)
            item['currency'] = self.content_extractor.extract_currency(response)
            item['availability'] = self.content_extractor.extract_availability(response)
            item['brand'] = self.content_extractor.extract_brand(response)
            item['category'] = self.content_extractor.extract_product_category(response)
            item['rating'] = self.content_extractor.extract_rating(response)
            item['review_count'] = self.content_extractor.extract_review_count(response)
            item['specifications'] = self.content_extractor.extract_specifications(response)
            
            return item
            
        except Exception as e:
            self.logger.error(f"Error extracting product from {response.url}: {e}")
            return None
    
    def extract_links(self, response) -> Generator[scrapy.Request, None, None]:
        """Extract and follow links."""
        try:
            links = response.css('a::attr(href)').getall()
            current_depth = response.meta.get('depth', 0)
            
            for link in links:
                if not link:
                    continue
                
                # Convert relative URLs to absolute
                absolute_url = urljoin(response.url, link)
                
                # Filter URLs
                if self.url_filter.should_follow(absolute_url):
                    yield scrapy.Request(
                        url=absolute_url,
                        callback=self.parse,
                        meta={'depth': current_depth + 1},
                        errback=self.handle_error
                    )
                    
        except Exception as e:
            self.logger.error(f"Error extracting links from {response.url}: {e}")
    
    def calculate_quality_score(self, item: Dict[str, Any]) -> float:
        """Calculate content quality score."""
        score = 0.0
        
        # Content length score
        content_length = len(item.get('content', ''))
        if content_length > 1000:
            score += 0.3
        elif content_length > 500:
            score += 0.2
        elif content_length > 100:
            score += 0.1
        
        # Title presence
        if item.get('title'):
            score += 0.2
        
        # Description presence
        if item.get('description'):
            score += 0.1
        
        # Keywords presence
        if item.get('keywords'):
            score += 0.1
        
        # Links presence
        if item.get('links'):
            score += 0.1
        
        # Images presence
        if item.get('images'):
            score += 0.1
        
        # Language detection
        if item.get('language'):
            score += 0.1
        
        return min(score, 1.0)
    
    def handle_error(self, failure):
        """Handle request errors."""
        self.logger.error(f"Request failed: {failure.request.url} - {failure.value}")
