"""
ASI Scraper Pipelines

Data processing pipelines for scraped items.
"""

import json
import hashlib
import logging
from typing import Dict, Any, Optional
from datetime import datetime
import redis
from kafka import KafkaProducer
from kafka.errors import KafkaError
import grpc
from prometheus_client import Counter, Histogram, Gauge
import scrapy
from scrapy.exceptions import DropItem

from .items import WebPageItem, NewsArticleItem, ProductItem


# Metrics
items_processed = Counter('asi_scraper_items_processed_total', 'Total items processed', ['item_type', 'status'])
processing_duration = Histogram('asi_scraper_processing_duration_seconds', 'Time spent processing items', ['pipeline'])
kafka_messages_sent = Counter('asi_scraper_kafka_messages_sent_total', 'Kafka messages sent', ['topic', 'status'])
duplicate_items = Counter('asi_scraper_duplicate_items_total', 'Duplicate items detected', ['item_type'])
validation_errors = Counter('asi_scraper_validation_errors_total', 'Validation errors', ['error_type'])


class ValidationPipeline:
    """Validate scraped items."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.min_content_length = 100
        self.max_content_length = 10 * 1024 * 1024  # 10MB
    
    def process_item(self, item, spider):
        """Validate item data."""
        start_time = datetime.utcnow()
        
        try:
            # Basic validation
            if not item.get('url'):
                validation_errors.labels(error_type='missing_url').inc()
                raise DropItem("Missing URL")
            
            if not item.get('title'):
                validation_errors.labels(error_type='missing_title').inc()
                self.logger.warning(f"Missing title for {item['url']}")
            
            # Content validation
            content = item.get('content', '')
            if len(content) < self.min_content_length:
                validation_errors.labels(error_type='content_too_short').inc()
                raise DropItem(f"Content too short: {len(content)} chars")
            
            if len(content) > self.max_content_length:
                validation_errors.labels(error_type='content_too_long').inc()
                raise DropItem(f"Content too long: {len(content)} chars")
            
            # URL validation
            if not item['url'].startswith(('http://', 'https://')):
                validation_errors.labels(error_type='invalid_url').inc()
                raise DropItem(f"Invalid URL: {item['url']}")
            
            # Type-specific validation
            if isinstance(item, NewsArticleItem):
                self._validate_news_item(item)
            elif isinstance(item, ProductItem):
                self._validate_product_item(item)
            
            items_processed.labels(item_type=type(item).__name__, status='validated').inc()
            
            duration = (datetime.utcnow() - start_time).total_seconds()
            processing_duration.labels(pipeline='validation').observe(duration)
            
            return item
            
        except DropItem:
            items_processed.labels(item_type=type(item).__name__, status='dropped').inc()
            raise
        except Exception as e:
            validation_errors.labels(error_type='processing_error').inc()
            self.logger.error(f"Validation error for {item.get('url', 'unknown')}: {e}")
            raise DropItem(f"Validation error: {e}")
    
    def _validate_news_item(self, item):
        """Validate news article specific fields."""
        if not item.get('headline') and not item.get('title'):
            validation_errors.labels(error_type='missing_headline').inc()
            raise DropItem("News article missing headline")
    
    def _validate_product_item(self, item):
        """Validate product specific fields."""
        if not item.get('product_name') and not item.get('title'):
            validation_errors.labels(error_type='missing_product_name').inc()
            raise DropItem("Product missing name")


class DeduplicationPipeline:
    """Remove duplicate items using Redis."""
    
    def __init__(self, redis_host='localhost', redis_port=6379, redis_db=0, 
                 redis_password=None, ttl_seconds=86400):
        self.redis_host = redis_host
        self.redis_port = redis_port
        self.redis_db = redis_db
        self.redis_password = redis_password
        self.ttl_seconds = ttl_seconds
        self.redis_client = None
        self.logger = logging.getLogger(__name__)
    
    @classmethod
    def from_crawler(cls, crawler):
        """Create pipeline from crawler settings."""
        return cls(
            redis_host=crawler.settings.get('REDIS_HOST', 'localhost'),
            redis_port=crawler.settings.get('REDIS_PORT', 6379),
            redis_db=crawler.settings.get('REDIS_DB', 0),
            redis_password=crawler.settings.get('REDIS_PASSWORD'),
            ttl_seconds=crawler.settings.get('DEDUP_TTL_SECONDS', 86400)
        )
    
    def open_spider(self, spider):
        """Initialize Redis connection."""
        try:
            self.redis_client = redis.Redis(
                host=self.redis_host,
                port=self.redis_port,
                db=self.redis_db,
                password=self.redis_password,
                decode_responses=True
            )
            # Test connection
            self.redis_client.ping()
            self.logger.info("Connected to Redis for deduplication")
        except Exception as e:
            self.logger.error(f"Failed to connect to Redis: {e}")
            self.redis_client = None
    
    def close_spider(self, spider):
        """Close Redis connection."""
        if self.redis_client:
            self.redis_client.close()
    
    def process_item(self, item, spider):
        """Check for duplicates and filter."""
        start_time = datetime.utcnow()
        
        try:
            # Generate item fingerprint
            fingerprint = self._generate_fingerprint(item)
            
            if self.redis_client:
                # Check if item exists
                if self.redis_client.exists(fingerprint):
                    duplicate_items.labels(item_type=type(item).__name__).inc()
                    raise DropItem(f"Duplicate item: {item.get('url', 'unknown')}")
                
                # Store fingerprint with TTL
                self.redis_client.setex(fingerprint, self.ttl_seconds, "1")
            else:
                # Fallback: use in-memory set (not persistent)
                if not hasattr(spider, '_seen_fingerprints'):
                    spider._seen_fingerprints = set()
                
                if fingerprint in spider._seen_fingerprints:
                    duplicate_items.labels(item_type=type(item).__name__).inc()
                    raise DropItem(f"Duplicate item: {item.get('url', 'unknown')}")
                
                spider._seen_fingerprints.add(fingerprint)
            
            duration = (datetime.utcnow() - start_time).total_seconds()
            processing_duration.labels(pipeline='deduplication').observe(duration)
            
            return item
            
        except DropItem:
            raise
        except Exception as e:
            self.logger.error(f"Deduplication error for {item.get('url', 'unknown')}: {e}")
            return item  # Continue processing on error
    
    def _generate_fingerprint(self, item) -> str:
        """Generate unique fingerprint for item."""
        # Use URL and content hash for fingerprinting
        url = item.get('url', '')
        content = item.get('content', '')
        title = item.get('title', '')
        
        # Create hash from key fields
        fingerprint_data = f"{url}|{title}|{hashlib.md5(content.encode()).hexdigest()}"
        return hashlib.sha256(fingerprint_data.encode()).hexdigest()


class DataEnrichmentPipeline:
    """Enrich items with additional metadata."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def process_item(self, item, spider):
        """Enrich item with additional data."""
        start_time = datetime.utcnow()
        
        try:
            # Add processing metadata
            item['processed_at'] = datetime.utcnow().isoformat()
            item['spider_name'] = spider.name
            item['scraper_version'] = '1.0.0'
            
            # Calculate content metrics
            content = item.get('content', '')
            if content:
                item['word_count'] = len(content.split())
                item['character_count'] = len(content)
                item['paragraph_count'] = len([p for p in content.split('\n\n') if p.strip()])
            
            # Extract domain from URL
            if item.get('url'):
                from urllib.parse import urlparse
                parsed_url = urlparse(item['url'])
                item['domain'] = parsed_url.netloc
                item['url_path'] = parsed_url.path
            
            # Add quality score if not present
            if 'quality_score' not in item:
                item['quality_score'] = self._calculate_quality_score(item)
            
            # Add content hash for tracking
            if content:
                item['content_hash'] = hashlib.md5(content.encode()).hexdigest()
            
            duration = (datetime.utcnow() - start_time).total_seconds()
            processing_duration.labels(pipeline='enrichment').observe(duration)
            
            return item
            
        except Exception as e:
            self.logger.error(f"Enrichment error for {item.get('url', 'unknown')}: {e}")
            return item  # Continue processing on error
    
    def _calculate_quality_score(self, item) -> float:
        """Calculate content quality score."""
        score = 0.0
        
        # Content length score
        content_length = len(item.get('content', ''))
        if content_length > 1000:
            score += 0.3
        elif content_length > 500:
            score += 0.2
        elif content_length > 100:
            score += 0.1
        
        # Title presence
        if item.get('title'):
            score += 0.2
        
        # Description presence
        if item.get('description'):
            score += 0.1
        
        # Keywords presence
        if item.get('keywords'):
            score += 0.1
        
        # Links presence
        if item.get('links'):
            score += 0.1
        
        # Images presence
        if item.get('images'):
            score += 0.1
        
        # Language detection
        if item.get('language') and item['language'] != 'unknown':
            score += 0.1
        
        return min(score, 1.0)


class KafkaPipeline:
    """Send items to Kafka."""
    
    def __init__(self, kafka_config):
        self.kafka_config = kafka_config
        self.producer = None
        self.logger = logging.getLogger(__name__)
    
    @classmethod
    def from_crawler(cls, crawler):
        """Create pipeline from crawler settings."""
        kafka_config = crawler.settings.get('KAFKA_PRODUCER_CONFIG', {})
        return cls(kafka_config)
    
    def open_spider(self, spider):
        """Initialize Kafka producer."""
        try:
            self.producer = KafkaProducer(**self.kafka_config)
            self.logger.info("Connected to Kafka")
        except Exception as e:
            self.logger.error(f"Failed to connect to Kafka: {e}")
            self.producer = None
    
    def close_spider(self, spider):
        """Close Kafka producer."""
        if self.producer:
            self.producer.flush()
            self.producer.close()
    
    def process_item(self, item, spider):
        """Send item to Kafka."""
        start_time = datetime.utcnow()
        
        try:
            if not self.producer:
                self.logger.warning("Kafka producer not available, skipping item")
                return item
            
            # Determine topic based on item type
            topic = self._get_topic_for_item(item)
            
            # Prepare message
            message = dict(item)
            message_json = json.dumps(message, default=str, ensure_ascii=False)
            
            # Send to Kafka
            future = self.producer.send(
                topic,
                key=item.get('url', '').encode('utf-8'),
                value=message_json.encode('utf-8')
            )
            
            # Add callback for monitoring
            future.add_callback(self._on_send_success, topic)
            future.add_errback(self._on_send_error, topic)
            
            duration = (datetime.utcnow() - start_time).total_seconds()
            processing_duration.labels(pipeline='kafka').observe(duration)
            
            return item
            
        except Exception as e:
            kafka_messages_sent.labels(topic='unknown', status='error').inc()
            self.logger.error(f"Kafka error for {item.get('url', 'unknown')}: {e}")
            return item  # Continue processing on error
    
    def _get_topic_for_item(self, item) -> str:
        """Determine Kafka topic based on item type."""
        if isinstance(item, NewsArticleItem):
            return 'asi-news-articles'
        elif isinstance(item, ProductItem):
            return 'asi-products'
        else:
            return 'asi-web-pages'
    
    def _on_send_success(self, record_metadata, topic):
        """Handle successful Kafka send."""
        kafka_messages_sent.labels(topic=topic, status='success').inc()
        self.logger.debug(f"Message sent to {topic}:{record_metadata.partition}:{record_metadata.offset}")
    
    def _on_send_error(self, exception, topic):
        """Handle Kafka send error."""
        kafka_messages_sent.labels(topic=topic, status='error').inc()
        self.logger.error(f"Failed to send message to {topic}: {exception}")


class MetricsPipeline:
    """Collect metrics for monitoring."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.items_count = 0
    
    def process_item(self, item, spider):
        """Update metrics."""
        try:
            self.items_count += 1
            items_processed.labels(item_type=type(item).__name__, status='completed').inc()
            
            # Log progress every 100 items
            if self.items_count % 100 == 0:
                self.logger.info(f"Processed {self.items_count} items")
            
            return item
            
        except Exception as e:
            self.logger.error(f"Metrics error: {e}")
            return item
