"""
URL filtering and validation utilities for web scraping.
"""

import re
from urllib.parse import urlparse, urljoin
from typing import List, Set, Optional


class URLFilter:
    """Intelligent URL filtering for web scraping."""
    
    def __init__(self, allowed_domains: List[str] = None):
        self.allowed_domains = set(allowed_domains or [])
        
        # Common file extensions to skip
        self.skip_extensions = {
            '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
            '.zip', '.rar', '.tar', '.gz', '.7z',
            '.mp3', '.mp4', '.avi', '.mov', '.wmv', '.flv',
            '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg', '.ico',
            '.css', '.js', '.xml', '.rss', '.atom',
            '.exe', '.dmg', '.pkg', '.deb', '.rpm'
        }
        
        # URL patterns to skip
        self.skip_patterns = [
            r'/admin/',
            r'/login',
            r'/logout',
            r'/register',
            r'/signup',
            r'/signin',
            r'/account/',
            r'/profile/',
            r'/settings/',
            r'/api/',
            r'/ajax/',
            r'/json/',
            r'/xml/',
            r'/rss/',
            r'/feed/',
            r'/search\?',
            r'/filter\?',
            r'/sort\?',
            r'\.php\?',
            r'\.asp\?',
            r'\.jsp\?',
            r'/print/',
            r'/share/',
            r'/email/',
            r'/download/',
            r'/redirect/',
            r'javascript:',
            r'mailto:',
            r'tel:',
            r'ftp:',
            r'#'
        ]
        
        # Compile patterns for efficiency
        self.compiled_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in self.skip_patterns]
        
        # Track seen URLs to avoid duplicates
        self.seen_urls: Set[str] = set()
    
    def should_follow(self, url: str) -> bool:
        """Determine if a URL should be followed."""
        if not url or not isinstance(url, str):
            return False
        
        # Parse URL
        try:
            parsed = urlparse(url.strip())
        except Exception:
            return False
        
        # Check if URL is valid
        if not parsed.scheme or not parsed.netloc:
            return False
        
        # Only follow HTTP/HTTPS
        if parsed.scheme not in ('http', 'https'):
            return False
        
        # Check domain restrictions
        if self.allowed_domains and parsed.netloc not in self.allowed_domains:
            return False
        
        # Check for duplicate URLs
        normalized_url = self.normalize_url(url)
        if normalized_url in self.seen_urls:
            return False
        
        # Check file extensions
        path = parsed.path.lower()
        if any(path.endswith(ext) for ext in self.skip_extensions):
            return False
        
        # Check skip patterns
        full_url = url.lower()
        if any(pattern.search(full_url) for pattern in self.compiled_patterns):
            return False
        
        # Check for suspicious query parameters
        if self.has_suspicious_params(parsed.query):
            return False
        
        # Check URL length (very long URLs are often spam)
        if len(url) > 2000:
            return False
        
        # Check for too many path segments (might be infinite loops)
        path_segments = [seg for seg in parsed.path.split('/') if seg]
        if len(path_segments) > 10:
            return False
        
        # Add to seen URLs
        self.seen_urls.add(normalized_url)
        
        return True
    
    def normalize_url(self, url: str) -> str:
        """Normalize URL for deduplication."""
        parsed = urlparse(url.strip().lower())
        
        # Remove fragment
        normalized = f"{parsed.scheme}://{parsed.netloc}{parsed.path}"
        
        # Sort query parameters for consistency
        if parsed.query:
            params = sorted(parsed.query.split('&'))
            # Filter out tracking parameters
            filtered_params = [
                param for param in params
                if not self.is_tracking_param(param)
            ]
            if filtered_params:
                normalized += '?' + '&'.join(filtered_params)
        
        # Remove trailing slash for consistency
        if normalized.endswith('/') and len(normalized) > 1:
            normalized = normalized[:-1]
        
        return normalized
    
    def is_tracking_param(self, param: str) -> bool:
        """Check if a query parameter is for tracking."""
        tracking_params = {
            'utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content',
            'gclid', 'fbclid', 'msclkid', 'twclid',
            'ref', 'referrer', 'source', 'campaign',
            'sessionid', 'sid', 'ssid',
            '_ga', '_gid', '_gat',
            'timestamp', 'ts', 'time',
            'random', 'rand', 'r'
        }
        
        param_name = param.split('=')[0].lower()
        return param_name in tracking_params
    
    def has_suspicious_params(self, query: str) -> bool:
        """Check for suspicious query parameters."""
        if not query:
            return False
        
        suspicious_patterns = [
            r'id=\d{10,}',  # Very long IDs
            r'session=',
            r'token=',
            r'key=',
            r'hash=',
            r'signature=',
            r'callback=',
            r'jsonp=',
            r'format=json',
            r'format=xml',
            r'action=delete',
            r'action=remove',
            r'debug=',
            r'test=',
            r'admin=',
        ]
        
        query_lower = query.lower()
        return any(re.search(pattern, query_lower) for pattern in suspicious_patterns)
    
    def add_allowed_domain(self, domain: str):
        """Add a domain to the allowed list."""
        self.allowed_domains.add(domain)
    
    def remove_allowed_domain(self, domain: str):
        """Remove a domain from the allowed list."""
        self.allowed_domains.discard(domain)
    
    def clear_seen_urls(self):
        """Clear the seen URLs cache."""
        self.seen_urls.clear()
    
    def get_stats(self) -> dict:
        """Get filtering statistics."""
        return {
            'allowed_domains': list(self.allowed_domains),
            'seen_urls_count': len(self.seen_urls),
            'skip_extensions_count': len(self.skip_extensions),
            'skip_patterns_count': len(self.skip_patterns)
        }
