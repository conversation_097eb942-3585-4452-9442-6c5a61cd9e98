"""
Intelligent content extraction utilities for web scraping.
"""

import re
import json
from typing import List, Dict, Any, Optional
from urllib.parse import urljoin, urlparse
from datetime import datetime
import dateutil.parser

from w3lib.html import remove_tags
from langdetect import detect, LangDetectError


class ContentExtractor:
    """Intelligent content extraction from web pages."""
    
    def __init__(self):
        # Common patterns
        self.email_pattern = re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b')
        self.phone_pattern = re.compile(r'(\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})')
        self.price_pattern = re.compile(r'[\$£€¥₹]\s*[\d,]+\.?\d*|[\d,]+\.?\d*\s*[\$£€¥₹]')
        self.date_patterns = [
            r'\d{4}-\d{2}-\d{2}',
            r'\d{2}/\d{2}/\d{4}',
            r'\d{2}-\d{2}-\d{4}',
            r'[A-Za-z]+ \d{1,2}, \d{4}',
            r'\d{1,2} [A-Za-z]+ \d{4}'
        ]
    
    def extract_title(self, response) -> str:
        """Extract page title."""
        # Try multiple selectors
        selectors = [
            'title::text',
            'h1::text',
            '.title::text',
            '#title::text',
            '[property="og:title"]::attr(content)',
            '[name="twitter:title"]::attr(content)'
        ]
        
        for selector in selectors:
            title = response.css(selector).get()
            if title:
                return title.strip()
        
        return ""
    
    def extract_meta_description(self, response) -> str:
        """Extract meta description."""
        selectors = [
            'meta[name="description"]::attr(content)',
            'meta[property="og:description"]::attr(content)',
            'meta[name="twitter:description"]::attr(content)'
        ]
        
        for selector in selectors:
            description = response.css(selector).get()
            if description:
                return description.strip()
        
        return ""
    
    def extract_meta_keywords(self, response) -> List[str]:
        """Extract meta keywords."""
        keywords = response.css('meta[name="keywords"]::attr(content)').get()
        if keywords:
            return [kw.strip() for kw in keywords.split(',') if kw.strip()]
        return []
    
    def extract_main_content(self, response) -> str:
        """Extract main content from the page."""
        # Try multiple content selectors
        content_selectors = [
            'article',
            '.content',
            '.main-content',
            '#content',
            '.post-content',
            '.entry-content',
            'main',
            '.article-body',
            '.story-body'
        ]
        
        for selector in content_selectors:
            content = response.css(selector).get()
            if content:
                # Remove HTML tags and clean text
                text = remove_tags(content)
                text = re.sub(r'\s+', ' ', text).strip()
                if len(text) > 100:  # Minimum content length
                    return text
        
        # Fallback: extract all paragraph text
        paragraphs = response.css('p::text').getall()
        if paragraphs:
            content = ' '.join(p.strip() for p in paragraphs if p.strip())
            if len(content) > 100:
                return content
        
        # Last resort: body text
        body_text = remove_tags(response.css('body').get() or '')
        return re.sub(r'\s+', ' ', body_text).strip()
    
    def extract_links(self, response) -> List[str]:
        """Extract all links from the page."""
        links = []
        for link in response.css('a::attr(href)').getall():
            if link:
                absolute_url = urljoin(response.url, link)
                links.append(absolute_url)
        return list(set(links))  # Remove duplicates
    
    def extract_images(self, response) -> List[str]:
        """Extract all image URLs from the page."""
        images = []
        for img in response.css('img::attr(src)').getall():
            if img:
                absolute_url = urljoin(response.url, img)
                images.append(absolute_url)
        return list(set(images))
    
    def extract_emails(self, response) -> List[str]:
        """Extract email addresses from the page."""
        text = response.text
        emails = self.email_pattern.findall(text)
        return list(set(emails))
    
    def extract_phone_numbers(self, response) -> List[str]:
        """Extract phone numbers from the page."""
        text = response.text
        phones = self.phone_pattern.findall(text)
        return ['-'.join(phone) for phone in phones if phone]
    
    def extract_meta_tags(self, response) -> Dict[str, str]:
        """Extract all meta tags."""
        meta_tags = {}
        
        # Standard meta tags
        for meta in response.css('meta'):
            name = meta.css('::attr(name)').get()
            property_attr = meta.css('::attr(property)').get()
            content = meta.css('::attr(content)').get()
            
            if name and content:
                meta_tags[name] = content
            elif property_attr and content:
                meta_tags[property_attr] = content
        
        return meta_tags
    
    def detect_language(self, response) -> str:
        """Detect page language."""
        # Try HTML lang attribute first
        lang = response.css('html::attr(lang)').get()
        if lang:
            return lang.split('-')[0]  # Get primary language code
        
        # Try meta tag
        lang = response.css('meta[http-equiv="content-language"]::attr(content)').get()
        if lang:
            return lang.split('-')[0]
        
        # Use language detection on content
        try:
            content = self.extract_main_content(response)
            if content and len(content) > 50:
                return detect(content)
        except LangDetectError:
            pass
        
        return "unknown"
    
    def extract_headline(self, response) -> str:
        """Extract news headline."""
        selectors = [
            'h1.headline::text',
            'h1.title::text',
            '.headline::text',
            '.article-headline::text',
            'h1::text'
        ]
        
        for selector in selectors:
            headline = response.css(selector).get()
            if headline:
                return headline.strip()
        
        return self.extract_title(response)
    
    def extract_author(self, response) -> str:
        """Extract article author."""
        selectors = [
            '.author::text',
            '.byline::text',
            '[rel="author"]::text',
            '.article-author::text',
            '[itemprop="author"]::text'
        ]
        
        for selector in selectors:
            author = response.css(selector).get()
            if author:
                return author.strip()
        
        return ""
    
    def extract_published_date(self, response) -> str:
        """Extract publication date."""
        # Try structured data first
        selectors = [
            '[itemprop="datePublished"]::attr(content)',
            '[property="article:published_time"]::attr(content)',
            'time::attr(datetime)',
            '.published::text',
            '.date::text'
        ]
        
        for selector in selectors:
            date_str = response.css(selector).get()
            if date_str:
                try:
                    # Try to parse and normalize the date
                    parsed_date = dateutil.parser.parse(date_str)
                    return parsed_date.isoformat()
                except:
                    continue
        
        # Try to find dates in text
        text = response.text
        for pattern in self.date_patterns:
            matches = re.findall(pattern, text)
            if matches:
                try:
                    parsed_date = dateutil.parser.parse(matches[0])
                    return parsed_date.isoformat()
                except:
                    continue
        
        return ""
    
    def extract_category(self, response) -> str:
        """Extract article category."""
        selectors = [
            '.category::text',
            '.section::text',
            '[itemprop="articleSection"]::text',
            '.breadcrumb a:last-child::text'
        ]
        
        for selector in selectors:
            category = response.css(selector).get()
            if category:
                return category.strip()
        
        return ""
    
    def extract_tags(self, response) -> List[str]:
        """Extract article tags."""
        tags = []
        
        # Try various tag selectors
        selectors = [
            '.tags a::text',
            '.tag::text',
            '[rel="tag"]::text',
            '.article-tags a::text'
        ]
        
        for selector in selectors:
            tag_elements = response.css(selector).getall()
            if tag_elements:
                tags.extend([tag.strip() for tag in tag_elements if tag.strip()])
        
        return list(set(tags))
    
    def extract_product_name(self, response) -> str:
        """Extract product name."""
        selectors = [
            'h1.product-title::text',
            '.product-name::text',
            '[itemprop="name"]::text',
            'h1::text'
        ]
        
        for selector in selectors:
            name = response.css(selector).get()
            if name:
                return name.strip()
        
        return ""
    
    def extract_price(self, response) -> str:
        """Extract product price."""
        selectors = [
            '.price::text',
            '[itemprop="price"]::attr(content)',
            '.product-price::text',
            '.current-price::text'
        ]
        
        for selector in selectors:
            price = response.css(selector).get()
            if price:
                # Extract numeric price
                price_match = self.price_pattern.search(price)
                if price_match:
                    return price_match.group().strip()
        
        return ""
    
    def extract_currency(self, response) -> str:
        """Extract currency from price."""
        price_text = self.extract_price(response)
        if '$' in price_text:
            return 'USD'
        elif '£' in price_text:
            return 'GBP'
        elif '€' in price_text:
            return 'EUR'
        elif '¥' in price_text:
            return 'JPY'
        elif '₹' in price_text:
            return 'INR'
        return ""
    
    def extract_availability(self, response) -> str:
        """Extract product availability."""
        selectors = [
            '.availability::text',
            '.stock-status::text',
            '[itemprop="availability"]::text'
        ]
        
        for selector in selectors:
            availability = response.css(selector).get()
            if availability:
                return availability.strip()
        
        return ""
    
    def extract_brand(self, response) -> str:
        """Extract product brand."""
        selectors = [
            '.brand::text',
            '[itemprop="brand"]::text',
            '.manufacturer::text'
        ]
        
        for selector in selectors:
            brand = response.css(selector).get()
            if brand:
                return brand.strip()
        
        return ""
    
    def extract_product_category(self, response) -> str:
        """Extract product category."""
        return self.extract_category(response)
    
    def extract_rating(self, response) -> Optional[float]:
        """Extract product rating."""
        selectors = [
            '[itemprop="ratingValue"]::attr(content)',
            '.rating::text',
            '.stars::attr(data-rating)'
        ]
        
        for selector in selectors:
            rating = response.css(selector).get()
            if rating:
                try:
                    return float(rating.strip())
                except ValueError:
                    continue
        
        return None
    
    def extract_review_count(self, response) -> Optional[int]:
        """Extract number of reviews."""
        selectors = [
            '[itemprop="reviewCount"]::text',
            '.review-count::text',
            '.reviews-count::text'
        ]
        
        for selector in selectors:
            count = response.css(selector).get()
            if count:
                # Extract numeric value
                numbers = re.findall(r'\d+', count)
                if numbers:
                    try:
                        return int(numbers[0])
                    except ValueError:
                        continue
        
        return None
    
    def extract_specifications(self, response) -> Dict[str, str]:
        """Extract product specifications."""
        specs = {}
        
        # Try to find specification tables
        spec_tables = response.css('.specifications table, .specs table, .product-details table')
        
        for table in spec_tables:
            rows = table.css('tr')
            for row in rows:
                cells = row.css('td::text').getall()
                if len(cells) >= 2:
                    key = cells[0].strip()
                    value = cells[1].strip()
                    if key and value:
                        specs[key] = value
        
        # Try key-value pairs in lists
        spec_lists = response.css('.specifications dl, .specs dl')
        for dl in spec_lists:
            terms = dl.css('dt::text').getall()
            definitions = dl.css('dd::text').getall()
            
            for term, definition in zip(terms, definitions):
                if term and definition:
                    specs[term.strip()] = definition.strip()
        
        return specs
