"""
ASI Scraper Settings

Production-ready Scrapy settings for the ASI data ingestion pipeline.
"""

import os
from pathlib import Path

# Scrapy settings for asi_scraper project
BOT_NAME = 'asi_scraper'

SPIDER_MODULES = ['asi_scraper.spiders']
NEWSPIDER_MODULE = 'asi_scraper.spiders'

# Obey robots.txt rules (can be disabled for specific use cases)
ROBOTSTXT_OBEY = os.getenv('ROBOTSTXT_OBEY', 'True').lower() == 'true'

# Configure pipelines
ITEM_PIPELINES = {
    'asi_scraper.pipelines.ValidationPipeline': 100,
    'asi_scraper.pipelines.DeduplicationPipeline': 200,
    'asi_scraper.pipelines.DataEnrichmentPipeline': 300,
    'asi_scraper.pipelines.KafkaPipeline': 400,
    'asi_scraper.pipelines.MetricsPipeline': 500,
}

# Configure middlewares
DOWNLOADER_MIDDLEWARES = {
    'scrapy.downloadermiddlewares.useragent.UserAgentMiddleware': None,
    'scrapy_user_agents.middlewares.RandomUserAgentMiddleware': 400,
    'scrapy_rotating_proxies.middlewares.RotatingProxyMiddleware': 610,
    'scrapy_rotating_proxies.middlewares.BanDetectionMiddleware': 620,
    'asi_scraper.middlewares.RetryMiddleware': 550,
    'asi_scraper.middlewares.RateLimitMiddleware': 560,
}

# User agent settings
RANDOM_UA_PER_PROXY = True
RANDOM_UA_TYPE = 'random'

# Proxy settings
ROTATING_PROXY_LIST_PATH = os.getenv('PROXY_LIST_PATH', 'proxies.txt')
ROTATING_PROXY_CLOSE_SPIDER = True

# Request settings
CONCURRENT_REQUESTS = int(os.getenv('CONCURRENT_REQUESTS', '16'))
CONCURRENT_REQUESTS_PER_DOMAIN = int(os.getenv('CONCURRENT_REQUESTS_PER_DOMAIN', '8'))
DOWNLOAD_DELAY = float(os.getenv('DOWNLOAD_DELAY', '1.0'))
RANDOMIZE_DOWNLOAD_DELAY = True

# AutoThrottle settings
AUTOTHROTTLE_ENABLED = True
AUTOTHROTTLE_START_DELAY = 1
AUTOTHROTTLE_MAX_DELAY = 60
AUTOTHROTTLE_TARGET_CONCURRENCY = 2.0
AUTOTHROTTLE_DEBUG = False

# Memory usage settings
MEMUSAGE_ENABLED = True
MEMUSAGE_LIMIT_MB = int(os.getenv('MEMORY_LIMIT_MB', '2048'))
MEMUSAGE_WARNING_MB = int(os.getenv('MEMORY_WARNING_MB', '1536'))

# Cache settings
HTTPCACHE_ENABLED = os.getenv('HTTPCACHE_ENABLED', 'False').lower() == 'true'
HTTPCACHE_EXPIRATION_SECS = int(os.getenv('HTTPCACHE_EXPIRATION_SECS', '3600'))
HTTPCACHE_DIR = 'httpcache'
HTTPCACHE_IGNORE_HTTP_CODES = [500, 502, 503, 504, 408, 429]

# Retry settings
RETRY_ENABLED = True
RETRY_TIMES = int(os.getenv('RETRY_TIMES', '3'))
RETRY_HTTP_CODES = [500, 502, 503, 504, 408, 429]

# Timeout settings
DOWNLOAD_TIMEOUT = int(os.getenv('DOWNLOAD_TIMEOUT', '180'))

# Compression
COMPRESSION_ENABLED = True

# Logging
LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
LOG_FORMAT = '%(levelname)s: %(message)s'

# Custom settings
KAFKA_BOOTSTRAP_SERVERS = os.getenv('KAFKA_BOOTSTRAP_SERVERS', 'localhost:9092').split(',')
KAFKA_TOPIC = os.getenv('KAFKA_TOPIC', 'asi-web-scraping')
KAFKA_PRODUCER_CONFIG = {
    'bootstrap_servers': KAFKA_BOOTSTRAP_SERVERS,
    'value_serializer': lambda x: x.encode('utf-8') if isinstance(x, str) else x,
    'key_serializer': lambda x: x.encode('utf-8') if isinstance(x, str) else x,
    'acks': 'all',
    'retries': 3,
    'batch_size': 16384,
    'linger_ms': 10,
    'buffer_memory': 33554432,
    'compression_type': 'snappy',
}

# Redis settings for deduplication
REDIS_HOST = os.getenv('REDIS_HOST', 'localhost')
REDIS_PORT = int(os.getenv('REDIS_PORT', '6379'))
REDIS_DB = int(os.getenv('REDIS_DB', '0'))
REDIS_PASSWORD = os.getenv('REDIS_PASSWORD')

# gRPC settings
GRPC_DATA_INTEGRATION_ENDPOINT = os.getenv('GRPC_DATA_INTEGRATION_ENDPOINT', 'localhost:50052')
GRPC_TIMEOUT = int(os.getenv('GRPC_TIMEOUT', '30'))

# Monitoring
PROMETHEUS_PORT = int(os.getenv('PROMETHEUS_PORT', '8082'))
SENTRY_DSN = os.getenv('SENTRY_DSN')

# Data quality settings
MIN_CONTENT_LENGTH = int(os.getenv('MIN_CONTENT_LENGTH', '100'))
MAX_CONTENT_LENGTH = int(os.getenv('MAX_CONTENT_LENGTH', '10485760'))  # 10MB

# Rate limiting
RATE_LIMIT_ENABLED = os.getenv('RATE_LIMIT_ENABLED', 'True').lower() == 'true'
RATE_LIMIT_REQUESTS_PER_MINUTE = int(os.getenv('RATE_LIMIT_REQUESTS_PER_MINUTE', '60'))

# Request fingerprinting
REQUEST_FINGERPRINTER_IMPLEMENTATION = '2.7'

# Set settings whose default value is deprecated to a future-proof value
TWISTED_REACTOR = 'twisted.internet.asyncioreactor.AsyncioSelectorReactor'
FEED_EXPORT_ENCODING = 'utf-8'
