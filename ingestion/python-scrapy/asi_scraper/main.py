"""
ASI Scraper Main Application

Entry point for the ASI web scraping service with monitoring and health checks.
"""

import os
import sys
import logging
import asyncio
import signal
from typing import Dict, Any, Optional
from datetime import datetime
import threading
import time

import uvicorn
from fastapi import FastAPI, HTTPException
from fastapi.responses import PlainTextResponse
from prometheus_client import generate_latest, CONTENT_TYPE_LATEST
from scrapy.crawler import CrawlerProcess
from scrapy.utils.project import get_project_settings
from twisted.internet import reactor

from .spiders.general_spider import GeneralSpider


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ScrapyService:
    """Service wrapper for Scrapy crawler."""
    
    def __init__(self):
        self.process = None
        self.is_running = False
        self.stats = {
            'start_time': None,
            'items_scraped': 0,
            'pages_crawled': 0,
            'errors': 0,
            'active_spiders': 0
        }
    
    def start_spider(self, spider_name: str, **kwargs) -> Dict[str, Any]:
        """Start a spider with given parameters."""
        try:
            if self.is_running:
                raise Exception("Crawler is already running")
            
            # Get project settings
            settings = get_project_settings()
            
            # Override settings from environment
            self._update_settings_from_env(settings)
            
            # Create crawler process
            self.process = CrawlerProcess(settings)
            
            # Add spider
            if spider_name == 'general':
                self.process.crawl(GeneralSpider, **kwargs)
            else:
                raise Exception(f"Unknown spider: {spider_name}")
            
            # Start crawling in a separate thread
            def run_crawler():
                self.is_running = True
                self.stats['start_time'] = datetime.utcnow()
                self.stats['active_spiders'] = 1
                
                try:
                    self.process.start(stop_after_crawl=True)
                except Exception as e:
                    logger.error(f"Crawler error: {e}")
                    self.stats['errors'] += 1
                finally:
                    self.is_running = False
                    self.stats['active_spiders'] = 0
            
            crawler_thread = threading.Thread(target=run_crawler, daemon=True)
            crawler_thread.start()
            
            return {
                'status': 'started',
                'spider': spider_name,
                'message': 'Spider started successfully'
            }
            
        except Exception as e:
            logger.error(f"Failed to start spider {spider_name}: {e}")
            return {
                'status': 'error',
                'spider': spider_name,
                'message': str(e)
            }
    
    def stop_spider(self) -> Dict[str, Any]:
        """Stop the running spider."""
        try:
            if not self.is_running:
                return {
                    'status': 'not_running',
                    'message': 'No spider is currently running'
                }
            
            if self.process:
                self.process.stop()
            
            return {
                'status': 'stopped',
                'message': 'Spider stopped successfully'
            }
            
        except Exception as e:
            logger.error(f"Failed to stop spider: {e}")
            return {
                'status': 'error',
                'message': str(e)
            }
    
    def get_status(self) -> Dict[str, Any]:
        """Get current status and statistics."""
        return {
            'is_running': self.is_running,
            'stats': self.stats.copy(),
            'uptime_seconds': (
                (datetime.utcnow() - self.stats['start_time']).total_seconds()
                if self.stats['start_time'] else 0
            )
        }
    
    def _update_settings_from_env(self, settings):
        """Update Scrapy settings from environment variables."""
        env_mappings = {
            'CONCURRENT_REQUESTS': 'CONCURRENT_REQUESTS',
            'DOWNLOAD_DELAY': 'DOWNLOAD_DELAY',
            'ROBOTSTXT_OBEY': 'ROBOTSTXT_OBEY',
            'LOG_LEVEL': 'LOG_LEVEL',
            'KAFKA_BOOTSTRAP_SERVERS': 'KAFKA_BOOTSTRAP_SERVERS',
            'KAFKA_TOPIC': 'KAFKA_TOPIC',
            'REDIS_HOST': 'REDIS_HOST',
            'REDIS_PORT': 'REDIS_PORT',
        }
        
        for env_var, setting_name in env_mappings.items():
            value = os.getenv(env_var)
            if value is not None:
                # Convert types as needed
                if setting_name in ['CONCURRENT_REQUESTS', 'REDIS_PORT']:
                    value = int(value)
                elif setting_name in ['DOWNLOAD_DELAY']:
                    value = float(value)
                elif setting_name in ['ROBOTSTXT_OBEY']:
                    value = value.lower() == 'true'
                
                settings.set(setting_name, value)


# Global service instance
scraper_service = ScrapyService()

# FastAPI app for HTTP API
app = FastAPI(
    title="ASI Scraper Service",
    description="Web scraping service for the ASI data ingestion pipeline",
    version="1.0.0"
)


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": "asi-scraper",
        "version": "1.0.0"
    }


@app.get("/metrics")
async def metrics():
    """Prometheus metrics endpoint."""
    return PlainTextResponse(
        generate_latest(),
        media_type=CONTENT_TYPE_LATEST
    )


@app.get("/status")
async def get_status():
    """Get scraper status and statistics."""
    return scraper_service.get_status()


@app.post("/spiders/{spider_name}/start")
async def start_spider(spider_name: str, config: Dict[str, Any] = None):
    """Start a spider with configuration."""
    if config is None:
        config = {}
    
    result = scraper_service.start_spider(spider_name, **config)
    
    if result['status'] == 'error':
        raise HTTPException(status_code=400, detail=result['message'])
    
    return result


@app.post("/spiders/stop")
async def stop_spider():
    """Stop the running spider."""
    result = scraper_service.stop_spider()
    
    if result['status'] == 'error':
        raise HTTPException(status_code=400, detail=result['message'])
    
    return result


@app.get("/spiders")
async def list_spiders():
    """List available spiders."""
    return {
        "spiders": [
            {
                "name": "general",
                "description": "General purpose spider for web scraping",
                "parameters": [
                    "start_urls",
                    "allowed_domains",
                    "max_depth",
                    "follow_links",
                    "content_types"
                ]
            }
        ]
    }


def signal_handler(signum, frame):
    """Handle shutdown signals."""
    logger.info(f"Received signal {signum}, shutting down...")
    
    # Stop scraper service
    scraper_service.stop_spider()
    
    # Exit
    sys.exit(0)


def main():
    """Main entry point."""
    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Get configuration from environment
    host = os.getenv('HOST', '0.0.0.0')
    port = int(os.getenv('PORT', '8082'))
    log_level = os.getenv('LOG_LEVEL', 'info').lower()
    
    logger.info(f"Starting ASI Scraper Service on {host}:{port}")
    
    # Start FastAPI server
    try:
        uvicorn.run(
            app,
            host=host,
            port=port,
            log_level=log_level,
            access_log=True
        )
    except Exception as e:
        logger.error(f"Failed to start server: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
