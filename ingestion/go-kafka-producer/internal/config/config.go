package config

import (
	"os"
	"strconv"
	"strings"
	"time"

	"gopkg.in/yaml.v3"
)

// Config holds the application configuration
type Config struct {
	GRPCPort    int           `yaml:"grpc_port"`
	MetricsPort int           `yaml:"metrics_port"`
	Kafka       KafkaConfig   `yaml:"kafka"`
	Logging     LoggingConfig `yaml:"logging"`
}

// KafkaConfig holds Kafka-specific configuration
type KafkaConfig struct {
	Brokers              []string      `yaml:"brokers"`
	ClientID             string        `yaml:"client_id"`
	MaxMessageBytes      int           `yaml:"max_message_bytes"`
	RequiredAcks         int           `yaml:"required_acks"`
	Timeout              time.Duration `yaml:"timeout"`
	CompressionType      string        `yaml:"compression_type"`
	FlushFrequency       time.Duration `yaml:"flush_frequency"`
	FlushMessages        int           `yaml:"flush_messages"`
	FlushBytes           int           `yaml:"flush_bytes"`
	RetryMax             int           `yaml:"retry_max"`
	RetryBackoff         time.Duration `yaml:"retry_backoff"`
	EnableIdempotence    bool          `yaml:"enable_idempotence"`
	MaxInFlightRequests  int           `yaml:"max_in_flight_requests"`
	BatchSize            int           `yaml:"batch_size"`
	LingerMs             time.Duration `yaml:"linger_ms"`
	SecurityProtocol     string        `yaml:"security_protocol"`
	SASLMechanism        string        `yaml:"sasl_mechanism"`
	SASLUsername         string        `yaml:"sasl_username"`
	SASLPassword         string        `yaml:"sasl_password"`
	TLSEnabled           bool          `yaml:"tls_enabled"`
	TLSSkipVerify        bool          `yaml:"tls_skip_verify"`
}

// LoggingConfig holds logging configuration
type LoggingConfig struct {
	Level  string `yaml:"level"`
	Format string `yaml:"format"`
}

// Load loads configuration from environment variables and config file
func Load() (*Config, error) {
	cfg := &Config{
		GRPCPort:    getEnvInt("GRPC_PORT", 50051),
		MetricsPort: getEnvInt("METRICS_PORT", 8080),
		Kafka: KafkaConfig{
			Brokers:              getEnvStringSlice("KAFKA_BROKERS", []string{"localhost:9092"}),
			ClientID:             getEnvString("KAFKA_CLIENT_ID", "asi-ingestion-producer"),
			MaxMessageBytes:      getEnvInt("KAFKA_MAX_MESSAGE_BYTES", 1000000),
			RequiredAcks:         getEnvInt("KAFKA_REQUIRED_ACKS", 1),
			Timeout:              getEnvDuration("KAFKA_TIMEOUT", 30*time.Second),
			CompressionType:      getEnvString("KAFKA_COMPRESSION", "snappy"),
			FlushFrequency:       getEnvDuration("KAFKA_FLUSH_FREQUENCY", 100*time.Millisecond),
			FlushMessages:        getEnvInt("KAFKA_FLUSH_MESSAGES", 100),
			FlushBytes:           getEnvInt("KAFKA_FLUSH_BYTES", 16384),
			RetryMax:             getEnvInt("KAFKA_RETRY_MAX", 3),
			RetryBackoff:         getEnvDuration("KAFKA_RETRY_BACKOFF", 100*time.Millisecond),
			EnableIdempotence:    getEnvBool("KAFKA_ENABLE_IDEMPOTENCE", true),
			MaxInFlightRequests:  getEnvInt("KAFKA_MAX_IN_FLIGHT", 5),
			BatchSize:            getEnvInt("KAFKA_BATCH_SIZE", 16384),
			LingerMs:             getEnvDuration("KAFKA_LINGER_MS", 5*time.Millisecond),
			SecurityProtocol:     getEnvString("KAFKA_SECURITY_PROTOCOL", "PLAINTEXT"),
			SASLMechanism:        getEnvString("KAFKA_SASL_MECHANISM", ""),
			SASLUsername:         getEnvString("KAFKA_SASL_USERNAME", ""),
			SASLPassword:         getEnvString("KAFKA_SASL_PASSWORD", ""),
			TLSEnabled:           getEnvBool("KAFKA_TLS_ENABLED", false),
			TLSSkipVerify:        getEnvBool("KAFKA_TLS_SKIP_VERIFY", false),
		},
		Logging: LoggingConfig{
			Level:  getEnvString("LOG_LEVEL", "info"),
			Format: getEnvString("LOG_FORMAT", "json"),
		},
	}

	// Load from config file if exists
	if configFile := getEnvString("CONFIG_FILE", ""); configFile != "" {
		if err := loadFromFile(cfg, configFile); err != nil {
			return nil, err
		}
	}

	return cfg, nil
}

func loadFromFile(cfg *Config, filename string) error {
	data, err := os.ReadFile(filename)
	if err != nil {
		return err
	}
	return yaml.Unmarshal(data, cfg)
}

func getEnvString(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getEnvBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}

func getEnvDuration(key string, defaultValue time.Duration) time.Duration {
	if value := os.Getenv(key); value != "" {
		if duration, err := time.ParseDuration(value); err == nil {
			return duration
		}
	}
	return defaultValue
}

func getEnvStringSlice(key string, defaultValue []string) []string {
	if value := os.Getenv(key); value != "" {
		return strings.Split(value, ",")
	}
	return defaultValue
}
