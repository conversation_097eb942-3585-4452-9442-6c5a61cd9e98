package kafka

import (
	"context"
	"crypto/tls"
	"fmt"
	"sync"
	"time"

	"github.com/Shopify/sarama"
	"github.com/asi-system/ingestion/go-kafka-producer/internal/config"
	"github.com/asi-system/ingestion/go-kafka-producer/internal/metrics"
	"github.com/sirupsen/logrus"
)

// Producer wraps Sarama async producer with additional functionality
type Producer struct {
	producer sarama.AsyncProducer
	logger   *logrus.Logger
	metrics  *metrics.Collector
	wg       sync.WaitGroup
	ctx      context.Context
	cancel   context.CancelFunc
}

// Message represents a Kafka message to be produced
type Message struct {
	Topic     string
	Key       string
	Value     []byte
	Headers   map[string]string
	Timestamp time.Time
}

// NewProducer creates a new Kafka producer
func NewProducer(cfg config.KafkaConfig, logger *logrus.Logger, metrics *metrics.Collector) (*Producer, error) {
	config := sarama.NewConfig()
	
	// Producer configuration
	config.Producer.RequiredAcks = sarama.RequiredAcks(cfg.RequiredAcks)
	config.Producer.Timeout = cfg.Timeout
	config.Producer.Compression = getCompressionType(cfg.CompressionType)
	config.Producer.Flush.Frequency = cfg.FlushFrequency
	config.Producer.Flush.Messages = cfg.FlushMessages
	config.Producer.Flush.Bytes = cfg.FlushBytes
	config.Producer.Retry.Max = cfg.RetryMax
	config.Producer.Retry.Backoff = cfg.RetryBackoff
	config.Producer.Idempotent = cfg.EnableIdempotence
	config.Net.MaxOpenRequests = cfg.MaxInFlightRequests
	config.Producer.MaxMessageBytes = cfg.MaxMessageBytes
	
	// Client configuration
	config.ClientID = cfg.ClientID
	config.Version = sarama.V2_8_0_0
	
	// Security configuration
	if cfg.TLSEnabled {
		config.Net.TLS.Enable = true
		config.Net.TLS.Config = &tls.Config{
			InsecureSkipVerify: cfg.TLSSkipVerify,
		}
	}
	
	if cfg.SASLMechanism != "" {
		config.Net.SASL.Enable = true
		config.Net.SASL.Mechanism = sarama.SASLMechanism(cfg.SASLMechanism)
		config.Net.SASL.User = cfg.SASLUsername
		config.Net.SASL.Password = cfg.SASLPassword
	}
	
	// Create producer
	producer, err := sarama.NewAsyncProducer(cfg.Brokers, config)
	if err != nil {
		return nil, fmt.Errorf("failed to create Kafka producer: %w", err)
	}
	
	ctx, cancel := context.WithCancel(context.Background())
	
	p := &Producer{
		producer: producer,
		logger:   logger,
		metrics:  metrics,
		ctx:      ctx,
		cancel:   cancel,
	}
	
	// Start background goroutines for handling successes and errors
	p.wg.Add(2)
	go p.handleSuccesses()
	go p.handleErrors()
	
	logger.Info("Kafka producer initialized successfully")
	return p, nil
}

// SendMessage sends a message to Kafka asynchronously
func (p *Producer) SendMessage(msg *Message) error {
	headers := make([]sarama.RecordHeader, 0, len(msg.Headers))
	for k, v := range msg.Headers {
		headers = append(headers, sarama.RecordHeader{
			Key:   []byte(k),
			Value: []byte(v),
		})
	}
	
	kafkaMsg := &sarama.ProducerMessage{
		Topic:     msg.Topic,
		Key:       sarama.StringEncoder(msg.Key),
		Value:     sarama.ByteEncoder(msg.Value),
		Headers:   headers,
		Timestamp: msg.Timestamp,
	}
	
	select {
	case p.producer.Input() <- kafkaMsg:
		p.metrics.IncMessagesSent(msg.Topic)
		p.logger.WithFields(logrus.Fields{
			"topic": msg.Topic,
			"key":   msg.Key,
			"size":  len(msg.Value),
		}).Debug("Message sent to Kafka")
		return nil
	case <-p.ctx.Done():
		return fmt.Errorf("producer is shutting down")
	default:
		p.metrics.IncMessagesDropped(msg.Topic)
		return fmt.Errorf("producer input channel is full")
	}
}

// SendMessageSync sends a message to Kafka synchronously with timeout
func (p *Producer) SendMessageSync(msg *Message, timeout time.Duration) error {
	done := make(chan error, 1)
	
	// Create a unique correlation ID for tracking
	correlationID := fmt.Sprintf("%s-%d", msg.Key, time.Now().UnixNano())
	if msg.Headers == nil {
		msg.Headers = make(map[string]string)
	}
	msg.Headers["correlation_id"] = correlationID
	
	// Send the message
	if err := p.SendMessage(msg); err != nil {
		return err
	}
	
	// Wait for completion or timeout
	ctx, cancel := context.WithTimeout(p.ctx, timeout)
	defer cancel()
	
	select {
	case <-ctx.Done():
		return fmt.Errorf("message send timeout after %v", timeout)
	case err := <-done:
		return err
	}
}

// GetMetrics returns current producer metrics
func (p *Producer) GetMetrics() map[string]interface{} {
	return map[string]interface{}{
		"messages_sent":    p.metrics.GetMessagesSent(),
		"messages_failed":  p.metrics.GetMessagesFailed(),
		"messages_dropped": p.metrics.GetMessagesDropped(),
		"queue_depth":      len(p.producer.Input()),
	}
}

// Close gracefully shuts down the producer
func (p *Producer) Close() error {
	p.logger.Info("Shutting down Kafka producer...")
	
	p.cancel()
	
	// Close the producer
	if err := p.producer.Close(); err != nil {
		p.logger.WithError(err).Error("Error closing Kafka producer")
		return err
	}
	
	// Wait for background goroutines to finish
	p.wg.Wait()
	
	p.logger.Info("Kafka producer shut down successfully")
	return nil
}

// handleSuccesses processes successful message deliveries
func (p *Producer) handleSuccesses() {
	defer p.wg.Done()
	
	for {
		select {
		case success := <-p.producer.Successes():
			p.metrics.IncMessagesSuccess(success.Topic)
			p.logger.WithFields(logrus.Fields{
				"topic":     success.Topic,
				"partition": success.Partition,
				"offset":    success.Offset,
			}).Debug("Message delivered successfully")
			
		case <-p.ctx.Done():
			return
		}
	}
}

// handleErrors processes message delivery errors
func (p *Producer) handleErrors() {
	defer p.wg.Done()
	
	for {
		select {
		case err := <-p.producer.Errors():
			p.metrics.IncMessagesFailed(err.Msg.Topic)
			p.logger.WithFields(logrus.Fields{
				"topic": err.Msg.Topic,
				"error": err.Err.Error(),
			}).Error("Failed to deliver message")
			
		case <-p.ctx.Done():
			return
		}
	}
}

// getCompressionType converts string to Sarama compression type
func getCompressionType(compression string) sarama.CompressionCodec {
	switch compression {
	case "gzip":
		return sarama.CompressionGZIP
	case "snappy":
		return sarama.CompressionSnappy
	case "lz4":
		return sarama.CompressionLZ4
	case "zstd":
		return sarama.CompressionZSTD
	default:
		return sarama.CompressionNone
	}
}
