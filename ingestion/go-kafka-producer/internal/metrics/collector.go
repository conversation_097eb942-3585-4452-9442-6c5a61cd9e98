package metrics

import (
	"sync"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

// Collector holds all metrics for the ingestion service
type Collector struct {
	// Counters
	messagesSent    *prometheus.CounterVec
	messagesSuccess *prometheus.CounterVec
	messagesFailed  *prometheus.CounterVec
	messagesDropped *prometheus.CounterVec
	grpcRequests    *prometheus.CounterVec
	
	// Histograms
	messageSizeBytes    *prometheus.HistogramVec
	processingDuration  *prometheus.HistogramVec
	grpcDuration        *prometheus.HistogramVec
	
	// Gauges
	queueDepth          *prometheus.GaugeVec
	activeConnections   prometheus.Gauge
	
	// Internal counters for non-Prometheus metrics
	mu                  sync.RWMutex
	totalSent           map[string]int64
	totalSuccess        map[string]int64
	totalFailed         map[string]int64
	totalDropped        map[string]int64
}

// NewCollector creates a new metrics collector
func NewCollector() *Collector {
	return &Collector{
		messagesSent: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "asi_ingestion_messages_sent_total",
				Help: "Total number of messages sent to Kafka",
			},
			[]string{"topic", "source_type"},
		),
		messagesSuccess: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "asi_ingestion_messages_success_total",
				Help: "Total number of messages successfully delivered",
			},
			[]string{"topic", "source_type"},
		),
		messagesFailed: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "asi_ingestion_messages_failed_total",
				Help: "Total number of messages that failed to deliver",
			},
			[]string{"topic", "source_type"},
		),
		messagesDropped: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "asi_ingestion_messages_dropped_total",
				Help: "Total number of messages dropped due to queue full",
			},
			[]string{"topic", "source_type"},
		),
		grpcRequests: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "asi_ingestion_grpc_requests_total",
				Help: "Total number of gRPC requests",
			},
			[]string{"method", "status"},
		),
		messageSizeBytes: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "asi_ingestion_message_size_bytes",
				Help:    "Size of ingested messages in bytes",
				Buckets: prometheus.ExponentialBuckets(1024, 2, 10), // 1KB to 512MB
			},
			[]string{"topic", "source_type"},
		),
		processingDuration: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "asi_ingestion_processing_duration_seconds",
				Help:    "Time spent processing messages",
				Buckets: prometheus.DefBuckets,
			},
			[]string{"operation", "status"},
		),
		grpcDuration: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "asi_ingestion_grpc_duration_seconds",
				Help:    "Duration of gRPC requests",
				Buckets: prometheus.DefBuckets,
			},
			[]string{"method"},
		),
		queueDepth: promauto.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "asi_ingestion_queue_depth",
				Help: "Current depth of message queues",
			},
			[]string{"queue_type"},
		),
		activeConnections: promauto.NewGauge(
			prometheus.GaugeOpts{
				Name: "asi_ingestion_active_connections",
				Help: "Number of active gRPC connections",
			},
		),
		totalSent:    make(map[string]int64),
		totalSuccess: make(map[string]int64),
		totalFailed:  make(map[string]int64),
		totalDropped: make(map[string]int64),
	}
}

// IncMessagesSent increments the messages sent counter
func (c *Collector) IncMessagesSent(topic string) {
	c.messagesSent.WithLabelValues(topic, "unknown").Inc()
	c.mu.Lock()
	c.totalSent[topic]++
	c.mu.Unlock()
}

// IncMessagesSuccess increments the messages success counter
func (c *Collector) IncMessagesSuccess(topic string) {
	c.messagesSuccess.WithLabelValues(topic, "unknown").Inc()
	c.mu.Lock()
	c.totalSuccess[topic]++
	c.mu.Unlock()
}

// IncMessagesFailed increments the messages failed counter
func (c *Collector) IncMessagesFailed(topic string) {
	c.messagesFailed.WithLabelValues(topic, "unknown").Inc()
	c.mu.Lock()
	c.totalFailed[topic]++
	c.mu.Unlock()
}

// IncMessagesDropped increments the messages dropped counter
func (c *Collector) IncMessagesDropped(topic string) {
	c.messagesDropped.WithLabelValues(topic, "unknown").Inc()
	c.mu.Lock()
	c.totalDropped[topic]++
	c.mu.Unlock()
}

// IncGRPCRequests increments the gRPC requests counter
func (c *Collector) IncGRPCRequests(method, status string) {
	c.grpcRequests.WithLabelValues(method, status).Inc()
}

// ObserveMessageSize records message size
func (c *Collector) ObserveMessageSize(topic string, size int) {
	c.messageSizeBytes.WithLabelValues(topic, "unknown").Observe(float64(size))
}

// ObserveProcessingDuration records processing duration
func (c *Collector) ObserveProcessingDuration(operation, status string, duration time.Duration) {
	c.processingDuration.WithLabelValues(operation, status).Observe(duration.Seconds())
}

// ObserveGRPCDuration records gRPC request duration
func (c *Collector) ObserveGRPCDuration(method string, duration time.Duration) {
	c.grpcDuration.WithLabelValues(method).Observe(duration.Seconds())
}

// SetQueueDepth sets the current queue depth
func (c *Collector) SetQueueDepth(queueType string, depth int) {
	c.queueDepth.WithLabelValues(queueType).Set(float64(depth))
}

// SetActiveConnections sets the number of active connections
func (c *Collector) SetActiveConnections(count int) {
	c.activeConnections.Set(float64(count))
}

// GetMessagesSent returns total messages sent for a topic
func (c *Collector) GetMessagesSent() map[string]int64 {
	c.mu.RLock()
	defer c.mu.RUnlock()
	result := make(map[string]int64)
	for k, v := range c.totalSent {
		result[k] = v
	}
	return result
}

// GetMessagesSuccess returns total successful messages for a topic
func (c *Collector) GetMessagesSuccess() map[string]int64 {
	c.mu.RLock()
	defer c.mu.RUnlock()
	result := make(map[string]int64)
	for k, v := range c.totalSuccess {
		result[k] = v
	}
	return result
}

// GetMessagesFailed returns total failed messages for a topic
func (c *Collector) GetMessagesFailed() map[string]int64 {
	c.mu.RLock()
	defer c.mu.RUnlock()
	result := make(map[string]int64)
	for k, v := range c.totalFailed {
		result[k] = v
	}
	return result
}

// GetMessagesDropped returns total dropped messages for a topic
func (c *Collector) GetMessagesDropped() map[string]int64 {
	c.mu.RLock()
	defer c.mu.RUnlock()
	result := make(map[string]int64)
	for k, v := range c.totalDropped {
		result[k] = v
	}
	return result
}
