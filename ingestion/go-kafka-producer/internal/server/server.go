package server

import (
	"context"
	"fmt"
	"time"

	"github.com/asi-system/ingestion/go-kafka-producer/internal/kafka"
	"github.com/asi-system/ingestion/go-kafka-producer/internal/metrics"
	pb "github.com/asi-system/ingestion/proto/gen/go"
	"github.com/golang/protobuf/ptypes/timestamp"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// IngestionServer implements the DataIngestionService
type IngestionServer struct {
	pb.UnimplementedDataIngestionServiceServer
	producer *kafka.Producer
	logger   *logrus.Logger
	metrics  *metrics.Collector
}

// NewIngestionServer creates a new ingestion server
func NewIngestionServer(producer *kafka.Producer, logger *logrus.Logger, metrics *metrics.Collector) *IngestionServer {
	return &IngestionServer{
		producer: producer,
		logger:   logger,
		metrics:  metrics,
	}
}

// IngestStructuredData handles structured data ingestion
func (s *IngestionServer) IngestStructuredData(ctx context.Context, req *pb.IngestStructuredDataRequest) (*pb.IngestResponse, error) {
	start := time.Now()
	defer func() {
		s.metrics.ObserveGRPCDuration("IngestStructuredData", time.Since(start))
	}()

	// Validate request
	if req.Metadata == nil {
		s.metrics.IncGRPCRequests("IngestStructuredData", "error")
		return nil, status.Error(codes.InvalidArgument, "metadata is required")
	}

	if req.KafkaTopic == "" {
		s.metrics.IncGRPCRequests("IngestStructuredData", "error")
		return nil, status.Error(codes.InvalidArgument, "kafka_topic is required")
	}

	// Serialize payload
	payloadBytes, err := proto.Marshal(req.Payload)
	if err != nil {
		s.metrics.IncGRPCRequests("IngestStructuredData", "error")
		s.logger.WithError(err).Error("Failed to marshal payload")
		return nil, status.Error(codes.Internal, "failed to serialize payload")
	}

	// Create Kafka message
	msg := &kafka.Message{
		Topic:     req.KafkaTopic,
		Key:       req.PartitionKey,
		Value:     payloadBytes,
		Headers:   s.createHeaders(req.Metadata),
		Timestamp: time.Now(),
	}

	// Send to Kafka
	if err := s.producer.SendMessage(msg); err != nil {
		s.metrics.IncGRPCRequests("IngestStructuredData", "error")
		s.logger.WithError(err).WithField("topic", req.KafkaTopic).Error("Failed to send message to Kafka")
		return nil, status.Error(codes.Internal, "failed to send message")
	}

	s.metrics.IncGRPCRequests("IngestStructuredData", "success")
	s.metrics.ObserveMessageSize(req.KafkaTopic, len(payloadBytes))
	s.metrics.ObserveProcessingDuration("structured_ingestion", "success", time.Since(start))

	messageID := fmt.Sprintf("%s-%d", req.PartitionKey, time.Now().UnixNano())

	return &pb.IngestResponse{
		Success:     true,
		MessageId:   messageID,
		ProcessedAt: timestamppb.Now(),
		QueueDepth:  int64(len(s.producer.Input())),
	}, nil
}

// IngestUnstructuredData handles unstructured data ingestion
func (s *IngestionServer) IngestUnstructuredData(ctx context.Context, req *pb.IngestUnstructuredDataRequest) (*pb.IngestResponse, error) {
	start := time.Now()
	defer func() {
		s.metrics.ObserveGRPCDuration("IngestUnstructuredData", time.Since(start))
	}()

	// Validate request
	if req.Metadata == nil {
		s.metrics.IncGRPCRequests("IngestUnstructuredData", "error")
		return nil, status.Error(codes.InvalidArgument, "metadata is required")
	}

	if req.KafkaTopic == "" {
		s.metrics.IncGRPCRequests("IngestUnstructuredData", "error")
		return nil, status.Error(codes.InvalidArgument, "kafka_topic is required")
	}

	// Create Kafka message
	msg := &kafka.Message{
		Topic:     req.KafkaTopic,
		Key:       req.PartitionKey,
		Value:     req.Payload,
		Headers:   s.createHeaders(req.Metadata),
		Timestamp: time.Now(),
	}

	// Send to Kafka
	if err := s.producer.SendMessage(msg); err != nil {
		s.metrics.IncGRPCRequests("IngestUnstructuredData", "error")
		s.logger.WithError(err).WithField("topic", req.KafkaTopic).Error("Failed to send message to Kafka")
		return nil, status.Error(codes.Internal, "failed to send message")
	}

	s.metrics.IncGRPCRequests("IngestUnstructuredData", "success")
	s.metrics.ObserveMessageSize(req.KafkaTopic, len(req.Payload))
	s.metrics.ObserveProcessingDuration("unstructured_ingestion", "success", time.Since(start))

	messageID := fmt.Sprintf("%s-%d", req.PartitionKey, time.Now().UnixNano())

	return &pb.IngestResponse{
		Success:     true,
		MessageId:   messageID,
		ProcessedAt: timestamppb.Now(),
		QueueDepth:  int64(len(s.producer.Input())),
	}, nil
}

// StreamIngestData handles streaming data ingestion
func (s *IngestionServer) StreamIngestData(stream pb.DataIngestionService_StreamIngestDataServer) error {
	start := time.Now()
	defer func() {
		s.metrics.ObserveGRPCDuration("StreamIngestData", time.Since(start))
	}()

	streamData := make(map[string][]byte)
	var totalBytes int64

	for {
		req, err := stream.Recv()
		if err != nil {
			s.metrics.IncGRPCRequests("StreamIngestData", "error")
			return status.Error(codes.Internal, "failed to receive stream data")
		}

		streamData[req.StreamId] = append(streamData[req.StreamId], req.Chunk...)
		totalBytes += int64(len(req.Chunk))

		response := &pb.StreamIngestResponse{
			StreamId:      req.StreamId,
			BytesReceived: totalBytes,
			Success:       true,
		}

		if req.IsFinal {
			// Process complete stream
			msg := &kafka.Message{
				Topic:     fmt.Sprintf("stream-%s", req.StreamId),
				Key:       req.StreamId,
				Value:     streamData[req.StreamId],
				Headers:   s.createHeaders(req.Metadata),
				Timestamp: time.Now(),
			}

			if err := s.producer.SendMessage(msg); err != nil {
				response.Success = false
				response.ErrorMessage = "failed to send stream to Kafka"
				s.logger.WithError(err).Error("Failed to send stream to Kafka")
			} else {
				s.metrics.ObserveMessageSize(msg.Topic, len(msg.Value))
			}

			delete(streamData, req.StreamId)
		}

		if err := stream.Send(response); err != nil {
			return status.Error(codes.Internal, "failed to send response")
		}

		if req.IsFinal {
			break
		}
	}

	s.metrics.IncGRPCRequests("StreamIngestData", "success")
	return nil
}

// GetIngestionStatus returns current ingestion status and metrics
func (s *IngestionServer) GetIngestionStatus(ctx context.Context, req *pb.GetIngestionStatusRequest) (*pb.IngestionStatusResponse, error) {
	start := time.Now()
	defer func() {
		s.metrics.ObserveGRPCDuration("GetIngestionStatus", time.Since(start))
	}()

	producerMetrics := s.producer.GetMetrics()

	// Calculate totals
	var totalSent, totalSuccess, totalFailed int64
	if sentMap, ok := producerMetrics["messages_sent"].(map[string]int64); ok {
		for _, count := range sentMap {
			totalSent += count
		}
	}
	if successMap, ok := producerMetrics["messages_success"].(map[string]int64); ok {
		for _, count := range successMap {
			totalSuccess += count
		}
	}
	if failedMap, ok := producerMetrics["messages_failed"].(map[string]int64); ok {
		for _, count := range failedMap {
			totalFailed += count
		}
	}

	metrics := &pb.IngestionMetrics{
		TotalMessages:      totalSent,
		SuccessfulMessages: totalSuccess,
		FailedMessages:     totalFailed,
		QueueDepth:         producerMetrics["queue_depth"].(int64),
		ThroughputPerSecond: float64(totalSuccess) / time.Since(start).Seconds(),
	}

	s.metrics.IncGRPCRequests("GetIngestionStatus", "success")

	return &pb.IngestionStatusResponse{
		Metrics: metrics,
		SystemHealth: map[string]string{
			"kafka_producer": "healthy",
			"grpc_server":    "healthy",
		},
	}, nil
}

// HealthCheck performs a health check
func (s *IngestionServer) HealthCheck(ctx context.Context, req *pb.HealthCheckRequest) (*pb.HealthCheckResponse, error) {
	return &pb.HealthCheckResponse{
		Healthy:   true,
		Version:   "1.0.0",
		Timestamp: timestamppb.Now(),
		Dependencies: map[string]string{
			"kafka": "healthy",
		},
	}, nil
}

// createHeaders creates Kafka headers from metadata
func (s *IngestionServer) createHeaders(metadata *pb.DataMetadata) map[string]string {
	headers := make(map[string]string)

	headers["source_id"] = metadata.SourceId
	headers["source_type"] = metadata.SourceType.String()
	headers["format"] = metadata.Format.String()
	headers["priority"] = metadata.Priority.String()
	headers["schema_version"] = metadata.SchemaVersion
	headers["checksum"] = metadata.Checksum

	if metadata.Timestamp != nil {
		headers["timestamp"] = metadata.Timestamp.AsTime().Format(time.RFC3339)
	}

	// Add custom tags
	for k, v := range metadata.Tags {
		headers[fmt.Sprintf("tag_%s", k)] = v
	}

	return headers
}