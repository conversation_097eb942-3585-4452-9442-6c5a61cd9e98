package main

import (
	"context"
	"fmt"
	"log"
	"net"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/asi-system/ingestion/go-kafka-producer/internal/config"
	"github.com/asi-system/ingestion/go-kafka-producer/internal/kafka"
	"github.com/asi-system/ingestion/go-kafka-producer/internal/server"
	"github.com/asi-system/ingestion/go-kafka-producer/internal/metrics"
	pb "github.com/asi-system/ingestion/proto/gen/go"
	
	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	grpc_logrus "github.com/grpc-ecosystem/go-grpc-middleware/logging/logrus"
	grpc_recovery "github.com/grpc-ecosystem/go-grpc-middleware/recovery"
	grpc_prometheus "github.com/grpc-ecosystem/go-grpc-prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/health"
	"google.golang.org/grpc/health/grpc_health_v1"
	"google.golang.org/grpc/reflection"
)

func main() {
	// Initialize logger
	logger := logrus.New()
	logger.SetFormatter(&logrus.JSONFormatter{})
	logger.SetLevel(logrus.InfoLevel)

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		logger.Fatalf("Failed to load configuration: %v", err)
	}

	// Initialize metrics
	metricsCollector := metrics.NewCollector()

	// Initialize Kafka producer
	kafkaProducer, err := kafka.NewProducer(cfg.Kafka, logger, metricsCollector)
	if err != nil {
		logger.Fatalf("Failed to create Kafka producer: %v", err)
	}
	defer kafkaProducer.Close()

	// Initialize gRPC server
	grpcServer := createGRPCServer(logger, kafkaProducer, metricsCollector)

	// Start metrics server
	go startMetricsServer(cfg.MetricsPort, logger)

	// Start gRPC server
	lis, err := net.Listen("tcp", fmt.Sprintf(":%d", cfg.GRPCPort))
	if err != nil {
		logger.Fatalf("Failed to listen on port %d: %v", cfg.GRPCPort, err)
	}

	logger.Infof("Starting gRPC server on port %d", cfg.GRPCPort)
	go func() {
		if err := grpcServer.Serve(lis); err != nil {
			logger.Fatalf("Failed to serve gRPC: %v", err)
		}
	}()

	// Wait for interrupt signal
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)
	<-c

	logger.Info("Shutting down gracefully...")
	
	// Graceful shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	done := make(chan struct{})
	go func() {
		grpcServer.GracefulStop()
		close(done)
	}()

	select {
	case <-done:
		logger.Info("Server stopped gracefully")
	case <-ctx.Done():
		logger.Warn("Server shutdown timeout, forcing stop")
		grpcServer.Stop()
	}
}

func createGRPCServer(logger *logrus.Logger, producer *kafka.Producer, metrics *metrics.Collector) *grpc.Server {
	// gRPC middleware
	logrusEntry := logrus.NewEntry(logger)
	
	opts := []grpc.ServerOption{
		grpc.StreamInterceptor(grpc_middleware.ChainStreamServer(
			grpc_prometheus.StreamServerInterceptor,
			grpc_logrus.StreamServerInterceptor(logrusEntry),
			grpc_recovery.StreamServerInterceptor(),
		)),
		grpc.UnaryInterceptor(grpc_middleware.ChainUnaryServer(
			grpc_prometheus.UnaryServerInterceptor,
			grpc_logrus.UnaryServerInterceptor(logrusEntry),
			grpc_recovery.UnaryServerInterceptor(),
		)),
	}

	s := grpc.NewServer(opts...)

	// Register services
	ingestionServer := server.NewIngestionServer(producer, logger, metrics)
	pb.RegisterDataIngestionServiceServer(s, ingestionServer)

	// Register health service
	healthServer := health.NewServer()
	grpc_health_v1.RegisterHealthServer(s, healthServer)
	healthServer.SetServingStatus("", grpc_health_v1.HealthCheckResponse_SERVING)

	// Enable reflection for debugging
	reflection.Register(s)

	// Initialize Prometheus metrics
	grpc_prometheus.Register(s)

	return s
}

func startMetricsServer(port int, logger *logrus.Logger) {
	http.Handle("/metrics", promhttp.Handler())
	http.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	})

	logger.Infof("Starting metrics server on port %d", port)
	if err := http.ListenAndServe(fmt.Sprintf(":%d", port), nil); err != nil {
		logger.Errorf("Metrics server failed: %v", err)
	}
}
