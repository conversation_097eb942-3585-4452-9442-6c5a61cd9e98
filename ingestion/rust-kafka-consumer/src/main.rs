use anyhow::Result;
use std::sync::Arc;
use tokio::signal;
use tracing::{info, error};

mod config;
mod consumer;
mod grpc_client;
mod metrics;
mod processor;

use config::Config;
use consumer::KafkaConsumer;
use grpc_client::GrpcClient;
use metrics::MetricsCollector;
use processor::MessageProcessor;

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize tracing
    tracing_subscriber::fmt()
        .with_env_filter(tracing_subscriber::EnvFilter::from_default_env())
        .json()
        .init();

    info!("Starting ASI Kafka Consumer");

    // Load configuration
    let config = Config::load()?;
    info!("Configuration loaded successfully");

    // Initialize metrics collector
    let metrics = Arc::new(MetricsCollector::new());

    // Initialize gRPC client for downstream communication
    let grpc_client = Arc::new(GrpcClient::new(&config.grpc).await?);

    // Initialize message processor
    let processor = Arc::new(MessageProcessor::new(
        grpc_client.clone(),
        metrics.clone(),
    ));

    // Initialize Kafka consumer
    let consumer = KafkaConsumer::new(
        &config.kafka,
        processor.clone(),
        metrics.clone(),
    )?;

    // Start metrics server
    let metrics_server = start_metrics_server(config.metrics_port, metrics.clone());

    // Start consumer
    let consumer_handle = tokio::spawn(async move {
        if let Err(e) = consumer.start().await {
            error!("Consumer error: {}", e);
        }
    });

    // Wait for shutdown signal
    info!("Consumer started, waiting for shutdown signal...");
    signal::ctrl_c().await?;
    info!("Shutdown signal received, stopping consumer...");

    // Graceful shutdown
    consumer_handle.abort();
    metrics_server.abort();

    info!("Consumer stopped successfully");
    Ok(())
}

async fn start_metrics_server(
    port: u16,
    metrics: Arc<MetricsCollector>,
) -> tokio::task::JoinHandle<()> {
    tokio::spawn(async move {
        use hyper::service::{make_service_fn, service_fn};
        use hyper::{Body, Request, Response, Server};
        use std::convert::Infallible;

        let make_svc = make_service_fn(move |_conn| {
            let metrics = metrics.clone();
            async move {
                Ok::<_, Infallible>(service_fn(move |req: Request<Body>| {
                    let metrics = metrics.clone();
                    async move {
                        match req.uri().path() {
                            "/metrics" => {
                                let encoder = prometheus::TextEncoder::new();
                                let metric_families = metrics.gather();
                                let output = encoder.encode_to_string(&metric_families).unwrap();
                                Ok::<_, Infallible>(Response::new(Body::from(output)))
                            }
                            "/health" => {
                                Ok::<_, Infallible>(Response::new(Body::from("OK")))
                            }
                            _ => {
                                let mut response = Response::new(Body::from("Not Found"));
                                *response.status_mut() = hyper::StatusCode::NOT_FOUND;
                                Ok::<_, Infallible>(response)
                            }
                        }
                    }
                }))
            }
        });

        let addr = ([0, 0, 0, 0], port).into();
        let server = Server::bind(&addr).serve(make_svc);

        info!("Metrics server listening on http://{}", addr);

        if let Err(e) = server.await {
            error!("Metrics server error: {}", e);
        }
    })
}
