use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::env;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Config {
    pub kafka: KafkaConfig,
    pub grpc: GrpcConfig,
    pub processing: ProcessingConfig,
    pub metrics_port: u16,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct KafkaConfig {
    pub brokers: Vec<String>,
    pub group_id: String,
    pub topics: Vec<String>,
    pub auto_offset_reset: String,
    pub enable_auto_commit: bool,
    pub auto_commit_interval_ms: u32,
    pub session_timeout_ms: u32,
    pub heartbeat_interval_ms: u32,
    pub max_poll_interval_ms: u32,
    pub fetch_min_bytes: u32,
    pub fetch_max_wait_ms: u32,
    pub max_partition_fetch_bytes: u32,
    pub client_id: String,
    pub security_protocol: String,
    pub sasl_mechanism: Option<String>,
    pub sasl_username: Option<String>,
    pub sasl_password: Option<String>,
    pub ssl_ca_location: Option<String>,
    pub ssl_certificate_location: Option<String>,
    pub ssl_key_location: Option<String>,
    pub ssl_key_password: Option<String>,
    pub additional_properties: HashMap<String, String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GrpcConfig {
    pub data_integration_endpoint: String,
    pub learning_engine_endpoint: String,
    pub timeout_seconds: u64,
    pub max_retries: u32,
    pub retry_delay_ms: u64,
    pub tls_enabled: bool,
    pub tls_ca_cert: Option<String>,
    pub tls_client_cert: Option<String>,
    pub tls_client_key: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProcessingConfig {
    pub max_concurrent_messages: usize,
    pub batch_size: usize,
    pub batch_timeout_ms: u64,
    pub retry_attempts: u32,
    pub retry_backoff_ms: u64,
    pub dead_letter_topic: String,
    pub enable_deduplication: bool,
    pub deduplication_window_minutes: u32,
}

impl Config {
    pub fn load() -> Result<Self> {
        let config = Config {
            kafka: KafkaConfig {
                brokers: get_env_vec("KAFKA_BROKERS", vec!["localhost:9092".to_string()]),
                group_id: get_env_string("KAFKA_GROUP_ID", "asi-consumer-group".to_string()),
                topics: get_env_vec("KAFKA_TOPICS", vec!["asi-ingestion".to_string()]),
                auto_offset_reset: get_env_string("KAFKA_AUTO_OFFSET_RESET", "earliest".to_string()),
                enable_auto_commit: get_env_bool("KAFKA_ENABLE_AUTO_COMMIT", false),
                auto_commit_interval_ms: get_env_u32("KAFKA_AUTO_COMMIT_INTERVAL_MS", 5000),
                session_timeout_ms: get_env_u32("KAFKA_SESSION_TIMEOUT_MS", 30000),
                heartbeat_interval_ms: get_env_u32("KAFKA_HEARTBEAT_INTERVAL_MS", 3000),
                max_poll_interval_ms: get_env_u32("KAFKA_MAX_POLL_INTERVAL_MS", 300000),
                fetch_min_bytes: get_env_u32("KAFKA_FETCH_MIN_BYTES", 1),
                fetch_max_wait_ms: get_env_u32("KAFKA_FETCH_MAX_WAIT_MS", 500),
                max_partition_fetch_bytes: get_env_u32("KAFKA_MAX_PARTITION_FETCH_BYTES", 1048576),
                client_id: get_env_string("KAFKA_CLIENT_ID", "asi-kafka-consumer".to_string()),
                security_protocol: get_env_string("KAFKA_SECURITY_PROTOCOL", "PLAINTEXT".to_string()),
                sasl_mechanism: get_env_optional("KAFKA_SASL_MECHANISM"),
                sasl_username: get_env_optional("KAFKA_SASL_USERNAME"),
                sasl_password: get_env_optional("KAFKA_SASL_PASSWORD"),
                ssl_ca_location: get_env_optional("KAFKA_SSL_CA_LOCATION"),
                ssl_certificate_location: get_env_optional("KAFKA_SSL_CERTIFICATE_LOCATION"),
                ssl_key_location: get_env_optional("KAFKA_SSL_KEY_LOCATION"),
                ssl_key_password: get_env_optional("KAFKA_SSL_KEY_PASSWORD"),
                additional_properties: HashMap::new(),
            },
            grpc: GrpcConfig {
                data_integration_endpoint: get_env_string(
                    "GRPC_DATA_INTEGRATION_ENDPOINT",
                    "http://localhost:50052".to_string(),
                ),
                learning_engine_endpoint: get_env_string(
                    "GRPC_LEARNING_ENGINE_ENDPOINT",
                    "http://localhost:50053".to_string(),
                ),
                timeout_seconds: get_env_u64("GRPC_TIMEOUT_SECONDS", 30),
                max_retries: get_env_u32("GRPC_MAX_RETRIES", 3),
                retry_delay_ms: get_env_u64("GRPC_RETRY_DELAY_MS", 1000),
                tls_enabled: get_env_bool("GRPC_TLS_ENABLED", false),
                tls_ca_cert: get_env_optional("GRPC_TLS_CA_CERT"),
                tls_client_cert: get_env_optional("GRPC_TLS_CLIENT_CERT"),
                tls_client_key: get_env_optional("GRPC_TLS_CLIENT_KEY"),
            },
            processing: ProcessingConfig {
                max_concurrent_messages: get_env_usize("PROCESSING_MAX_CONCURRENT", 100),
                batch_size: get_env_usize("PROCESSING_BATCH_SIZE", 10),
                batch_timeout_ms: get_env_u64("PROCESSING_BATCH_TIMEOUT_MS", 1000),
                retry_attempts: get_env_u32("PROCESSING_RETRY_ATTEMPTS", 3),
                retry_backoff_ms: get_env_u64("PROCESSING_RETRY_BACKOFF_MS", 1000),
                dead_letter_topic: get_env_string(
                    "PROCESSING_DEAD_LETTER_TOPIC",
                    "asi-dead-letter".to_string(),
                ),
                enable_deduplication: get_env_bool("PROCESSING_ENABLE_DEDUPLICATION", true),
                deduplication_window_minutes: get_env_u32("PROCESSING_DEDUPLICATION_WINDOW_MINUTES", 60),
            },
            metrics_port: get_env_u16("METRICS_PORT", 8081),
        };

        Ok(config)
    }
}

fn get_env_string(key: &str, default: String) -> String {
    env::var(key).unwrap_or(default)
}

fn get_env_optional(key: &str) -> Option<String> {
    env::var(key).ok()
}

fn get_env_bool(key: &str, default: bool) -> bool {
    env::var(key)
        .ok()
        .and_then(|v| v.parse().ok())
        .unwrap_or(default)
}

fn get_env_u16(key: &str, default: u16) -> u16 {
    env::var(key)
        .ok()
        .and_then(|v| v.parse().ok())
        .unwrap_or(default)
}

fn get_env_u32(key: &str, default: u32) -> u32 {
    env::var(key)
        .ok()
        .and_then(|v| v.parse().ok())
        .unwrap_or(default)
}

fn get_env_u64(key: &str, default: u64) -> u64 {
    env::var(key)
        .ok()
        .and_then(|v| v.parse().ok())
        .unwrap_or(default)
}

fn get_env_usize(key: &str, default: usize) -> usize {
    env::var(key)
        .ok()
        .and_then(|v| v.parse().ok())
        .unwrap_or(default)
}

fn get_env_vec(key: &str, default: Vec<String>) -> Vec<String> {
    env::var(key)
        .ok()
        .map(|v| v.split(',').map(|s| s.trim().to_string()).collect())
        .unwrap_or(default)
}
