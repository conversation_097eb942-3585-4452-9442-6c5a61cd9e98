use anyhow::{Context, Result};
use async_trait::async_trait;
use serde_json::Value;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tracing::{debug, error, info, warn};

use crate::grpc_client::GrpcClient;
use crate::metrics::MetricsCollector;

/// Trait for processing Kafka messages
#[async_trait]
pub trait MessageProcessor: Send + Sync {
    async fn process_message(&self, topic: &str, key: Option<&str>, payload: &[u8]) -> Result<()>;
}

/// Main message processor implementation
pub struct MessageProcessor {
    grpc_client: Arc<GrpcClient>,
    metrics: Arc<MetricsCollector>,
}

impl MessageProcessor {
    /// Create a new message processor
    pub fn new(grpc_client: Arc<GrpcClient>, metrics: Arc<MetricsCollector>) -> Self {
        Self {
            grpc_client,
            metrics,
        }
    }

    /// Process structured data message
    async fn process_structured_data(&self, topic: &str, key: Option<&str>, payload: &[u8]) -> Result<()> {
        let start_time = Instant::now();
        
        // Parse JSON payload
        let data: Value = serde_json::from_slice(payload)
            .context("Failed to parse JSON payload")?;
        
        debug!("Processing structured data: topic={}, key={:?}, size={}", topic, key, payload.len());
        
        // Extract metadata if present
        let metadata = data.get("metadata").cloned().unwrap_or(Value::Null);
        let payload_data = data.get("payload").cloned().unwrap_or(data.clone());
        
        // Route to appropriate downstream service based on topic
        match topic {
            topic if topic.contains("sensor") => {
                self.process_sensor_data(key, &payload_data, &metadata).await?;
            }
            topic if topic.contains("web") => {
                self.process_web_data(key, &payload_data, &metadata).await?;
            }
            topic if topic.contains("log") => {
                self.process_log_data(key, &payload_data, &metadata).await?;
            }
            topic if topic.contains("stream") => {
                self.process_stream_data(key, &payload_data, &metadata).await?;
            }
            _ => {
                self.process_generic_data(topic, key, &payload_data, &metadata).await?;
            }
        }
        
        let duration = start_time.elapsed();
        self.metrics.record_grpc_request("data_integration", "process_structured", "success", duration);
        
        Ok(())
    }

    /// Process unstructured data message
    async fn process_unstructured_data(&self, topic: &str, key: Option<&str>, payload: &[u8]) -> Result<()> {
        let start_time = Instant::now();
        
        debug!("Processing unstructured data: topic={}, key={:?}, size={}", topic, key, payload.len());
        
        // Determine data type based on content
        let data_type = self.detect_data_type(payload);
        
        // Route to appropriate processing pipeline
        match data_type.as_str() {
            "text" => {
                self.process_text_data(key, payload).await?;
            }
            "image" => {
                self.process_image_data(key, payload).await?;
            }
            "audio" => {
                self.process_audio_data(key, payload).await?;
            }
            "binary" => {
                self.process_binary_data(key, payload).await?;
            }
            _ => {
                warn!("Unknown data type for unstructured data: {}", data_type);
                self.process_raw_data(key, payload).await?;
            }
        }
        
        let duration = start_time.elapsed();
        self.metrics.record_grpc_request("data_integration", "process_unstructured", "success", duration);
        
        Ok(())
    }

    /// Process sensor data
    async fn process_sensor_data(&self, key: Option<&str>, data: &Value, metadata: &Value) -> Result<()> {
        // Send to Data Integration service for sensor data processing
        self.grpc_client.send_sensor_data(key, data, metadata).await
            .context("Failed to send sensor data to Data Integration service")?;
        
        // Also send to Learning Engine for real-time learning
        self.grpc_client.send_learning_data("sensor", data).await
            .context("Failed to send sensor data to Learning Engine")?;
        
        info!("Sensor data processed successfully");
        Ok(())
    }

    /// Process web scraping data
    async fn process_web_data(&self, key: Option<&str>, data: &Value, metadata: &Value) -> Result<()> {
        // Send to Data Integration service for web data processing
        self.grpc_client.send_web_data(key, data, metadata).await
            .context("Failed to send web data to Data Integration service")?;
        
        // Extract text for NLP processing
        if let Some(text_content) = data.get("text").and_then(|v| v.as_str()) {
            self.grpc_client.send_nlp_data(text_content).await
                .context("Failed to send text data to Learning Engine")?;
        }
        
        info!("Web data processed successfully");
        Ok(())
    }

    /// Process log data
    async fn process_log_data(&self, key: Option<&str>, data: &Value, metadata: &Value) -> Result<()> {
        // Send to Data Integration service for log analysis
        self.grpc_client.send_log_data(key, data, metadata).await
            .context("Failed to send log data to Data Integration service")?;
        
        // Check for anomalies
        if let Some(level) = data.get("level").and_then(|v| v.as_str()) {
            if level == "ERROR" || level == "CRITICAL" {
                self.grpc_client.send_anomaly_alert(data).await
                    .context("Failed to send anomaly alert")?;
            }
        }
        
        info!("Log data processed successfully");
        Ok(())
    }

    /// Process streaming data
    async fn process_stream_data(&self, key: Option<&str>, data: &Value, metadata: &Value) -> Result<()> {
        // Send to Data Integration service for stream processing
        self.grpc_client.send_stream_data(key, data, metadata).await
            .context("Failed to send stream data to Data Integration service")?;
        
        // Send to real-time decision engine
        self.grpc_client.send_decision_data(data).await
            .context("Failed to send stream data to Decision Engine")?;
        
        info!("Stream data processed successfully");
        Ok(())
    }

    /// Process generic structured data
    async fn process_generic_data(&self, topic: &str, key: Option<&str>, data: &Value, metadata: &Value) -> Result<()> {
        // Send to Data Integration service for generic processing
        self.grpc_client.send_generic_data(topic, key, data, metadata).await
            .context("Failed to send generic data to Data Integration service")?;
        
        info!("Generic data processed successfully");
        Ok(())
    }

    /// Process text data
    async fn process_text_data(&self, key: Option<&str>, payload: &[u8]) -> Result<()> {
        let text = String::from_utf8_lossy(payload);
        
        // Send to NLP pipeline
        self.grpc_client.send_nlp_data(&text).await
            .context("Failed to send text data to Learning Engine")?;
        
        info!("Text data processed successfully");
        Ok(())
    }

    /// Process image data
    async fn process_image_data(&self, key: Option<&str>, payload: &[u8]) -> Result<()> {
        // Send to computer vision pipeline
        self.grpc_client.send_vision_data(payload).await
            .context("Failed to send image data to Learning Engine")?;
        
        info!("Image data processed successfully");
        Ok(())
    }

    /// Process audio data
    async fn process_audio_data(&self, key: Option<&str>, payload: &[u8]) -> Result<()> {
        // Send to audio processing pipeline
        self.grpc_client.send_audio_data(payload).await
            .context("Failed to send audio data to Learning Engine")?;
        
        info!("Audio data processed successfully");
        Ok(())
    }

    /// Process binary data
    async fn process_binary_data(&self, key: Option<&str>, payload: &[u8]) -> Result<()> {
        // Send to binary data processing pipeline
        self.grpc_client.send_binary_data(payload).await
            .context("Failed to send binary data to Data Integration service")?;
        
        info!("Binary data processed successfully");
        Ok(())
    }

    /// Process raw data
    async fn process_raw_data(&self, key: Option<&str>, payload: &[u8]) -> Result<()> {
        // Send to raw data processing pipeline
        self.grpc_client.send_raw_data(payload).await
            .context("Failed to send raw data to Data Integration service")?;
        
        info!("Raw data processed successfully");
        Ok(())
    }

    /// Detect data type from payload
    fn detect_data_type(&self, payload: &[u8]) -> String {
        // Simple heuristics for data type detection
        if payload.len() < 4 {
            return "unknown".to_string();
        }
        
        // Check for common file signatures
        match &payload[0..4] {
            [0xFF, 0xD8, 0xFF, _] => "image".to_string(), // JPEG
            [0x89, 0x50, 0x4E, 0x47] => "image".to_string(), // PNG
            [0x52, 0x49, 0x46, 0x46] => "audio".to_string(), // WAV
            _ => {
                // Check if it's valid UTF-8 text
                if String::from_utf8(payload.to_vec()).is_ok() {
                    "text".to_string()
                } else {
                    "binary".to_string()
                }
            }
        }
    }
}

#[async_trait]
impl MessageProcessor for MessageProcessor {
    async fn process_message(&self, topic: &str, key: Option<&str>, payload: &[u8]) -> Result<()> {
        // Try to parse as JSON first (structured data)
        if let Ok(_) = serde_json::from_slice::<Value>(payload) {
            self.process_structured_data(topic, key, payload).await
        } else {
            // Process as unstructured data
            self.process_unstructured_data(topic, key, payload).await
        }
    }
}
