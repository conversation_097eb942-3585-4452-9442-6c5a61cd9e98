use anyhow::{Context, Result};
use rdkafka::config::ClientConfig;
use rdkafka::consumer::{Consumer, StreamConsumer};
use rdkafka::message::Message;
use rdkafka::util::Timeout;
use std::sync::Arc;
use std::time::Duration;
use tokio::time::{interval, sleep};
use tracing::{debug, error, info, warn};

use crate::config::KafkaConfig;
use crate::metrics::MetricsCollector;
use crate::processor::MessageProcessor;

/// High-performance Kafka consumer with automatic reconnection and backpressure handling
pub struct KafkaConsumer {
    consumer: StreamConsumer,
    processor: Arc<dyn MessageProcessor>,
    metrics: Arc<MetricsCollector>,
    config: KafkaConfig,
}

impl KafkaConsumer {
    /// Create a new Kafka consumer instance
    pub fn new(
        config: &KafkaConfig,
        processor: Arc<dyn MessageProcessor>,
        metrics: Arc<MetricsCollector>,
    ) -> Result<Self> {
        let consumer = Self::create_consumer(config)?;
        
        Ok(Self {
            consumer,
            processor,
            metrics: metrics.clone(),
            config: config.clone(),
        })
    }

    /// Create and configure the Kafka consumer
    fn create_consumer(config: &KafkaConfig) -> Result<StreamConsumer> {
        let mut client_config = ClientConfig::new();
        
        // Basic configuration
        client_config
            .set("bootstrap.servers", config.brokers.join(","))
            .set("group.id", &config.group_id)
            .set("auto.offset.reset", &config.auto_offset_reset)
            .set("enable.auto.commit", config.enable_auto_commit.to_string())
            .set("auto.commit.interval.ms", config.auto_commit_interval_ms.to_string())
            .set("session.timeout.ms", config.session_timeout_ms.to_string())
            .set("heartbeat.interval.ms", config.heartbeat_interval_ms.to_string())
            .set("max.poll.interval.ms", config.max_poll_interval_ms.to_string())
            .set("fetch.min.bytes", config.fetch_min_bytes.to_string())
            .set("fetch.max.wait.ms", config.fetch_max_wait_ms.to_string())
            .set("max.partition.fetch.bytes", config.max_partition_fetch_bytes.to_string())
            .set("client.id", &config.client_id);

        // Security configuration
        if config.security_protocol != "PLAINTEXT" {
            client_config.set("security.protocol", &config.security_protocol);
        }

        if let Some(ref sasl_mechanism) = config.sasl_mechanism {
            client_config
                .set("sasl.mechanism", sasl_mechanism)
                .set("sasl.username", config.sasl_username.as_ref().unwrap_or(&String::new()))
                .set("sasl.password", config.sasl_password.as_ref().unwrap_or(&String::new()));
        }

        // Performance tuning
        client_config
            .set("socket.keepalive.enable", "true")
            .set("socket.nagle.disable", "true")
            .set("socket.receive.buffer.bytes", "65536")
            .set("socket.send.buffer.bytes", "65536")
            .set("reconnect.backoff.ms", "100")
            .set("reconnect.backoff.max.ms", "10000");

        let consumer: StreamConsumer = client_config
            .create()
            .context("Failed to create Kafka consumer")?;

        info!("Kafka consumer created successfully");
        Ok(consumer)
    }

    /// Start consuming messages from Kafka
    pub async fn start(&self) -> Result<()> {
        // Subscribe to topics
        self.consumer
            .subscribe(&self.config.topics.iter().map(|s| s.as_str()).collect::<Vec<_>>())
            .context("Failed to subscribe to topics")?;

        info!("Subscribed to topics: {:?}", self.config.topics);

        // Start metrics collection
        let metrics_handle = self.start_metrics_collection();

        // Main consumption loop
        let mut consecutive_errors = 0;
        const MAX_CONSECUTIVE_ERRORS: u32 = 10;

        loop {
            match self.consume_message().await {
                Ok(()) => {
                    consecutive_errors = 0;
                }
                Err(e) => {
                    consecutive_errors += 1;
                    error!("Error consuming message: {}", e);
                    
                    if consecutive_errors >= MAX_CONSECUTIVE_ERRORS {
                        error!("Too many consecutive errors, stopping consumer");
                        break;
                    }
                    
                    // Exponential backoff
                    let delay = Duration::from_millis(100 * 2_u64.pow(consecutive_errors.min(10)));
                    sleep(delay).await;
                }
            }
        }

        metrics_handle.abort();
        Ok(())
    }

    /// Consume a single message from Kafka
    async fn consume_message(&self) -> Result<()> {
        let message = self.consumer
            .recv()
            .await
            .context("Failed to receive message from Kafka")?;

        let start_time = std::time::Instant::now();

        // Extract message details
        let topic = message.topic();
        let partition = message.partition();
        let offset = message.offset();
        let key = message.key().map(|k| String::from_utf8_lossy(k).to_string());
        let payload = message.payload().unwrap_or(&[]);

        debug!(
            "Received message: topic={}, partition={}, offset={}, size={}",
            topic, partition, offset, payload.len()
        );

        // Update metrics
        self.metrics.inc_messages_received(topic);
        self.metrics.observe_message_size(topic, payload.len());

        // Process the message
        match self.processor.process_message(topic, key.as_deref(), payload).await {
            Ok(()) => {
                self.metrics.inc_messages_processed(topic);
                debug!("Message processed successfully");
            }
            Err(e) => {
                self.metrics.inc_messages_failed(topic);
                error!("Failed to process message: {}", e);
                return Err(e);
            }
        }

        // Record processing duration
        let duration = start_time.elapsed();
        self.metrics.observe_processing_duration(topic, duration);

        // Commit offset if auto-commit is disabled
        if !self.config.enable_auto_commit {
            if let Err(e) = self.consumer.commit_message(&message, rdkafka::consumer::CommitMode::Async) {
                warn!("Failed to commit offset: {}", e);
            }
        }

        Ok(())
    }

    /// Start background metrics collection
    fn start_metrics_collection(&self) -> tokio::task::JoinHandle<()> {
        let metrics = self.metrics.clone();
        let consumer = self.consumer.clone();
        
        tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(30));
            
            loop {
                interval.tick().await;
                
                // Collect consumer lag metrics
                if let Ok(metadata) = consumer.fetch_metadata(None, Timeout::After(Duration::from_secs(5))) {
                    for topic in metadata.topics() {
                        for partition in topic.partitions() {
                            // In a real implementation, we would calculate lag here
                            // For now, we'll just update the connection status
                            metrics.set_connection_status("connected");
                        }
                    }
                }
            }
        })
    }
}
