use prometheus::{
    <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ge<PERSON><PERSON>, Histogram, HistogramVec, Registry, TextEncoder,
};
use std::sync::Arc;
use std::time::Duration;
use tracing::error;

/// Comprehensive metrics collector for Kafka consumer
pub struct MetricsCollector {
    registry: Registry,
    
    // Message metrics
    messages_received: CounterVec,
    messages_processed: CounterVec,
    messages_failed: CounterVec,
    message_size_bytes: HistogramVec,
    processing_duration: HistogramVec,
    
    // Consumer metrics
    consumer_lag: GaugeVec,
    connection_status: Gauge,
    partition_assignments: GaugeVec,
    
    // System metrics
    memory_usage: Gauge,
    cpu_usage: Gauge,
    
    // gRPC metrics
    grpc_requests: CounterVec,
    grpc_duration: HistogramVec,
    grpc_errors: CounterVec,
}

impl MetricsCollector {
    /// Create a new metrics collector
    pub fn new() -> Self {
        let registry = Registry::new();
        
        let messages_received = CounterVec::new(
            prometheus::Opts::new(
                "asi_consumer_messages_received_total",
                "Total number of messages received from <PERSON><PERSON><PERSON>",
            ),
            &["topic", "partition"],
        ).expect("Failed to create messages_received metric");
        
        let messages_processed = CounterVec::new(
            prometheus::Opts::new(
                "asi_consumer_messages_processed_total",
                "Total number of messages successfully processed",
            ),
            &["topic", "partition"],
        ).expect("Failed to create messages_processed metric");
        
        let messages_failed = CounterVec::new(
            prometheus::Opts::new(
                "asi_consumer_messages_failed_total",
                "Total number of messages that failed processing",
            ),
            &["topic", "partition", "error_type"],
        ).expect("Failed to create messages_failed metric");
        
        let message_size_bytes = HistogramVec::new(
            prometheus::HistogramOpts::new(
                "asi_consumer_message_size_bytes",
                "Size of consumed messages in bytes",
            ).buckets(vec![1.0, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0]),
            &["topic"],
        ).expect("Failed to create message_size_bytes metric");
        
        let processing_duration = HistogramVec::new(
            prometheus::HistogramOpts::new(
                "asi_consumer_processing_duration_seconds",
                "Time spent processing messages",
            ).buckets(vec![0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, 5.0]),
            &["topic", "status"],
        ).expect("Failed to create processing_duration metric");
        
        let consumer_lag = GaugeVec::new(
            prometheus::Opts::new(
                "asi_consumer_lag",
                "Consumer lag per topic partition",
            ),
            &["topic", "partition"],
        ).expect("Failed to create consumer_lag metric");
        
        let connection_status = Gauge::new(
            "asi_consumer_connection_status",
            "Connection status (1=connected, 0=disconnected)",
        ).expect("Failed to create connection_status metric");
        
        let partition_assignments = GaugeVec::new(
            prometheus::Opts::new(
                "asi_consumer_partition_assignments",
                "Number of assigned partitions per topic",
            ),
            &["topic"],
        ).expect("Failed to create partition_assignments metric");
        
        let memory_usage = Gauge::new(
            "asi_consumer_memory_usage_bytes",
            "Memory usage in bytes",
        ).expect("Failed to create memory_usage metric");
        
        let cpu_usage = Gauge::new(
            "asi_consumer_cpu_usage_percent",
            "CPU usage percentage",
        ).expect("Failed to create cpu_usage metric");
        
        let grpc_requests = CounterVec::new(
            prometheus::Opts::new(
                "asi_consumer_grpc_requests_total",
                "Total number of gRPC requests made",
            ),
            &["service", "method", "status"],
        ).expect("Failed to create grpc_requests metric");
        
        let grpc_duration = HistogramVec::new(
            prometheus::HistogramOpts::new(
                "asi_consumer_grpc_duration_seconds",
                "Duration of gRPC requests",
            ).buckets(vec![0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, 5.0]),
            &["service", "method"],
        ).expect("Failed to create grpc_duration metric");
        
        let grpc_errors = CounterVec::new(
            prometheus::Opts::new(
                "asi_consumer_grpc_errors_total",
                "Total number of gRPC errors",
            ),
            &["service", "method", "error_code"],
        ).expect("Failed to create grpc_errors metric");
        
        // Register all metrics
        registry.register(Box::new(messages_received.clone())).unwrap();
        registry.register(Box::new(messages_processed.clone())).unwrap();
        registry.register(Box::new(messages_failed.clone())).unwrap();
        registry.register(Box::new(message_size_bytes.clone())).unwrap();
        registry.register(Box::new(processing_duration.clone())).unwrap();
        registry.register(Box::new(consumer_lag.clone())).unwrap();
        registry.register(Box::new(connection_status.clone())).unwrap();
        registry.register(Box::new(partition_assignments.clone())).unwrap();
        registry.register(Box::new(memory_usage.clone())).unwrap();
        registry.register(Box::new(cpu_usage.clone())).unwrap();
        registry.register(Box::new(grpc_requests.clone())).unwrap();
        registry.register(Box::new(grpc_duration.clone())).unwrap();
        registry.register(Box::new(grpc_errors.clone())).unwrap();
        
        Self {
            registry,
            messages_received,
            messages_processed,
            messages_failed,
            message_size_bytes,
            processing_duration,
            consumer_lag,
            connection_status,
            partition_assignments,
            memory_usage,
            cpu_usage,
            grpc_requests,
            grpc_duration,
            grpc_errors,
        }
    }
    
    /// Increment messages received counter
    pub fn inc_messages_received(&self, topic: &str) {
        self.messages_received
            .with_label_values(&[topic, "0"]) // Default partition for now
            .inc();
    }
    
    /// Increment messages processed counter
    pub fn inc_messages_processed(&self, topic: &str) {
        self.messages_processed
            .with_label_values(&[topic, "0"])
            .inc();
    }
    
    /// Increment messages failed counter
    pub fn inc_messages_failed(&self, topic: &str) {
        self.messages_failed
            .with_label_values(&[topic, "0", "processing_error"])
            .inc();
    }
    
    /// Observe message size
    pub fn observe_message_size(&self, topic: &str, size: usize) {
        self.message_size_bytes
            .with_label_values(&[topic])
            .observe(size as f64);
    }
    
    /// Observe processing duration
    pub fn observe_processing_duration(&self, topic: &str, duration: Duration) {
        self.processing_duration
            .with_label_values(&[topic, "success"])
            .observe(duration.as_secs_f64());
    }
    
    /// Set connection status
    pub fn set_connection_status(&self, status: &str) {
        let value = if status == "connected" { 1.0 } else { 0.0 };
        self.connection_status.set(value);
    }
    
    /// Record gRPC request
    pub fn record_grpc_request(&self, service: &str, method: &str, status: &str, duration: Duration) {
        self.grpc_requests
            .with_label_values(&[service, method, status])
            .inc();
        
        self.grpc_duration
            .with_label_values(&[service, method])
            .observe(duration.as_secs_f64());
    }
    
    /// Record gRPC error
    pub fn record_grpc_error(&self, service: &str, method: &str, error_code: &str) {
        self.grpc_errors
            .with_label_values(&[service, method, error_code])
            .inc();
    }
    
    /// Gather all metrics for Prometheus export
    pub fn gather(&self) -> Vec<prometheus::proto::MetricFamily> {
        self.registry.gather()
    }
    
    /// Get metrics as text format
    pub fn export_text(&self) -> Result<String, prometheus::Error> {
        let encoder = TextEncoder::new();
        let metric_families = self.gather();
        encoder.encode_to_string(&metric_families)
    }
}
