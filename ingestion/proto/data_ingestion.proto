syntax = "proto3";

package asi.ingestion.v1;

import "google/protobuf/timestamp.proto";
import "google/protobuf/any.proto";

option go_package = "github.com/asi-system/ingestion/proto/gen/go";

// Data Ingestion Service for ASI System
service DataIngestionService {
  // Ingest structured data
  rpc IngestStructuredData(IngestStructuredDataRequest) returns (IngestResponse);
  
  // Ingest unstructured data (text, binary)
  rpc IngestUnstructuredData(IngestUnstructuredDataRequest) returns (IngestResponse);
  
  // Stream data ingestion for high-throughput scenarios
  rpc StreamIngestData(stream StreamIngestRequest) returns (stream StreamIngestResponse);
  
  // Get ingestion status and metrics
  rpc GetIngestionStatus(GetIngestionStatusRequest) returns (IngestionStatusResponse);
  
  // Health check
  rpc HealthCheck(HealthCheckRequest) returns (HealthCheckResponse);
}

// Data source types
enum DataSourceType {
  DATA_SOURCE_TYPE_UNSPECIFIED = 0;
  DATA_SOURCE_TYPE_API = 1;
  DATA_SOURCE_TYPE_SENSOR = 2;
  DATA_SOURCE_TYPE_LOG = 3;
  DATA_SOURCE_TYPE_WEB = 4;
  DATA_SOURCE_TYPE_DATABASE = 5;
  DATA_SOURCE_TYPE_FILE = 6;
}

// Data format types
enum DataFormat {
  DATA_FORMAT_UNSPECIFIED = 0;
  DATA_FORMAT_JSON = 1;
  DATA_FORMAT_XML = 2;
  DATA_FORMAT_CSV = 3;
  DATA_FORMAT_BINARY = 4;
  DATA_FORMAT_TEXT = 5;
  DATA_FORMAT_PROTOBUF = 6;
}

// Priority levels for data processing
enum Priority {
  PRIORITY_UNSPECIFIED = 0;
  PRIORITY_LOW = 1;
  PRIORITY_NORMAL = 2;
  PRIORITY_HIGH = 3;
  PRIORITY_CRITICAL = 4;
}

// Metadata for data ingestion
message DataMetadata {
  string source_id = 1;
  DataSourceType source_type = 2;
  DataFormat format = 3;
  google.protobuf.Timestamp timestamp = 4;
  map<string, string> tags = 5;
  Priority priority = 6;
  string schema_version = 7;
  int64 size_bytes = 8;
  string checksum = 9;
}

// Structured data ingestion request
message IngestStructuredDataRequest {
  DataMetadata metadata = 1;
  google.protobuf.Any payload = 2;
  string kafka_topic = 3;
  string partition_key = 4;
}

// Unstructured data ingestion request
message IngestUnstructuredDataRequest {
  DataMetadata metadata = 1;
  bytes payload = 2;
  string kafka_topic = 3;
  string partition_key = 4;
}

// Stream ingestion request
message StreamIngestRequest {
  string stream_id = 1;
  DataMetadata metadata = 2;
  bytes chunk = 3;
  bool is_final = 4;
}

// Stream ingestion response
message StreamIngestResponse {
  string stream_id = 1;
  int64 bytes_received = 2;
  bool success = 3;
  string error_message = 4;
}

// Ingestion response
message IngestResponse {
  bool success = 1;
  string message_id = 2;
  string error_message = 3;
  google.protobuf.Timestamp processed_at = 4;
  int64 queue_depth = 5;
}

// Ingestion status request
message GetIngestionStatusRequest {
  string source_id = 1;
  google.protobuf.Timestamp since = 2;
}

// Ingestion metrics
message IngestionMetrics {
  int64 total_messages = 1;
  int64 successful_messages = 2;
  int64 failed_messages = 3;
  double throughput_per_second = 4;
  double average_latency_ms = 5;
  int64 queue_depth = 6;
  map<string, int64> error_counts = 7;
}

// Ingestion status response
message IngestionStatusResponse {
  IngestionMetrics metrics = 1;
  repeated string active_sources = 2;
  map<string, string> system_health = 3;
}

// Health check request/response
message HealthCheckRequest {}

message HealthCheckResponse {
  bool healthy = 1;
  string version = 2;
  google.protobuf.Timestamp timestamp = 3;
  map<string, string> dependencies = 4;
}
