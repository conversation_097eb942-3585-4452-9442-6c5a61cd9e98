# ASI System Test Configuration
# =============================

# Global test execution settings
parallel_execution: true
max_workers: 8
timeout_seconds: 3600
coverage_threshold: 80.0
performance_baseline_file: 'testing/performance_baseline.json'
report_formats: ['json', 'html', 'junit', 'coverage']
artifact_retention_days: 30

# Test environment settings
environment:
  ASI_TEST_MODE: "true"
  ASI_LOG_LEVEL: "DEBUG"
  ASI_CONFIG_PATH: "testing/test_config.json"
  DOCKER_BUILDKIT: "1"
  COMPOSE_DOCKER_CLI_BUILD: "1"

# Module-specific test configurations
modules:
  data_ingestion:
    languages: ['go', 'rust', 'python']
    test_types: ['unit', 'integration', 'performance', 'load']
    timeout_multiplier: 1.5
    coverage_threshold: 85.0
    performance_benchmarks:
      - throughput_test
      - latency_test
      - memory_usage_test
    dependencies:
      - kafka
      - redis
      - postgresql

  global_data_integration:
    languages: ['scala', 'java', 'rust']
    test_types: ['unit', 'integration', 'performance']
    timeout_multiplier: 2.0
    coverage_threshold: 80.0
    performance_benchmarks:
      - etl_pipeline_test
      - graph_query_test
      - data_validation_test
    dependencies:
      - neo4j
      - spark
      - elasticsearch

  learning_engine:
    languages: ['python', 'cpp']
    test_types: ['unit', 'integration', 'performance', 'load', 'stress']
    timeout_multiplier: 3.0
    coverage_threshold: 90.0
    performance_benchmarks:
      - training_speed_test
      - inference_latency_test
      - memory_efficiency_test
      - gpu_utilization_test
    dependencies:
      - pytorch
      - tensorflow
      - cuda
    gpu_required: true
    memory_limit: "16GB"

  decision_engine:
    languages: ['rust', 'python']
    test_types: ['unit', 'integration', 'performance', 'stress']
    timeout_multiplier: 1.0
    coverage_threshold: 95.0
    performance_benchmarks:
      - decision_latency_test
      - rule_evaluation_test
      - hybrid_reasoning_test
    real_time_requirements: true
    max_latency_ms: 10

  self_improvement_engine:
    languages: ['lisp', 'python', 'julia']
    test_types: ['unit', 'integration', 'performance']
    timeout_multiplier: 4.0
    coverage_threshold: 75.0
    performance_benchmarks:
      - rlhf_training_test
      - evolution_speed_test
      - genetic_programming_test
    dependencies:
      - sbcl
      - julia
      - deap

  ui_ux_module:
    languages: ['typescript', 'python']
    test_types: ['unit', 'integration', 'e2e']
    timeout_multiplier: 2.0
    coverage_threshold: 80.0
    e2e_tests:
      - dashboard_functionality
      - real_time_updates
      - user_interactions
      - model_inspector
    dependencies:
      - nodejs
      - chromium
      - streamlit

  core_runtime_control:
    languages: ['rust', 'cpp']
    test_types: ['unit', 'integration', 'performance', 'stress']
    timeout_multiplier: 1.5
    coverage_threshold: 95.0
    performance_benchmarks:
      - real_time_scheduling_test
      - inference_acceleration_test
      - memory_management_test
    real_time_requirements: true
    max_latency_us: 100

  deployment_orchestration:
    languages: ['go', 'yaml']
    test_types: ['unit', 'integration', 'e2e']
    timeout_multiplier: 3.0
    coverage_threshold: 70.0
    infrastructure_tests:
      - kubernetes_deployment
      - terraform_provisioning
      - monitoring_setup
      - service_mesh_config
    dependencies:
      - docker
      - kubernetes
      - terraform
      - helm

  security_ethics_control:
    languages: ['rust', 'python']
    test_types: ['unit', 'integration', 'security', 'stress']
    timeout_multiplier: 2.0
    coverage_threshold: 95.0
    security_tests:
      - vulnerability_scanning
      - penetration_testing
      - policy_enforcement
      - audit_trail_verification
    dependencies:
      - vault
      - opa
      - blockchain_node

# Performance baseline thresholds
performance_thresholds:
  data_ingestion:
    throughput_min: 10000  # events/second
    latency_max: 100       # milliseconds
    memory_max: 2048       # MB
    cpu_max: 80           # percentage

  learning_engine:
    training_time_max: 3600    # seconds
    inference_latency_max: 50  # milliseconds
    gpu_memory_max: 8192       # MB
    accuracy_min: 0.85         # percentage

  decision_engine:
    decision_latency_max: 10   # milliseconds
    throughput_min: 100000     # decisions/second
    memory_max: 512            # MB

  ui_ux_module:
    page_load_max: 2000        # milliseconds
    interaction_delay_max: 100 # milliseconds
    bundle_size_max: 5         # MB

# Test data and fixtures
test_data:
  datasets:
    - name: "sample_training_data"
      path: "testing/data/training_sample.json"
      size: "100MB"
    - name: "benchmark_queries"
      path: "testing/data/benchmark_queries.sql"
      size: "10MB"
    - name: "security_test_vectors"
      path: "testing/data/security_vectors.json"
      size: "50MB"

  mock_services:
    - name: "mock_external_api"
      port: 8080
      config: "testing/mocks/external_api.yaml"
    - name: "mock_database"
      port: 5432
      config: "testing/mocks/database.yaml"

# CI/CD Integration
ci_cd:
  github_actions:
    enabled: true
    workflow_file: ".github/workflows/test.yml"
    parallel_jobs: 4
    cache_dependencies: true

  jenkins:
    enabled: false
    pipeline_file: "Jenkinsfile"

  gitlab_ci:
    enabled: false
    config_file: ".gitlab-ci.yml"

# Reporting and notifications
reporting:
  formats:
    - json
    - html
    - junit
    - coverage
    - performance

  destinations:
    - file_system
    - s3_bucket
    - database

  notifications:
    slack:
      enabled: false
      webhook_url: "${SLACK_WEBHOOK_URL}"
      channels: ["#asi-testing", "#asi-alerts"]

    email:
      enabled: false
      smtp_server: "${SMTP_SERVER}"
      recipients: ["<EMAIL>"]

# Quality gates
quality_gates:
  coverage:
    minimum: 80.0
    fail_on_decrease: true
    
  performance:
    fail_on_regression: true
    regression_threshold: 10.0  # percentage

  security:
    fail_on_vulnerabilities: true
    max_critical: 0
    max_high: 2

# Test execution matrix
execution_matrix:
  - os: "ubuntu-latest"
    python_version: "3.9"
    rust_version: "1.70"
    go_version: "1.20"
    node_version: "18"

  - os: "ubuntu-latest"
    python_version: "3.10"
    rust_version: "1.71"
    go_version: "1.21"
    node_version: "20"

# Resource limits
resource_limits:
  memory: "32GB"
  cpu_cores: 16
  disk_space: "100GB"
  gpu_memory: "16GB"
  network_bandwidth: "1Gbps"

# Cleanup and maintenance
cleanup:
  auto_cleanup: true
  retention_policy:
    test_results: 30  # days
    artifacts: 7      # days
    logs: 14         # days
    cache: 3         # days
