#!/usr/bin/env python3
"""
ASI System Test Orchestrator
============================

Comprehensive testing framework for the ASI system that orchestrates
unit tests, integration tests, end-to-end tests, and performance benchmarks
across all modules and languages.
"""

import asyncio
import json
import logging
import os
import subprocess
import sys
import time
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple
from enum import Enum
import concurrent.futures
import yaml

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class TestType(Enum):
    """Types of tests."""
    UNIT = "unit"
    INTEGRATION = "integration"
    E2E = "e2e"
    PERFORMANCE = "performance"
    SECURITY = "security"
    LOAD = "load"
    STRESS = "stress"


class TestStatus(Enum):
    """Test execution status."""
    PENDING = "pending"
    RUNNING = "running"
    PASSED = "passed"
    FAILED = "failed"
    SKIPPED = "skipped"
    ERROR = "error"


@dataclass
class TestResult:
    """Test execution result."""
    test_id: str
    test_type: TestType
    module: str
    language: str
    status: TestStatus
    duration_seconds: float
    start_time: datetime
    end_time: Optional[datetime] = None
    output: str = ""
    error_message: str = ""
    coverage_percentage: float = 0.0
    performance_metrics: Dict[str, Any] = field(default_factory=dict)
    artifacts: List[str] = field(default_factory=list)


@dataclass
class TestSuite:
    """Test suite configuration."""
    name: str
    test_type: TestType
    module: str
    language: str
    command: str
    working_directory: str
    timeout_seconds: int = 300
    environment_variables: Dict[str, str] = field(default_factory=dict)
    dependencies: List[str] = field(default_factory=list)
    parallel: bool = True
    retry_count: int = 0
    coverage_enabled: bool = True
    performance_benchmarks: bool = False


class TestOrchestrator:
    """
    Main test orchestrator that manages and executes all tests across the ASI system.
    """

    def __init__(self, config_path: str = "testing/config.yaml"):
        self.config_path = Path(config_path)
        self.config = self._load_config()
        self.test_suites: List[TestSuite] = []
        self.results: List[TestResult] = []
        self.start_time: Optional[datetime] = None
        self.end_time: Optional[datetime] = None

        # Load test suites
        self._load_test_suites()

        logger.info(f"Test Orchestrator initialized with {len(self.test_suites)} test suites")

    def _load_config(self) -> Dict[str, Any]:
        """Load test configuration."""
        if self.config_path.exists():
            with open(self.config_path, 'r') as f:
                return yaml.safe_load(f)
        else:
            # Default configuration
            return {
                'parallel_execution': True,
                'max_workers': 4,
                'timeout_seconds': 1800,
                'coverage_threshold': 80.0,
                'performance_baseline_file': 'testing/performance_baseline.json',
                'report_formats': ['json', 'html', 'junit'],
                'artifact_retention_days': 30,
                'modules': {
                    'data_ingestion': {
                        'languages': ['go', 'rust', 'python'],
                        'test_types': ['unit', 'integration', 'performance']
                    },
                    'global_data_integration': {
                        'languages': ['scala', 'java', 'rust'],
                        'test_types': ['unit', 'integration', 'performance']
                    },
                    'learning_engine': {
                        'languages': ['python', 'cpp'],
                        'test_types': ['unit', 'integration', 'performance', 'load']
                    },
                    'decision_engine': {
                        'languages': ['rust', 'python'],
                        'test_types': ['unit', 'integration', 'performance']
                    },
                    'self_improvement_engine': {
                        'languages': ['lisp', 'python', 'julia'],
                        'test_types': ['unit', 'integration', 'performance']
                    },
                    'ui_ux_module': {
                        'languages': ['typescript', 'python'],
                        'test_types': ['unit', 'integration', 'e2e']
                    },
                    'core_runtime_control': {
                        'languages': ['rust', 'cpp'],
                        'test_types': ['unit', 'integration', 'performance', 'stress']
                    },
                    'deployment_orchestration': {
                        'languages': ['go', 'yaml'],
                        'test_types': ['unit', 'integration', 'e2e']
                    },
                    'security_ethics_control': {
                        'languages': ['rust', 'python'],
                        'test_types': ['unit', 'integration', 'security']
                    }
                }
            }

    def _load_test_suites(self):
        """Load test suite configurations."""
        for module_name, module_config in self.config.get('modules', {}).items():
            for language in module_config.get('languages', []):
                for test_type in module_config.get('test_types', []):
                    suite = self._create_test_suite(module_name, language, test_type)
                    if suite:
                        self.test_suites.append(suite)

    def _create_test_suite(self, module: str, language: str, test_type: str) -> Optional[TestSuite]:
        """Create a test suite configuration."""
        try:
            test_type_enum = TestType(test_type)

            # Determine command and working directory based on language and test type
            command, working_dir = self._get_test_command(module, language, test_type_enum)

            if not command:
                return None

            return TestSuite(
                name=f"{module}_{language}_{test_type}",
                test_type=test_type_enum,
                module=module,
                language=language,
                command=command,
                working_directory=working_dir,
                timeout_seconds=self._get_timeout(test_type_enum),
                environment_variables=self._get_environment_variables(module, language),
                parallel=self._is_parallel_safe(test_type_enum),
                coverage_enabled=test_type_enum in [TestType.UNIT, TestType.INTEGRATION],
                performance_benchmarks=test_type_enum == TestType.PERFORMANCE
            )

        except ValueError:
            logger.warning(f"Unknown test type: {test_type}")
            return None

    def _get_test_command(self, module: str, language: str, test_type: TestType) -> Tuple[str, str]:
        """Get test command and working directory for a module/language/test type."""
        base_dir = Path(module)

        commands = {
            'python': {
                TestType.UNIT: "python -m pytest tests/unit/ -v --cov=src --cov-report=xml",
                TestType.INTEGRATION: "python -m pytest tests/integration/ -v",
                TestType.PERFORMANCE: "python -m pytest tests/performance/ -v --benchmark-only",
                TestType.E2E: "python -m pytest tests/e2e/ -v",
                TestType.SECURITY: "python -m pytest tests/security/ -v"
            },
            'rust': {
                TestType.UNIT: "cargo test --lib",
                TestType.INTEGRATION: "cargo test --test '*'",
                TestType.PERFORMANCE: "cargo bench",
                TestType.STRESS: "cargo test --release -- --ignored stress"
            },
            'go': {
                TestType.UNIT: "go test -v -race -coverprofile=coverage.out ./...",
                TestType.INTEGRATION: "go test -v -tags=integration ./...",
                TestType.PERFORMANCE: "go test -v -bench=. -benchmem ./...",
                TestType.E2E: "go test -v -tags=e2e ./..."
            },
            'cpp': {
                TestType.UNIT: "ctest --output-on-failure",
                TestType.INTEGRATION: "ctest --output-on-failure -L integration",
                TestType.PERFORMANCE: "ctest --output-on-failure -L performance"
            },
            'typescript': {
                TestType.UNIT: "npm test -- --coverage",
                TestType.INTEGRATION: "npm run test:integration",
                TestType.E2E: "npm run test:e2e"
            },
            'scala': {
                TestType.UNIT: "sbt test",
                TestType.INTEGRATION: "sbt it:test",
                TestType.PERFORMANCE: "sbt jmh:run"
            },
            'java': {
                TestType.UNIT: "mvn test",
                TestType.INTEGRATION: "mvn verify -P integration-tests",
                TestType.PERFORMANCE: "mvn test -P performance-tests"
            },
            'julia': {
                TestType.UNIT: "julia --project=. -e 'using Pkg; Pkg.test()'",
                TestType.INTEGRATION: "julia --project=. test/integration_tests.jl",
                TestType.PERFORMANCE: "julia --project=. test/benchmark_tests.jl"
            },
            'lisp': {
                TestType.UNIT: "sbcl --eval '(asdf:test-system :asi-self-improvement)'",
                TestType.INTEGRATION: "sbcl --eval '(load \"test/integration-tests.lisp\")'"
            }
        }

        # Get language-specific subdirectory
        lang_dirs = {
            'python': 'python-*',
            'rust': 'rust-*',
            'go': 'go-*',
            'cpp': 'cpp-*',
            'typescript': '*-dashboard',
            'scala': 'scala-*',
            'java': 'java-*',
            'julia': 'julia-*',
            'lisp': 'lisp-*'
        }

        if language in commands and test_type in commands[language]:
            # Find the actual directory
            import glob
            pattern = str(base_dir / lang_dirs.get(language, language + '-*'))
            matching_dirs = glob.glob(pattern)

            if matching_dirs:
                working_dir = matching_dirs[0]
                return commands[language][test_type], working_dir

        return "", ""

    def _get_timeout(self, test_type: TestType) -> int:
        """Get timeout for test type."""
        timeouts = {
            TestType.UNIT: 300,
            TestType.INTEGRATION: 600,
            TestType.E2E: 1800,
            TestType.PERFORMANCE: 3600,
            TestType.SECURITY: 900,
            TestType.LOAD: 3600,
            TestType.STRESS: 7200
        }
        return timeouts.get(test_type, 300)

    def _get_environment_variables(self, module: str, language: str) -> Dict[str, str]:
        """Get environment variables for test execution."""
        env_vars = {
            'ASI_TEST_MODE': 'true',
            'ASI_LOG_LEVEL': 'DEBUG',
            'ASI_MODULE': module,
            'ASI_LANGUAGE': language
        }

        # Add language-specific environment variables
        if language == 'python':
            env_vars.update({
                'PYTHONPATH': '.:src',
                'PYTEST_CURRENT_TEST': 'true'
            })
        elif language == 'rust':
            env_vars.update({
                'RUST_BACKTRACE': '1',
                'RUST_LOG': 'debug'
            })
        elif language == 'go':
            env_vars.update({
                'GO111MODULE': 'on',
                'CGO_ENABLED': '1'
            })

        return env_vars

    def _is_parallel_safe(self, test_type: TestType) -> bool:
        """Check if test type is safe for parallel execution."""
        # E2E and stress tests are typically not parallel-safe
        return test_type not in [TestType.E2E, TestType.STRESS, TestType.LOAD]

    async def run_all_tests(self, test_types: Optional[List[TestType]] = None,
                           modules: Optional[List[str]] = None) -> Dict[str, Any]:
        """Run all configured tests."""
        self.start_time = datetime.utcnow()
        logger.info("Starting comprehensive test execution")

        # Filter test suites
        suites_to_run = self._filter_test_suites(test_types, modules)
        logger.info(f"Running {len(suites_to_run)} test suites")

        # Execute tests
        if self.config.get('parallel_execution', True):
            await self._run_tests_parallel(suites_to_run)
        else:
            await self._run_tests_sequential(suites_to_run)

        self.end_time = datetime.utcnow()

        # Generate reports
        report = self._generate_test_report()

        # Save results
        await self._save_results()

        logger.info("Test execution completed")
        return report

    def _filter_test_suites(self, test_types: Optional[List[TestType]],
                           modules: Optional[List[str]]) -> List[TestSuite]:
        """Filter test suites based on criteria."""
        filtered = self.test_suites

        if test_types:
            filtered = [s for s in filtered if s.test_type in test_types]

        if modules:
            filtered = [s for s in filtered if s.module in modules]

        return filtered

    async def _run_tests_parallel(self, test_suites: List[TestSuite]):
        """Run tests in parallel."""
        max_workers = self.config.get('max_workers', 4)

        # Separate parallel-safe and sequential tests
        parallel_suites = [s for s in test_suites if s.parallel]
        sequential_suites = [s for s in test_suites if not s.parallel]

        # Run parallel tests
        if parallel_suites:
            with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                tasks = []
                for suite in parallel_suites:
                    task = executor.submit(self._run_single_test, suite)
                    tasks.append(task)

                # Wait for all parallel tests to complete
                for future in concurrent.futures.as_completed(tasks):
                    try:
                        result = future.result()
                        self.results.append(result)
                    except Exception as e:
                        logger.error(f"Parallel test execution failed: {e}")

        # Run sequential tests
        for suite in sequential_suites:
            try:
                result = self._run_single_test(suite)
                self.results.append(result)
            except Exception as e:
                logger.error(f"Sequential test execution failed: {e}")

    async def _run_tests_sequential(self, test_suites: List[TestSuite]):
        """Run tests sequentially."""
        for suite in test_suites:
            try:
                result = self._run_single_test(suite)
                self.results.append(result)
            except Exception as e:
                logger.error(f"Test execution failed: {e}")

    def _run_single_test(self, suite: TestSuite) -> TestResult:
        """Run a single test suite."""
        logger.info(f"Running test suite: {suite.name}")

        start_time = datetime.utcnow()
        result = TestResult(
            test_id=suite.name,
            test_type=suite.test_type,
            module=suite.module,
            language=suite.language,
            status=TestStatus.RUNNING,
            duration_seconds=0.0,
            start_time=start_time
        )

        try:
            # Prepare environment
            env = os.environ.copy()
            env.update(suite.environment_variables)

            # Execute test command
            process = subprocess.Popen(
                suite.command,
                shell=True,
                cwd=suite.working_directory,
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True
            )

            # Wait for completion with timeout
            try:
                output, _ = process.communicate(timeout=suite.timeout_seconds)
                return_code = process.returncode
            except subprocess.TimeoutExpired:
                process.kill()
                output = "Test timed out"
                return_code = -1

            # Determine status
            if return_code == 0:
                result.status = TestStatus.PASSED
            else:
                result.status = TestStatus.FAILED
                result.error_message = f"Process exited with code {return_code}"

            result.output = output
            result.end_time = datetime.utcnow()
            result.duration_seconds = (result.end_time - start_time).total_seconds()

            # Extract coverage information
            if suite.coverage_enabled:
                result.coverage_percentage = self._extract_coverage(output, suite.language)

            # Extract performance metrics
            if suite.performance_benchmarks:
                result.performance_metrics = self._extract_performance_metrics(output, suite.language)

            logger.info(f"Test suite {suite.name} completed with status: {result.status}")

        except Exception as e:
            result.status = TestStatus.ERROR
            result.error_message = str(e)
            result.end_time = datetime.utcnow()
            result.duration_seconds = (result.end_time - start_time).total_seconds()
            logger.error(f"Test suite {suite.name} failed with error: {e}")

        return result

    def _extract_coverage(self, output: str, language: str) -> float:
        """Extract code coverage percentage from test output."""
        import re

        patterns = {
            'python': r'TOTAL.*?(\d+)%',
            'go': r'coverage: ([\d.]+)% of statements',
            'rust': r'test result: ok\. \d+ passed.*?(\d+\.\d+)% coverage',
            'typescript': r'All files.*?(\d+\.\d+)',
            'java': r'Line coverage: ([\d.]+)%',
            'cpp': r'lines......: ([\d.]+)%'
        }

        if language in patterns:
            match = re.search(patterns[language], output)
            if match:
                return float(match.group(1))

        return 0.0

    def _extract_performance_metrics(self, output: str, language: str) -> Dict[str, Any]:
        """Extract performance metrics from test output."""
        metrics = {}

        # Language-specific metric extraction
        if language == 'python':
            # Extract pytest-benchmark results
            import re
            benchmark_pattern = r'(\w+)\s+(\d+\.\d+)\s+(\d+\.\d+)\s+(\d+\.\d+)\s+(\d+\.\d+)'
            matches = re.findall(benchmark_pattern, output)
            for match in matches:
                test_name, min_time, max_time, mean_time, stddev = match
                metrics[test_name] = {
                    'min_time_seconds': float(min_time),
                    'max_time_seconds': float(max_time),
                    'mean_time_seconds': float(mean_time),
                    'stddev_seconds': float(stddev)
                }

        elif language == 'rust':
            # Extract cargo bench results
            import re
            bench_pattern = r'test (\w+)\s+\.\.\. bench:\s+(\d+,?\d*) ns/iter'
            matches = re.findall(bench_pattern, output)
            for match in matches:
                test_name, ns_per_iter = match
                metrics[test_name] = {
                    'nanoseconds_per_iteration': int(ns_per_iter.replace(',', ''))
                }

        elif language == 'go':
            # Extract go test benchmark results
            import re
            bench_pattern = r'Benchmark(\w+)-\d+\s+(\d+)\s+(\d+) ns/op'
            matches = re.findall(bench_pattern, output)
            for match in matches:
                test_name, iterations, ns_per_op = match
                metrics[test_name] = {
                    'iterations': int(iterations),
                    'nanoseconds_per_operation': int(ns_per_op)
                }

        return metrics

    def _generate_test_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report."""
        total_tests = len(self.results)
        passed_tests = len([r for r in self.results if r.status == TestStatus.PASSED])
        failed_tests = len([r for r in self.results if r.status == TestStatus.FAILED])
        error_tests = len([r for r in self.results if r.status == TestStatus.ERROR])

        total_duration = (self.end_time - self.start_time).total_seconds() if self.end_time else 0

        # Calculate coverage statistics
        coverage_results = [r for r in self.results if r.coverage_percentage > 0]
        avg_coverage = sum(r.coverage_percentage for r in coverage_results) / len(coverage_results) if coverage_results else 0

        # Group results by module and test type
        by_module = {}
        by_test_type = {}

        for result in self.results:
            # By module
            if result.module not in by_module:
                by_module[result.module] = {'passed': 0, 'failed': 0, 'error': 0, 'total': 0}
            by_module[result.module]['total'] += 1
            if result.status == TestStatus.PASSED:
                by_module[result.module]['passed'] += 1
            elif result.status == TestStatus.FAILED:
                by_module[result.module]['failed'] += 1
            elif result.status == TestStatus.ERROR:
                by_module[result.module]['error'] += 1

            # By test type
            test_type_key = result.test_type.value
            if test_type_key not in by_test_type:
                by_test_type[test_type_key] = {'passed': 0, 'failed': 0, 'error': 0, 'total': 0}
            by_test_type[test_type_key]['total'] += 1
            if result.status == TestStatus.PASSED:
                by_test_type[test_type_key]['passed'] += 1
            elif result.status == TestStatus.FAILED:
                by_test_type[test_type_key]['failed'] += 1
            elif result.status == TestStatus.ERROR:
                by_test_type[test_type_key]['error'] += 1

        report = {
            'summary': {
                'total_tests': total_tests,
                'passed': passed_tests,
                'failed': failed_tests,
                'errors': error_tests,
                'success_rate': (passed_tests / total_tests * 100) if total_tests > 0 else 0,
                'total_duration_seconds': total_duration,
                'average_coverage_percentage': avg_coverage,
                'start_time': self.start_time.isoformat() if self.start_time else None,
                'end_time': self.end_time.isoformat() if self.end_time else None
            },
            'by_module': by_module,
            'by_test_type': by_test_type,
            'detailed_results': [
                {
                    'test_id': r.test_id,
                    'module': r.module,
                    'language': r.language,
                    'test_type': r.test_type.value,
                    'status': r.status.value,
                    'duration_seconds': r.duration_seconds,
                    'coverage_percentage': r.coverage_percentage,
                    'error_message': r.error_message,
                    'performance_metrics': r.performance_metrics
                }
                for r in self.results
            ]
        }

        return report

    async def _save_results(self):
        """Save test results to files."""
        timestamp = datetime.utcnow().strftime('%Y%m%d_%H%M%S')
        results_dir = Path('testing/results') / timestamp
        results_dir.mkdir(parents=True, exist_ok=True)

        # Save JSON report
        report = self._generate_test_report()
        with open(results_dir / 'test_report.json', 'w') as f:
            json.dump(report, f, indent=2)

        # Save detailed results
        detailed_results = []
        for result in self.results:
            detailed_results.append({
                'test_id': result.test_id,
                'test_type': result.test_type.value,
                'module': result.module,
                'language': result.language,
                'status': result.status.value,
                'duration_seconds': result.duration_seconds,
                'start_time': result.start_time.isoformat(),
                'end_time': result.end_time.isoformat() if result.end_time else None,
                'output': result.output,
                'error_message': result.error_message,
                'coverage_percentage': result.coverage_percentage,
                'performance_metrics': result.performance_metrics,
                'artifacts': result.artifacts
            })

        with open(results_dir / 'detailed_results.json', 'w') as f:
            json.dump(detailed_results, f, indent=2)

        logger.info(f"Test results saved to {results_dir}")


async def main():
    """Main entry point for test orchestrator."""
    import argparse

    parser = argparse.ArgumentParser(description='ASI System Test Orchestrator')
    parser.add_argument('--config', default='testing/config.yaml', help='Test configuration file')
    parser.add_argument('--test-types', nargs='+', choices=[t.value for t in TestType],
                       help='Test types to run')
    parser.add_argument('--modules', nargs='+', help='Modules to test')
    parser.add_argument('--parallel', action='store_true', default=True, help='Run tests in parallel')
    parser.add_argument('--output', default='testing/results', help='Output directory for results')

    args = parser.parse_args()

    # Create orchestrator
    orchestrator = TestOrchestrator(args.config)

    # Convert test types
    test_types = None
    if args.test_types:
        test_types = [TestType(t) for t in args.test_types]

    # Run tests
    try:
        report = await orchestrator.run_all_tests(test_types=test_types, modules=args.modules)

        # Print summary
        summary = report['summary']
        print(f"\n{'='*60}")
        print(f"ASI SYSTEM TEST RESULTS")
        print(f"{'='*60}")
        print(f"Total Tests: {summary['total_tests']}")
        print(f"Passed: {summary['passed']}")
        print(f"Failed: {summary['failed']}")
        print(f"Errors: {summary['errors']}")
        print(f"Success Rate: {summary['success_rate']:.1f}%")
        print(f"Average Coverage: {summary['average_coverage_percentage']:.1f}%")
        print(f"Total Duration: {summary['total_duration_seconds']:.1f}s")
        print(f"{'='*60}")

        # Exit with appropriate code
        if summary['failed'] > 0 or summary['errors'] > 0:
            sys.exit(1)
        else:
            sys.exit(0)

    except Exception as e:
        logger.error(f"Test orchestration failed: {e}")
        sys.exit(1)


if __name__ == '__main__':
    asyncio.run(main())