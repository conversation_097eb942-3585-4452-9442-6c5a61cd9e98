# ASI System - Security Baseline Policy
# =====================================

package asi.security.baseline

import rego.v1

# Default deny for security baseline violations
default allow_security_operation := false

# Allow operation if all security baseline requirements are met
allow_security_operation if {
    # Encryption requirements
    encryption_requirements_met
    
    # Authentication requirements
    authentication_requirements_met
    
    # Network security requirements
    network_security_requirements_met
    
    # Logging and monitoring requirements
    logging_monitoring_requirements_met
    
    # Vulnerability management requirements
    vulnerability_management_requirements_met
    
    # Configuration security requirements
    configuration_security_requirements_met
}

# Encryption Requirements
encryption_requirements_met if {
    # Data at rest encryption
    data_at_rest_encrypted
    
    # Data in transit encryption
    data_in_transit_encrypted
    
    # Key management compliance
    key_management_compliant
    
    # Encryption algorithm compliance
    encryption_algorithms_compliant
}

data_at_rest_encrypted if {
    operation := input.security_operation
    
    # Sensitive data must be encrypted at rest
    involves_sensitive_data(operation)
    operation.encryption.at_rest.enabled == true
    operation.encryption.at_rest.algorithm in data.security_config.approved_encryption_algorithms
}

data_at_rest_encrypted if {
    operation := input.security_operation
    
    # Non-sensitive data doesn't require encryption
    not involves_sensitive_data(operation)
}

data_in_transit_encrypted if {
    operation := input.security_operation
    
    # All network communications must be encrypted
    operation.encryption.in_transit.enabled == true
    operation.encryption.in_transit.protocol in ["TLS1.3", "TLS1.2"]
    operation.encryption.in_transit.cipher_suite in data.security_config.approved_cipher_suites
}

key_management_compliant if {
    operation := input.security_operation
    key_mgmt := operation.encryption.key_management
    
    # Keys must be managed by approved systems
    key_mgmt.system in data.security_config.approved_key_management_systems
    
    # Key rotation must be enabled
    key_mgmt.rotation_enabled == true
    
    # Key rotation frequency must be appropriate
    key_rotation_frequency_appropriate(key_mgmt.rotation_frequency_days)
}

encryption_algorithms_compliant if {
    operation := input.security_operation
    algorithms := operation.encryption.algorithms_used
    
    # All algorithms must be approved
    unapproved_algorithms := algorithms - data.security_config.approved_encryption_algorithms
    count(unapproved_algorithms) == 0
    
    # No deprecated algorithms
    deprecated_algorithms := algorithms & data.security_config.deprecated_encryption_algorithms
    count(deprecated_algorithms) == 0
}

# Authentication Requirements
authentication_requirements_met if {
    # Strong authentication required
    strong_authentication_enabled
    
    # Multi-factor authentication for privileged access
    mfa_for_privileged_access
    
    # Session management compliance
    session_management_compliant
    
    # Password policy compliance
    password_policy_compliant
}

strong_authentication_enabled if {
    operation := input.security_operation
    auth := operation.authentication
    
    # Authentication must be enabled
    auth.enabled == true
    
    # Authentication method must be strong
    auth.method in data.security_config.strong_auth_methods
    
    # Authentication tokens must be secure
    auth.token_security.secure == true
}

mfa_for_privileged_access if {
    operation := input.security_operation
    
    # Privileged operations require MFA
    is_privileged_operation(operation)
    operation.authentication.mfa.enabled == true
    operation.authentication.mfa.factors >= 2
}

mfa_for_privileged_access if {
    operation := input.security_operation
    
    # Non-privileged operations don't require MFA
    not is_privileged_operation(operation)
}

session_management_compliant if {
    operation := input.security_operation
    session := operation.session_management
    
    # Session timeout must be configured
    session.timeout_enabled == true
    session.timeout_minutes <= data.security_config.max_session_timeout_minutes
    
    # Session tokens must be secure
    session.token_security.httponly == true
    session.token_security.secure == true
    session.token_security.samesite == "Strict"
}

password_policy_compliant if {
    operation := input.security_operation
    
    # Password authentication requires policy compliance
    operation.authentication.method == "password"
    password_policy := operation.authentication.password_policy
    
    # Minimum length requirement
    password_policy.min_length >= data.security_config.password_policy.min_length
    
    # Complexity requirements
    password_policy.require_uppercase == true
    password_policy.require_lowercase == true
    password_policy.require_numbers == true
    password_policy.require_special_chars == true
    
    # Password history
    password_policy.history_count >= data.security_config.password_policy.min_history_count
}

password_policy_compliant if {
    operation := input.security_operation
    
    # Non-password authentication doesn't require password policy
    operation.authentication.method != "password"
}

# Network Security Requirements
network_security_requirements_met if {
    # Firewall rules configured
    firewall_configured
    
    # Network segmentation implemented
    network_segmentation_implemented
    
    # Intrusion detection enabled
    intrusion_detection_enabled
    
    # DDoS protection enabled
    ddos_protection_enabled
}

firewall_configured if {
    operation := input.security_operation
    firewall := operation.network_security.firewall
    
    # Firewall must be enabled
    firewall.enabled == true
    
    # Default deny policy
    firewall.default_policy == "deny"
    
    # Rules must be reviewed recently
    rules_review_age := get_days_since(firewall.rules_last_reviewed)
    rules_review_age <= data.security_config.firewall_review_frequency_days
}

network_segmentation_implemented if {
    operation := input.security_operation
    segmentation := operation.network_security.segmentation
    
    # Network segmentation must be enabled
    segmentation.enabled == true
    
    # Appropriate segmentation for data classification
    segmentation_appropriate_for_classification(segmentation, operation.data_classification)
}

intrusion_detection_enabled if {
    operation := input.security_operation
    ids := operation.network_security.intrusion_detection
    
    # IDS must be enabled
    ids.enabled == true
    
    # IDS must be actively monitoring
    ids.monitoring_active == true
    
    # IDS signatures must be up to date
    signature_age := get_days_since(ids.signatures_last_updated)
    signature_age <= data.security_config.ids_signature_update_frequency_days
}

ddos_protection_enabled if {
    operation := input.security_operation
    ddos := operation.network_security.ddos_protection
    
    # DDoS protection must be enabled for public-facing services
    is_public_facing(operation)
    ddos.enabled == true
    ddos.rate_limiting.enabled == true
}

ddos_protection_enabled if {
    operation := input.security_operation
    
    # Internal services don't require DDoS protection
    not is_public_facing(operation)
}

# Logging and Monitoring Requirements
logging_monitoring_requirements_met if {
    # Security logging enabled
    security_logging_enabled
    
    # Log retention compliance
    log_retention_compliant
    
    # Security monitoring enabled
    security_monitoring_enabled
    
    # Alerting configured
    alerting_configured
}

security_logging_enabled if {
    operation := input.security_operation
    logging := operation.logging
    
    # Security logging must be enabled
    logging.security_events.enabled == true
    
    # Log level must be appropriate
    logging.security_events.level in ["INFO", "WARN", "ERROR"]
    
    # Logs must be tamper-proof
    logging.security_events.tamper_proof == true
    
    # Logs must be centralized
    logging.security_events.centralized == true
}

log_retention_compliant if {
    operation := input.security_operation
    retention := operation.logging.retention
    
    # Retention period must meet minimum requirements
    retention.security_logs_days >= data.security_config.min_security_log_retention_days
    
    # Audit logs must be retained longer
    retention.audit_logs_days >= data.security_config.min_audit_log_retention_days
}

security_monitoring_enabled if {
    operation := input.security_operation
    monitoring := operation.monitoring
    
    # Security monitoring must be enabled
    monitoring.security_events.enabled == true
    
    # Real-time monitoring for critical events
    monitoring.security_events.real_time == true
    
    # Anomaly detection enabled
    monitoring.anomaly_detection.enabled == true
}

alerting_configured if {
    operation := input.security_operation
    alerting := operation.alerting
    
    # Security alerting must be enabled
    alerting.security_events.enabled == true
    
    # Critical alerts must have immediate notification
    alerting.critical_events.immediate_notification == true
    
    # Alert escalation must be configured
    alerting.escalation.configured == true
}

# Vulnerability Management Requirements
vulnerability_management_requirements_met if {
    # Vulnerability scanning enabled
    vulnerability_scanning_enabled
    
    # Patch management compliant
    patch_management_compliant
    
    # Security assessments current
    security_assessments_current
    
    # Dependency scanning enabled
    dependency_scanning_enabled
}

vulnerability_scanning_enabled if {
    operation := input.security_operation
    vuln_scan := operation.vulnerability_management.scanning
    
    # Vulnerability scanning must be enabled
    vuln_scan.enabled == true
    
    # Scanning frequency must be appropriate
    scan_frequency_appropriate(vuln_scan.frequency_days, operation.risk_level)
    
    # Critical vulnerabilities must be addressed quickly
    vuln_scan.critical_remediation_sla_hours <= data.security_config.critical_vuln_sla_hours
}

patch_management_compliant if {
    operation := input.security_operation
    patch_mgmt := operation.vulnerability_management.patch_management
    
    # Patch management process must be defined
    patch_mgmt.process_defined == true
    
    # Patches must be tested before deployment
    patch_mgmt.testing_required == true
    
    # Critical patches must be applied quickly
    patch_mgmt.critical_patch_sla_hours <= data.security_config.critical_patch_sla_hours
}

security_assessments_current if {
    operation := input.security_operation
    assessments := operation.security_assessments
    
    # Security assessment must be recent
    assessment_age := get_days_since(assessments.last_assessment_date)
    assessment_age <= data.security_config.security_assessment_frequency_days
    
    # Assessment must be comprehensive
    assessments.comprehensive == true
}

dependency_scanning_enabled if {
    operation := input.security_operation
    dep_scan := operation.vulnerability_management.dependency_scanning
    
    # Dependency scanning must be enabled
    dep_scan.enabled == true
    
    # Scanning must include all dependencies
    dep_scan.comprehensive == true
    
    # Known vulnerable dependencies must be flagged
    dep_scan.vulnerability_detection == true
}

# Configuration Security Requirements
configuration_security_requirements_met if {
    # Secure configuration baseline
    secure_configuration_baseline
    
    # Configuration management
    configuration_management_compliant
    
    # Hardening guidelines followed
    hardening_guidelines_followed
    
    # Default credentials changed
    default_credentials_changed
}

secure_configuration_baseline if {
    operation := input.security_operation
    config := operation.configuration
    
    # Configuration must follow security baseline
    config.follows_security_baseline == true
    
    # Configuration must be validated
    config.validated == true
    
    # Configuration drift detection enabled
    config.drift_detection.enabled == true
}

configuration_management_compliant if {
    operation := input.security_operation
    config_mgmt := operation.configuration_management
    
    # Configuration changes must be tracked
    config_mgmt.change_tracking.enabled == true
    
    # Configuration changes must be approved
    config_mgmt.change_approval.required == true
    
    # Configuration backup must be maintained
    config_mgmt.backup.enabled == true
}

hardening_guidelines_followed if {
    operation := input.security_operation
    hardening := operation.hardening
    
    # System hardening must be applied
    hardening.applied == true
    
    # Hardening must follow approved guidelines
    hardening.guidelines in data.security_config.approved_hardening_guidelines
    
    # Hardening compliance must be verified
    hardening.compliance_verified == true
}

default_credentials_changed if {
    operation := input.security_operation
    credentials := operation.credentials
    
    # Default credentials must be changed
    credentials.defaults_changed == true
    
    # Credential strength must be verified
    credentials.strength_verified == true
}

# Helper functions
involves_sensitive_data(operation) if {
    operation.data_classification in ["confidential", "restricted", "top_secret"]
}

is_privileged_operation(operation) if {
    operation.privilege_level in ["admin", "root", "system"]
}

is_public_facing(operation) if {
    operation.network_exposure == "public"
}

get_days_since(timestamp) := days if {
    now := time.now_ns()
    diff_ns := now - timestamp
    days := diff_ns / (24 * 60 * 60 * 1000000000)
}

key_rotation_frequency_appropriate(frequency_days) if {
    # High-security keys must be rotated more frequently
    frequency_days <= data.security_config.max_key_rotation_frequency_days
}

segmentation_appropriate_for_classification(segmentation, classification) if {
    # Restricted data requires network isolation
    classification == "restricted"
    segmentation.isolation_level == "complete"
}

segmentation_appropriate_for_classification(segmentation, classification) if {
    # Confidential data requires VLAN segmentation
    classification == "confidential"
    segmentation.isolation_level in ["complete", "vlan"]
}

segmentation_appropriate_for_classification(segmentation, classification) if {
    # Internal data requires basic segmentation
    classification == "internal"
    segmentation.isolation_level in ["complete", "vlan", "basic"]
}

segmentation_appropriate_for_classification(segmentation, classification) if {
    # Public data doesn't require segmentation
    classification == "public"
}

scan_frequency_appropriate(frequency_days, risk_level) if {
    max_frequency := get_max_scan_frequency(risk_level)
    frequency_days <= max_frequency
}

get_max_scan_frequency("critical") := 7
get_max_scan_frequency("high") := 14
get_max_scan_frequency("medium") := 30
get_max_scan_frequency("low") := 90

# Security baseline audit information
security_baseline_audit := {
    "timestamp": time.now_ns(),
    "operation_id": input.security_operation.id,
    "decision": allow_security_operation,
    "compliance_checks": {
        "encryption": encryption_requirements_met,
        "authentication": authentication_requirements_met,
        "network_security": network_security_requirements_met,
        "logging_monitoring": logging_monitoring_requirements_met,
        "vulnerability_management": vulnerability_management_requirements_met,
        "configuration_security": configuration_security_requirements_met
    },
    "risk_level": input.security_operation.risk_level,
    "user": input.user.id,
    "request_id": input.request.id
}
