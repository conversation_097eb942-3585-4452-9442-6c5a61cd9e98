# ASI System - Data Governance Policy
# ===================================

package asi.security.data_governance

import rego.v1

# Default deny for data operations
default allow_data_operation := false

# Allow data operation if all governance rules are satisfied
allow_data_operation if {
    # Data classification compliance
    data_classification_compliant
    
    # Data retention compliance
    data_retention_compliant
    
    # Data privacy compliance
    data_privacy_compliant
    
    # Data quality requirements
    data_quality_compliant
    
    # Data lineage tracking
    data_lineage_tracked
    
    # Consent and legal basis
    has_legal_basis
}

# Data Classification Compliance
data_classification_compliant if {
    # Data must be properly classified
    has_data_classification
    
    # Classification must be appropriate for the data type
    classification_appropriate
    
    # Handling must match classification level
    handling_matches_classification
}

has_data_classification if {
    input.data.classification
    input.data.classification in ["public", "internal", "confidential", "restricted"]
}

classification_appropriate if {
    data_type := input.data.type
    classification := input.data.classification
    
    # Personal data must be at least internal
    data_type == "personal_data"
    classification_level(classification) >= classification_level("internal")
}

classification_appropriate if {
    data_type := input.data.type
    classification := input.data.classification
    
    # Financial data must be at least confidential
    data_type == "financial_data"
    classification_level(classification) >= classification_level("confidential")
}

classification_appropriate if {
    data_type := input.data.type
    classification := input.data.classification
    
    # Health data must be restricted
    data_type == "health_data"
    classification_level(classification) >= classification_level("restricted")
}

classification_appropriate if {
    data_type := input.data.type
    classification := input.data.classification
    
    # AI model data must be at least confidential
    data_type == "ai_model_data"
    classification_level(classification) >= classification_level("confidential")
}

classification_appropriate if {
    # Default case for other data types
    data_type := input.data.type
    not data_type in ["personal_data", "financial_data", "health_data", "ai_model_data"]
}

handling_matches_classification if {
    classification := input.data.classification
    operation := input.operation
    
    # Public data - no restrictions
    classification == "public"
}

handling_matches_classification if {
    classification := input.data.classification
    operation := input.operation
    
    # Internal data - encryption in transit
    classification == "internal"
    operation.encryption_in_transit == true
}

handling_matches_classification if {
    classification := input.data.classification
    operation := input.operation
    
    # Confidential data - encryption at rest and in transit
    classification == "confidential"
    operation.encryption_at_rest == true
    operation.encryption_in_transit == true
}

handling_matches_classification if {
    classification := input.data.classification
    operation := input.operation
    
    # Restricted data - full encryption + access logging
    classification == "restricted"
    operation.encryption_at_rest == true
    operation.encryption_in_transit == true
    operation.access_logging == true
    operation.audit_trail == true
}

# Data Retention Compliance
data_retention_compliant if {
    # Check if data is within retention period
    within_retention_period
    
    # Check if retention policy exists
    has_retention_policy
    
    # Check if deletion is scheduled appropriately
    deletion_scheduled_appropriately
}

within_retention_period if {
    data_age_days := get_data_age_days(input.data.created_timestamp)
    retention_days := get_retention_period(input.data.classification, input.data.type)
    data_age_days <= retention_days
}

has_retention_policy if {
    classification := input.data.classification
    data_type := input.data.type
    policy := data.retention_policies[classification][data_type]
    policy.retention_days
}

deletion_scheduled_appropriately if {
    # For data nearing retention limit, deletion must be scheduled
    data_age_days := get_data_age_days(input.data.created_timestamp)
    retention_days := get_retention_period(input.data.classification, input.data.type)
    
    # If data is within 30 days of retention limit
    days_until_deletion := retention_days - data_age_days
    days_until_deletion > 30
}

deletion_scheduled_appropriately if {
    # Deletion is already scheduled
    input.data.deletion_scheduled == true
}

# Data Privacy Compliance
data_privacy_compliant if {
    # GDPR compliance for EU data
    gdpr_compliant
    
    # CCPA compliance for California data
    ccpa_compliant
    
    # General privacy requirements
    general_privacy_compliant
}

gdpr_compliant if {
    # Not EU data, GDPR doesn't apply
    not is_eu_data(input.data)
}

gdpr_compliant if {
    # EU data with proper GDPR compliance
    is_eu_data(input.data)
    
    # Must have legal basis
    has_gdpr_legal_basis
    
    # Must respect data subject rights
    respects_data_subject_rights
    
    # Must have privacy notice
    has_privacy_notice
}

ccpa_compliant if {
    # Not California data, CCPA doesn't apply
    not is_california_data(input.data)
}

ccpa_compliant if {
    # California data with proper CCPA compliance
    is_california_data(input.data)
    
    # Must have opt-out mechanism
    has_opt_out_mechanism
    
    # Must not sell personal information without consent
    not_selling_without_consent
}

general_privacy_compliant if {
    # Data minimization principle
    data_minimized
    
    # Purpose limitation
    purpose_limited
    
    # Accuracy requirement
    data_accurate
}

# Data Quality Compliance
data_quality_compliant if {
    # Data completeness check
    data_complete
    
    # Data accuracy check
    data_accurate
    
    # Data consistency check
    data_consistent
    
    # Data validity check
    data_valid
}

data_complete if {
    # Check required fields are present
    required_fields := get_required_fields(input.data.type)
    provided_fields := object.keys(input.data.content)
    missing_fields := required_fields - provided_fields
    count(missing_fields) == 0
}

data_accurate if {
    # Data has been validated
    input.data.validated == true
    
    # Data validation timestamp is recent (within 24 hours)
    validation_age_hours := get_hours_since(input.data.validation_timestamp)
    validation_age_hours <= 24
}

data_consistent if {
    # Data passes consistency checks
    input.data.consistency_check_passed == true
}

data_valid if {
    # Data passes format validation
    input.data.format_valid == true
    
    # Data passes business rule validation
    input.data.business_rules_valid == true
}

# Data Lineage Tracking
data_lineage_tracked if {
    # Data source is documented
    has_data_source
    
    # Processing history is tracked
    has_processing_history
    
    # Data transformations are logged
    transformations_logged
}

has_data_source if {
    input.data.lineage.source
    input.data.lineage.source != ""
}

has_processing_history if {
    input.data.lineage.processing_steps
    count(input.data.lineage.processing_steps) > 0
}

transformations_logged if {
    # Each transformation must be documented
    transformations := input.data.lineage.transformations
    all_transformations_documented(transformations)
}

# Legal Basis and Consent
has_legal_basis if {
    # GDPR legal basis
    has_gdpr_legal_basis
}

has_legal_basis if {
    # Legitimate interest
    has_legitimate_interest
}

has_legal_basis if {
    # Contractual necessity
    has_contractual_basis
}

has_gdpr_legal_basis if {
    legal_basis := input.data.legal_basis
    legal_basis in [
        "consent",
        "contract",
        "legal_obligation",
        "vital_interests",
        "public_task",
        "legitimate_interests"
    ]
    
    # If consent, must be valid
    legal_basis != "consent"
}

has_gdpr_legal_basis if {
    legal_basis := input.data.legal_basis
    legal_basis == "consent"
    
    # Consent must be valid
    consent_valid
}

consent_valid if {
    consent := input.data.consent
    
    # Consent must exist
    consent
    
    # Consent must be freely given
    consent.freely_given == true
    
    # Consent must be specific
    consent.specific == true
    
    # Consent must be informed
    consent.informed == true
    
    # Consent must be unambiguous
    consent.unambiguous == true
    
    # Consent must not be expired
    consent_not_expired(consent.timestamp)
}

# Helper functions
classification_level("public") := 1
classification_level("internal") := 2
classification_level("confidential") := 3
classification_level("restricted") := 4

get_data_age_days(created_timestamp) := age_days if {
    now := time.now_ns()
    age_ns := now - created_timestamp
    age_days := age_ns / (24 * 60 * 60 * 1000000000)
}

get_retention_period(classification, data_type) := days if {
    policy := data.retention_policies[classification][data_type]
    days := policy.retention_days
}

get_hours_since(timestamp) := hours if {
    now := time.now_ns()
    diff_ns := now - timestamp
    hours := diff_ns / (60 * 60 * 1000000000)
}

is_eu_data(data) if {
    data.jurisdiction == "EU"
}

is_eu_data(data) if {
    data.subject_location in data.eu_countries
}

is_california_data(data) if {
    data.jurisdiction == "CA"
}

is_california_data(data) if {
    data.subject_location == "California"
}

get_required_fields(data_type) := fields if {
    schema := data.data_schemas[data_type]
    fields := {field | field := schema.required[_]}
}

all_transformations_documented(transformations) if {
    count(transformations) > 0
    all_documented := [t | 
        t := transformations[_]
        t.description
        t.timestamp
        t.operator
    ]
    count(all_documented) == count(transformations)
}

consent_not_expired(consent_timestamp) if {
    # Consent expires after 2 years
    max_age_ns := 2 * 365 * 24 * 60 * 60 * 1000000000
    now := time.now_ns()
    age := now - consent_timestamp
    age <= max_age_ns
}

has_legitimate_interest if {
    interest := input.data.legitimate_interest
    interest.documented == true
    interest.balancing_test_passed == true
}

has_contractual_basis if {
    contract := input.data.contract
    contract.exists == true
    contract.data_processing_clause == true
}

respects_data_subject_rights if {
    # Right to access
    input.data.access_mechanism_available == true
    
    # Right to rectification
    input.data.rectification_mechanism_available == true
    
    # Right to erasure
    input.data.erasure_mechanism_available == true
    
    # Right to portability
    input.data.portability_mechanism_available == true
}

has_privacy_notice if {
    notice := input.data.privacy_notice
    notice.provided == true
    notice.comprehensive == true
    notice.accessible == true
}

has_opt_out_mechanism if {
    input.data.opt_out_available == true
}

not_selling_without_consent if {
    not input.operation.involves_sale
}

not_selling_without_consent if {
    input.operation.involves_sale
    input.data.sale_consent == true
}

data_minimized if {
    # Only necessary data is collected
    input.data.minimization_check_passed == true
}

purpose_limited if {
    # Data is used only for stated purposes
    stated_purposes := input.data.stated_purposes
    actual_purpose := input.operation.purpose
    actual_purpose in stated_purposes
}

# Audit information for data governance decisions
data_governance_audit := {
    "timestamp": time.now_ns(),
    "data_id": input.data.id,
    "operation": input.operation.type,
    "classification": input.data.classification,
    "decision": allow_data_operation,
    "compliance_checks": {
        "classification_compliant": data_classification_compliant,
        "retention_compliant": data_retention_compliant,
        "privacy_compliant": data_privacy_compliant,
        "quality_compliant": data_quality_compliant,
        "lineage_tracked": data_lineage_tracked,
        "legal_basis": has_legal_basis
    },
    "user": input.user.id,
    "request_id": input.request.id
}
