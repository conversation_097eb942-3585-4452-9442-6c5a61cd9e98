# ASI System - Access Control Policy
# ==================================

package asi.security.access_control

import rego.v1

# Default deny - fail closed security model
default allow := false

# Allow access if all conditions are met
allow if {
    # User must be authenticated
    is_authenticated
    
    # User must have required permissions
    has_required_permissions
    
    # Request must be within rate limits
    within_rate_limits
    
    # Request must be from trusted source
    is_trusted_source
    
    # Time-based access controls
    is_valid_time_access
    
    # Resource-specific access controls
    has_resource_access
}

# Authentication checks
is_authenticated if {
    # JWT token validation
    input.user.token
    jwt_valid(input.user.token)
}

is_authenticated if {
    # mTLS certificate validation
    input.user.certificate
    cert_valid(input.user.certificate)
}

is_authenticated if {
    # API key validation
    input.user.api_key
    api_key_valid(input.user.api_key)
}

# JWT token validation
jwt_valid(token) if {
    # Decode and verify JWT
    payload := io.jwt.decode_verify(token, {"secret": data.jwt_secret})
    
    # Check expiration
    now := time.now_ns() / 1000000000
    payload[1].exp > now
    
    # Check issuer
    payload[1].iss == "asi-system"
    
    # Check audience
    payload[1].aud == "asi-api"
}

# Certificate validation
cert_valid(cert) if {
    # Verify certificate chain
    x509.verify(cert, data.ca_certificates)
    
    # Check certificate is not revoked
    not cert_revoked(cert)
    
    # Check certificate purpose
    cert_has_purpose(cert, "client_auth")
}

# API key validation
api_key_valid(key) if {
    # Check if key exists in valid keys
    key in data.valid_api_keys
    
    # Check if key is not expired
    key_info := data.api_key_info[key]
    now := time.now_ns() / 1000000000
    key_info.expires > now
    
    # Check if key is not revoked
    not key_info.revoked
}

# Permission checks
has_required_permissions if {
    # Get user roles
    user_roles := get_user_roles(input.user)
    
    # Get required permissions for the resource and action
    required_perms := get_required_permissions(input.resource, input.action)
    
    # Check if user has all required permissions
    user_permissions := get_user_permissions(user_roles)
    required_perms & user_permissions == required_perms
}

# Get user roles from token or database
get_user_roles(user) := roles if {
    # From JWT token
    payload := io.jwt.decode_verify(user.token, {"secret": data.jwt_secret})
    roles := payload[1].roles
}

get_user_roles(user) := roles if {
    # From user database
    user_info := data.users[user.id]
    roles := user_info.roles
}

# Get required permissions for resource and action
get_required_permissions(resource, action) := perms if {
    resource_config := data.resource_permissions[resource]
    perms := resource_config[action]
}

# Get user permissions from roles
get_user_permissions(roles) := permissions if {
    # Collect all permissions from all roles
    role_perms := [perm | 
        role := roles[_]
        role_config := data.roles[role]
        perm := role_config.permissions[_]
    ]
    permissions := {perm | perm := role_perms[_]}
}

# Rate limiting checks
within_rate_limits if {
    # Check global rate limits
    within_global_rate_limits
    
    # Check per-user rate limits
    within_user_rate_limits
    
    # Check per-resource rate limits
    within_resource_rate_limits
}

within_global_rate_limits if {
    current_rate := get_current_global_rate()
    max_rate := data.rate_limits.global.requests_per_minute
    current_rate <= max_rate
}

within_user_rate_limits if {
    user_id := input.user.id
    current_rate := get_current_user_rate(user_id)
    max_rate := data.rate_limits.per_user.requests_per_minute
    current_rate <= max_rate
}

within_resource_rate_limits if {
    resource := input.resource
    current_rate := get_current_resource_rate(resource)
    resource_config := data.rate_limits.per_resource[resource]
    max_rate := resource_config.requests_per_minute
    current_rate <= max_rate
}

# Trusted source validation
is_trusted_source if {
    # Check IP address whitelist
    ip_whitelisted
}

is_trusted_source if {
    # Check network segment
    from_trusted_network
}

is_trusted_source if {
    # Check geographic location
    from_allowed_location
}

ip_whitelisted if {
    client_ip := input.request.client_ip
    client_ip in data.trusted_ips
}

from_trusted_network if {
    client_ip := input.request.client_ip
    trusted_networks := data.trusted_networks
    some network in trusted_networks
    net.cidr_contains(network, client_ip)
}

from_allowed_location if {
    client_ip := input.request.client_ip
    location := get_ip_location(client_ip)
    location.country in data.allowed_countries
}

# Time-based access controls
is_valid_time_access if {
    # Check business hours restriction
    within_business_hours
}

is_valid_time_access if {
    # Check if user has 24/7 access
    has_always_access
}

within_business_hours if {
    now := time.now_ns()
    weekday := time.weekday(now)
    hour := time.clock(now)[0]
    
    # Monday to Friday (1-5), 9 AM to 6 PM
    weekday >= 1
    weekday <= 5
    hour >= 9
    hour <= 18
}

has_always_access if {
    user_roles := get_user_roles(input.user)
    "admin" in user_roles
}

has_always_access if {
    user_roles := get_user_roles(input.user)
    "security_team" in user_roles
}

# Resource-specific access controls
has_resource_access if {
    # Check data classification access
    has_data_classification_access
    
    # Check resource ownership
    has_resource_ownership
    
    # Check compartmentalized access
    has_compartment_access
}

has_data_classification_access if {
    resource_classification := get_resource_classification(input.resource)
    user_clearance := get_user_clearance(input.user)
    classification_level(user_clearance) >= classification_level(resource_classification)
}

has_resource_ownership if {
    resource_owner := get_resource_owner(input.resource)
    resource_owner == input.user.id
}

has_resource_ownership if {
    # Team ownership
    resource_team := get_resource_team(input.resource)
    user_teams := get_user_teams(input.user)
    resource_team in user_teams
}

has_compartment_access if {
    resource_compartment := get_resource_compartment(input.resource)
    user_compartments := get_user_compartments(input.user)
    resource_compartment in user_compartments
}

# Classification levels (higher number = higher classification)
classification_level("public") := 1
classification_level("internal") := 2
classification_level("confidential") := 3
classification_level("restricted") := 4
classification_level("top_secret") := 5

# Helper functions for external data lookups
get_current_global_rate() := rate if {
    # This would typically query a rate limiting service
    # For now, return a mock value
    rate := 100
}

get_current_user_rate(user_id) := rate if {
    # This would typically query a rate limiting service
    # For now, return a mock value
    rate := 10
}

get_current_resource_rate(resource) := rate if {
    # This would typically query a rate limiting service
    # For now, return a mock value
    rate := 50
}

get_ip_location(ip) := location if {
    # This would typically query a GeoIP service
    # For now, return a mock location
    location := {"country": "US", "region": "CA", "city": "San Francisco"}
}

get_resource_classification(resource) := classification if {
    resource_info := data.resources[resource]
    classification := resource_info.classification
}

get_user_clearance(user) := clearance if {
    user_info := data.users[user.id]
    clearance := user_info.security_clearance
}

get_resource_owner(resource) := owner if {
    resource_info := data.resources[resource]
    owner := resource_info.owner
}

get_resource_team(resource) := team if {
    resource_info := data.resources[resource]
    team := resource_info.team
}

get_user_teams(user) := teams if {
    user_info := data.users[user.id]
    teams := user_info.teams
}

get_resource_compartment(resource) := compartment if {
    resource_info := data.resources[resource]
    compartment := resource_info.compartment
}

get_user_compartments(user) := compartments if {
    user_info := data.users[user.id]
    compartments := user_info.compartments
}

cert_revoked(cert) if {
    # Check certificate revocation list
    cert_serial := x509.parse(cert).serial_number
    cert_serial in data.revoked_certificates
}

cert_has_purpose(cert, purpose) if {
    cert_info := x509.parse(cert)
    purpose in cert_info.key_usage
}

# Audit logging for access decisions
audit_log := {
    "timestamp": time.now_ns(),
    "user": input.user.id,
    "resource": input.resource,
    "action": input.action,
    "decision": allow,
    "reason": denial_reason,
    "request_id": input.request.id,
    "client_ip": input.request.client_ip,
    "user_agent": input.request.user_agent
}

# Provide detailed denial reasons for debugging and auditing
denial_reason := reason if {
    not is_authenticated
    reason := "authentication_failed"
}

denial_reason := reason if {
    is_authenticated
    not has_required_permissions
    reason := "insufficient_permissions"
}

denial_reason := reason if {
    is_authenticated
    has_required_permissions
    not within_rate_limits
    reason := "rate_limit_exceeded"
}

denial_reason := reason if {
    is_authenticated
    has_required_permissions
    within_rate_limits
    not is_trusted_source
    reason := "untrusted_source"
}

denial_reason := reason if {
    is_authenticated
    has_required_permissions
    within_rate_limits
    is_trusted_source
    not is_valid_time_access
    reason := "invalid_time_access"
}

denial_reason := reason if {
    is_authenticated
    has_required_permissions
    within_rate_limits
    is_trusted_source
    is_valid_time_access
    not has_resource_access
    reason := "resource_access_denied"
}

denial_reason := "access_granted" if allow
