# ASI System - Ethics Compliance Policy
# =====================================

package asi.security.ethics_compliance

import rego.v1

# Default deny for AI operations that may have ethical implications
default allow_ai_operation := false

# Allow AI operation if all ethical requirements are met
allow_ai_operation if {
    # Transparency requirements
    transparency_compliant
    
    # Fairness requirements
    fairness_compliant
    
    # Privacy protection
    privacy_protected
    
    # Accountability measures
    accountability_ensured
    
    # Human oversight requirements
    human_oversight_adequate
    
    # Beneficence principle
    beneficence_maintained
    
    # No prohibited uses
    not prohibited_use
}

# Transparency Compliance
transparency_compliant if {
    # AI decisions must be explainable
    explainable_decisions
    
    # Audit trail must be maintained
    audit_trail_maintained
    
    # Model documentation must be available
    model_documented
    
    # Decision process must be transparent
    decision_process_transparent
}

explainable_decisions if {
    operation := input.ai_operation
    
    # High-risk decisions require explanations
    is_high_risk_decision(operation)
    operation.explanation_available == true
    operation.explanation_quality_score >= 0.8
}

explainable_decisions if {
    operation := input.ai_operation
    
    # Low-risk decisions don't require explanations
    not is_high_risk_decision(operation)
}

audit_trail_maintained if {
    operation := input.ai_operation
    
    # Audit trail must exist
    operation.audit_trail
    
    # Must include key decision factors
    required_audit_fields := {
        "timestamp", "model_version", "input_data", 
        "decision", "confidence_score", "user_id"
    }
    audit_fields := {field | field := object.keys(operation.audit_trail)[_]}
    required_audit_fields & audit_fields == required_audit_fields
}

model_documented if {
    model := input.ai_operation.model
    
    # Model must have documentation
    model.documentation
    
    # Documentation must be comprehensive
    doc := model.documentation
    required_doc_sections := {
        "purpose", "training_data", "performance_metrics",
        "limitations", "bias_assessment", "ethical_considerations"
    }
    doc_sections := {section | section := object.keys(doc)[_]}
    required_doc_sections & doc_sections == required_doc_sections
}

decision_process_transparent if {
    operation := input.ai_operation
    
    # Decision process must be documented
    operation.decision_process_documented == true
    
    # Stakeholders must be informed
    operation.stakeholders_informed == true
}

# Fairness Compliance
fairness_compliant if {
    # Bias testing completed
    bias_testing_completed
    
    # Fairness metrics within acceptable range
    fairness_metrics_acceptable
    
    # Protected attributes not discriminated against
    no_protected_attribute_discrimination
    
    # Diverse representation in training data
    diverse_training_data
}

bias_testing_completed if {
    model := input.ai_operation.model
    
    # Bias testing must be recent (within 30 days)
    bias_test_age_days := get_days_since(model.bias_test_timestamp)
    bias_test_age_days <= 30
    
    # Bias testing must cover all protected attributes
    tested_attributes := model.bias_test_results.tested_attributes
    required_attributes := data.ethics_config.protected_attributes
    required_attributes & tested_attributes == required_attributes
}

fairness_metrics_acceptable if {
    model := input.ai_operation.model
    bias_results := model.bias_test_results
    
    # All fairness metrics must be within thresholds
    all_metrics_acceptable(bias_results.fairness_metrics)
}

no_protected_attribute_discrimination if {
    model := input.ai_operation.model
    bias_results := model.bias_test_results
    
    # Discrimination score must be below threshold
    max_discrimination := data.ethics_config.max_discrimination_score
    bias_results.discrimination_score <= max_discrimination
}

diverse_training_data if {
    model := input.ai_operation.model
    training_data := model.training_data_stats
    
    # Representation ratios must meet minimum thresholds
    min_representation := data.ethics_config.min_representation_ratio
    all_groups_represented(training_data.group_representation, min_representation)
}

# Privacy Protection
privacy_protected if {
    # Data minimization applied
    data_minimization_applied
    
    # Anonymization techniques used where appropriate
    anonymization_appropriate
    
    # Consent obtained where required
    consent_obtained_where_required
    
    # Privacy-preserving techniques used
    privacy_preserving_techniques_used
}

data_minimization_applied if {
    operation := input.ai_operation
    
    # Only necessary data is used
    operation.data_minimization_check == true
    
    # Data usage is proportional to purpose
    operation.proportionality_assessment == true
}

anonymization_appropriate if {
    operation := input.ai_operation
    data_sensitivity := operation.data.sensitivity_level
    
    # High sensitivity data must be anonymized
    data_sensitivity == "high"
    operation.data.anonymized == true
}

anonymization_appropriate if {
    operation := input.ai_operation
    data_sensitivity := operation.data.sensitivity_level
    
    # Lower sensitivity data doesn't require anonymization
    data_sensitivity in ["low", "medium"]
}

consent_obtained_where_required if {
    operation := input.ai_operation
    
    # Personal data requires consent
    involves_personal_data(operation)
    operation.consent.obtained == true
    operation.consent.valid == true
}

consent_obtained_where_required if {
    operation := input.ai_operation
    
    # Non-personal data doesn't require consent
    not involves_personal_data(operation)
}

privacy_preserving_techniques_used if {
    operation := input.ai_operation
    
    # Appropriate techniques are used based on data sensitivity
    techniques := operation.privacy_techniques
    required_techniques := get_required_privacy_techniques(operation.data.sensitivity_level)
    required_techniques & techniques == required_techniques
}

# Accountability Measures
accountability_ensured if {
    # Clear ownership assigned
    clear_ownership
    
    # Responsibility chain documented
    responsibility_chain_documented
    
    # Incident response procedures in place
    incident_response_ready
    
    # Regular audits scheduled
    regular_audits_scheduled
}

clear_ownership if {
    operation := input.ai_operation
    
    # System owner must be assigned
    operation.ownership.system_owner
    
    # Model owner must be assigned
    operation.ownership.model_owner
    
    # Data owner must be assigned
    operation.ownership.data_owner
}

responsibility_chain_documented if {
    operation := input.ai_operation
    
    # Responsibility matrix must exist
    operation.responsibility_matrix
    
    # Escalation procedures must be defined
    operation.escalation_procedures
}

incident_response_ready if {
    operation := input.ai_operation
    
    # Incident response plan must exist
    operation.incident_response_plan
    
    # Response team must be identified
    operation.incident_response_team
}

regular_audits_scheduled if {
    operation := input.ai_operation
    
    # Next audit must be scheduled
    operation.next_audit_date
    
    # Audit frequency must be appropriate for risk level
    audit_frequency_appropriate(operation.risk_level, operation.next_audit_date)
}

# Human Oversight Requirements
human_oversight_adequate if {
    # Human review for critical decisions
    human_review_for_critical_decisions
    
    # Override capability available
    override_capability_available
    
    # Escalation mechanisms in place
    escalation_mechanisms_available
    
    # Human approval for high-risk operations
    human_approval_for_high_risk
}

human_review_for_critical_decisions if {
    operation := input.ai_operation
    
    # Critical decisions require human review
    is_critical_decision(operation)
    operation.human_review.completed == true
    operation.human_review.reviewer_qualified == true
}

human_review_for_critical_decisions if {
    operation := input.ai_operation
    
    # Non-critical decisions don't require human review
    not is_critical_decision(operation)
}

override_capability_available if {
    operation := input.ai_operation
    
    # Override mechanism must be available
    operation.override_available == true
    
    # Override must be accessible to authorized users
    operation.override_accessible == true
}

escalation_mechanisms_available if {
    operation := input.ai_operation
    
    # Escalation procedures must be defined
    operation.escalation_procedures
    
    # Escalation contacts must be available
    operation.escalation_contacts
}

human_approval_for_high_risk if {
    operation := input.ai_operation
    
    # High-risk operations require human approval
    is_high_risk_operation(operation)
    operation.human_approval.obtained == true
    operation.human_approval.approver_authorized == true
}

human_approval_for_high_risk if {
    operation := input.ai_operation
    
    # Low-risk operations don't require human approval
    not is_high_risk_operation(operation)
}

# Beneficence Principle
beneficence_maintained if {
    # Positive impact assessment completed
    positive_impact_assessed
    
    # Harm mitigation measures in place
    harm_mitigation_in_place
    
    # Risk assessment completed
    risk_assessment_completed
    
    # Benefits outweigh risks
    benefits_outweigh_risks
}

positive_impact_assessed if {
    operation := input.ai_operation
    
    # Impact assessment must be completed
    operation.impact_assessment.completed == true
    
    # Positive impacts must be identified
    count(operation.impact_assessment.positive_impacts) > 0
}

harm_mitigation_in_place if {
    operation := input.ai_operation
    
    # Potential harms must be identified
    potential_harms := operation.risk_assessment.potential_harms
    
    # Mitigation measures must exist for each harm
    mitigation_measures := operation.harm_mitigation.measures
    all_harms_mitigated(potential_harms, mitigation_measures)
}

risk_assessment_completed if {
    operation := input.ai_operation
    
    # Risk assessment must be recent
    risk_assessment_age := get_days_since(operation.risk_assessment.timestamp)
    risk_assessment_age <= 90  # 3 months
    
    # Risk assessment must be comprehensive
    operation.risk_assessment.comprehensive == true
}

benefits_outweigh_risks if {
    operation := input.ai_operation
    
    # Benefit-risk analysis must show positive outcome
    operation.benefit_risk_analysis.net_benefit > 0
    
    # Analysis must be validated
    operation.benefit_risk_analysis.validated == true
}

# Prohibited Uses Check
prohibited_use if {
    operation := input.ai_operation
    use_case := operation.use_case
    
    # Check against prohibited use cases
    use_case in data.ethics_config.prohibited_uses
}

# Helper functions
is_high_risk_decision(operation) if {
    operation.risk_level in ["high", "critical"]
}

is_critical_decision(operation) if {
    operation.decision_type in data.ethics_config.critical_decision_types
}

is_high_risk_operation(operation) if {
    operation.risk_level in ["high", "critical"]
}

involves_personal_data(operation) if {
    "personal_data" in operation.data.types
}

get_days_since(timestamp) := days if {
    now := time.now_ns()
    diff_ns := now - timestamp
    days := diff_ns / (24 * 60 * 60 * 1000000000)
}

all_metrics_acceptable(metrics) if {
    thresholds := data.ethics_config.fairness_thresholds
    acceptable_metrics := [metric |
        metric := metrics[name]
        threshold := thresholds[name]
        metric <= threshold
    ]
    count(acceptable_metrics) == count(metrics)
}

all_groups_represented(representation, min_ratio) if {
    underrepresented := [group |
        group := representation[name]
        group < min_ratio
    ]
    count(underrepresented) == 0
}

get_required_privacy_techniques(sensitivity) := techniques if {
    sensitivity == "high"
    techniques := {"differential_privacy", "homomorphic_encryption", "secure_multiparty_computation"}
}

get_required_privacy_techniques(sensitivity) := techniques if {
    sensitivity == "medium"
    techniques := {"anonymization", "pseudonymization"}
}

get_required_privacy_techniques(sensitivity) := techniques if {
    sensitivity == "low"
    techniques := {"basic_anonymization"}
}

audit_frequency_appropriate(risk_level, next_audit_date) if {
    days_until_audit := get_days_until(next_audit_date)
    max_days := get_max_audit_interval(risk_level)
    days_until_audit <= max_days
}

get_max_audit_interval("critical") := 30
get_max_audit_interval("high") := 90
get_max_audit_interval("medium") := 180
get_max_audit_interval("low") := 365

get_days_until(future_timestamp) := days if {
    now := time.now_ns()
    diff_ns := future_timestamp - now
    days := diff_ns / (24 * 60 * 60 * 1000000000)
}

all_harms_mitigated(harms, mitigations) if {
    mitigated_harms := [harm |
        harm := harms[_]
        harm.id in mitigations
    ]
    count(mitigated_harms) == count(harms)
}

# Ethics compliance audit information
ethics_audit := {
    "timestamp": time.now_ns(),
    "operation_id": input.ai_operation.id,
    "model_id": input.ai_operation.model.id,
    "decision": allow_ai_operation,
    "compliance_checks": {
        "transparency": transparency_compliant,
        "fairness": fairness_compliant,
        "privacy": privacy_protected,
        "accountability": accountability_ensured,
        "human_oversight": human_oversight_adequate,
        "beneficence": beneficence_maintained,
        "prohibited_use": prohibited_use
    },
    "risk_level": input.ai_operation.risk_level,
    "user": input.user.id,
    "request_id": input.request.id
}
