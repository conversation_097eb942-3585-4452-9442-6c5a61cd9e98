# ASI System - OPA Policy Engine Dockerfile
# =========================================

# Use official OPA image as base
FROM openpolicyagent/opa:0.55.0-envoy

# Set working directory
WORKDIR /app

# Create directories for policies and data
RUN mkdir -p /app/policies /app/data /app/bundles /app/logs

# Copy policy files
COPY policies/ /app/policies/
COPY data/ /app/data/

# Copy configuration files
COPY config/ /app/config/

# Create non-root user for security
RUN addgroup -g 1001 opa && \
    adduser -D -u 1001 -G opa opa && \
    chown -R opa:opa /app

# Switch to non-root user
USER opa

# Expose OPA server port
EXPOSE 8181

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8181/health || exit 1

# Set environment variables
ENV OPA_LOG_LEVEL=info
ENV OPA_LOG_FORMAT=json
ENV OPA_CONFIG_FILE=/app/config/opa-config.yaml

# Default command to run OPA server
CMD ["run", \
     "--server", \
     "--config-file=/app/config/opa-config.yaml", \
     "--addr=0.0.0.0:8181", \
     "--log-level=${OPA_LOG_LEVEL}", \
     "--log-format=${OPA_LOG_FORMAT}", \
     "/app/policies"]
