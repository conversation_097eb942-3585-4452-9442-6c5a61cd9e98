#!/usr/bin/env python3
"""
ASI System - Blockchain Audit Service
=====================================

Service for logging audit events to blockchain for immutable audit trails.
Provides integration with Ethereum smart contracts for tamper-proof logging.
"""

import asyncio
import json
import time
import logging
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import hashlib
from web3 import Web3
from web3.contract import Contract
from web3.middleware import geth_poa_middleware
from eth_account import Account
from eth_account.messages import encode_defunct
import redis
import aiohttp
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives import serialization

logger = logging.getLogger(__name__)


class EventType(Enum):
    """Blockchain audit event types."""
    SECURITY_VIOLATION = 0
    ETHICS_VIOLATION = 1
    POLICY_ENFORCEMENT = 2
    ACCESS_CONTROL = 3
    DATA_GOVERNANCE = 4
    ANOMALY_DETECTION = 5
    SYSTEM_OPERATION = 6
    COMPLIANCE_CHECK = 7


class Severity(Enum):
    """Event severity levels."""
    LOW = 0
    MEDIUM = 1
    HIGH = 2
    CRITICAL = 3


@dataclass
class AuditEvent:
    """Audit event for blockchain logging."""
    event_type: EventType
    severity: Severity
    source: str
    description: str
    data_hash: str
    timestamp: float
    metadata: Dict[str, Any]
    user_id: Optional[str] = None
    session_id: Optional[str] = None


@dataclass
class BlockchainTransaction:
    """Blockchain transaction result."""
    tx_hash: str
    block_number: int
    gas_used: int
    event_id: int
    timestamp: float
    confirmed: bool


class BlockchainAuditor:
    """
    Blockchain audit service that logs security and ethics events
    to an immutable blockchain audit trail.
    """
    
    def __init__(self, config: Dict[str, Any], audit_logger):
        self.config = config
        self.audit_logger = audit_logger
        
        # Web3 connection
        self.w3 = None
        self.contract = None
        self.account = None
        
        # Redis for caching and queuing
        self.redis_client = None
        
        # State
        self.is_initialized = False
        self.is_running = False
        self.pending_events = []
        self.confirmation_tasks = []
        
        # Metrics
        self.events_logged = 0
        self.events_confirmed = 0
        self.failed_transactions = 0
        
        # Configuration
        self.network_url = config.get('network_url', 'http://localhost:8545')
        self.contract_address = config.get('contract_address')
        self.private_key = config.get('private_key')
        self.gas_limit = config.get('gas_limit', 500000)
        self.gas_price_gwei = config.get('gas_price_gwei', 20)
        self.confirmation_blocks = config.get('confirmation_blocks', 3)
        self.batch_size = config.get('batch_size', 10)
        self.batch_timeout = config.get('batch_timeout', 60)
        
    async def initialize(self):
        """Initialize the blockchain auditor."""
        logger.info("Initializing blockchain auditor")
        
        try:
            # Initialize Web3 connection
            await self._initialize_web3()
            
            # Initialize smart contract
            await self._initialize_contract()
            
            # Initialize Redis connection
            await self._initialize_redis()
            
            # Load pending events from Redis
            await self._load_pending_events()
            
            self.is_initialized = True
            logger.info("Blockchain auditor initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize blockchain auditor: {e}")
            raise
    
    async def _initialize_web3(self):
        """Initialize Web3 connection."""
        logger.info(f"Connecting to blockchain network: {self.network_url}")
        
        # Create Web3 instance
        self.w3 = Web3(Web3.HTTPProvider(self.network_url))
        
        # Add PoA middleware if needed
        if self.config.get('poa_network', False):
            self.w3.middleware_onion.inject(geth_poa_middleware, layer=0)
        
        # Check connection
        if not self.w3.is_connected():
            raise ConnectionError("Failed to connect to blockchain network")
        
        # Initialize account
        if not self.private_key:
            raise ValueError("Private key not configured")
        
        self.account = Account.from_key(self.private_key)
        logger.info(f"Using account: {self.account.address}")
        
        # Check account balance
        balance = self.w3.eth.get_balance(self.account.address)
        balance_eth = self.w3.from_wei(balance, 'ether')
        logger.info(f"Account balance: {balance_eth} ETH")
        
        if balance_eth < 0.01:  # Minimum balance check
            logger.warning("Low account balance, transactions may fail")
    
    async def _initialize_contract(self):
        """Initialize smart contract interface."""
        if not self.contract_address:
            raise ValueError("Contract address not configured")
        
        # Load contract ABI
        contract_abi = await self._load_contract_abi()
        
        # Create contract instance
        self.contract = self.w3.eth.contract(
            address=Web3.to_checksum_address(self.contract_address),
            abi=contract_abi
        )
        
        # Verify contract is accessible
        try:
            current_event_id = self.contract.functions.getCurrentEventId().call()
            logger.info(f"Connected to audit contract, current event ID: {current_event_id}")
        except Exception as e:
            raise ConnectionError(f"Failed to access audit contract: {e}")
    
    async def _initialize_redis(self):
        """Initialize Redis connection for caching and queuing."""
        redis_config = self.config.get('redis', {})
        
        self.redis_client = redis.Redis(
            host=redis_config.get('host', 'localhost'),
            port=redis_config.get('port', 6379),
            password=redis_config.get('password'),
            decode_responses=True
        )
        
        # Test connection
        self.redis_client.ping()
        logger.info("Connected to Redis for blockchain audit caching")
    
    async def log_audit_event(self, event: AuditEvent) -> str:
        """
        Log an audit event to the blockchain.
        
        Args:
            event: The audit event to log
            
        Returns:
            Transaction hash of the blockchain transaction
        """
        if not self.is_initialized:
            raise RuntimeError("Blockchain auditor not initialized")
        
        try:
            # Prepare event data
            event_data = await self._prepare_event_data(event)
            
            # Create signature
            signature = await self._sign_event_data(event_data)
            
            # Submit transaction
            tx_hash = await self._submit_transaction(
                event.event_type,
                event.severity,
                event.source,
                event.description,
                event_data['data_hash'],
                signature
            )
            
            # Store pending transaction
            await self._store_pending_transaction(tx_hash, event, event_data)
            
            # Start confirmation monitoring
            asyncio.create_task(self._monitor_transaction_confirmation(tx_hash))
            
            self.events_logged += 1
            logger.info(f"Audit event logged to blockchain: {tx_hash}")
            
            return tx_hash
            
        except Exception as e:
            self.failed_transactions += 1
            logger.error(f"Failed to log audit event to blockchain: {e}")
            raise
    
    async def _prepare_event_data(self, event: AuditEvent) -> Dict[str, Any]:
        """Prepare event data for blockchain logging."""
        # Create data hash
        event_content = {
            'event_type': event.event_type.value,
            'severity': event.severity.value,
            'source': event.source,
            'description': event.description,
            'timestamp': event.timestamp,
            'metadata': event.metadata,
            'user_id': event.user_id,
            'session_id': event.session_id
        }
        
        content_json = json.dumps(event_content, sort_keys=True)
        data_hash = hashlib.sha256(content_json.encode()).hexdigest()
        
        return {
            'content': event_content,
            'content_json': content_json,
            'data_hash': f"0x{data_hash}"
        }
    
    async def _sign_event_data(self, event_data: Dict[str, Any]) -> bytes:
        """Create digital signature for event data."""
        # Create message hash
        message_hash = Web3.keccak(text=event_data['content_json'])
        
        # Sign message
        message = encode_defunct(message_hash)
        signed_message = Account.sign_message(message, private_key=self.private_key)
        
        return signed_message.signature
    
    async def _submit_transaction(
        self,
        event_type: EventType,
        severity: Severity,
        source: str,
        description: str,
        data_hash: str,
        signature: bytes
    ) -> str:
        """Submit transaction to blockchain."""
        # Get current nonce
        nonce = self.w3.eth.get_transaction_count(self.account.address)
        
        # Build transaction
        transaction = self.contract.functions.logAuditEvent(
            event_type.value,
            severity.value,
            source,
            description,
            data_hash,
            signature
        ).build_transaction({
            'from': self.account.address,
            'nonce': nonce,
            'gas': self.gas_limit,
            'gasPrice': self.w3.to_wei(self.gas_price_gwei, 'gwei'),
        })
        
        # Sign transaction
        signed_txn = self.w3.eth.account.sign_transaction(transaction, private_key=self.private_key)
        
        # Send transaction
        tx_hash = self.w3.eth.send_raw_transaction(signed_txn.rawTransaction)
        
        return tx_hash.hex()
    
    async def _store_pending_transaction(
        self,
        tx_hash: str,
        event: AuditEvent,
        event_data: Dict[str, Any]
    ):
        """Store pending transaction for confirmation monitoring."""
        pending_data = {
            'tx_hash': tx_hash,
            'event': asdict(event),
            'event_data': event_data,
            'timestamp': time.time(),
            'confirmed': False,
            'confirmation_count': 0
        }
        
        # Store in Redis
        self.redis_client.hset(
            "blockchain_pending_transactions",
            tx_hash,
            json.dumps(pending_data, default=str)
        )
        
        # Add to pending list
        self.pending_events.append(pending_data)
    
    async def _monitor_transaction_confirmation(self, tx_hash: str):
        """Monitor transaction confirmation."""
        try:
            # Wait for transaction receipt
            receipt = self.w3.eth.wait_for_transaction_receipt(tx_hash, timeout=300)
            
            if receipt.status == 1:  # Success
                # Get event ID from logs
                event_id = await self._extract_event_id_from_receipt(receipt)
                
                # Wait for confirmations
                await self._wait_for_confirmations(receipt.blockNumber)
                
                # Mark as confirmed
                await self._mark_transaction_confirmed(tx_hash, receipt, event_id)
                
                self.events_confirmed += 1
                logger.info(f"Transaction confirmed: {tx_hash}, Event ID: {event_id}")
                
            else:
                logger.error(f"Transaction failed: {tx_hash}")
                await self._mark_transaction_failed(tx_hash, "Transaction reverted")
                
        except Exception as e:
            logger.error(f"Error monitoring transaction {tx_hash}: {e}")
            await self._mark_transaction_failed(tx_hash, str(e))
    
    async def _extract_event_id_from_receipt(self, receipt) -> int:
        """Extract event ID from transaction receipt."""
        # Parse logs to find AuditEventLogged event
        for log in receipt.logs:
            try:
                decoded_log = self.contract.events.AuditEventLogged().processLog(log)
                return decoded_log.args.eventId
            except:
                continue
        
        raise ValueError("Could not extract event ID from transaction receipt")
    
    async def _wait_for_confirmations(self, block_number: int):
        """Wait for required number of confirmations."""
        while True:
            current_block = self.w3.eth.block_number
            confirmations = current_block - block_number
            
            if confirmations >= self.confirmation_blocks:
                break
            
            await asyncio.sleep(15)  # Wait 15 seconds between checks
    
    async def _mark_transaction_confirmed(
        self,
        tx_hash: str,
        receipt,
        event_id: int
    ):
        """Mark transaction as confirmed."""
        # Update Redis
        pending_data_json = self.redis_client.hget("blockchain_pending_transactions", tx_hash)
        if pending_data_json:
            pending_data = json.loads(pending_data_json)
            pending_data.update({
                'confirmed': True,
                'block_number': receipt.blockNumber,
                'gas_used': receipt.gasUsed,
                'event_id': event_id,
                'confirmation_timestamp': time.time()
            })
            
            # Move to confirmed transactions
            self.redis_client.hset(
                "blockchain_confirmed_transactions",
                tx_hash,
                json.dumps(pending_data, default=str)
            )
            
            # Remove from pending
            self.redis_client.hdel("blockchain_pending_transactions", tx_hash)
        
        # Remove from pending list
        self.pending_events = [
            event for event in self.pending_events 
            if event['tx_hash'] != tx_hash
        ]
    
    async def _mark_transaction_failed(self, tx_hash: str, error_message: str):
        """Mark transaction as failed."""
        # Update Redis
        pending_data_json = self.redis_client.hget("blockchain_pending_transactions", tx_hash)
        if pending_data_json:
            pending_data = json.loads(pending_data_json)
            pending_data.update({
                'failed': True,
                'error_message': error_message,
                'failure_timestamp': time.time()
            })
            
            # Move to failed transactions
            self.redis_client.hset(
                "blockchain_failed_transactions",
                tx_hash,
                json.dumps(pending_data, default=str)
            )
            
            # Remove from pending
            self.redis_client.hdel("blockchain_pending_transactions", tx_hash)
        
        # Remove from pending list
        self.pending_events = [
            event for event in self.pending_events 
            if event['tx_hash'] != tx_hash
        ]
        
        self.failed_transactions += 1
    
    async def get_audit_event_from_blockchain(self, event_id: int) -> Dict[str, Any]:
        """Retrieve audit event from blockchain by ID."""
        if not self.is_initialized:
            raise RuntimeError("Blockchain auditor not initialized")
        
        try:
            # Call smart contract
            event_data = self.contract.functions.getAuditEvent(event_id).call()
            
            # Parse event data
            return {
                'id': event_data[0],
                'timestamp': event_data[1],
                'event_type': EventType(event_data[2]).name,
                'severity': Severity(event_data[3]).name,
                'source': event_data[4],
                'description': event_data[5],
                'data_hash': event_data[6],
                'previous_hash': event_data[7],
                'reporter': event_data[8],
                'validated': event_data[9],
                'block_number': event_data[10],
                'signature': event_data[11].hex()
            }
            
        except Exception as e:
            logger.error(f"Failed to retrieve audit event {event_id}: {e}")
            raise
    
    async def verify_chain_integrity(self, from_event_id: int, to_event_id: int) -> bool:
        """Verify blockchain audit chain integrity."""
        if not self.is_initialized:
            raise RuntimeError("Blockchain auditor not initialized")
        
        try:
            # Call smart contract verification function
            is_valid = self.contract.functions.verifyChainIntegrity(
                from_event_id, 
                to_event_id
            ).call()
            
            logger.info(f"Chain integrity verification ({from_event_id}-{to_event_id}): {is_valid}")
            return is_valid
            
        except Exception as e:
            logger.error(f"Failed to verify chain integrity: {e}")
            return False
    
    async def get_audit_statistics(self) -> Dict[str, Any]:
        """Get audit statistics from blockchain."""
        if not self.is_initialized:
            raise RuntimeError("Blockchain auditor not initialized")
        
        try:
            # Call smart contract
            stats = self.contract.functions.getAuditStatistics().call()
            
            return {
                'total_events': stats[0],
                'validated_events': stats[1],
                'critical_events': stats[2],
                'last_event_timestamp': stats[3],
                'local_events_logged': self.events_logged,
                'local_events_confirmed': self.events_confirmed,
                'local_failed_transactions': self.failed_transactions,
                'pending_transactions': len(self.pending_events)
            }
            
        except Exception as e:
            logger.error(f"Failed to get audit statistics: {e}")
            return {}
    
    async def _load_contract_abi(self) -> List[Dict[str, Any]]:
        """Load contract ABI from file or configuration."""
        abi_path = self.config.get('contract_abi_path', 'contracts/AuditTrail.json')
        
        try:
            with open(abi_path, 'r') as f:
                contract_data = json.load(f)
                return contract_data.get('abi', contract_data)
        except FileNotFoundError:
            # Fallback to minimal ABI
            return [
                {
                    "inputs": [
                        {"type": "uint8", "name": "_eventType"},
                        {"type": "uint8", "name": "_severity"},
                        {"type": "string", "name": "_source"},
                        {"type": "string", "name": "_description"},
                        {"type": "bytes32", "name": "_dataHash"},
                        {"type": "bytes", "name": "_signature"}
                    ],
                    "name": "logAuditEvent",
                    "outputs": [],
                    "type": "function"
                },
                {
                    "inputs": [{"type": "uint256", "name": "_eventId"}],
                    "name": "getAuditEvent",
                    "outputs": [{"type": "tuple", "components": []}],
                    "type": "function"
                },
                {
                    "inputs": [],
                    "name": "getCurrentEventId",
                    "outputs": [{"type": "uint256"}],
                    "type": "function"
                }
            ]
    
    async def _load_pending_events(self):
        """Load pending events from Redis."""
        try:
            pending_txs = self.redis_client.hgetall("blockchain_pending_transactions")
            
            for tx_hash, data_json in pending_txs.items():
                pending_data = json.loads(data_json)
                
                # Check if transaction is old (older than 1 hour)
                if time.time() - pending_data['timestamp'] > 3600:
                    logger.warning(f"Old pending transaction found: {tx_hash}")
                    # Could implement retry logic here
                
                self.pending_events.append(pending_data)
            
            logger.info(f"Loaded {len(self.pending_events)} pending transactions")
            
        except Exception as e:
            logger.error(f"Failed to load pending events: {e}")
    
    async def shutdown(self):
        """Shutdown the blockchain auditor."""
        logger.info("Shutting down blockchain auditor")
        
        self.is_running = False
        
        # Cancel confirmation tasks
        for task in self.confirmation_tasks:
            task.cancel()
        
        # Wait for pending transactions (with timeout)
        if self.pending_events:
            logger.info(f"Waiting for {len(self.pending_events)} pending transactions...")
            await asyncio.sleep(30)  # Give some time for confirmations
        
        logger.info("Blockchain auditor shut down")
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get blockchain auditor metrics."""
        return {
            'events_logged': self.events_logged,
            'events_confirmed': self.events_confirmed,
            'failed_transactions': self.failed_transactions,
            'pending_transactions': len(self.pending_events),
            'is_initialized': self.is_initialized,
            'is_running': self.is_running
        }
