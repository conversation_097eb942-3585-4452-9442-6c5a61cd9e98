# ASI System - Security & Ethics Control Configuration
# ===================================================

# General security settings
general:
  environment: "development"  # development, staging, production
  debug: false
  log_level: "INFO"
  strict_mode: true
  fail_closed: true  # Fail securely when policies cannot be evaluated

# Policy enforcement configuration
policy_enforcement:
  enabled: true
  opa_server_url: "http://opa-server:8181"
  policy_bundle_url: "http://opa-server:8181/v1/data"
  decision_timeout_ms: 5000
  cache_ttl_seconds: 300
  
  # Policy evaluation modes
  modes:
    access_control: "enforce"      # enforce, monitor, disabled
    data_governance: "enforce"     # enforce, monitor, disabled
    ethics_compliance: "enforce"   # enforce, monitor, disabled
    resource_limits: "monitor"     # enforce, monitor, disabled
    security_baseline: "enforce"   # enforce, monitor, disabled
  
  # Enforcement actions
  actions:
    deny_access: true
    log_violation: true
    alert_security_team: true
    quarantine_resource: false
    terminate_session: false

# Anomaly detection configuration
anomaly_detection:
  enabled: true
  service_url: "http://anomaly-detector:8080"
  
  # Detection algorithms
  algorithms:
    isolation_forest:
      enabled: true
      contamination: 0.1
      n_estimators: 100
    
    lstm_autoencoder:
      enabled: true
      sequence_length: 50
      threshold: 0.95
    
    statistical_outlier:
      enabled: true
      z_score_threshold: 3.0
      iqr_multiplier: 1.5
  
  # Behavioral analysis
  behavioral_analysis:
    enabled: true
    user_behavior_profiling: true
    system_behavior_profiling: true
    api_usage_analysis: true
    data_access_patterns: true
    
    # Thresholds
    max_api_calls_per_minute: 1000
    max_data_access_per_hour: 10000
    unusual_time_access_threshold: 0.05
    geographic_anomaly_threshold: 0.1
  
  # Real-time monitoring
  real_time:
    enabled: true
    kafka_bootstrap_servers: "kafka:9092"
    kafka_topics:
      - "asi-security-events"
      - "asi-audit-logs"
      - "asi-metrics"
    processing_window_seconds: 60
    alert_threshold: 0.8

# Secure execution environment
secure_execution:
  enabled: true
  enclave_service_url: "http://security-enclave:9090"
  
  # Trusted Execution Environment
  tee:
    enabled: true
    type: "sgx"  # sgx, trustzone, sev
    attestation_required: true
    remote_attestation_url: "https://attestation-service.intel.com"
  
  # Code integrity
  code_integrity:
    enabled: true
    signature_verification: true
    hash_verification: true
    allowed_signers:
      - "asi-development-team"
      - "asi-security-team"
  
  # Isolation
  isolation:
    process_isolation: true
    memory_isolation: true
    network_isolation: true
    filesystem_isolation: true
    
    # Resource limits
    max_memory_mb: 2048
    max_cpu_percent: 50
    max_network_bandwidth_mbps: 100
    max_disk_io_mbps: 50

# Audit and compliance
audit:
  enabled: true
  
  # Audit logging
  logging:
    enabled: true
    log_level: "INFO"
    log_format: "json"
    log_destination: "file"  # file, syslog, kafka, blockchain
    log_file_path: "/var/log/asi-security/audit.log"
    log_rotation: true
    max_log_size_mb: 100
    max_log_files: 10
  
  # Blockchain audit trail
  blockchain:
    enabled: false  # Enable for production
    network: "ethereum"  # ethereum, hyperledger, polygon
    contract_address: "0x..."
    private_key_path: "/secrets/blockchain-key"
    gas_limit: 500000
    confirmation_blocks: 3
  
  # Compliance frameworks
  compliance:
    gdpr:
      enabled: true
      data_retention_days: 2555  # 7 years
      right_to_be_forgotten: true
      consent_tracking: true
    
    hipaa:
      enabled: false
      encryption_required: true
      access_logging: true
      minimum_necessary: true
    
    sox:
      enabled: false
      financial_controls: true
      audit_trail_integrity: true
      segregation_of_duties: true
    
    custom:
      enabled: true
      policies:
        - "asi-ethics-policy"
        - "asi-ai-governance"
        - "asi-data-protection"

# Threat detection and response
threat_detection:
  enabled: true
  
  # Threat intelligence
  threat_intel:
    enabled: true
    feeds:
      - name: "misp"
        url: "https://misp.asi-system.com"
        api_key: "${MISP_API_KEY}"
        update_interval_hours: 1
      
      - name: "otx"
        url: "https://otx.alienvault.com"
        api_key: "${OTX_API_KEY}"
        update_interval_hours: 6
    
    ioc_matching: true
    reputation_scoring: true
  
  # Intrusion detection
  intrusion_detection:
    enabled: true
    network_monitoring: true
    host_monitoring: true
    application_monitoring: true
    
    # Detection rules
    rules:
      - name: "suspicious_login_attempts"
        pattern: "failed_login_count > 5"
        severity: "medium"
        action: "alert"
      
      - name: "privilege_escalation"
        pattern: "role_change AND elevated_permissions"
        severity: "high"
        action: "block"
      
      - name: "data_exfiltration"
        pattern: "large_data_transfer AND external_destination"
        severity: "critical"
        action: "quarantine"
  
  # Incident response
  incident_response:
    enabled: true
    auto_response: true
    escalation_enabled: true
    
    # Response actions
    actions:
      alert:
        enabled: true
        channels: ["email", "slack", "webhook"]
      
      block:
        enabled: true
        duration_minutes: 60
        whitelist_override: true
      
      quarantine:
        enabled: true
        isolation_network: "quarantine-vlan"
        notification_required: true

# Authentication and authorization
auth:
  enabled: true
  
  # Authentication methods
  methods:
    jwt:
      enabled: true
      secret_key: "${JWT_SECRET_KEY}"
      expiration_hours: 24
      refresh_enabled: true
    
    oauth2:
      enabled: false
      provider: "auth0"
      client_id: "${OAUTH2_CLIENT_ID}"
      client_secret: "${OAUTH2_CLIENT_SECRET}"
    
    mtls:
      enabled: false
      ca_cert_path: "/certs/ca.crt"
      client_cert_required: true
  
  # Authorization
  authorization:
    rbac:
      enabled: true
      roles:
        - name: "admin"
          permissions: ["*"]
          description: "Full system access"
        
        - name: "security_analyst"
          permissions: ["security:read", "audit:read", "policies:read"]
          description: "Security monitoring and analysis"
        
        - name: "operator"
          permissions: ["system:read", "metrics:read"]
          description: "System monitoring and operations"
        
        - name: "developer"
          permissions: ["api:read", "logs:read"]
          description: "Development and debugging access"
    
    abac:
      enabled: true
      attributes:
        - "user.role"
        - "resource.classification"
        - "environment.security_level"
        - "time.business_hours"
        - "location.trusted_network"

# Encryption and cryptography
encryption:
  enabled: true
  
  # Data encryption
  data_at_rest:
    enabled: true
    algorithm: "AES-256-GCM"
    key_rotation_days: 90
    key_management: "vault"  # vault, kms, local
  
  data_in_transit:
    enabled: true
    tls_version: "1.3"
    cipher_suites:
      - "TLS_AES_256_GCM_SHA384"
      - "TLS_CHACHA20_POLY1305_SHA256"
    certificate_validation: true
  
  # Key management
  key_management:
    vault_url: "https://vault.asi-system.com"
    vault_token: "${VAULT_TOKEN}"
    key_rotation_enabled: true
    backup_enabled: true
    hsm_enabled: false

# Network security
network:
  enabled: true
  
  # Firewall rules
  firewall:
    enabled: true
    default_policy: "deny"
    rules:
      - name: "allow_internal_communication"
        source: "10.0.0.0/8"
        destination: "10.0.0.0/8"
        ports: ["80", "443", "8080-8090"]
        action: "allow"
      
      - name: "allow_external_api"
        source: "0.0.0.0/0"
        destination: "api-gateway"
        ports: ["443"]
        action: "allow"
      
      - name: "deny_all_others"
        source: "0.0.0.0/0"
        destination: "0.0.0.0/0"
        ports: ["*"]
        action: "deny"
  
  # DDoS protection
  ddos_protection:
    enabled: true
    rate_limiting: true
    connection_limiting: true
    geo_blocking: false
    
    # Rate limits
    requests_per_second: 1000
    connections_per_ip: 100
    burst_allowance: 200

# Monitoring and alerting
monitoring:
  enabled: true
  
  # Metrics collection
  metrics:
    enabled: true
    prometheus_url: "http://prometheus:9090"
    collection_interval_seconds: 15
    retention_days: 30
  
  # Alerting
  alerting:
    enabled: true
    alert_manager_url: "http://alertmanager:9093"
    
    # Alert channels
    channels:
      email:
        enabled: true
        smtp_server: "smtp.asi-system.com"
        recipients: ["<EMAIL>"]
      
      slack:
        enabled: true
        webhook_url: "${SLACK_WEBHOOK_URL}"
        channel: "#security-alerts"
      
      webhook:
        enabled: true
        url: "https://incident-response.asi-system.com/webhook"
        secret: "${WEBHOOK_SECRET}"
    
    # Alert rules
    rules:
      - name: "high_threat_detection_rate"
        expression: "rate(threats_detected_total[5m]) > 10"
        severity: "warning"
        duration: "2m"
      
      - name: "policy_violation_spike"
        expression: "rate(policy_violations_total[5m]) > 5"
        severity: "critical"
        duration: "1m"
      
      - name: "anomaly_detection_failure"
        expression: "up{job=\"anomaly-detector\"} == 0"
        severity: "critical"
        duration: "30s"

# Integration with ASI modules
integration:
  enabled: true
  
  # Module endpoints
  modules:
    ingestion:
      enabled: true
      endpoints:
        - "http://go-kafka-producer:8080"
        - "http://rust-kafka-consumer:8081"
        - "http://python-scrapy:8082"
    
    data_integration:
      enabled: true
      endpoints:
        - "http://scala-spark-pipeline:8085"
        - "http://java-protocol-service:8083"
        - "http://rust-device-integration:8084"
    
    learning_engine:
      enabled: true
      endpoints:
        - "http://python-training-engine:8090"
        - "http://cpp-inference-engine:8091"
        - "http://grpc-model-server:8092"
    
    decision_engine:
      enabled: true
      endpoints:
        - "http://python-rule-engine:8070"
        - "http://rust-decision-loop:8071"
        - "http://cpp-edge-processor:8072"
    
    self_improvement:
      enabled: true
      endpoints:
        - "http://python-model-retraining:8080"
        - "http://lisp-symbolic-refactor:8081"
        - "http://julia-performance-analytics:8082"
    
    ui_ux:
      enabled: true
      endpoints:
        - "http://react-dashboard:3000"
        - "http://streamlit-inspector:8501"
        - "http://websocket-server:8080"
    
    core_runtime:
      enabled: true
      endpoints:
        - "http://rust-control-kernel:9091"
        - "http://cpp-inference-accelerator:9092"
  
  # Monitoring hooks
  hooks:
    kafka_interceptor: true
    grpc_interceptor: true
    http_middleware: true
    log_aggregator: true
    metrics_collector: true
