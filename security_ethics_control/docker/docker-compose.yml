# ASI System - Security & Ethics Control Layer
# Docker Compose Configuration
# ============================================

version: '3.8'

networks:
  asi-security-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  opa-policies:
    driver: local
  opa-data:
    driver: local
  anomaly-models:
    driver: local
  security-enclave-data:
    driver: local
  postgres-data:
    driver: local
  redis-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local

services:
  # PostgreSQL Database for audit logs and security data
  postgres:
    image: postgres:15-alpine
    container_name: asi-security-postgres
    environment:
      POSTGRES_DB: asi_security
      POSTGRES_USER: asi_security
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-secure_password}
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256"
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "5432:5432"
    networks:
      - asi-security-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U asi_security -d asi_security"]
      interval: 30s
      timeout: 10s
      retries: 3
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp
      - /var/run/postgresql

  # Redis for caching and real-time data
  redis:
    image: redis:7-alpine
    container_name: asi-security-redis
    command: redis-server --requirepass ${REDIS_PASSWORD:-secure_password} --appendonly yes
    volumes:
      - redis-data:/data
    ports:
      - "6379:6379"
    networks:
      - asi-security-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp

  # Open Policy Agent (OPA) Policy Engine
  opa-policy-engine:
    build:
      context: ../opa-policy-engine
      dockerfile: Dockerfile
    container_name: asi-opa-policy-engine
    environment:
      OPA_LOG_LEVEL: info
      OPA_LOG_FORMAT: json
    volumes:
      - opa-policies:/app/policies:ro
      - opa-data:/app/data:ro
      - ../configs/security_config.yaml:/app/config/security_config.yaml:ro
    ports:
      - "8181:8181"
    networks:
      - asi-security-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8181/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp
      - /app/logs
    depends_on:
      - redis

  # Python Anomaly Detection Service
  anomaly-detection:
    build:
      context: ../python-anomaly-detection
      dockerfile: Dockerfile
    container_name: asi-anomaly-detection
    environment:
      DATABASE_URL: postgresql://asi_security:${POSTGRES_PASSWORD:-secure_password}@postgres:5432/asi_security
      REDIS_URL: redis://:${REDIS_PASSWORD:-secure_password}@redis:6379/0
      OPA_URL: http://opa-policy-engine:8181
      LOG_LEVEL: INFO
      PYTHONPATH: /app/src
    volumes:
      - anomaly-models:/app/models
      - ../configs/security_config.yaml:/app/config/security_config.yaml:ro
      - ../configs/ethics_policies.yaml:/app/config/ethics_policies.yaml:ro
    ports:
      - "8080:8080"
    networks:
      - asi-security-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp
      - /app/logs
    depends_on:
      - postgres
      - redis
      - opa-policy-engine

  # Rust Security Enclave
  security-enclave:
    build:
      context: ../rust-security-enclave
      dockerfile: Dockerfile
    container_name: asi-security-enclave
    environment:
      DATABASE_URL: postgresql://asi_security:${POSTGRES_PASSWORD:-secure_password}@postgres:5432/asi_security
      REDIS_URL: redis://:${REDIS_PASSWORD:-secure_password}@redis:6379/1
      RUST_LOG: info
      RUST_BACKTRACE: 1
    volumes:
      - security-enclave-data:/app/data
      - ../configs/security_config.yaml:/app/config/security_config.yaml:ro
    ports:
      - "9090:9090"
    networks:
      - asi-security-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9090/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp
      - /app/logs
    depends_on:
      - postgres
      - redis

  # Kafka Security Interceptor
  kafka-interceptor:
    build:
      context: ../monitoring-hooks/kafka-interceptors
      dockerfile: Dockerfile
    container_name: asi-kafka-interceptor
    environment:
      KAFKA_BOOTSTRAP_SERVERS: ${KAFKA_BOOTSTRAP_SERVERS:-kafka:9092}
      REDIS_URL: redis://:${REDIS_PASSWORD:-secure_password}@redis:6379/2
      OPA_URL: http://opa-policy-engine:8181
      ANOMALY_DETECTOR_URL: http://anomaly-detection:8080
      LOG_LEVEL: INFO
    volumes:
      - ../configs/security_config.yaml:/app/config/security_config.yaml:ro
    networks:
      - asi-security-network
    restart: unless-stopped
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp
      - /app/logs
    depends_on:
      - redis
      - opa-policy-engine
      - anomaly-detection

  # Prometheus for metrics collection
  prometheus:
    image: prom/prometheus:latest
    container_name: asi-security-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
    volumes:
      - ../monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - asi-security-network
    restart: unless-stopped
    security_opt:
      - no-new-privileges:true
    user: "65534:65534"

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    container_name: asi-security-grafana
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_ADMIN_PASSWORD:-admin}
      GF_USERS_ALLOW_SIGN_UP: false
      GF_SECURITY_DISABLE_GRAVATAR: true
      GF_ANALYTICS_REPORTING_ENABLED: false
      GF_ANALYTICS_CHECK_FOR_UPDATES: false
    volumes:
      - grafana-data:/var/lib/grafana
      - ../monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
      - ../monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
    ports:
      - "3000:3000"
    networks:
      - asi-security-network
    restart: unless-stopped
    security_opt:
      - no-new-privileges:true
    user: "472:472"
    depends_on:
      - prometheus

  # Jaeger for distributed tracing
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: asi-security-jaeger
    environment:
      COLLECTOR_OTLP_ENABLED: true
    ports:
      - "16686:16686"
      - "14268:14268"
    networks:
      - asi-security-network
    restart: unless-stopped
    security_opt:
      - no-new-privileges:true

  # Security Dashboard (React frontend)
  security-dashboard:
    build:
      context: ../ui/security-dashboard
      dockerfile: Dockerfile
    container_name: asi-security-dashboard
    environment:
      REACT_APP_API_URL: http://localhost:8080
      REACT_APP_OPA_URL: http://localhost:8181
      REACT_APP_GRAFANA_URL: http://localhost:3000
      REACT_APP_JAEGER_URL: http://localhost:16686
    ports:
      - "3001:3000"
    networks:
      - asi-security-network
    restart: unless-stopped
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp
    depends_on:
      - anomaly-detection
      - opa-policy-engine
      - grafana

  # Blockchain Audit Service (optional)
  blockchain-auditor:
    build:
      context: ../blockchain-audit/audit-service
      dockerfile: Dockerfile
    container_name: asi-blockchain-auditor
    environment:
      DATABASE_URL: postgresql://asi_security:${POSTGRES_PASSWORD:-secure_password}@postgres:5432/asi_security
      REDIS_URL: redis://:${REDIS_PASSWORD:-secure_password}@redis:6379/3
      BLOCKCHAIN_NETWORK_URL: ${BLOCKCHAIN_NETWORK_URL:-http://localhost:8545}
      BLOCKCHAIN_CONTRACT_ADDRESS: ${BLOCKCHAIN_CONTRACT_ADDRESS}
      BLOCKCHAIN_PRIVATE_KEY: ${BLOCKCHAIN_PRIVATE_KEY}
      LOG_LEVEL: INFO
    volumes:
      - ../configs/security_config.yaml:/app/config/security_config.yaml:ro
      - ../blockchain-audit/smart-contracts:/app/contracts:ro
    ports:
      - "8082:8080"
    networks:
      - asi-security-network
    restart: unless-stopped
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp
      - /app/logs
    depends_on:
      - postgres
      - redis
    profiles:
      - blockchain

  # Nginx reverse proxy for security services
  nginx:
    image: nginx:alpine
    container_name: asi-security-nginx
    volumes:
      - ../nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ../nginx/ssl:/etc/nginx/ssl:ro
    ports:
      - "443:443"
      - "80:80"
    networks:
      - asi-security-network
    restart: unless-stopped
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /var/cache/nginx
      - /var/run
    depends_on:
      - opa-policy-engine
      - anomaly-detection
      - security-enclave
      - security-dashboard

# Health check for the entire stack
  healthcheck:
    image: curlimages/curl:latest
    container_name: asi-security-healthcheck
    command: |
      sh -c '
        echo "Checking ASI Security Services Health..."
        curl -f http://opa-policy-engine:8181/health || exit 1
        curl -f http://anomaly-detection:8080/health || exit 1
        curl -f http://security-enclave:9090/health || exit 1
        curl -f http://prometheus:9090/-/healthy || exit 1
        curl -f http://grafana:3000/api/health || exit 1
        echo "All services are healthy!"
      '
    networks:
      - asi-security-network
    depends_on:
      - opa-policy-engine
      - anomaly-detection
      - security-enclave
      - prometheus
      - grafana
    profiles:
      - healthcheck
