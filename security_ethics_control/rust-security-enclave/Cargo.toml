[package]
name = "asi-security-enclave"
version = "1.0.0"
edition = "2021"
authors = ["ASI Security Team <<EMAIL>>"]
description = "Secure execution environment and audit system for ASI"
license = "MIT"
repository = "https://github.com/asi-system/security-ethics-control"
keywords = ["security", "enclave", "audit", "tee", "asi"]
categories = ["cryptography", "security", "audit"]

[dependencies]
# Async runtime
tokio = { version = "1.35", features = ["full"] }
tokio-util = "0.7"

# gRPC and networking
tonic = "0.10"
tonic-build = "0.10"
prost = "0.12"
tower = "0.4"
tower-http = { version = "0.5", features = ["cors", "trace"] }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
bincode = "1.3"

# Cryptography
ring = "0.17"
rustls = "0.21"
rustls-pemfile = "1.0"
x509-parser = "0.15"
sha2 = "0.10"
aes-gcm = "0.10"
chacha20poly1305 = "0.10"
ed25519-dalek = { version = "2.0", features = ["rand_core"] }
rand = "0.8"
rand_core = "0.6"

# TEE and SGX support
sgx-isa = "2.0"
sgx-types = "2.0"
intel-sgx-sdk = { version = "2.0", optional = true }

# Database and storage
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "postgres", "chrono", "uuid"] }
redis = { version = "0.24", features = ["tokio-comp", "connection-manager"] }

# Blockchain integration
web3 = { version = "0.19", optional = true }
ethereum-types = { version = "0.14", optional = true }
secp256k1 = { version = "0.28", optional = true }

# Logging and observability
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }
tracing-opentelemetry = "0.22"
opentelemetry = "0.21"
opentelemetry-jaeger = "0.20"
metrics = "0.22"
metrics-exporter-prometheus = "0.13"

# Configuration
config = "0.13"
clap = { version = "4.4", features = ["derive"] }

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Time and UUID
chrono = { version = "0.4", features = ["serde"] }
uuid = { version = "1.6", features = ["v4", "serde"] }

# HTTP client
reqwest = { version = "0.11", features = ["json", "rustls-tls"] }

# Utilities
bytes = "1.5"
base64 = "0.21"
hex = "0.4"
once_cell = "1.19"
parking_lot = "0.12"

# Memory protection
zeroize = { version = "1.7", features = ["zeroize_derive"] }
secrecy = { version = "0.8", features = ["serde"] }

# Hardware security module support
pkcs11 = { version = "0.8", optional = true }

# Attestation
dcap-qvl = { version = "0.4", optional = true }

[build-dependencies]
tonic-build = "0.10"

[features]
default = ["sgx", "blockchain", "hsm"]
sgx = ["intel-sgx-sdk", "dcap-qvl"]
blockchain = ["web3", "ethereum-types", "secp256k1"]
hsm = ["pkcs11"]

[dev-dependencies]
tempfile = "3.8"
criterion = "0.5"

[[bin]]
name = "asi-security-enclave"
path = "src/main.rs"

[[bench]]
name = "crypto_bench"
harness = false

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"

[profile.dev]
opt-level = 0
debug = true
overflow-checks = true

# Security-focused profile
[profile.security]
inherits = "release"
opt-level = "s"  # Optimize for size to reduce attack surface
debug = false
strip = true
panic = "abort"
overflow-checks = true
