//! ASI System - Security Enclave Service
//! =====================================
//!
//! Secure execution environment providing trusted computing, audit logging,
//! and cryptographic operations for the ASI system. This service implements
//! a Trusted Execution Environment (TEE) with support for Intel SGX,
//! ARM TrustZone, and software-based secure enclaves.

use anyhow::Result;
use clap::Parser;
use std::net::SocketAddr;
use std::sync::Arc;
use tokio::signal;
use tracing::{info, error, warn};
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

mod config;
mod enclave;
mod audit;
mod crypto;
mod attestation;
mod api;
mod database;
mod blockchain;
mod metrics;
mod error;

use config::Config;
use enclave::SecureEnclave;
use audit::AuditLogger;
use crypto::CryptoManager;
use attestation::AttestationService;
use api::grpc_server::GrpcServer;
use database::DatabaseManager;
use blockchain::BlockchainAuditor;
use metrics::MetricsCollector;

#[derive(Parser)]
#[command(name = "asi-security-enclave")]
#[command(about = "ASI System Security Enclave Service")]
struct Args {
    /// Configuration file path
    #[arg(short, long, default_value = "config/security-enclave.toml")]
    config: String,

    /// Log level
    #[arg(short, long, default_value = "info")]
    log_level: String,

    /// Bind address
    #[arg(short, long, default_value = "0.0.0.0:9090")]
    bind: SocketAddr,

    /// Enable SGX enclave
    #[arg(long)]
    enable_sgx: bool,

    /// Enable blockchain audit
    #[arg(long)]
    enable_blockchain: bool,

    /// Enable HSM integration
    #[arg(long)]
    enable_hsm: bool,
}

/// Main application state
pub struct AppState {
    pub config: Arc<Config>,
    pub enclave: Arc<SecureEnclave>,
    pub audit_logger: Arc<AuditLogger>,
    pub crypto_manager: Arc<CryptoManager>,
    pub attestation_service: Arc<AttestationService>,
    pub database: Arc<DatabaseManager>,
    pub blockchain_auditor: Option<Arc<BlockchainAuditor>>,
    pub metrics: Arc<MetricsCollector>,
}

impl AppState {
    /// Create new application state
    pub async fn new(config: Config) -> Result<Self> {
        let config = Arc::new(config);
        
        // Initialize metrics collector
        let metrics = Arc::new(MetricsCollector::new()?);
        
        // Initialize database manager
        let database = Arc::new(DatabaseManager::new(&config.database).await?);
        
        // Initialize crypto manager
        let crypto_manager = Arc::new(CryptoManager::new(&config.crypto).await?);
        
        // Initialize audit logger
        let audit_logger = Arc::new(
            AuditLogger::new(&config.audit, database.clone(), crypto_manager.clone()).await?
        );
        
        // Initialize secure enclave
        let enclave = Arc::new(
            SecureEnclave::new(&config.enclave, crypto_manager.clone(), audit_logger.clone()).await?
        );
        
        // Initialize attestation service
        let attestation_service = Arc::new(
            AttestationService::new(&config.attestation, enclave.clone()).await?
        );
        
        // Initialize blockchain auditor (optional)
        let blockchain_auditor = if config.blockchain.enabled {
            Some(Arc::new(
                BlockchainAuditor::new(&config.blockchain, audit_logger.clone()).await?
            ))
        } else {
            None
        };
        
        Ok(Self {
            config,
            enclave,
            audit_logger,
            crypto_manager,
            attestation_service,
            database,
            blockchain_auditor,
            metrics,
        })
    }
    
    /// Initialize all services
    pub async fn initialize(&self) -> Result<()> {
        info!("Initializing ASI Security Enclave services");
        
        // Initialize database schema
        self.database.initialize_schema().await?;
        
        // Initialize crypto manager
        self.crypto_manager.initialize().await?;
        
        // Initialize audit logger
        self.audit_logger.initialize().await?;
        
        // Initialize secure enclave
        self.enclave.initialize().await?;
        
        // Initialize attestation service
        self.attestation_service.initialize().await?;
        
        // Initialize blockchain auditor if enabled
        if let Some(blockchain_auditor) = &self.blockchain_auditor {
            blockchain_auditor.initialize().await?;
        }
        
        // Start metrics collection
        self.metrics.start_collection().await?;
        
        info!("All services initialized successfully");
        Ok(())
    }
    
    /// Shutdown all services gracefully
    pub async fn shutdown(&self) -> Result<()> {
        info!("Shutting down ASI Security Enclave services");
        
        // Stop metrics collection
        self.metrics.stop_collection().await?;
        
        // Shutdown blockchain auditor if enabled
        if let Some(blockchain_auditor) = &self.blockchain_auditor {
            blockchain_auditor.shutdown().await?;
        }
        
        // Shutdown attestation service
        self.attestation_service.shutdown().await?;
        
        // Shutdown secure enclave
        self.enclave.shutdown().await?;
        
        // Shutdown audit logger
        self.audit_logger.shutdown().await?;
        
        // Shutdown crypto manager
        self.crypto_manager.shutdown().await?;
        
        // Shutdown database
        self.database.shutdown().await?;
        
        info!("All services shut down successfully");
        Ok(())
    }
}

#[tokio::main]
async fn main() -> Result<()> {
    // Parse command line arguments
    let args = Args::parse();
    
    // Initialize tracing
    init_tracing(&args.log_level)?;
    
    info!("Starting ASI Security Enclave Service");
    info!("Version: {}", env!("CARGO_PKG_VERSION"));
    info!("Bind address: {}", args.bind);
    
    // Load configuration
    let config = Config::load(&args.config).await?;
    info!("Configuration loaded from: {}", args.config);
    
    // Create application state
    let app_state = AppState::new(config).await?;
    
    // Initialize services
    app_state.initialize().await?;
    
    // Create gRPC server
    let grpc_server = GrpcServer::new(app_state.clone());
    
    // Start the server
    let server_handle = tokio::spawn(async move {
        if let Err(e) = grpc_server.serve(args.bind).await {
            error!("gRPC server error: {}", e);
        }
    });
    
    // Wait for shutdown signal
    tokio::select! {
        _ = signal::ctrl_c() => {
            info!("Received SIGINT, shutting down gracefully");
        }
        _ = async {
            let mut sigterm = signal::unix::signal(signal::unix::SignalKind::terminate())?;
            sigterm.recv().await;
            Ok::<(), std::io::Error>(())
        } => {
            info!("Received SIGTERM, shutting down gracefully");
        }
        result = server_handle => {
            match result {
                Ok(_) => info!("gRPC server completed"),
                Err(e) => error!("gRPC server task failed: {}", e),
            }
        }
    }
    
    // Shutdown services
    app_state.shutdown().await?;
    
    info!("ASI Security Enclave Service shut down successfully");
    Ok(())
}

/// Initialize tracing/logging
fn init_tracing(log_level: &str) -> Result<()> {
    let env_filter = tracing_subscriber::EnvFilter::try_from_default_env()
        .unwrap_or_else(|_| tracing_subscriber::EnvFilter::new(log_level));
    
    let formatting_layer = tracing_subscriber::fmt::layer()
        .with_target(true)
        .with_thread_ids(true)
        .with_file(true)
        .with_line_number(true)
        .json();
    
    // Initialize Jaeger tracer for distributed tracing
    let tracer = opentelemetry_jaeger::new_agent_pipeline()
        .with_service_name("asi-security-enclave")
        .install_simple()?;
    
    let telemetry_layer = tracing_opentelemetry::layer().with_tracer(tracer);
    
    tracing_subscriber::registry()
        .with(env_filter)
        .with(formatting_layer)
        .with(telemetry_layer)
        .init();
    
    Ok(())
}
