# ASI System - Security & Ethics Control Layer Makefile
# =====================================================

# Color codes for output
RED := \033[0;31m
GREEN := \033[0;32m
YELLOW := \033[0;33m
BLUE := \033[0;34m
PURPLE := \033[0;35m
CYAN := \033[0;36m
WHITE := \033[0;37m
RESET := \033[0m

# Configuration
PROJECT_NAME := asi-security-ethics
CONTAINER_REGISTRY ?= asi-registry.com
IMAGE_TAG ?= latest
NAMESPACE ?= asi-security
KUBECONFIG ?= ~/.kube/config

# Directories
OPA_DIR := opa-policy-engine
PYTHON_DIR := python-anomaly-detection
RUST_DIR := rust-security-enclave
BLOCKCHAIN_DIR := blockchain-audit
MONITORING_DIR := monitoring-hooks
K8S_DIR := k8s
DOCKER_DIR := docker

# Default target
.DEFAULT_GOAL := help

# Help target
.PHONY: help
help: ## Show this help message
	@echo "$(CYAN)ASI System - Security & Ethics Control Layer$(RESET)"
	@echo "$(CYAN)=============================================$(RESET)"
	@echo ""
	@echo "$(YELLOW)Available targets:$(RESET)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(GREEN)%-20s$(RESET) %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""

# Prerequisites check
.PHONY: check-deps
check-deps: ## Check required dependencies
	@echo "$(BLUE)Checking dependencies...$(RESET)"
	@command -v docker >/dev/null 2>&1 || { echo "$(RED)Docker is required but not installed$(RESET)"; exit 1; }
	@command -v docker-compose >/dev/null 2>&1 || { echo "$(RED)Docker Compose is required but not installed$(RESET)"; exit 1; }
	@command -v kubectl >/dev/null 2>&1 || { echo "$(RED)kubectl is required but not installed$(RESET)"; exit 1; }
	@command -v opa >/dev/null 2>&1 || { echo "$(RED)OPA is required but not installed$(RESET)"; exit 1; }
	@command -v rust >/dev/null 2>&1 || { echo "$(RED)Rust is required but not installed$(RESET)"; exit 1; }
	@command -v python3 >/dev/null 2>&1 || { echo "$(RED)Python 3 is required but not installed$(RESET)"; exit 1; }
	@echo "$(GREEN)All dependencies are installed$(RESET)"

# Build targets
.PHONY: build-all
build-all: build-opa build-python build-rust ## Build all components
	@echo "$(GREEN)All components built successfully$(RESET)"

.PHONY: build-opa
build-opa: ## Build OPA policy engine
	@echo "$(BLUE)Building OPA policy engine...$(RESET)"
	@cd $(OPA_DIR) && docker build -t $(CONTAINER_REGISTRY)/opa-policy-engine:$(IMAGE_TAG) .
	@echo "$(GREEN)OPA policy engine built$(RESET)"

.PHONY: build-python
build-python: ## Build Python anomaly detection service
	@echo "$(BLUE)Building Python anomaly detection service...$(RESET)"
	@cd $(PYTHON_DIR) && docker build -t $(CONTAINER_REGISTRY)/anomaly-detection:$(IMAGE_TAG) .
	@echo "$(GREEN)Python anomaly detection service built$(RESET)"

.PHONY: build-rust
build-rust: ## Build Rust security enclave
	@echo "$(BLUE)Building Rust security enclave...$(RESET)"
	@cd $(RUST_DIR) && docker build -t $(CONTAINER_REGISTRY)/security-enclave:$(IMAGE_TAG) .
	@echo "$(GREEN)Rust security enclave built$(RESET)"

# Development environment
.PHONY: dev-start
dev-start: check-deps ## Start development environment
	@echo "$(BLUE)Starting development environment...$(RESET)"
	@docker-compose -f $(DOCKER_DIR)/docker-compose.yml up -d
	@echo "$(GREEN)Development environment started$(RESET)"
	@echo "$(YELLOW)Access points:$(RESET)"
	@echo "  - OPA Console: http://localhost:8181"
	@echo "  - Anomaly Detection API: http://localhost:8080"
	@echo "  - Security Enclave API: http://localhost:9090"
	@echo "  - Security Dashboard: http://localhost:3001/security"

.PHONY: dev-stop
dev-stop: ## Stop development environment
	@echo "$(BLUE)Stopping development environment...$(RESET)"
	@docker-compose -f $(DOCKER_DIR)/docker-compose.yml down
	@echo "$(GREEN)Development environment stopped$(RESET)"

.PHONY: dev-restart
dev-restart: dev-stop dev-start ## Restart development environment

.PHONY: dev-logs
dev-logs: ## Show logs from development environment
	@docker-compose -f $(DOCKER_DIR)/docker-compose.yml logs -f

.PHONY: dev-status
dev-status: ## Show status of development services
	@docker-compose -f $(DOCKER_DIR)/docker-compose.yml ps

# Policy management
.PHONY: test-policies
test-policies: ## Test OPA policies
	@echo "$(BLUE)Testing OPA policies...$(RESET)"
	@cd $(OPA_DIR) && opa test policies/
	@echo "$(GREEN)All policies passed tests$(RESET)"

.PHONY: validate-policies
validate-policies: ## Validate OPA policies syntax
	@echo "$(BLUE)Validating OPA policies...$(RESET)"
	@cd $(OPA_DIR) && find policies/ -name "*.rego" -exec opa fmt --diff {} \;
	@echo "$(GREEN)All policies are valid$(RESET)"

.PHONY: generate-policies
generate-policies: ## Generate policy documentation
	@echo "$(BLUE)Generating policy documentation...$(RESET)"
	@./scripts/generate-policies.sh
	@echo "$(GREEN)Policy documentation generated$(RESET)"

# Security testing
.PHONY: test-security
test-security: ## Run security test suite
	@echo "$(BLUE)Running security tests...$(RESET)"
	@python3 -m pytest tests/security_tests.py -v
	@echo "$(GREEN)Security tests completed$(RESET)"

.PHONY: test-ethics
test-ethics: ## Run ethics compliance tests
	@echo "$(BLUE)Running ethics compliance tests...$(RESET)"
	@python3 -m pytest tests/ethics_tests.py -v
	@echo "$(GREEN)Ethics compliance tests completed$(RESET)"

.PHONY: test-integration
test-integration: ## Run integration tests
	@echo "$(BLUE)Running integration tests...$(RESET)"
	@python3 -m pytest tests/integration_test.py -v
	@echo "$(GREEN)Integration tests completed$(RESET)"

.PHONY: test-all
test-all: test-policies test-security test-ethics test-integration ## Run all tests

# Security scanning
.PHONY: vuln-scan
vuln-scan: ## Run vulnerability scanning
	@echo "$(BLUE)Running vulnerability scan...$(RESET)"
	@docker run --rm -v $(PWD):/app -w /app aquasec/trivy fs .
	@echo "$(GREEN)Vulnerability scan completed$(RESET)"

.PHONY: pentest
pentest: ## Run penetration testing
	@echo "$(BLUE)Running penetration tests...$(RESET)"
	@./scripts/pentest.sh
	@echo "$(GREEN)Penetration testing completed$(RESET)"

# Kubernetes deployment
.PHONY: k8s-deploy
k8s-deploy: ## Deploy to Kubernetes
	@echo "$(BLUE)Deploying to Kubernetes...$(RESET)"
	@kubectl apply -f $(K8S_DIR)/namespace.yaml
	@kubectl apply -f $(K8S_DIR)/rbac.yaml
	@kubectl apply -f $(K8S_DIR)/network-policies.yaml
	@kubectl apply -f $(K8S_DIR)/pod-security-policies.yaml
	@kubectl apply -f $(K8S_DIR)/deployments/
	@echo "$(GREEN)Deployed to Kubernetes$(RESET)"

.PHONY: k8s-undeploy
k8s-undeploy: ## Remove deployment from Kubernetes
	@echo "$(BLUE)Removing deployment from Kubernetes...$(RESET)"
	@kubectl delete -f $(K8S_DIR)/deployments/ || true
	@kubectl delete -f $(K8S_DIR)/pod-security-policies.yaml || true
	@kubectl delete -f $(K8S_DIR)/network-policies.yaml || true
	@kubectl delete -f $(K8S_DIR)/rbac.yaml || true
	@kubectl delete -f $(K8S_DIR)/namespace.yaml || true
	@echo "$(GREEN)Deployment removed$(RESET)"

.PHONY: k8s-status
k8s-status: ## Show Kubernetes deployment status
	@echo "$(BLUE)Kubernetes deployment status:$(RESET)"
	@kubectl get pods,svc,ingress -n $(NAMESPACE)

.PHONY: k8s-logs
k8s-logs: ## Show logs from Kubernetes pods
	@echo "$(BLUE)Showing logs from all pods...$(RESET)"
	@kubectl logs -f -l app.kubernetes.io/part-of=$(PROJECT_NAME) -n $(NAMESPACE)

# Monitoring and auditing
.PHONY: security-audit
security-audit: ## Generate security audit report
	@echo "$(BLUE)Generating security audit report...$(RESET)"
	@./scripts/audit-report.sh
	@echo "$(GREEN)Security audit report generated$(RESET)"

.PHONY: compliance-check
compliance-check: ## Check compliance status
	@echo "$(BLUE)Checking compliance status...$(RESET)"
	@python3 -c "from python-anomaly-detection.src.compliance import check_compliance; check_compliance()"
	@echo "$(GREEN)Compliance check completed$(RESET)"

.PHONY: threat-intel
threat-intel: ## Update threat intelligence
	@echo "$(BLUE)Updating threat intelligence...$(RESET)"
	@python3 -c "from python-anomaly-detection.src.threat_intelligence import update_intel; update_intel()"
	@echo "$(GREEN)Threat intelligence updated$(RESET)"

# Blockchain operations (optional)
.PHONY: blockchain-deploy
blockchain-deploy: ## Deploy blockchain audit contracts
	@echo "$(BLUE)Deploying blockchain audit contracts...$(RESET)"
	@cd $(BLOCKCHAIN_DIR) && ./scripts/deploy-contracts.sh
	@echo "$(GREEN)Blockchain contracts deployed$(RESET)"

.PHONY: blockchain-audit
blockchain-audit: ## Query blockchain audit trail
	@echo "$(BLUE)Querying blockchain audit trail...$(RESET)"
	@cd $(BLOCKCHAIN_DIR) && ./scripts/query-audit.sh
	@echo "$(GREEN)Blockchain audit query completed$(RESET)"

# Utility targets
.PHONY: clean
clean: ## Clean up resources
	@echo "$(BLUE)Cleaning up resources...$(RESET)"
	@docker system prune -f
	@docker volume prune -f
	@echo "$(GREEN)Cleanup completed$(RESET)"

.PHONY: setup
setup: ## Setup security environment
	@echo "$(BLUE)Setting up security environment...$(RESET)"
	@./scripts/setup-security.sh
	@echo "$(GREEN)Security environment setup completed$(RESET)"

.PHONY: status
status: ## Show overall system status
	@echo "$(BLUE)Security System Status:$(RESET)"
	@echo "$(YELLOW)Docker Compose Services:$(RESET)"
	@docker-compose -f $(DOCKER_DIR)/docker-compose.yml ps 2>/dev/null || echo "  No services running"
	@echo ""
	@echo "$(YELLOW)Kubernetes Pods:$(RESET)"
	@kubectl get pods -n $(NAMESPACE) 2>/dev/null || echo "  No pods found"
	@echo ""
	@echo "$(YELLOW)OPA Policies:$(RESET)"
	@find $(OPA_DIR)/policies -name "*.rego" | wc -l | xargs echo "  Policies loaded:"
	@echo ""

.PHONY: logs
logs: ## Show logs from all services
	@echo "$(BLUE)Showing logs from all services...$(RESET)"
	@if docker-compose -f $(DOCKER_DIR)/docker-compose.yml ps -q | grep -q .; then \
		docker-compose -f $(DOCKER_DIR)/docker-compose.yml logs -f; \
	else \
		kubectl logs -f -l app.kubernetes.io/part-of=$(PROJECT_NAME) -n $(NAMESPACE) 2>/dev/null || echo "No services running"; \
	fi

# Environment setup
.PHONY: install-deps
install-deps: ## Install required dependencies
	@echo "$(BLUE)Installing dependencies...$(RESET)"
	@# Install OPA
	@curl -L -o opa https://openpolicyagent.org/downloads/v0.55.0/opa_linux_amd64_static
	@chmod 755 ./opa
	@sudo mv opa /usr/local/bin
	@# Install Python dependencies
	@cd $(PYTHON_DIR) && pip3 install -r requirements.txt
	@# Install Rust dependencies
	@cd $(RUST_DIR) && cargo build --release
	@echo "$(GREEN)Dependencies installed$(RESET)"

# Documentation
.PHONY: docs
docs: ## Generate documentation
	@echo "$(BLUE)Generating documentation...$(RESET)"
	@cd $(PYTHON_DIR) && python3 -m pydoc -w src/
	@cd $(RUST_DIR) && cargo doc --no-deps
	@echo "$(GREEN)Documentation generated$(RESET)"
