#!/usr/bin/env python3
"""
ASI System - Anomaly Detection Service
=====================================

Main entry point for the anomaly detection service that monitors
ASI system behavior and detects security threats and anomalies.
"""

import asyncio
import logging
import signal
import sys
from contextlib import asynccontextmanager
from typing import Dict, Any

import uvicorn
from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from prometheus_client import make_asgi_app
import structlog

from .config import Settings, get_settings
from .api import router as api_router
from .anomaly_detector import AnomalyDetectorService
from .behavioral_analysis import BehavioralAnalysisService
from .threat_intelligence import ThreatIntelligenceService
from .monitoring import setup_monitoring, MetricsCollector
from .kafka_consumer import KafkaConsumerService
from .redis_client import RedisClient
from .database import DatabaseManager
from .auth import AuthManager
from .utils.logging import setup_logging
from .utils.health import HealthChecker

# Setup structured logging
logger = structlog.get_logger(__name__)

# Global service instances
services: Dict[str, Any] = {}


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager for startup and shutdown."""
    # Startup
    logger.info("Starting ASI Anomaly Detection Service")
    
    try:
        # Initialize configuration
        settings = get_settings()
        
        # Setup monitoring
        setup_monitoring()
        
        # Initialize database
        db_manager = DatabaseManager(settings.database_url)
        await db_manager.initialize()
        services["database"] = db_manager
        
        # Initialize Redis client
        redis_client = RedisClient(settings.redis_url)
        await redis_client.initialize()
        services["redis"] = redis_client
        
        # Initialize authentication manager
        auth_manager = AuthManager(settings)
        services["auth"] = auth_manager
        
        # Initialize metrics collector
        metrics_collector = MetricsCollector()
        services["metrics"] = metrics_collector
        
        # Initialize threat intelligence service
        threat_intel = ThreatIntelligenceService(settings, redis_client)
        await threat_intel.initialize()
        services["threat_intel"] = threat_intel
        
        # Initialize behavioral analysis service
        behavioral_analysis = BehavioralAnalysisService(
            settings, db_manager, redis_client, metrics_collector
        )
        await behavioral_analysis.initialize()
        services["behavioral_analysis"] = behavioral_analysis
        
        # Initialize anomaly detector service
        anomaly_detector = AnomalyDetectorService(
            settings, db_manager, redis_client, behavioral_analysis, threat_intel
        )
        await anomaly_detector.initialize()
        services["anomaly_detector"] = anomaly_detector
        
        # Initialize Kafka consumer
        kafka_consumer = KafkaConsumerService(
            settings, anomaly_detector, behavioral_analysis, metrics_collector
        )
        await kafka_consumer.initialize()
        services["kafka_consumer"] = kafka_consumer
        
        # Initialize health checker
        health_checker = HealthChecker(services)
        services["health_checker"] = health_checker
        
        # Start background services
        await start_background_services()
        
        logger.info("ASI Anomaly Detection Service started successfully")
        
        yield
        
    except Exception as e:
        logger.error("Failed to start service", error=str(e))
        raise
    
    # Shutdown
    logger.info("Shutting down ASI Anomaly Detection Service")
    
    try:
        # Stop background services
        await stop_background_services()
        
        # Cleanup services
        for service_name, service in services.items():
            if hasattr(service, 'cleanup'):
                await service.cleanup()
                logger.info(f"Cleaned up {service_name}")
        
        logger.info("ASI Anomaly Detection Service shut down successfully")
        
    except Exception as e:
        logger.error("Error during shutdown", error=str(e))


async def start_background_services():
    """Start background services."""
    logger.info("Starting background services")
    
    # Start Kafka consumer
    kafka_consumer = services["kafka_consumer"]
    asyncio.create_task(kafka_consumer.start_consuming())
    
    # Start threat intelligence updates
    threat_intel = services["threat_intel"]
    asyncio.create_task(threat_intel.start_periodic_updates())
    
    # Start behavioral analysis
    behavioral_analysis = services["behavioral_analysis"]
    asyncio.create_task(behavioral_analysis.start_analysis())
    
    # Start anomaly detection
    anomaly_detector = services["anomaly_detector"]
    asyncio.create_task(anomaly_detector.start_detection())
    
    logger.info("Background services started")


async def stop_background_services():
    """Stop background services."""
    logger.info("Stopping background services")
    
    # Stop services gracefully
    for service_name in ["kafka_consumer", "threat_intel", "behavioral_analysis", "anomaly_detector"]:
        service = services.get(service_name)
        if service and hasattr(service, 'stop'):
            await service.stop()
            logger.info(f"Stopped {service_name}")


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    settings = get_settings()
    
    app = FastAPI(
        title="ASI Anomaly Detection Service",
        description="Security and behavioral anomaly detection for ASI System",
        version="1.0.0",
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None,
        lifespan=lifespan
    )
    
    # Add middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.allowed_origins,
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE"],
        allow_headers=["*"],
    )
    
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=settings.allowed_hosts
    )
    
    # Include API routes
    app.include_router(api_router, prefix="/api/v1")
    
    # Add Prometheus metrics endpoint
    metrics_app = make_asgi_app()
    app.mount("/metrics", metrics_app)
    
    # Health check endpoint
    @app.get("/health")
    async def health_check():
        """Health check endpoint."""
        health_checker = services.get("health_checker")
        if not health_checker:
            raise HTTPException(status_code=503, detail="Service not ready")
        
        health_status = await health_checker.check_health()
        
        if health_status["status"] == "healthy":
            return health_status
        else:
            raise HTTPException(status_code=503, detail=health_status)
    
    # Readiness check endpoint
    @app.get("/ready")
    async def readiness_check():
        """Readiness check endpoint."""
        # Check if all critical services are initialized
        required_services = ["database", "redis", "anomaly_detector", "behavioral_analysis"]
        
        for service_name in required_services:
            if service_name not in services:
                raise HTTPException(
                    status_code=503, 
                    detail=f"Service {service_name} not ready"
                )
        
        return {"status": "ready", "timestamp": asyncio.get_event_loop().time()}
    
    # Liveness check endpoint
    @app.get("/live")
    async def liveness_check():
        """Liveness check endpoint."""
        return {"status": "alive", "timestamp": asyncio.get_event_loop().time()}
    
    return app


def setup_signal_handlers():
    """Setup signal handlers for graceful shutdown."""
    def signal_handler(signum, frame):
        logger.info(f"Received signal {signum}, shutting down gracefully")
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)


def main():
    """Main entry point."""
    # Setup logging
    setup_logging()
    
    # Setup signal handlers
    setup_signal_handlers()
    
    # Get settings
    settings = get_settings()
    
    # Create application
    app = create_app()
    
    # Configure uvicorn
    uvicorn_config = {
        "app": app,
        "host": settings.host,
        "port": settings.port,
        "log_level": settings.log_level.lower(),
        "access_log": settings.debug,
        "reload": settings.debug,
        "workers": 1 if settings.debug else settings.workers,
    }
    
    # Add SSL configuration if enabled
    if settings.ssl_enabled:
        uvicorn_config.update({
            "ssl_keyfile": settings.ssl_keyfile,
            "ssl_certfile": settings.ssl_certfile,
        })
    
    logger.info(
        "Starting ASI Anomaly Detection Service",
        host=settings.host,
        port=settings.port,
        debug=settings.debug,
        workers=uvicorn_config["workers"]
    )
    
    # Run the application
    uvicorn.run(**uvicorn_config)


if __name__ == "__main__":
    main()
