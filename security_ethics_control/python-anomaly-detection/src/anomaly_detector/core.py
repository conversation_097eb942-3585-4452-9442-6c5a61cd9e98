"""
ASI System - Core Anomaly Detection Engine
==========================================

Core anomaly detection algorithms and orchestration for the ASI system.
Implements multiple detection methods including statistical, ML-based, and
deep learning approaches for comprehensive threat detection.
"""

import asyncio
import json
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import numpy as np
import pandas as pd
from sklearn.ensemble import IsolationForest
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import DBSCAN
import tensorflow as tf
import structlog

from ..config import Settings
from ..database import DatabaseManager
from ..redis_client import RedisClient
from ..behavioral_analysis import BehavioralAnalysisService
from ..threat_intelligence import ThreatIntelligenceService
from ..ml_models import (
    LSTMAutoencoder, 
    StatisticalAnomalyDetector,
    NetworkAnomalyDetector,
    UserBehaviorAnomalyDetector
)
from ..utils.metrics import AnomalyMetrics
from ..utils.alerts import AlertManager

logger = structlog.get_logger(__name__)


class AnomalySeverity(Enum):
    """Anomaly severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AnomalyType(Enum):
    """Types of anomalies that can be detected."""
    STATISTICAL = "statistical"
    BEHAVIORAL = "behavioral"
    NETWORK = "network"
    SECURITY = "security"
    PERFORMANCE = "performance"
    DATA_QUALITY = "data_quality"
    ETHICS_VIOLATION = "ethics_violation"


@dataclass
class AnomalyEvent:
    """Represents a detected anomaly event."""
    id: str
    timestamp: float
    type: AnomalyType
    severity: AnomalySeverity
    confidence: float
    description: str
    source: str
    affected_entities: List[str]
    raw_data: Dict[str, Any]
    context: Dict[str, Any]
    detection_method: str
    remediation_suggestions: List[str]
    false_positive_probability: float


class AnomalyDetectorService:
    """
    Main anomaly detection service that orchestrates multiple detection
    algorithms and provides unified anomaly detection for the ASI system.
    """
    
    def __init__(
        self,
        settings: Settings,
        db_manager: DatabaseManager,
        redis_client: RedisClient,
        behavioral_analysis: BehavioralAnalysisService,
        threat_intel: ThreatIntelligenceService
    ):
        self.settings = settings
        self.db_manager = db_manager
        self.redis_client = redis_client
        self.behavioral_analysis = behavioral_analysis
        self.threat_intel = threat_intel
        
        # Detection models
        self.isolation_forest = None
        self.lstm_autoencoder = None
        self.statistical_detector = None
        self.network_detector = None
        self.user_behavior_detector = None
        
        # Utilities
        self.scaler = StandardScaler()
        self.metrics = AnomalyMetrics()
        self.alert_manager = AlertManager(settings)
        
        # State
        self.is_running = False
        self.detection_tasks = []
        self.model_cache = {}
        
        # Configuration
        self.detection_config = settings.anomaly_detection
        self.thresholds = self._load_thresholds()
        
    async def initialize(self):
        """Initialize the anomaly detection service."""
        logger.info("Initializing anomaly detection service")
        
        try:
            # Initialize detection models
            await self._initialize_models()
            
            # Load historical data for training
            await self._load_training_data()
            
            # Train initial models
            await self._train_models()
            
            # Initialize alert manager
            await self.alert_manager.initialize()
            
            logger.info("Anomaly detection service initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize anomaly detection service", error=str(e))
            raise
    
    async def _initialize_models(self):
        """Initialize all detection models."""
        logger.info("Initializing detection models")
        
        # Isolation Forest for general anomaly detection
        self.isolation_forest = IsolationForest(
            contamination=self.detection_config.isolation_forest.contamination,
            n_estimators=self.detection_config.isolation_forest.n_estimators,
            random_state=42
        )
        
        # LSTM Autoencoder for sequence anomaly detection
        self.lstm_autoencoder = LSTMAutoencoder(
            sequence_length=self.detection_config.lstm_autoencoder.sequence_length,
            feature_dim=50,  # Will be adjusted based on actual data
            encoding_dim=20
        )
        
        # Statistical anomaly detector
        self.statistical_detector = StatisticalAnomalyDetector(
            z_score_threshold=self.detection_config.statistical_outlier.z_score_threshold,
            iqr_multiplier=self.detection_config.statistical_outlier.iqr_multiplier
        )
        
        # Network anomaly detector
        self.network_detector = NetworkAnomalyDetector()
        
        # User behavior anomaly detector
        self.user_behavior_detector = UserBehaviorAnomalyDetector()
        
        logger.info("Detection models initialized")
    
    async def _load_training_data(self):
        """Load historical data for model training."""
        logger.info("Loading training data")
        
        try:
            # Load data from the last 30 days
            end_time = time.time()
            start_time = end_time - (30 * 24 * 60 * 60)  # 30 days
            
            # Load different types of data
            training_data = {
                'metrics': await self._load_metrics_data(start_time, end_time),
                'logs': await self._load_log_data(start_time, end_time),
                'network': await self._load_network_data(start_time, end_time),
                'user_behavior': await self._load_user_behavior_data(start_time, end_time)
            }
            
            # Cache training data
            await self.redis_client.set(
                "anomaly_detection:training_data",
                json.dumps(training_data, default=str),
                ex=3600  # 1 hour cache
            )
            
            logger.info("Training data loaded successfully")
            return training_data
            
        except Exception as e:
            logger.error("Failed to load training data", error=str(e))
            raise
    
    async def _train_models(self):
        """Train all detection models with historical data."""
        logger.info("Training detection models")
        
        try:
            # Get training data
            training_data_json = await self.redis_client.get("anomaly_detection:training_data")
            if not training_data_json:
                logger.warning("No training data available, using default models")
                return
            
            training_data = json.loads(training_data_json)
            
            # Train Isolation Forest
            if training_data['metrics']:
                metrics_df = pd.DataFrame(training_data['metrics'])
                features = self._extract_features(metrics_df)
                if len(features) > 0:
                    features_scaled = self.scaler.fit_transform(features)
                    self.isolation_forest.fit(features_scaled)
                    logger.info("Isolation Forest trained")
            
            # Train LSTM Autoencoder
            if training_data['logs']:
                sequences = self._prepare_sequences(training_data['logs'])
                if len(sequences) > 0:
                    await self.lstm_autoencoder.train(sequences)
                    logger.info("LSTM Autoencoder trained")
            
            # Train other specialized detectors
            await self.network_detector.train(training_data['network'])
            await self.user_behavior_detector.train(training_data['user_behavior'])
            
            logger.info("All detection models trained successfully")
            
        except Exception as e:
            logger.error("Failed to train models", error=str(e))
            raise
    
    async def detect_anomalies(self, data: Dict[str, Any]) -> List[AnomalyEvent]:
        """
        Main anomaly detection method that runs all detection algorithms
        and returns a list of detected anomalies.
        """
        anomalies = []
        
        try:
            # Extract different types of data
            metrics_data = data.get('metrics', {})
            log_data = data.get('logs', {})
            network_data = data.get('network', {})
            user_data = data.get('user_behavior', {})
            
            # Run different detection methods
            detection_tasks = [
                self._detect_statistical_anomalies(metrics_data),
                self._detect_behavioral_anomalies(user_data),
                self._detect_network_anomalies(network_data),
                self._detect_sequence_anomalies(log_data),
                self._detect_security_anomalies(data),
            ]
            
            # Execute all detection methods concurrently
            results = await asyncio.gather(*detection_tasks, return_exceptions=True)
            
            # Collect all anomalies
            for result in results:
                if isinstance(result, Exception):
                    logger.error("Detection method failed", error=str(result))
                    continue
                
                if isinstance(result, list):
                    anomalies.extend(result)
            
            # Post-process anomalies
            anomalies = await self._post_process_anomalies(anomalies)
            
            # Update metrics
            self.metrics.update_detection_metrics(len(anomalies))
            
            # Log detection results
            logger.info(
                "Anomaly detection completed",
                total_anomalies=len(anomalies),
                critical=len([a for a in anomalies if a.severity == AnomalySeverity.CRITICAL]),
                high=len([a for a in anomalies if a.severity == AnomalySeverity.HIGH])
            )
            
            return anomalies
            
        except Exception as e:
            logger.error("Anomaly detection failed", error=str(e))
            self.metrics.increment_detection_errors()
            return []
    
    async def _detect_statistical_anomalies(self, data: Dict[str, Any]) -> List[AnomalyEvent]:
        """Detect statistical anomalies using Isolation Forest and statistical methods."""
        anomalies = []
        
        if not data:
            return anomalies
        
        try:
            # Convert data to DataFrame
            df = pd.DataFrame([data]) if isinstance(data, dict) else pd.DataFrame(data)
            
            # Extract numerical features
            features = self._extract_features(df)
            if len(features) == 0:
                return anomalies
            
            # Scale features
            features_scaled = self.scaler.transform(features)
            
            # Isolation Forest detection
            if self.isolation_forest:
                isolation_scores = self.isolation_forest.decision_function(features_scaled)
                isolation_predictions = self.isolation_forest.predict(features_scaled)
                
                for i, (score, prediction) in enumerate(zip(isolation_scores, isolation_predictions)):
                    if prediction == -1:  # Anomaly detected
                        confidence = abs(score)
                        severity = self._calculate_severity(confidence, "isolation_forest")
                        
                        anomaly = AnomalyEvent(
                            id=f"stat_{int(time.time())}_{i}",
                            timestamp=time.time(),
                            type=AnomalyType.STATISTICAL,
                            severity=severity,
                            confidence=confidence,
                            description=f"Statistical anomaly detected with isolation score {score:.3f}",
                            source="isolation_forest",
                            affected_entities=["system"],
                            raw_data=data,
                            context={"isolation_score": score, "feature_count": len(features[i])},
                            detection_method="isolation_forest",
                            remediation_suggestions=self._get_remediation_suggestions("statistical"),
                            false_positive_probability=self._estimate_false_positive_rate("statistical")
                        )
                        anomalies.append(anomaly)
            
            # Statistical outlier detection
            statistical_anomalies = await self.statistical_detector.detect(df)
            anomalies.extend(statistical_anomalies)
            
        except Exception as e:
            logger.error("Statistical anomaly detection failed", error=str(e))
        
        return anomalies
    
    async def _detect_behavioral_anomalies(self, data: Dict[str, Any]) -> List[AnomalyEvent]:
        """Detect behavioral anomalies in user and system behavior."""
        anomalies = []
        
        if not data:
            return anomalies
        
        try:
            # Use behavioral analysis service
            behavioral_anomalies = await self.behavioral_analysis.detect_anomalies(data)
            
            # Convert to AnomalyEvent format
            for anomaly in behavioral_anomalies:
                anomaly_event = AnomalyEvent(
                    id=f"behav_{int(time.time())}_{anomaly['id']}",
                    timestamp=time.time(),
                    type=AnomalyType.BEHAVIORAL,
                    severity=AnomalySeverity(anomaly['severity']),
                    confidence=anomaly['confidence'],
                    description=anomaly['description'],
                    source="behavioral_analysis",
                    affected_entities=anomaly.get('affected_entities', []),
                    raw_data=data,
                    context=anomaly.get('context', {}),
                    detection_method="behavioral_analysis",
                    remediation_suggestions=self._get_remediation_suggestions("behavioral"),
                    false_positive_probability=self._estimate_false_positive_rate("behavioral")
                )
                anomalies.append(anomaly_event)
            
        except Exception as e:
            logger.error("Behavioral anomaly detection failed", error=str(e))
        
        return anomalies
    
    async def _detect_network_anomalies(self, data: Dict[str, Any]) -> List[AnomalyEvent]:
        """Detect network-based anomalies."""
        anomalies = []
        
        if not data:
            return anomalies
        
        try:
            network_anomalies = await self.network_detector.detect(data)
            
            for anomaly in network_anomalies:
                anomaly_event = AnomalyEvent(
                    id=f"net_{int(time.time())}_{anomaly['id']}",
                    timestamp=time.time(),
                    type=AnomalyType.NETWORK,
                    severity=AnomalySeverity(anomaly['severity']),
                    confidence=anomaly['confidence'],
                    description=anomaly['description'],
                    source="network_detector",
                    affected_entities=anomaly.get('affected_entities', []),
                    raw_data=data,
                    context=anomaly.get('context', {}),
                    detection_method="network_analysis",
                    remediation_suggestions=self._get_remediation_suggestions("network"),
                    false_positive_probability=self._estimate_false_positive_rate("network")
                )
                anomalies.append(anomaly_event)
            
        except Exception as e:
            logger.error("Network anomaly detection failed", error=str(e))
        
        return anomalies
    
    async def _detect_sequence_anomalies(self, data: Dict[str, Any]) -> List[AnomalyEvent]:
        """Detect sequence-based anomalies using LSTM Autoencoder."""
        anomalies = []
        
        if not data:
            return anomalies
        
        try:
            # Prepare sequence data
            sequences = self._prepare_sequences([data])
            
            if len(sequences) > 0 and self.lstm_autoencoder:
                # Get reconstruction errors
                reconstruction_errors = await self.lstm_autoencoder.predict(sequences)
                
                # Detect anomalies based on reconstruction error threshold
                threshold = self.detection_config.lstm_autoencoder.threshold
                
                for i, error in enumerate(reconstruction_errors):
                    if error > threshold:
                        confidence = min(error / threshold, 1.0)
                        severity = self._calculate_severity(confidence, "lstm_autoencoder")
                        
                        anomaly = AnomalyEvent(
                            id=f"seq_{int(time.time())}_{i}",
                            timestamp=time.time(),
                            type=AnomalyType.BEHAVIORAL,
                            severity=severity,
                            confidence=confidence,
                            description=f"Sequence anomaly detected with reconstruction error {error:.3f}",
                            source="lstm_autoencoder",
                            affected_entities=["system"],
                            raw_data=data,
                            context={"reconstruction_error": error, "threshold": threshold},
                            detection_method="lstm_autoencoder",
                            remediation_suggestions=self._get_remediation_suggestions("sequence"),
                            false_positive_probability=self._estimate_false_positive_rate("sequence")
                        )
                        anomalies.append(anomaly)
            
        except Exception as e:
            logger.error("Sequence anomaly detection failed", error=str(e))
        
        return anomalies
    
    async def _detect_security_anomalies(self, data: Dict[str, Any]) -> List[AnomalyEvent]:
        """Detect security-specific anomalies using threat intelligence."""
        anomalies = []
        
        try:
            # Use threat intelligence service
            security_threats = await self.threat_intel.analyze_data(data)
            
            for threat in security_threats:
                anomaly_event = AnomalyEvent(
                    id=f"sec_{int(time.time())}_{threat['id']}",
                    timestamp=time.time(),
                    type=AnomalyType.SECURITY,
                    severity=AnomalySeverity(threat['severity']),
                    confidence=threat['confidence'],
                    description=threat['description'],
                    source="threat_intelligence",
                    affected_entities=threat.get('affected_entities', []),
                    raw_data=data,
                    context=threat.get('context', {}),
                    detection_method="threat_intelligence",
                    remediation_suggestions=self._get_remediation_suggestions("security"),
                    false_positive_probability=self._estimate_false_positive_rate("security")
                )
                anomalies.append(anomaly_event)
            
        except Exception as e:
            logger.error("Security anomaly detection failed", error=str(e))
        
        return anomalies
    
    def _extract_features(self, df: pd.DataFrame) -> np.ndarray:
        """Extract numerical features from DataFrame."""
        # Select only numerical columns
        numerical_cols = df.select_dtypes(include=[np.number]).columns
        
        if len(numerical_cols) == 0:
            return np.array([])
        
        # Handle missing values
        features = df[numerical_cols].fillna(0).values
        
        return features
    
    def _prepare_sequences(self, data: List[Dict[str, Any]]) -> np.ndarray:
        """Prepare sequence data for LSTM Autoencoder."""
        # This is a simplified implementation
        # In practice, you would extract meaningful sequences from log data
        sequences = []
        
        for item in data:
            # Convert item to numerical sequence
            # This is a placeholder - implement based on your data structure
            sequence = [hash(str(v)) % 1000 for v in item.values() if v is not None]
            if len(sequence) > 0:
                sequences.append(sequence)
        
        if not sequences:
            return np.array([])
        
        # Pad sequences to same length
        max_length = self.detection_config.lstm_autoencoder.sequence_length
        padded_sequences = []
        
        for seq in sequences:
            if len(seq) > max_length:
                seq = seq[:max_length]
            else:
                seq = seq + [0] * (max_length - len(seq))
            padded_sequences.append(seq)
        
        return np.array(padded_sequences)
    
    async def _post_process_anomalies(self, anomalies: List[AnomalyEvent]) -> List[AnomalyEvent]:
        """Post-process detected anomalies to reduce false positives and duplicates."""
        if not anomalies:
            return anomalies
        
        # Remove duplicates based on similarity
        unique_anomalies = self._remove_duplicate_anomalies(anomalies)
        
        # Filter based on confidence thresholds
        filtered_anomalies = [
            a for a in unique_anomalies 
            if a.confidence >= self.thresholds.get(a.type.value, 0.5)
        ]
        
        # Sort by severity and confidence
        filtered_anomalies.sort(
            key=lambda x: (x.severity.value, x.confidence), 
            reverse=True
        )
        
        return filtered_anomalies
    
    def _remove_duplicate_anomalies(self, anomalies: List[AnomalyEvent]) -> List[AnomalyEvent]:
        """Remove duplicate anomalies based on similarity."""
        # Simple deduplication based on type, source, and time window
        unique_anomalies = []
        seen_signatures = set()
        
        for anomaly in anomalies:
            # Create signature for deduplication
            signature = f"{anomaly.type.value}_{anomaly.source}_{int(anomaly.timestamp // 60)}"
            
            if signature not in seen_signatures:
                unique_anomalies.append(anomaly)
                seen_signatures.add(signature)
        
        return unique_anomalies
    
    def _calculate_severity(self, confidence: float, method: str) -> AnomalySeverity:
        """Calculate anomaly severity based on confidence and detection method."""
        # Method-specific severity calculation
        method_weights = {
            "isolation_forest": 1.0,
            "lstm_autoencoder": 1.2,
            "threat_intelligence": 1.5,
            "behavioral_analysis": 1.1
        }
        
        weighted_confidence = confidence * method_weights.get(method, 1.0)
        
        if weighted_confidence >= 0.9:
            return AnomalySeverity.CRITICAL
        elif weighted_confidence >= 0.7:
            return AnomalySeverity.HIGH
        elif weighted_confidence >= 0.5:
            return AnomalySeverity.MEDIUM
        else:
            return AnomalySeverity.LOW
    
    def _get_remediation_suggestions(self, anomaly_type: str) -> List[str]:
        """Get remediation suggestions based on anomaly type."""
        suggestions = {
            "statistical": [
                "Review system metrics and performance indicators",
                "Check for resource constraints or bottlenecks",
                "Validate data quality and completeness"
            ],
            "behavioral": [
                "Review user access patterns and permissions",
                "Investigate unusual user activities",
                "Check for compromised accounts"
            ],
            "network": [
                "Review network traffic patterns",
                "Check firewall rules and network policies",
                "Investigate potential network intrusions"
            ],
            "security": [
                "Immediately isolate affected systems",
                "Review security logs and audit trails",
                "Contact security team for incident response"
            ],
            "sequence": [
                "Review system logs for unusual patterns",
                "Check application behavior and workflows",
                "Validate system configuration changes"
            ]
        }
        
        return suggestions.get(anomaly_type, ["Contact system administrator"])
    
    def _estimate_false_positive_rate(self, method: str) -> float:
        """Estimate false positive rate for different detection methods."""
        # Historical false positive rates (would be learned from feedback)
        rates = {
            "isolation_forest": 0.15,
            "lstm_autoencoder": 0.20,
            "behavioral_analysis": 0.10,
            "network_analysis": 0.12,
            "threat_intelligence": 0.05,
            "statistical": 0.18
        }
        
        return rates.get(method, 0.15)
    
    def _load_thresholds(self) -> Dict[str, float]:
        """Load detection thresholds from configuration."""
        return {
            "statistical": 0.6,
            "behavioral": 0.5,
            "network": 0.7,
            "security": 0.4,
            "performance": 0.6,
            "data_quality": 0.5,
            "ethics_violation": 0.3
        }
    
    async def _load_metrics_data(self, start_time: float, end_time: float) -> List[Dict[str, Any]]:
        """Load metrics data from database."""
        # Placeholder implementation
        return []
    
    async def _load_log_data(self, start_time: float, end_time: float) -> List[Dict[str, Any]]:
        """Load log data from database."""
        # Placeholder implementation
        return []
    
    async def _load_network_data(self, start_time: float, end_time: float) -> List[Dict[str, Any]]:
        """Load network data from database."""
        # Placeholder implementation
        return []
    
    async def _load_user_behavior_data(self, start_time: float, end_time: float) -> List[Dict[str, Any]]:
        """Load user behavior data from database."""
        # Placeholder implementation
        return []
    
    async def start_detection(self):
        """Start the anomaly detection service."""
        self.is_running = True
        logger.info("Anomaly detection service started")
    
    async def stop(self):
        """Stop the anomaly detection service."""
        self.is_running = False
        
        # Cancel all detection tasks
        for task in self.detection_tasks:
            task.cancel()
        
        logger.info("Anomaly detection service stopped")
    
    async def cleanup(self):
        """Cleanup resources."""
        await self.stop()
        
        if self.alert_manager:
            await self.alert_manager.cleanup()
