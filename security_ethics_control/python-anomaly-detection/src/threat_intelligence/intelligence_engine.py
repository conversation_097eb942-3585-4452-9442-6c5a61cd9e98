"""
Threat Intelligence Engine for ASI Security & Ethics Control Layer.

Provides advanced threat detection, intelligence gathering, and security
analysis capabilities for the ASI system.
"""

import asyncio
import time
import json
import hashlib
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Set, Tuple
from enum import Enum
import logging
import numpy as np
import pandas as pd
from collections import defaultdict, deque

from ..utils.logger import get_logger, PerformanceLogger
from ..utils.metrics import get_metrics_collector, ThreatIntelligenceMetrics
from ..utils.config import Config

logger = get_logger(__name__)


class ThreatLevel(Enum):
    """Threat severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ThreatCategory(Enum):
    """Categories of threats."""
    MALWARE = "malware"
    PHISHING = "phishing"
    DATA_BREACH = "data_breach"
    INSIDER_THREAT = "insider_threat"
    APT = "advanced_persistent_threat"
    DDOS = "distributed_denial_of_service"
    PRIVILEGE_ESCALATION = "privilege_escalation"
    LATERAL_MOVEMENT = "lateral_movement"
    DATA_EXFILTRATION = "data_exfiltration"
    SYSTEM_COMPROMISE = "system_compromise"
    AI_POISONING = "ai_model_poisoning"
    ADVERSARIAL_ATTACK = "adversarial_attack"


class IndicatorType(Enum):
    """Types of threat indicators."""
    IP_ADDRESS = "ip_address"
    DOMAIN = "domain"
    URL = "url"
    FILE_HASH = "file_hash"
    EMAIL = "email"
    USER_AGENT = "user_agent"
    CERTIFICATE = "certificate"
    BEHAVIOR_PATTERN = "behavior_pattern"


@dataclass
class ThreatIndicator:
    """Threat indicator of compromise (IoC)."""
    id: str
    type: IndicatorType
    value: str
    threat_level: ThreatLevel
    category: ThreatCategory
    confidence: float
    first_seen: datetime
    last_seen: datetime
    source: str
    description: str
    tags: List[str] = field(default_factory=list)
    context: Dict[str, Any] = field(default_factory=dict)
    false_positive_rate: float = 0.0
    expiration: Optional[datetime] = None


@dataclass
class ThreatEvent:
    """Detected threat event."""
    id: str
    timestamp: datetime
    threat_level: ThreatLevel
    category: ThreatCategory
    title: str
    description: str
    indicators: List[ThreatIndicator]
    affected_assets: List[str]
    attack_vector: str
    confidence: float
    source_ip: Optional[str] = None
    target_ip: Optional[str] = None
    user_context: Optional[str] = None
    raw_data: Dict[str, Any] = field(default_factory=dict)
    mitigation_steps: List[str] = field(default_factory=list)
    related_events: List[str] = field(default_factory=list)


@dataclass
class ThreatCampaign:
    """Coordinated threat campaign."""
    id: str
    name: str
    description: str
    threat_actor: str
    start_time: datetime
    end_time: Optional[datetime]
    events: List[ThreatEvent]
    indicators: List[ThreatIndicator]
    tactics: List[str]
    techniques: List[str]
    procedures: List[str]
    confidence: float
    impact_assessment: Dict[str, Any]


class ThreatIntelligenceEngine:
    """
    Advanced threat intelligence engine for comprehensive security analysis.
    """
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = get_logger(__name__)
        self.metrics = get_metrics_collector()
        
        # Threat intelligence data
        self.indicators: Dict[str, ThreatIndicator] = {}
        self.events: deque = deque(maxlen=10000)
        self.campaigns: Dict[str, ThreatCampaign] = {}
        
        # Analysis engines
        self.pattern_analyzer = PatternAnalyzer()
        self.correlation_engine = CorrelationEngine()
        self.attribution_engine = AttributionEngine()
        
        # Threat feeds
        self.threat_feeds = []
        self.feed_update_interval = 3600  # 1 hour
        
        # Machine learning models
        self.ml_models = {}
        
        # Statistics
        self.stats = {
            'indicators_processed': 0,
            'events_analyzed': 0,
            'campaigns_identified': 0,
            'false_positives': 0,
            'true_positives': 0
        }
        
        self.logger.info("Threat Intelligence Engine initialized")
    
    async def initialize(self):
        """Initialize the threat intelligence engine."""
        try:
            # Load threat indicators
            await self._load_threat_indicators()
            
            # Initialize ML models
            await self._initialize_ml_models()
            
            # Start threat feed updates
            asyncio.create_task(self._threat_feed_updater())
            
            # Start correlation analysis
            asyncio.create_task(self._correlation_analyzer())
            
            self.logger.info("Threat Intelligence Engine initialization complete")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Threat Intelligence Engine: {e}")
            raise
    
    async def analyze_threat(self, data: Dict[str, Any]) -> List[ThreatEvent]:
        """Analyze data for potential threats."""
        start_time = time.time()
        threats = []
        
        try:
            # Extract indicators from data
            indicators = await self._extract_indicators(data)
            
            # Check against known threat indicators
            matched_indicators = await self._match_indicators(indicators)
            
            # Perform behavioral analysis
            behavioral_threats = await self._analyze_behavior(data)
            
            # Perform pattern analysis
            pattern_threats = await self._analyze_patterns(data)
            
            # Combine all threat detections
            all_threats = matched_indicators + behavioral_threats + pattern_threats
            
            # Correlate and deduplicate
            threats = await self._correlate_threats(all_threats)
            
            # Update statistics
            self.stats['events_analyzed'] += 1
            
            # Record metrics
            analysis_time = time.time() - start_time
            self.metrics.record_threat_analysis(ThreatIntelligenceMetrics(
                analysis_time_ms=analysis_time * 1000,
                threats_detected=len(threats),
                indicators_matched=len(matched_indicators),
                confidence_avg=np.mean([t.confidence for t in threats]) if threats else 0.0
            ))
            
            return threats
            
        except Exception as e:
            self.logger.error(f"Threat analysis failed: {e}")
            return []
    
    async def _extract_indicators(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract potential threat indicators from data."""
        indicators = []
        
        # Extract IP addresses
        ip_addresses = self._extract_ip_addresses(data)
        for ip in ip_addresses:
            indicators.append({
                'type': IndicatorType.IP_ADDRESS,
                'value': ip,
                'context': data
            })
        
        # Extract domains
        domains = self._extract_domains(data)
        for domain in domains:
            indicators.append({
                'type': IndicatorType.DOMAIN,
                'value': domain,
                'context': data
            })
        
        # Extract URLs
        urls = self._extract_urls(data)
        for url in urls:
            indicators.append({
                'type': IndicatorType.URL,
                'value': url,
                'context': data
            })
        
        # Extract file hashes
        file_hashes = self._extract_file_hashes(data)
        for file_hash in file_hashes:
            indicators.append({
                'type': IndicatorType.FILE_HASH,
                'value': file_hash,
                'context': data
            })
        
        # Extract behavioral patterns
        patterns = self._extract_behavior_patterns(data)
        for pattern in patterns:
            indicators.append({
                'type': IndicatorType.BEHAVIOR_PATTERN,
                'value': pattern,
                'context': data
            })
        
        return indicators
    
    async def _match_indicators(self, indicators: List[Dict[str, Any]]) -> List[ThreatEvent]:
        """Match extracted indicators against known threat indicators."""
        threats = []
        
        for indicator in indicators:
            indicator_value = indicator['value']
            indicator_type = indicator['type']
            
            # Check against known threat indicators
            for known_indicator in self.indicators.values():
                if (known_indicator.type == indicator_type and 
                    known_indicator.value == indicator_value):
                    
                    # Create threat event
                    threat = ThreatEvent(
                        id=f"ioc_{int(time.time())}_{hash(indicator_value) % 10000}",
                        timestamp=datetime.utcnow(),
                        threat_level=known_indicator.threat_level,
                        category=known_indicator.category,
                        title=f"Known threat indicator detected: {indicator_value}",
                        description=known_indicator.description,
                        indicators=[known_indicator],
                        affected_assets=self._identify_affected_assets(indicator['context']),
                        attack_vector=self._determine_attack_vector(known_indicator),
                        confidence=known_indicator.confidence,
                        raw_data=indicator['context'],
                        mitigation_steps=self._get_mitigation_steps(known_indicator)
                    )
                    
                    threats.append(threat)
                    break
        
        return threats
    
    async def _analyze_behavior(self, data: Dict[str, Any]) -> List[ThreatEvent]:
        """Analyze behavioral patterns for threats."""
        threats = []
        
        try:
            # Analyze login patterns
            login_threats = await self._analyze_login_behavior(data)
            threats.extend(login_threats)
            
            # Analyze network behavior
            network_threats = await self._analyze_network_behavior(data)
            threats.extend(network_threats)
            
            # Analyze file access patterns
            file_threats = await self._analyze_file_behavior(data)
            threats.extend(file_threats)
            
            # Analyze API usage patterns
            api_threats = await self._analyze_api_behavior(data)
            threats.extend(api_threats)
            
        except Exception as e:
            self.logger.error(f"Behavioral analysis failed: {e}")
        
        return threats
    
    async def _analyze_patterns(self, data: Dict[str, Any]) -> List[ThreatEvent]:
        """Analyze data patterns for threat indicators."""
        threats = []
        
        try:
            # Use pattern analyzer
            patterns = await self.pattern_analyzer.analyze(data)
            
            for pattern in patterns:
                if pattern['threat_score'] > 0.7:  # High threat score
                    threat = ThreatEvent(
                        id=f"pattern_{int(time.time())}_{hash(str(pattern)) % 10000}",
                        timestamp=datetime.utcnow(),
                        threat_level=self._score_to_threat_level(pattern['threat_score']),
                        category=ThreatCategory.SYSTEM_COMPROMISE,
                        title=f"Suspicious pattern detected: {pattern['pattern_type']}",
                        description=pattern['description'],
                        indicators=[],
                        affected_assets=pattern.get('affected_assets', []),
                        attack_vector=pattern.get('attack_vector', 'unknown'),
                        confidence=pattern['threat_score'],
                        raw_data=data,
                        mitigation_steps=pattern.get('mitigation_steps', [])
                    )
                    threats.append(threat)
            
        except Exception as e:
            self.logger.error(f"Pattern analysis failed: {e}")
        
        return threats
    
    async def _correlate_threats(self, threats: List[ThreatEvent]) -> List[ThreatEvent]:
        """Correlate and deduplicate threat events."""
        if not threats:
            return []
        
        # Use correlation engine
        correlated_threats = await self.correlation_engine.correlate(threats)
        
        # Check for campaign indicators
        campaigns = await self._identify_campaigns(correlated_threats)
        
        # Update campaign tracking
        for campaign in campaigns:
            self.campaigns[campaign.id] = campaign
            self.stats['campaigns_identified'] += 1
        
        return correlated_threats
    
    async def _identify_campaigns(self, threats: List[ThreatEvent]) -> List[ThreatCampaign]:
        """Identify coordinated threat campaigns."""
        campaigns = []
        
        # Group threats by similarity
        threat_groups = self._group_similar_threats(threats)
        
        for group in threat_groups:
            if len(group) >= 3:  # Minimum events for campaign
                campaign = ThreatCampaign(
                    id=f"campaign_{int(time.time())}_{hash(str(group)) % 10000}",
                    name=f"Campaign {datetime.utcnow().strftime('%Y%m%d_%H%M')}",
                    description="Coordinated threat activity detected",
                    threat_actor="Unknown",
                    start_time=min(t.timestamp for t in group),
                    end_time=max(t.timestamp for t in group),
                    events=group,
                    indicators=list(set(ind for event in group for ind in event.indicators)),
                    tactics=self._extract_tactics(group),
                    techniques=self._extract_techniques(group),
                    procedures=self._extract_procedures(group),
                    confidence=np.mean([t.confidence for t in group]),
                    impact_assessment=self._assess_campaign_impact(group)
                )
                campaigns.append(campaign)
        
        return campaigns
    
    def _group_similar_threats(self, threats: List[ThreatEvent]) -> List[List[ThreatEvent]]:
        """Group similar threats together."""
        groups = []
        used_threats = set()
        
        for threat in threats:
            if threat.id in used_threats:
                continue
            
            group = [threat]
            used_threats.add(threat.id)
            
            # Find similar threats
            for other_threat in threats:
                if (other_threat.id not in used_threats and 
                    self._threats_similar(threat, other_threat)):
                    group.append(other_threat)
                    used_threats.add(other_threat.id)
            
            groups.append(group)
        
        return groups
    
    def _threats_similar(self, threat1: ThreatEvent, threat2: ThreatEvent) -> bool:
        """Check if two threats are similar."""
        # Same category
        if threat1.category != threat2.category:
            return False
        
        # Similar timing (within 1 hour)
        time_diff = abs((threat1.timestamp - threat2.timestamp).total_seconds())
        if time_diff > 3600:
            return False
        
        # Similar attack vector
        if threat1.attack_vector == threat2.attack_vector:
            return True
        
        # Overlapping indicators
        indicators1 = set(ind.value for ind in threat1.indicators)
        indicators2 = set(ind.value for ind in threat2.indicators)
        overlap = len(indicators1 & indicators2) / max(len(indicators1 | indicators2), 1)
        
        return overlap > 0.3
    
    # Helper methods for indicator extraction
    def _extract_ip_addresses(self, data: Dict[str, Any]) -> List[str]:
        """Extract IP addresses from data."""
        import re
        ip_pattern = r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b'
        text = json.dumps(data)
        return list(set(re.findall(ip_pattern, text)))
    
    def _extract_domains(self, data: Dict[str, Any]) -> List[str]:
        """Extract domain names from data."""
        import re
        domain_pattern = r'\b[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*\b'
        text = json.dumps(data)
        domains = re.findall(domain_pattern, text)
        return [d[0] + d[1] for d in domains if '.' in d[1]]
    
    def _extract_urls(self, data: Dict[str, Any]) -> List[str]:
        """Extract URLs from data."""
        import re
        url_pattern = r'https?://[^\s<>"{}|\\^`\[\]]+'
        text = json.dumps(data)
        return list(set(re.findall(url_pattern, text)))
    
    def _extract_file_hashes(self, data: Dict[str, Any]) -> List[str]:
        """Extract file hashes from data."""
        import re
        # MD5, SHA1, SHA256 patterns
        hash_patterns = [
            r'\b[a-fA-F0-9]{32}\b',  # MD5
            r'\b[a-fA-F0-9]{40}\b',  # SHA1
            r'\b[a-fA-F0-9]{64}\b'   # SHA256
        ]
        
        text = json.dumps(data)
        hashes = []
        for pattern in hash_patterns:
            hashes.extend(re.findall(pattern, text))
        
        return list(set(hashes))
    
    def _extract_behavior_patterns(self, data: Dict[str, Any]) -> List[str]:
        """Extract behavioral patterns from data."""
        patterns = []
        
        # Check for suspicious patterns
        if 'failed_logins' in data and data.get('failed_logins', 0) > 10:
            patterns.append('excessive_failed_logins')
        
        if 'privilege_escalation' in data and data.get('privilege_escalation'):
            patterns.append('privilege_escalation_attempt')
        
        if 'unusual_network_activity' in data and data.get('unusual_network_activity'):
            patterns.append('unusual_network_activity')
        
        return patterns
    
    # Additional helper methods
    def _score_to_threat_level(self, score: float) -> ThreatLevel:
        """Convert threat score to threat level."""
        if score >= 0.9:
            return ThreatLevel.CRITICAL
        elif score >= 0.7:
            return ThreatLevel.HIGH
        elif score >= 0.5:
            return ThreatLevel.MEDIUM
        else:
            return ThreatLevel.LOW
    
    def _identify_affected_assets(self, context: Dict[str, Any]) -> List[str]:
        """Identify affected assets from context."""
        assets = []
        
        if 'hostname' in context:
            assets.append(context['hostname'])
        
        if 'ip_address' in context:
            assets.append(context['ip_address'])
        
        if 'user' in context:
            assets.append(f"user:{context['user']}")
        
        return assets
    
    def _determine_attack_vector(self, indicator: ThreatIndicator) -> str:
        """Determine attack vector from indicator."""
        if indicator.type == IndicatorType.IP_ADDRESS:
            return "network"
        elif indicator.type == IndicatorType.EMAIL:
            return "email"
        elif indicator.type == IndicatorType.FILE_HASH:
            return "file"
        elif indicator.type == IndicatorType.URL:
            return "web"
        else:
            return "unknown"
    
    def _get_mitigation_steps(self, indicator: ThreatIndicator) -> List[str]:
        """Get mitigation steps for threat indicator."""
        steps = []
        
        if indicator.type == IndicatorType.IP_ADDRESS:
            steps.append(f"Block IP address {indicator.value}")
            steps.append("Review firewall rules")
        
        elif indicator.type == IndicatorType.DOMAIN:
            steps.append(f"Block domain {indicator.value}")
            steps.append("Update DNS filtering")
        
        elif indicator.type == IndicatorType.FILE_HASH:
            steps.append(f"Quarantine files with hash {indicator.value}")
            steps.append("Run full system scan")
        
        return steps
    
    async def _load_threat_indicators(self):
        """Load threat indicators from various sources."""
        # This would load from threat intelligence feeds
        # For now, we'll create some sample indicators
        pass
    
    async def _initialize_ml_models(self):
        """Initialize machine learning models for threat detection."""
        # This would load pre-trained ML models
        pass
    
    async def _threat_feed_updater(self):
        """Update threat feeds periodically."""
        while True:
            try:
                # Update threat indicators from feeds
                await self._update_threat_feeds()
                await asyncio.sleep(self.feed_update_interval)
            except Exception as e:
                self.logger.error(f"Threat feed update failed: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes on error
    
    async def _correlation_analyzer(self):
        """Continuously analyze events for correlations."""
        while True:
            try:
                # Analyze recent events for correlations
                await self._analyze_event_correlations()
                await asyncio.sleep(60)  # Run every minute
            except Exception as e:
                self.logger.error(f"Correlation analysis failed: {e}")
                await asyncio.sleep(60)


# Supporting classes
class PatternAnalyzer:
    """Analyzes patterns in data for threat indicators."""
    
    async def analyze(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Analyze data for suspicious patterns."""
        patterns = []
        
        # Implement pattern analysis logic
        # This is a simplified version
        
        return patterns


class CorrelationEngine:
    """Correlates threat events to identify relationships."""
    
    async def correlate(self, threats: List[ThreatEvent]) -> List[ThreatEvent]:
        """Correlate threat events."""
        # Implement correlation logic
        # For now, just return the input
        return threats


class AttributionEngine:
    """Attributes threats to specific threat actors."""
    
    async def attribute(self, campaign: ThreatCampaign) -> str:
        """Attribute campaign to threat actor."""
        # Implement attribution logic
        return "Unknown"
