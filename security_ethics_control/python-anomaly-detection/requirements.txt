# ASI System - Python Anomaly Detection Service Requirements
# ==========================================================

# Core dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
python-multipart==0.0.6

# Machine Learning and Data Science
scikit-learn==1.3.2
numpy==1.24.4
pandas==2.1.4
scipy==1.11.4
tensorflow==2.15.0
torch==2.1.1
transformers==4.36.0

# Anomaly Detection Libraries
pyod==1.1.0
isolation-forest==0.1.0
hdbscan==0.8.33
statsmodels==0.14.0

# Time Series Analysis
prophet==1.1.5
tslearn==0.6.2
stumpy==1.12.0

# Data Processing
kafka-python==2.0.2
redis==5.0.1
elasticsearch==8.11.0
pymongo==4.6.0
psycopg2-binary==2.9.9
sqlalchemy==2.0.23

# Networking and APIs
grpcio==1.59.3
grpcio-tools==1.59.3
requests==2.31.0
aiohttp==3.9.1
websockets==12.0

# Security and Cryptography
cryptography==41.0.8
pyjwt==2.8.0
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0

# Monitoring and Observability
prometheus-client==0.19.0
opentelemetry-api==1.21.0
opentelemetry-sdk==1.21.0
opentelemetry-instrumentation-fastapi==0.42b0
jaeger-client==4.8.0

# Configuration and Environment
pydantic-settings==2.1.0
python-dotenv==1.0.0
pyyaml==6.0.1
toml==0.10.2

# Logging and Utilities
structlog==23.2.0
rich==13.7.0
click==8.1.7
typer==0.9.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2
factory-boy==3.3.0

# Development tools
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
pre-commit==3.6.0

# Visualization (for debugging and analysis)
matplotlib==3.8.2
seaborn==0.13.0
plotly==5.17.0

# Geospatial analysis
geoip2==4.7.0
geopy==2.4.1

# Natural Language Processing
spacy==3.7.2
nltk==3.8.1

# Image processing (for visual anomaly detection)
opencv-python==********
pillow==10.1.0

# Blockchain integration (optional)
web3==6.12.0
eth-account==0.9.0

# Performance optimization
numba==0.58.1
cython==3.0.6

# Memory profiling and optimization
memory-profiler==0.61.0
psutil==5.9.6

# Async support
asyncio==3.4.3
aiofiles==23.2.1
asyncpg==0.29.0

# Data validation and serialization
marshmallow==3.20.1
cerberus==1.3.5

# HTTP client with retry logic
tenacity==8.2.3
backoff==2.2.1

# Rate limiting
slowapi==0.1.9

# Background tasks
celery==5.3.4
redis==5.0.1

# Model serving and deployment
mlflow==2.8.1
bentoml==1.1.10

# Feature engineering
featuretools==1.28.0
tsfresh==0.20.1

# Hyperparameter optimization
optuna==3.4.0
hyperopt==0.2.7

# Model interpretability
shap==0.43.0
lime==*******

# Data drift detection
evidently==0.4.9
alibi-detect==0.11.4

# Streaming data processing
streamz==0.6.4
dask[complete]==2023.11.0

# Graph analysis (for network anomaly detection)
networkx==3.2.1
igraph==0.11.3

# Statistical analysis
pingouin==0.5.3
lifelines==0.27.8

# Time zone handling
pytz==2023.3
tzdata==2023.3

# Configuration management
hydra-core==1.3.2
omegaconf==2.3.0

# Distributed computing
ray[default]==2.8.1

# Model versioning
dvc==3.30.3

# Data quality
great-expectations==0.18.5

# Profiling
py-spy==0.3.14
line-profiler==4.1.1
