name: ASI System Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run tests daily at 2 AM UTC
    - cron: '0 2 * * *'

env:
  CARGO_TERM_COLOR: always
  PYTHONPATH: ${{ github.workspace }}
  GO111MODULE: on

jobs:
  # Unit Tests
  unit-tests-python:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.9, 3.10, 3.11]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Cache Python dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r tests/requirements.txt
        pip install -r requirements.txt || echo "No root requirements.txt found"
    
    - name: Run Python unit tests
      run: |
        pytest tests/unit/python/ \
          --cov=. \
          --cov-report=xml \
          --cov-report=html \
          --junit-xml=test-results-python.xml \
          -v \
          -m "unit and not slow"
    
    - name: Upload Python coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: python-unit
        name: python-unit-${{ matrix.python-version }}
    
    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: python-unit-test-results-${{ matrix.python-version }}
        path: |
          test-results-python.xml
          htmlcov/

  unit-tests-go:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        go-version: [1.19, 1.20, 1.21]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Go ${{ matrix.go-version }}
      uses: actions/setup-go@v4
      with:
        go-version: ${{ matrix.go-version }}
    
    - name: Cache Go modules
      uses: actions/cache@v3
      with:
        path: ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-
    
    - name: Install dependencies
      run: |
        go mod download
        go install github.com/jstemmer/go-junit-report/v2@latest
        go install github.com/axw/gocov/gocov@latest
        go install github.com/AlekSi/gocov-xml@latest
    
    - name: Run Go unit tests
      run: |
        cd tests/unit/go
        go test -v -race -coverprofile=coverage.out -covermode=atomic ./... 2>&1 | \
          go-junit-report -set-exit-code > test-results-go.xml
        gocov convert coverage.out | gocov-xml > coverage.xml
    
    - name: Upload Go coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./tests/unit/go/coverage.xml
        flags: go-unit
        name: go-unit-${{ matrix.go-version }}
    
    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: go-unit-test-results-${{ matrix.go-version }}
        path: |
          tests/unit/go/test-results-go.xml
          tests/unit/go/coverage.out

  unit-tests-rust:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        rust-version: [stable, beta]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Install Rust ${{ matrix.rust-version }}
      uses: actions-rs/toolchain@v1
      with:
        toolchain: ${{ matrix.rust-version }}
        components: rustfmt, clippy
        override: true
    
    - name: Cache Rust dependencies
      uses: actions/cache@v3
      with:
        path: |
          ~/.cargo/registry
          ~/.cargo/git
          target
        key: ${{ runner.os }}-cargo-${{ hashFiles('**/Cargo.lock') }}
        restore-keys: |
          ${{ runner.os }}-cargo-
    
    - name: Install cargo tools
      run: |
        cargo install cargo-tarpaulin || echo "cargo-tarpaulin already installed"
        cargo install cargo2junit || echo "cargo2junit already installed"
    
    - name: Run Rust unit tests
      run: |
        cd tests/unit/rust
        cargo test --verbose --all-features 2>&1 | cargo2junit > test-results-rust.xml
        cargo tarpaulin --out xml --output-dir .
    
    - name: Upload Rust coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./tests/unit/rust/cobertura.xml
        flags: rust-unit
        name: rust-unit-${{ matrix.rust-version }}
    
    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: rust-unit-test-results-${{ matrix.rust-version }}
        path: |
          tests/unit/rust/test-results-rust.xml
          tests/unit/rust/cobertura.xml

  unit-tests-cpp:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        compiler: [gcc-11, clang-14]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Install C++ dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y \
          build-essential \
          cmake \
          libgtest-dev \
          libgmock-dev \
          lcov \
          ${{ matrix.compiler }}
    
    - name: Build and run C++ tests
      run: |
        cd tests/unit/cpp
        mkdir -p build
        cd build
        cmake .. -DCMAKE_BUILD_TYPE=Debug -DENABLE_COVERAGE=ON
        make -j$(nproc)
        ctest --output-on-failure --verbose
        
        # Generate coverage report
        lcov --capture --directory . --output-file coverage.info
        lcov --remove coverage.info '/usr/*' --output-file coverage.info
        lcov --list coverage.info
    
    - name: Upload C++ coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./tests/unit/cpp/build/coverage.info
        flags: cpp-unit
        name: cpp-unit-${{ matrix.compiler }}

  # Integration Tests
  integration-tests:
    runs-on: ubuntu-latest
    needs: [unit-tests-python, unit-tests-go, unit-tests-rust]
    
    services:
      kafka:
        image: confluentinc/cp-kafka:latest
        env:
          KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
          KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
          KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
        ports:
          - 9092:9092
      
      zookeeper:
        image: confluentinc/cp-zookeeper:latest
        env:
          ZOOKEEPER_CLIENT_PORT: 2181
          ZOOKEEPER_TICK_TIME: 2000
        ports:
          - 2181:2181
      
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: 3.11
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r tests/requirements.txt
    
    - name: Wait for services
      run: |
        timeout 60 bash -c 'until nc -z localhost 9092; do sleep 1; done'
        timeout 60 bash -c 'until nc -z localhost 6379; do sleep 1; done'
    
    - name: Run integration tests
      run: |
        pytest tests/integration/ \
          --junit-xml=test-results-integration.xml \
          -v \
          -m "integration and not slow" \
          --timeout=300
    
    - name: Upload integration test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: integration-test-results
        path: test-results-integration.xml

  # End-to-End Tests
  e2e-tests:
    runs-on: ubuntu-latest
    needs: [integration-tests]
    if: github.event_name == 'push' || github.event_name == 'schedule'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: 3.11
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r tests/requirements.txt
    
    - name: Start ASI system services
      run: |
        # This would start the actual ASI system services
        # For now, we'll use docker-compose
        docker-compose -f docker/docker-compose.test.yml up -d
        sleep 30  # Wait for services to start
    
    - name: Run E2E tests
      run: |
        pytest tests/e2e/ \
          --junit-xml=test-results-e2e.xml \
          -v \
          -m "e2e and not performance" \
          --timeout=600
    
    - name: Stop services
      if: always()
      run: |
        docker-compose -f docker/docker-compose.test.yml down
    
    - name: Upload E2E test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: e2e-test-results
        path: test-results-e2e.xml

  # Performance Tests
  performance-tests:
    runs-on: ubuntu-latest
    needs: [e2e-tests]
    if: github.event_name == 'schedule' || contains(github.event.head_commit.message, '[perf]')
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: 3.11
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r tests/requirements.txt
    
    - name: Run performance tests
      run: |
        pytest tests/ \
          --benchmark-only \
          --benchmark-json=benchmark-results.json \
          -v \
          -m "performance"
    
    - name: Upload benchmark results
      uses: actions/upload-artifact@v3
      with:
        name: benchmark-results
        path: benchmark-results.json

  # Security Scanning
  security-scan:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'
    
    - name: Python security scan with bandit
      run: |
        pip install bandit[toml]
        bandit -r . -f json -o bandit-results.json || true
    
    - name: Upload security scan results
      uses: actions/upload-artifact@v3
      with:
        name: security-scan-results
        path: |
          trivy-results.sarif
          bandit-results.json

  # Code Quality
  code-quality:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: 3.11
    
    - name: Install linting tools
      run: |
        python -m pip install --upgrade pip
        pip install black isort flake8 mypy pylint
    
    - name: Run Python linting
      run: |
        black --check --diff .
        isort --check-only --diff .
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        mypy . --ignore-missing-imports || true
    
    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: 1.21
    
    - name: Run Go linting
      uses: golangci/golangci-lint-action@v3
      with:
        version: latest
        working-directory: tests/unit/go
    
    - name: Install Rust
      uses: actions-rs/toolchain@v1
      with:
        toolchain: stable
        components: rustfmt, clippy
    
    - name: Run Rust linting
      run: |
        cd tests/unit/rust
        cargo fmt --all -- --check
        cargo clippy --all-targets --all-features -- -D warnings

  # Test Report Generation
  test-report:
    runs-on: ubuntu-latest
    needs: [unit-tests-python, unit-tests-go, unit-tests-rust, unit-tests-cpp, integration-tests, e2e-tests]
    if: always()
    
    steps:
    - name: Download all test artifacts
      uses: actions/download-artifact@v3
    
    - name: Generate test report
      run: |
        echo "# ASI System Test Report" > test-report.md
        echo "" >> test-report.md
        echo "## Test Results Summary" >> test-report.md
        echo "" >> test-report.md
        
        # Count test results from XML files
        find . -name "test-results-*.xml" -exec echo "Found test results: {}" \;
        
        echo "Test report generated successfully"
    
    - name: Upload test report
      uses: actions/upload-artifact@v3
      with:
        name: test-report
        path: test-report.md
