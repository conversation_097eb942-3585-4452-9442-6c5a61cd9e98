name: ASI System Comprehensive Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run comprehensive tests daily at 2 AM UTC
    - cron: '0 2 * * *'
  workflow_dispatch:
    inputs:
      test_types:
        description: 'Test types to run (comma-separated)'
        required: false
        default: 'unit,integration'
      modules:
        description: 'Modules to test (comma-separated)'
        required: false
        default: 'all'

env:
  CARGO_TERM_COLOR: always
  RUST_BACKTRACE: 1
  PYTHONPATH: .
  GO111MODULE: on
  NODE_ENV: test
  ASI_TEST_MODE: true
  ASI_LOG_LEVEL: DEBUG

jobs:
  # Pre-flight checks
  pre-flight:
    runs-on: ubuntu-latest
    outputs:
      matrix: ${{ steps.set-matrix.outputs.matrix }}
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up test matrix
      id: set-matrix
      run: |
        # Determine which modules have changes
        if [ "${{ github.event_name }}" = "pull_request" ]; then
          CHANGED_FILES=$(git diff --name-only ${{ github.event.before }} ${{ github.sha }})
          echo "Changed files: $CHANGED_FILES"
          
          # Create dynamic matrix based on changed files
          MATRIX='{"include":[]}'
          
          if echo "$CHANGED_FILES" | grep -q "data_ingestion/"; then
            MATRIX=$(echo $MATRIX | jq '.include += [{"module": "data_ingestion", "languages": ["go", "rust", "python"]}]')
          fi
          
          if echo "$CHANGED_FILES" | grep -q "learning_engine/"; then
            MATRIX=$(echo $MATRIX | jq '.include += [{"module": "learning_engine", "languages": ["python", "cpp"]}]')
          fi
          
          # Add other modules...
          
          echo "matrix=$MATRIX" >> $GITHUB_OUTPUT
        else
          # Full test matrix for main branch or scheduled runs
          echo 'matrix={"include":[
            {"module": "data_ingestion", "languages": ["go", "rust", "python"]},
            {"module": "global_data_integration", "languages": ["scala", "java", "rust"]},
            {"module": "learning_engine", "languages": ["python", "cpp"]},
            {"module": "decision_engine", "languages": ["rust", "python"]},
            {"module": "self_improvement_engine", "languages": ["lisp", "python", "julia"]},
            {"module": "ui_ux_module", "languages": ["typescript", "python"]},
            {"module": "core_runtime_control", "languages": ["rust", "cpp"]},
            {"module": "deployment_orchestration", "languages": ["go", "yaml"]},
            {"module": "security_ethics_control", "languages": ["rust", "python"]}
          ]}' >> $GITHUB_OUTPUT
        fi

  # Unit tests
  unit-tests:
    needs: pre-flight
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix: ${{ fromJson(needs.pre-flight.outputs.matrix) }}
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      if: contains(matrix.languages, 'python')
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
        cache: 'pip'
    
    - name: Set up Rust
      if: contains(matrix.languages, 'rust')
      uses: actions-rs/toolchain@v1
      with:
        toolchain: stable
        components: rustfmt, clippy
        override: true
    
    - name: Set up Go
      if: contains(matrix.languages, 'go')
      uses: actions/setup-go@v4
      with:
        go-version: '1.21'
        cache: true
    
    - name: Set up Node.js
      if: contains(matrix.languages, 'typescript')
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'
    
    - name: Set up Java
      if: contains(matrix.languages, 'java')
      uses: actions/setup-java@v4
      with:
        distribution: 'temurin'
        java-version: '17'
        cache: 'maven'
    
    - name: Set up Scala
      if: contains(matrix.languages, 'scala')
      uses: coursier/setup-action@v1
      with:
        jvm: temurin:17
        apps: sbt
    
    - name: Set up Julia
      if: contains(matrix.languages, 'julia')
      uses: julia-actions/setup-julia@v1
      with:
        version: '1.9'
    
    - name: Set up SBCL (Lisp)
      if: contains(matrix.languages, 'lisp')
      run: |
        sudo apt-get update
        sudo apt-get install -y sbcl
    
    - name: Install Python dependencies
      if: contains(matrix.languages, 'python')
      run: |
        python -m pip install --upgrade pip
        find . -name "requirements*.txt" -path "./${{ matrix.module }}/*" -exec pip install -r {} \;
        pip install pytest pytest-cov pytest-benchmark pytest-xdist
    
    - name: Install Node.js dependencies
      if: contains(matrix.languages, 'typescript')
      run: |
        find . -name "package.json" -path "./${{ matrix.module }}/*" -execdir npm ci \;
    
    - name: Cache Rust dependencies
      if: contains(matrix.languages, 'rust')
      uses: actions/cache@v3
      with:
        path: |
          ~/.cargo/registry
          ~/.cargo/git
          target
        key: ${{ runner.os }}-cargo-${{ hashFiles('**/Cargo.lock') }}
    
    - name: Run unit tests
      run: |
        python testing/test_orchestrator.py \
          --test-types unit \
          --modules ${{ matrix.module }} \
          --parallel
    
    - name: Upload test results
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: unit-test-results-${{ matrix.module }}
        path: testing/results/
    
    - name: Upload coverage reports
      if: always()
      uses: codecov/codecov-action@v3
      with:
        files: ./coverage.xml,./coverage.out
        flags: unit-tests,${{ matrix.module }}
        name: codecov-${{ matrix.module }}

  # Integration tests
  integration-tests:
    needs: [pre-flight, unit-tests]
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix: ${{ fromJson(needs.pre-flight.outputs.matrix) }}
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: asi_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
      
      kafka:
        image: confluentinc/cp-kafka:latest
        env:
          KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
          KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
          KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
        ports:
          - 9092:9092
    
    steps:
    - uses: actions/checkout@v4
    
    # Setup steps similar to unit tests...
    
    - name: Start additional services
      run: |
        docker-compose -f testing/docker-compose.test.yml up -d
        sleep 30  # Wait for services to be ready
    
    - name: Run integration tests
      run: |
        python testing/test_orchestrator.py \
          --test-types integration \
          --modules ${{ matrix.module }} \
          --parallel
    
    - name: Upload test results
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: integration-test-results-${{ matrix.module }}
        path: testing/results/

  # Performance tests
  performance-tests:
    needs: [pre-flight, integration-tests]
    runs-on: ubuntu-latest
    if: github.event_name == 'schedule' || github.event_name == 'workflow_dispatch'
    strategy:
      fail-fast: false
      matrix: ${{ fromJson(needs.pre-flight.outputs.matrix) }}
    
    steps:
    - uses: actions/checkout@v4
    
    # Setup steps...
    
    - name: Run performance tests
      run: |
        python testing/test_orchestrator.py \
          --test-types performance \
          --modules ${{ matrix.module }}
    
    - name: Compare with baseline
      run: |
        python testing/performance_comparison.py \
          --current testing/results/latest/test_report.json \
          --baseline testing/performance_baseline.json \
          --threshold 10.0
    
    - name: Upload performance results
      uses: actions/upload-artifact@v3
      with:
        name: performance-test-results-${{ matrix.module }}
        path: testing/results/

  # End-to-end tests
  e2e-tests:
    needs: [pre-flight, integration-tests]
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        browser: [chromium, firefox]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'
    
    - name: Install Playwright
      run: |
        npm install -g @playwright/test
        npx playwright install ${{ matrix.browser }}
    
    - name: Start ASI system
      run: |
        docker-compose -f deployment_orchestration/docker-compose.yml up -d
        sleep 60  # Wait for system to be ready
    
    - name: Run E2E tests
      run: |
        python testing/test_orchestrator.py \
          --test-types e2e \
          --modules ui_ux_module
    
    - name: Upload E2E test results
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: e2e-test-results-${{ matrix.browser }}
        path: testing/results/

  # Security tests
  security-tests:
    needs: pre-flight
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Run security scans
      run: |
        # SAST scanning
        docker run --rm -v $(pwd):/src securecodewarrior/semgrep:latest \
          --config=auto /src
        
        # Dependency vulnerability scanning
        python testing/test_orchestrator.py \
          --test-types security \
          --modules security_ethics_control
    
    - name: Upload security results
      uses: actions/upload-artifact@v3
      with:
        name: security-test-results
        path: testing/results/

  # Generate final report
  test-report:
    needs: [unit-tests, integration-tests, performance-tests, e2e-tests, security-tests]
    if: always()
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Download all test results
      uses: actions/download-artifact@v3
      with:
        path: testing/results/
    
    - name: Generate comprehensive report
      run: |
        python testing/report_generator.py \
          --input testing/results/ \
          --output testing/final_report/ \
          --format html,json,junit
    
    - name: Upload final report
      uses: actions/upload-artifact@v3
      with:
        name: comprehensive-test-report
        path: testing/final_report/
    
    - name: Comment PR with results
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          const report = JSON.parse(fs.readFileSync('testing/final_report/summary.json', 'utf8'));
          
          const comment = `## 🧪 Test Results
          
          | Metric | Value |
          |--------|-------|
          | Total Tests | ${report.total_tests} |
          | Passed | ${report.passed} ✅ |
          | Failed | ${report.failed} ❌ |
          | Success Rate | ${report.success_rate.toFixed(1)}% |
          | Coverage | ${report.average_coverage.toFixed(1)}% |
          
          [View detailed report](${process.env.GITHUB_SERVER_URL}/${process.env.GITHUB_REPOSITORY}/actions/runs/${process.env.GITHUB_RUN_ID})
          `;
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: comment
          });
    
    - name: Fail if tests failed
      run: |
        if [ -f testing/final_report/summary.json ]; then
          FAILED=$(jq '.failed' testing/final_report/summary.json)
          ERRORS=$(jq '.errors' testing/final_report/summary.json)
          if [ "$FAILED" -gt 0 ] || [ "$ERRORS" -gt 0 ]; then
            echo "Tests failed or had errors"
            exit 1
          fi
        fi
