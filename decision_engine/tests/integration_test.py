#!/usr/bin/env python3
"""
Integration tests for the ASI Decision Engine.

Tests the complete decision-making pipeline including:
- Rule-based logic evaluation
- Neural model consultation
- Hybrid reasoning
- Fallback logic
- gRPC API integration
- Performance and metrics
"""

import asyncio
import json
import logging
import os
import sys
import time
import unittest
from pathlib import Path
from typing import Dict, List, Any
import tempfile
import uuid

import numpy as np
import requests
import grpc
from concurrent.futures import ThreadPoolExecutor

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "python-rule-engine" / "src"))

from asi_decision.logic.orchestrator import DecisionOrchestrator, DecisionRequest, DecisionType, DecisionPriority, DecisionContext
from asi_decision.rules.engine import RuleEngine, Rule, RuleType, RuleCondition, RuleAction
from asi_decision.integration.learning_engine import LearningEngineClient, InferenceRequest
from asi_decision.fallback.handler import FallbackHandler
from asi_decision.utils.config import Config
from asi_decision.utils.logger import get_logger
from asi_decision.utils.metrics import MetricsCollector, get_metrics_collector

logger = get_logger(__name__)


class TestDecisionEngineIntegration(unittest.TestCase):
    """Integration tests for the Decision Engine."""
    
    @classmethod
    def setUpClass(cls):
        """Set up test environment."""
        cls.logger = get_logger(__name__)
        cls.logger.info("Setting up Decision Engine integration tests")
        
        # Create test configuration
        cls.config = cls._create_test_config()
        
        # Initialize components
        cls.metrics = MetricsCollector()
        cls.orchestrator = None
        
        cls.logger.info("Test environment setup complete")
    
    @classmethod
    def _create_test_config(cls) -> Config:
        """Create test configuration."""
        config = Config()
        
        # Override for testing
        config.grpc.learning_engine_endpoint = "localhost:50060"
        config.decision.use_neural_models = False  # Disable for testing
        config.decision.enable_fallback_logic = True
        config.rule_engine.max_rules = 1000
        config.monitoring.enable_metrics = True
        
        return config
    
    async def asyncSetUp(self):
        """Async setup for each test."""
        self.orchestrator = DecisionOrchestrator(self.config)
        await self.orchestrator.initialize()
    
    async def asyncTearDown(self):
        """Async teardown for each test."""
        if self.orchestrator:
            await self.orchestrator.shutdown()
    
    def setUp(self):
        """Set up each test."""
        asyncio.run(self.asyncSetUp())
    
    def tearDown(self):
        """Tear down each test."""
        asyncio.run(self.asyncTearDown())
    
    def test_rule_engine_basic_functionality(self):
        """Test basic rule engine functionality."""
        self.logger.info("Testing rule engine basic functionality")
        
        # Create rule engine
        rule_engine = RuleEngine(max_rules=100)
        
        # Create test rule
        rule = Rule(
            rule_id="test_rule_1",
            name="Test Rule",
            description="A test rule for integration testing",
            rule_type=RuleType.CONDITIONAL,
            conditions=[
                RuleCondition(
                    field="score",
                    operator=">=",
                    value=0.8
                )
            ],
            action=RuleAction(
                action_type="return_value",
                parameters={"value": "approved"}
            ),
            priority=1,
            weight=1.0
        )
        
        # Add rule to engine
        self.assertTrue(rule_engine.add_rule(rule))
        
        # Test rule evaluation
        context = {"score": 0.9}
        results = rule_engine.evaluate_rules(context)
        
        self.assertEqual(len(results), 1)
        self.assertTrue(results[0].triggered)
        self.assertEqual(results[0].output, "approved")
        self.assertGreater(results[0].confidence, 0.0)
        
        self.logger.info("Rule engine basic functionality test passed")
    
    def test_complex_rule_evaluation(self):
        """Test complex rule evaluation with multiple conditions."""
        self.logger.info("Testing complex rule evaluation")
        
        rule_engine = RuleEngine(max_rules=100)
        
        # Create complex rule with multiple conditions
        rule = Rule(
            rule_id="complex_rule_1",
            name="Complex Test Rule",
            description="A complex rule with multiple conditions",
            rule_type=RuleType.LOGICAL,
            conditions=[
                RuleCondition(field="user.age", operator=">=", value=18),
                RuleCondition(field="user.credit_score", operator=">", value=700),
                RuleCondition(field="loan.amount", operator="<=", value=100000),
                RuleCondition(field="user.employment_status", operator="==", value="employed")
            ],
            action=RuleAction(
                action_type="return_value",
                parameters={"value": {"decision": "approved", "interest_rate": 3.5}}
            ),
            condition_logic="AND",
            priority=2
        )
        
        rule_engine.add_rule(rule)
        
        # Test with matching context
        context = {
            "user": {
                "age": 25,
                "credit_score": 750,
                "employment_status": "employed"
            },
            "loan": {
                "amount": 50000
            }
        }
        
        results = rule_engine.evaluate_rules(context)
        self.assertEqual(len(results), 1)
        self.assertTrue(results[0].triggered)
        
        # Test with non-matching context
        context["user"]["age"] = 17  # Below minimum age
        results = rule_engine.evaluate_rules(context)
        self.assertEqual(len(results), 1)
        self.assertFalse(results[0].triggered)
        
        self.logger.info("Complex rule evaluation test passed")
    
    async def test_decision_orchestrator_basic(self):
        """Test basic decision orchestrator functionality."""
        self.logger.info("Testing decision orchestrator basic functionality")
        
        # Create decision request
        request = DecisionRequest(
            request_id=str(uuid.uuid4()),
            decision_type=DecisionType.CLASSIFICATION,
            priority=DecisionPriority.NORMAL,
            context=DecisionContext(
                input_data={
                    "text": "This is a positive review",
                    "score": 0.85
                }
            ),
            timeout_ms=5000
        )
        
        # Make decision
        result = await self.orchestrator.make_decision(request)
        
        # Verify result
        self.assertIsNotNone(result)
        self.assertEqual(result.request_id, request.request_id)
        self.assertIsNotNone(result.decision_value)
        self.assertGreaterEqual(result.confidence, 0.0)
        self.assertLessEqual(result.confidence, 1.0)
        self.assertGreater(result.processing_time_ms, 0)
        
        self.logger.info("Decision orchestrator basic functionality test passed")
    
    async def test_fallback_logic(self):
        """Test fallback logic for low confidence decisions."""
        self.logger.info("Testing fallback logic")
        
        # Create request that should trigger fallback
        request = DecisionRequest(
            request_id=str(uuid.uuid4()),
            decision_type=DecisionType.CLASSIFICATION,
            priority=DecisionPriority.HIGH,
            context=DecisionContext(
                input_data={
                    "ambiguous_data": "unclear input",
                    "confidence_killer": True
                }
            ),
            timeout_ms=5000
        )
        
        # Make decision
        result = await self.orchestrator.make_decision(request)
        
        # Verify fallback was used if confidence is low
        if result.confidence < self.config.decision.default_confidence_threshold:
            self.assertTrue(result.used_fallback)
            self.assertIsNotNone(result.explanation)
            self.assertIn("fallback", result.explanation.lower())
        
        self.logger.info("Fallback logic test passed")
    
    def test_metrics_collection(self):
        """Test metrics collection functionality."""
        self.logger.info("Testing metrics collection")
        
        metrics = get_metrics_collector()
        
        # Record some test metrics
        from asi_decision.utils.metrics import DecisionMetrics, RuleMetrics, ModelMetrics
        
        decision_metrics = DecisionMetrics(
            decision_id="test_decision_1",
            decision_type="classification",
            processing_time_ms=50.0,
            confidence=0.85,
            rules_evaluated=3,
            models_consulted=1,
            used_fallback=False,
            memory_usage_mb=128.0
        )
        
        metrics.record_decision(decision_metrics)
        
        rule_metrics = RuleMetrics(
            rule_id="test_rule_1",
            rule_name="Test Rule",
            evaluation_time_ms=5.0,
            triggered=True,
            confidence=0.9
        )
        
        metrics.record_rule_evaluation(rule_metrics)
        
        # Get performance summary
        summary = metrics.get_performance_summary(window_minutes=60)
        
        self.assertIsInstance(summary, dict)
        self.assertIn("decisions", summary)
        self.assertIn("rules", summary)
        self.assertGreater(summary["decisions"]["total"], 0)
        
        self.logger.info("Metrics collection test passed")
    
    async def test_concurrent_decisions(self):
        """Test concurrent decision processing."""
        self.logger.info("Testing concurrent decision processing")
        
        # Create multiple decision requests
        requests = []
        for i in range(10):
            request = DecisionRequest(
                request_id=str(uuid.uuid4()),
                decision_type=DecisionType.CLASSIFICATION,
                priority=DecisionPriority.NORMAL,
                context=DecisionContext(
                    input_data={
                        "request_number": i,
                        "data": f"test_data_{i}"
                    }
                ),
                timeout_ms=5000
            )
            requests.append(request)
        
        # Process requests concurrently
        start_time = time.time()
        tasks = [self.orchestrator.make_decision(req) for req in requests]
        results = await asyncio.gather(*tasks)
        end_time = time.time()
        
        # Verify all requests were processed
        self.assertEqual(len(results), len(requests))
        
        for i, result in enumerate(results):
            self.assertIsNotNone(result)
            self.assertEqual(result.request_id, requests[i].request_id)
            self.assertGreaterEqual(result.confidence, 0.0)
        
        # Check that concurrent processing was faster than sequential
        total_time = end_time - start_time
        avg_processing_time = sum(r.processing_time_ms for r in results) / len(results) / 1000.0
        
        self.logger.info(f"Concurrent processing: {total_time:.2f}s total, {avg_processing_time:.3f}s avg per decision")
        self.logger.info("Concurrent decision processing test passed")
    
    def test_rule_management(self):
        """Test rule management operations."""
        self.logger.info("Testing rule management")
        
        rule_engine = RuleEngine(max_rules=100)
        
        # Test adding rules
        for i in range(5):
            rule = Rule(
                rule_id=f"rule_{i}",
                name=f"Rule {i}",
                description=f"Test rule number {i}",
                rule_type=RuleType.CONDITIONAL,
                conditions=[
                    RuleCondition(field="value", operator=">=", value=i * 10)
                ],
                action=RuleAction(
                    action_type="return_value",
                    parameters={"value": f"result_{i}"}
                ),
                tags=[f"tag_{i}", "test"]
            )
            self.assertTrue(rule_engine.add_rule(rule))
        
        # Test listing rules
        all_rules = rule_engine.list_rules()
        self.assertEqual(len(all_rules), 5)
        
        # Test getting rules by tag
        test_rules = rule_engine.get_rules_by_tag("test")
        self.assertEqual(len(test_rules), 5)
        
        # Test getting rules by type
        conditional_rules = rule_engine.get_rules_by_type(RuleType.CONDITIONAL)
        self.assertEqual(len(conditional_rules), 5)
        
        # Test removing rules
        self.assertTrue(rule_engine.remove_rule("rule_0"))
        remaining_rules = rule_engine.list_rules()
        self.assertEqual(len(remaining_rules), 4)
        
        # Test getting statistics
        stats = rule_engine.get_statistics()
        self.assertEqual(stats["total_rules"], 4)
        self.assertEqual(stats["enabled_rules"], 4)
        
        self.logger.info("Rule management test passed")
    
    async def test_performance_benchmarks(self):
        """Test performance benchmarks."""
        self.logger.info("Testing performance benchmarks")
        
        # Benchmark decision processing
        num_decisions = 100
        start_time = time.time()
        
        tasks = []
        for i in range(num_decisions):
            request = DecisionRequest(
                request_id=str(uuid.uuid4()),
                decision_type=DecisionType.CLASSIFICATION,
                priority=DecisionPriority.NORMAL,
                context=DecisionContext(
                    input_data={"benchmark_test": i}
                ),
                timeout_ms=1000
            )
            tasks.append(self.orchestrator.make_decision(request))
        
        results = await asyncio.gather(*tasks)
        end_time = time.time()
        
        # Calculate performance metrics
        total_time = end_time - start_time
        decisions_per_second = num_decisions / total_time
        avg_processing_time = sum(r.processing_time_ms for r in results) / len(results)
        
        self.logger.info(f"Performance benchmark results:")
        self.logger.info(f"  Total decisions: {num_decisions}")
        self.logger.info(f"  Total time: {total_time:.2f}s")
        self.logger.info(f"  Decisions per second: {decisions_per_second:.1f}")
        self.logger.info(f"  Average processing time: {avg_processing_time:.2f}ms")
        
        # Performance assertions
        self.assertGreater(decisions_per_second, 10)  # At least 10 decisions per second
        self.assertLess(avg_processing_time, 100)     # Less than 100ms average
        
        self.logger.info("Performance benchmark test passed")
    
    def test_configuration_validation(self):
        """Test configuration validation."""
        self.logger.info("Testing configuration validation")
        
        # Test valid configuration
        valid_config = Config()
        errors = valid_config.validate()
        self.assertEqual(len(errors), 0)
        
        # Test invalid configuration
        invalid_config = Config()
        invalid_config.decision.default_confidence_threshold = 1.5  # Invalid: > 1.0
        invalid_config.grpc.server_port = 99999  # Invalid: > 65535
        
        errors = invalid_config.validate()
        self.assertGreater(len(errors), 0)
        
        self.logger.info("Configuration validation test passed")


class TestDecisionEngineAPI(unittest.TestCase):
    """Test Decision Engine API endpoints."""
    
    def setUp(self):
        """Set up API tests."""
        self.base_url = "http://localhost:8080"
        self.grpc_endpoint = "localhost:50070"
    
    def test_health_endpoint(self):
        """Test health check endpoint."""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            if response.status_code == 200:
                health_data = response.json()
                self.assertIn("status", health_data)
                self.logger.info("Health endpoint test passed")
            else:
                self.logger.warning("Health endpoint not available (service may not be running)")
        except requests.exceptions.RequestException:
            self.logger.warning("Health endpoint not reachable (service may not be running)")
    
    def test_metrics_endpoint(self):
        """Test metrics endpoint."""
        try:
            response = requests.get(f"{self.base_url}/metrics", timeout=5)
            if response.status_code == 200:
                metrics_data = response.text
                self.assertIn("asi_", metrics_data)  # Should contain ASI metrics
                self.logger.info("Metrics endpoint test passed")
            else:
                self.logger.warning("Metrics endpoint not available (service may not be running)")
        except requests.exceptions.RequestException:
            self.logger.warning("Metrics endpoint not reachable (service may not be running)")


def run_integration_tests():
    """Run all integration tests."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Create test suite
    suite = unittest.TestSuite()
    
    # Add test cases
    suite.addTest(unittest.makeSuite(TestDecisionEngineIntegration))
    suite.addTest(unittest.makeSuite(TestDecisionEngineAPI))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Return success status
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_integration_tests()
    sys.exit(0 if success else 1)
