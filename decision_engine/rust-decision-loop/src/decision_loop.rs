//! High-performance decision loop implementation
//!
//! Provides real-time decision processing with sub-millisecond latency,
//! optimized for high-throughput scenarios.

use anyhow::{Context, Result};
use dashmap::DashMap;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant, SystemTime};
use tokio::sync::{mpsc, RwLock};
use tokio::time::{interval, timeout};
use tracing::{debug, error, info, instrument, warn};
use uuid::Uuid;

use crate::config::Config;
use crate::grpc_client::GrpcClient;
use crate::metrics::MetricsCollector;
use crate::performance::PerformanceMonitor;

/// Decision context for processing
#[derive(Debug, Clone)]
pub struct DecisionContext {
    pub request_id: String,
    pub input_data: HashMap<String, String>,
    pub metadata: HashMap<String, String>,
    pub timestamp: SystemTime,
    pub features: HashMap<String, f64>,
    pub risk_factors: Vec<RiskFactor>,
}

/// Risk factor assessment
#[derive(Debug, Clone)]
pub struct RiskFactor {
    pub factor_type: String,
    pub severity: f64,
    pub description: String,
}

/// Rule evaluation result
#[derive(Debug, Clone)]
pub struct RuleResult {
    pub rule_id: String,
    pub triggered: bool,
    pub confidence: f64,
    pub output: String,
    pub explanation: String,
}

/// Model prediction result
#[derive(Debug, Clone)]
pub struct ModelPrediction {
    pub model_id: String,
    pub model_type: String,
    pub prediction: String,
    pub confidence: f64,
    pub inference_time_ms: f64,
}

/// Hybrid decision result
#[derive(Debug, Clone)]
pub struct HybridDecisionResult {
    pub decision: String,
    pub confidence: f64,
    pub primary_source: String,
    pub used_fallback: bool,
}

/// Decision request structure
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct DecisionRequest {
    pub id: String,
    pub decision_type: String,
    pub priority: u8,
    pub context: serde_json::Value,
    pub timeout_ms: u64,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

/// Decision response structure
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct DecisionResponse {
    pub request_id: String,
    pub decision_id: String,
    pub decision_value: serde_json::Value,
    pub confidence: f64,
    pub processing_time_ms: f64,
    pub used_fallback: bool,
    pub explanation: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

/// Decision processing statistics
#[derive(Debug, Clone)]
pub struct DecisionStats {
    pub total_processed: u64,
    pub successful: u64,
    pub failed: u64,
    pub fallback_used: u64,
    pub avg_processing_time_ms: f64,
    pub max_processing_time_ms: f64,
    pub min_processing_time_ms: f64,
}

/// High-performance decision loop
pub struct DecisionLoop {
    config: Config,
    metrics: Arc<MetricsCollector>,
    grpc_client: Arc<GrpcClient>,
    performance_monitor: Arc<PerformanceMonitor>,
    
    // Request processing
    request_sender: mpsc::UnboundedSender<DecisionRequest>,
    request_receiver: Arc<RwLock<Option<mpsc::UnboundedReceiver<DecisionRequest>>>>,
    
    // Active decisions tracking
    active_decisions: Arc<DashMap<String, Instant>>,
    
    // Statistics
    stats: Arc<RwLock<DecisionStats>>,
    
    // Shutdown signal
    shutdown_sender: mpsc::UnboundedSender<()>,
    shutdown_receiver: Arc<RwLock<Option<mpsc::UnboundedReceiver<()>>>>,
}

impl DecisionLoop {
    /// Create a new decision loop
    pub async fn new(config: Config, metrics: Arc<MetricsCollector>) -> Result<Self> {
        let grpc_client = Arc::new(
            GrpcClient::new(&config.grpc)
                .await
                .context("Failed to create gRPC client")?
        );

        let performance_monitor = Arc::new(PerformanceMonitor::new(&config.performance));

        let (request_sender, request_receiver) = mpsc::unbounded_channel();
        let (shutdown_sender, shutdown_receiver) = mpsc::unbounded_channel();

        let stats = Arc::new(RwLock::new(DecisionStats {
            total_processed: 0,
            successful: 0,
            failed: 0,
            fallback_used: 0,
            avg_processing_time_ms: 0.0,
            max_processing_time_ms: 0.0,
            min_processing_time_ms: f64::MAX,
        }));

        Ok(Self {
            config,
            metrics,
            grpc_client,
            performance_monitor,
            request_sender,
            request_receiver: Arc::new(RwLock::new(Some(request_receiver))),
            active_decisions: Arc::new(DashMap::new()),
            stats,
            shutdown_sender,
            shutdown_receiver: Arc::new(RwLock::new(Some(shutdown_receiver))),
        })
    }

    /// Run the decision loop
    #[instrument(skip(self))]
    pub async fn run(&self) -> Result<()> {
        info!("Starting decision loop");

        // Take ownership of receivers
        let mut request_receiver = self.request_receiver
            .write()
            .await
            .take()
            .context("Request receiver already taken")?;

        let mut shutdown_receiver = self.shutdown_receiver
            .write()
            .await
            .take()
            .context("Shutdown receiver already taken")?;

        // Start worker tasks
        let worker_handles = self.start_workers().await?;

        // Start monitoring tasks
        let monitoring_handles = self.start_monitoring().await?;

        // Main processing loop
        loop {
            tokio::select! {
                // Process incoming requests
                Some(request) = request_receiver.recv() => {
                    self.handle_request(request).await;
                }

                // Handle shutdown signal
                Some(_) = shutdown_receiver.recv() => {
                    info!("Received shutdown signal");
                    break;
                }

                // Cleanup timeout requests
                _ = tokio::time::sleep(Duration::from_secs(1)) => {
                    self.cleanup_timeouts().await;
                }
            }
        }

        // Wait for workers to complete
        for handle in worker_handles {
            handle.abort();
        }

        // Wait for monitoring tasks to complete
        for handle in monitoring_handles {
            handle.abort();
        }

        info!("Decision loop stopped");
        Ok(())
    }

    /// Start worker tasks for parallel processing
    async fn start_workers(&self) -> Result<Vec<tokio::task::JoinHandle<()>>> {
        let mut handles = Vec::new();
        let worker_count = self.config.performance.worker_threads;

        for worker_id in 0..worker_count {
            let config = self.config.clone();
            let metrics = self.metrics.clone();
            let grpc_client = self.grpc_client.clone();
            let performance_monitor = self.performance_monitor.clone();
            let stats = self.stats.clone();

            let handle = tokio::spawn(async move {
                Self::worker_loop(
                    worker_id,
                    config,
                    metrics,
                    grpc_client,
                    performance_monitor,
                    stats,
                ).await;
            });

            handles.push(handle);
        }

        info!("Started {} worker tasks", worker_count);
        Ok(handles)
    }

    /// Start monitoring tasks
    async fn start_monitoring(&self) -> Result<Vec<tokio::task::JoinHandle<()>>> {
        let mut handles = Vec::new();

        // Performance monitoring
        if self.config.performance.enable_profiling {
            let performance_monitor = self.performance_monitor.clone();
            let handle = tokio::spawn(async move {
                let mut interval = interval(Duration::from_secs(10));
                loop {
                    interval.tick().await;
                    performance_monitor.collect_metrics().await;
                }
            });
            handles.push(handle);
        }

        // Statistics reporting
        let stats = self.stats.clone();
        let metrics = self.metrics.clone();
        let handle = tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(30));
            loop {
                interval.tick().await;
                let stats = stats.read().await;
                metrics.update_decision_stats(&*stats).await;
            }
        });
        handles.push(handle);

        info!("Started {} monitoring tasks", handles.len());
        Ok(handles)
    }

    /// Worker loop for processing decisions
    async fn worker_loop(
        worker_id: usize,
        config: Config,
        metrics: Arc<MetricsCollector>,
        grpc_client: Arc<GrpcClient>,
        performance_monitor: Arc<PerformanceMonitor>,
        stats: Arc<RwLock<DecisionStats>>,
    ) {
        debug!("Worker {} started", worker_id);

        // Worker-specific processing logic would go here
        // For now, this is a placeholder that demonstrates the structure

        loop {
            // Simulate processing work
            tokio::time::sleep(Duration::from_millis(100)).await;

            // Check for shutdown
            if performance_monitor.should_shutdown() {
                break;
            }
        }

        debug!("Worker {} stopped", worker_id);
    }

    /// Handle incoming decision request
    #[instrument(skip(self, request))]
    async fn handle_request(&self, request: DecisionRequest) {
        let start_time = Instant::now();
        let request_id = request.id.clone();

        // Track active decision
        self.active_decisions.insert(request_id.clone(), start_time);

        // Process with timeout
        let timeout_duration = Duration::from_millis(request.timeout_ms);
        let result = timeout(timeout_duration, self.process_decision(request)).await;

        let processing_time = start_time.elapsed();

        match result {
            Ok(Ok(response)) => {
                self.handle_successful_decision(response, processing_time).await;
            }
            Ok(Err(e)) => {
                self.handle_failed_decision(&request_id, e, processing_time).await;
            }
            Err(_) => {
                self.handle_timeout_decision(&request_id, processing_time).await;
            }
        }

        // Remove from active decisions
        self.active_decisions.remove(&request_id);
    }

    /// Process a decision request
    #[instrument(skip(self, request))]
    async fn process_decision(&self, request: DecisionRequest) -> Result<DecisionResponse> {
        let decision_id = Uuid::new_v4().to_string();
        let start_time = Instant::now();

        // 1. Validate the request
        self.validate_request(&request)?;

        // 2. Extract context and features
        let context = self.extract_context(&request).await?;

        // 3. Apply rules and consult models
        let rule_results = self.apply_rules(&context).await?;
        let model_predictions = self.consult_models(&context).await?;

        // 4. Make the final decision using hybrid reasoning
        let decision_result = self.make_hybrid_decision(&context, &rule_results, &model_predictions).await?;

        // 5. Generate explanation
        let explanation = self.generate_explanation(&decision_result, &rule_results, &model_predictions);

        let processing_time = start_time.elapsed().as_secs_f64() * 1000.0;

        Ok(DecisionResponse {
            request_id: request.id,
            decision_id,
            decision_value: serde_json::json!({"result": "approved", "score": 0.85}),
            confidence: 0.85,
            processing_time_ms: processing_time,
            used_fallback: false,
            explanation: "Decision made based on rule evaluation and model consultation".to_string(),
            timestamp: chrono::Utc::now(),
        })
    }

    /// Handle successful decision
    async fn handle_successful_decision(&self, response: DecisionResponse, processing_time: Duration) {
        let processing_ms = processing_time.as_secs_f64() * 1000.0;

        // Update statistics
        {
            let mut stats = self.stats.write().await;
            stats.total_processed += 1;
            stats.successful += 1;
            
            if response.used_fallback {
                stats.fallback_used += 1;
            }

            // Update timing statistics
            if processing_ms > stats.max_processing_time_ms {
                stats.max_processing_time_ms = processing_ms;
            }
            if processing_ms < stats.min_processing_time_ms {
                stats.min_processing_time_ms = processing_ms;
            }
            
            // Update average (simple moving average)
            stats.avg_processing_time_ms = 
                (stats.avg_processing_time_ms * (stats.total_processed - 1) as f64 + processing_ms) 
                / stats.total_processed as f64;
        }

        // Record metrics
        self.metrics.record_decision_success(
            &response.request_id,
            processing_ms,
            response.confidence,
            response.used_fallback,
        ).await;

        debug!(
            "Decision completed successfully: {} in {:.2}ms",
            response.decision_id,
            processing_ms
        );
    }

    /// Handle failed decision
    async fn handle_failed_decision(&self, request_id: &str, error: anyhow::Error, processing_time: Duration) {
        let processing_ms = processing_time.as_secs_f64() * 1000.0;

        // Update statistics
        {
            let mut stats = self.stats.write().await;
            stats.total_processed += 1;
            stats.failed += 1;
        }

        // Record metrics
        self.metrics.record_decision_failure(request_id, processing_ms, &error.to_string()).await;

        error!("Decision failed for {}: {} (took {:.2}ms)", request_id, error, processing_ms);
    }

    /// Handle timeout decision
    async fn handle_timeout_decision(&self, request_id: &str, processing_time: Duration) {
        let processing_ms = processing_time.as_secs_f64() * 1000.0;

        // Update statistics
        {
            let mut stats = self.stats.write().await;
            stats.total_processed += 1;
            stats.failed += 1;
        }

        // Record metrics
        self.metrics.record_decision_timeout(request_id, processing_ms).await;

        warn!("Decision timed out for {} after {:.2}ms", request_id, processing_ms);
    }

    /// Clean up timed out requests
    async fn cleanup_timeouts(&self) {
        let now = Instant::now();
        let timeout_threshold = Duration::from_secs(60); // 1 minute timeout

        let mut to_remove = Vec::new();
        for entry in self.active_decisions.iter() {
            if now.duration_since(*entry.value()) > timeout_threshold {
                to_remove.push(entry.key().clone());
            }
        }

        for request_id in to_remove {
            self.active_decisions.remove(&request_id);
            warn!("Cleaned up timed out request: {}", request_id);
        }
    }

    /// Submit a decision request
    pub async fn submit_request(&self, request: DecisionRequest) -> Result<()> {
        self.request_sender
            .send(request)
            .context("Failed to submit decision request")?;
        Ok(())
    }

    /// Get current statistics
    pub async fn get_stats(&self) -> DecisionStats {
        self.stats.read().await.clone()
    }

    /// Shutdown the decision loop
    pub async fn shutdown(&self) -> Result<()> {
        info!("Shutting down decision loop");
        
        self.shutdown_sender
            .send(())
            .context("Failed to send shutdown signal")?;
        
        // Wait for active decisions to complete (with timeout)
        let shutdown_timeout = Duration::from_secs(30);
        let start = Instant::now();
        
        while !self.active_decisions.is_empty() && start.elapsed() < shutdown_timeout {
            tokio::time::sleep(Duration::from_millis(100)).await;
        }
        
        if !self.active_decisions.is_empty() {
            warn!("Shutdown with {} active decisions remaining", self.active_decisions.len());
        }
        
        info!("Decision loop shutdown complete");
        Ok(())
    }

    /// Validate the incoming decision request
    fn validate_request(&self, request: &DecisionRequest) -> Result<()> {
        if request.context.is_empty() {
            return Err(anyhow::anyhow!("Request context cannot be empty"));
        }

        if request.timeout_ms == 0 {
            return Err(anyhow::anyhow!("Timeout must be greater than 0"));
        }

        if request.timeout_ms > 30000 {
            return Err(anyhow::anyhow!("Timeout cannot exceed 30 seconds"));
        }

        Ok(())
    }

    /// Extract and enrich context from the request
    async fn extract_context(&self, request: &DecisionRequest) -> Result<DecisionContext> {
        let mut context = DecisionContext {
            request_id: request.request_id.clone(),
            input_data: request.context.clone(),
            metadata: request.metadata.clone(),
            timestamp: std::time::SystemTime::now(),
            features: std::collections::HashMap::new(),
            risk_factors: Vec::new(),
        };

        // Extract features from input data
        context.features = self.extract_features(&request.context).await?;

        // Assess risk factors
        context.risk_factors = self.assess_risk_factors(&context).await?;

        Ok(context)
    }

    /// Extract features from input data
    async fn extract_features(&self, input_data: &std::collections::HashMap<String, String>) -> Result<std::collections::HashMap<String, f64>> {
        let mut features = std::collections::HashMap::new();

        // Extract numerical features
        for (key, value) in input_data {
            if let Ok(num_value) = value.parse::<f64>() {
                features.insert(format!("num_{}", key), num_value);
            }
        }

        // Extract categorical features (simplified)
        features.insert("categorical_count".to_string(), input_data.len() as f64);

        // Extract temporal features
        features.insert("timestamp_hour".to_string(), chrono::Utc::now().hour() as f64);
        features.insert("timestamp_day_of_week".to_string(), chrono::Utc::now().weekday().number_from_monday() as f64);

        Ok(features)
    }

    /// Assess risk factors from context
    async fn assess_risk_factors(&self, context: &DecisionContext) -> Result<Vec<RiskFactor>> {
        let mut risk_factors = Vec::new();

        // Financial risk assessment
        if let Some(amount) = context.features.get("num_amount") {
            if *amount > 10000.0 {
                risk_factors.push(RiskFactor {
                    factor_type: "financial".to_string(),
                    severity: if *amount > 100000.0 { 0.9 } else { 0.6 },
                    description: format!("High financial amount: ${:.2}", amount),
                });
            }
        }

        // Time-based risk assessment
        let current_hour = chrono::Utc::now().hour();
        if current_hour < 6 || current_hour > 22 {
            risk_factors.push(RiskFactor {
                factor_type: "temporal".to_string(),
                severity: 0.4,
                description: "Off-hours operation".to_string(),
            });
        }

        // Data completeness risk
        let completeness = context.features.len() as f64 / 10.0; // Assume 10 expected features
        if completeness < 0.5 {
            risk_factors.push(RiskFactor {
                factor_type: "data_quality".to_string(),
                severity: 1.0 - completeness,
                description: format!("Low data completeness: {:.1%}", completeness),
            });
        }

        Ok(risk_factors)
    }

    /// Apply rule-based logic
    async fn apply_rules(&self, context: &DecisionContext) -> Result<Vec<RuleResult>> {
        let mut rule_results = Vec::new();

        // Safety rules
        for risk_factor in &context.risk_factors {
            if risk_factor.severity > 0.8 {
                rule_results.push(RuleResult {
                    rule_id: format!("safety_{}", risk_factor.factor_type),
                    triggered: true,
                    confidence: risk_factor.severity,
                    output: "reject".to_string(),
                    explanation: format!("High risk detected: {}", risk_factor.description),
                });
            }
        }

        // Business logic rules
        if let Some(amount) = context.features.get("num_amount") {
            if *amount > 1000.0 && *amount < 10000.0 {
                rule_results.push(RuleResult {
                    rule_id: "business_approval".to_string(),
                    triggered: true,
                    confidence: 0.8,
                    output: "approve_with_review".to_string(),
                    explanation: "Amount requires review approval".to_string(),
                });
            }
        }

        // Time-based rules
        let current_hour = chrono::Utc::now().hour();
        if current_hour >= 9 && current_hour <= 17 {
            rule_results.push(RuleResult {
                rule_id: "business_hours".to_string(),
                triggered: true,
                confidence: 0.9,
                output: "normal_processing".to_string(),
                explanation: "Within business hours".to_string(),
            });
        }

        Ok(rule_results)
    }

    /// Consult neural models for predictions
    async fn consult_models(&self, context: &DecisionContext) -> Result<Vec<ModelPrediction>> {
        let mut predictions = Vec::new();

        // Simulate model consultation via gRPC
        // In real implementation, this would call the Learning Engine

        // Risk assessment model
        let risk_score = self.calculate_risk_score(context);
        predictions.push(ModelPrediction {
            model_id: "risk_assessment_v1".to_string(),
            model_type: "classification".to_string(),
            prediction: if risk_score > 0.7 { "high_risk".to_string() } else { "low_risk".to_string() },
            confidence: 0.85,
            inference_time_ms: 15.0,
        });

        // Fraud detection model
        let fraud_score = self.calculate_fraud_score(context);
        predictions.push(ModelPrediction {
            model_id: "fraud_detection_v2".to_string(),
            model_type: "binary_classification".to_string(),
            prediction: if fraud_score > 0.5 { "fraud".to_string() } else { "legitimate".to_string() },
            confidence: 0.92,
            inference_time_ms: 8.0,
        });

        // Recommendation model
        let recommendation = self.generate_recommendation(context);
        predictions.push(ModelPrediction {
            model_id: "recommendation_v1".to_string(),
            model_type: "recommendation".to_string(),
            prediction: recommendation,
            confidence: 0.78,
            inference_time_ms: 12.0,
        });

        Ok(predictions)
    }

    /// Calculate risk score based on context
    fn calculate_risk_score(&self, context: &DecisionContext) -> f64 {
        let mut risk_score = 0.0;

        // Aggregate risk factors
        for risk_factor in &context.risk_factors {
            risk_score += risk_factor.severity * 0.3;
        }

        // Feature-based risk
        if let Some(amount) = context.features.get("num_amount") {
            risk_score += (amount / 100000.0).min(0.5);
        }

        // Time-based risk
        let current_hour = chrono::Utc::now().hour();
        if current_hour < 6 || current_hour > 22 {
            risk_score += 0.2;
        }

        risk_score.min(1.0)
    }

    /// Calculate fraud score based on context
    fn calculate_fraud_score(&self, context: &DecisionContext) -> f64 {
        let mut fraud_score = 0.0;

        // Simple heuristics for fraud detection
        if let Some(amount) = context.features.get("num_amount") {
            if *amount > 50000.0 {
                fraud_score += 0.3;
            }
        }

        // Check for suspicious patterns
        if context.features.len() < 3 {
            fraud_score += 0.2;
        }

        // Time-based fraud indicators
        let current_hour = chrono::Utc::now().hour();
        if current_hour < 3 || current_hour > 23 {
            fraud_score += 0.3;
        }

        fraud_score.min(1.0)
    }

    /// Generate recommendation based on context
    fn generate_recommendation(&self, context: &DecisionContext) -> String {
        let risk_score = self.calculate_risk_score(context);
        let fraud_score = self.calculate_fraud_score(context);

        if fraud_score > 0.7 {
            "reject".to_string()
        } else if risk_score > 0.8 {
            "manual_review".to_string()
        } else if risk_score > 0.5 {
            "approve_with_conditions".to_string()
        } else {
            "approve".to_string()
        }
    }

    /// Make hybrid decision combining rules and models
    async fn make_hybrid_decision(
        &self,
        context: &DecisionContext,
        rule_results: &[RuleResult],
        model_predictions: &[ModelPrediction],
    ) -> Result<HybridDecisionResult> {
        // Check for high-confidence rule overrides
        for rule in rule_results {
            if rule.triggered && rule.confidence > 0.9 {
                return Ok(HybridDecisionResult {
                    decision: rule.output.clone(),
                    confidence: rule.confidence,
                    primary_source: "rules".to_string(),
                    used_fallback: false,
                });
            }
        }

        // Aggregate model predictions
        let mut model_votes: std::collections::HashMap<String, f64> = std::collections::HashMap::new();
        let mut total_confidence = 0.0;

        for prediction in model_predictions {
            *model_votes.entry(prediction.prediction.clone()).or_insert(0.0) += prediction.confidence;
            total_confidence += prediction.confidence;
        }

        // Find best model prediction
        let best_model_prediction = model_votes
            .iter()
            .max_by(|a, b| a.1.partial_cmp(b.1).unwrap_or(std::cmp::Ordering::Equal))
            .map(|(decision, confidence)| (decision.clone(), *confidence / total_confidence));

        // Combine with rule confidence
        if let Some((model_decision, model_confidence)) = best_model_prediction {
            let rule_confidence = rule_results
                .iter()
                .filter(|r| r.triggered)
                .map(|r| r.confidence)
                .fold(0.0, f64::max);

            let combined_confidence = (model_confidence * 0.7 + rule_confidence * 0.3).min(1.0);

            Ok(HybridDecisionResult {
                decision: model_decision,
                confidence: combined_confidence,
                primary_source: "hybrid".to_string(),
                used_fallback: false,
            })
        } else {
            // Fallback to conservative decision
            Ok(HybridDecisionResult {
                decision: "manual_review".to_string(),
                confidence: 0.3,
                primary_source: "fallback".to_string(),
                used_fallback: true,
            })
        }
    }

    /// Generate explanation for the decision
    fn generate_explanation(
        &self,
        decision_result: &HybridDecisionResult,
        rule_results: &[RuleResult],
        model_predictions: &[ModelPrediction],
    ) -> String {
        let mut explanation = format!(
            "Decision: {} (confidence: {:.2}, source: {})",
            decision_result.decision, decision_result.confidence, decision_result.primary_source
        );

        // Add rule explanations
        let triggered_rules: Vec<_> = rule_results.iter().filter(|r| r.triggered).collect();
        if !triggered_rules.is_empty() {
            explanation.push_str(&format!("\nTriggered rules ({}): ", triggered_rules.len()));
            for rule in triggered_rules {
                explanation.push_str(&format!("\n- {}: {} (confidence: {:.2})",
                    rule.rule_id, rule.explanation, rule.confidence));
            }
        }

        // Add model predictions
        if !model_predictions.is_empty() {
            explanation.push_str(&format!("\nModel predictions ({}): ", model_predictions.len()));
            for prediction in model_predictions {
                explanation.push_str(&format!("\n- {}: {} (confidence: {:.2})",
                    prediction.model_id, prediction.prediction, prediction.confidence));
            }
        }

        explanation
    }
}
