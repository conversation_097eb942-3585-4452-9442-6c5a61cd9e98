//! ASI Decision Loop - High-performance real-time decision processing
//!
//! This module provides ultra-low latency decision processing for the ASI Decision Engine,
//! optimized for real-time scenarios requiring sub-millisecond response times.

use anyhow::Result;
use clap::Parser;
use std::sync::Arc;
use tokio::signal;
use tracing::{info, error};

mod config;
mod decision_loop;
mod grpc_client;
mod metrics;
mod performance;

use config::Config;
use decision_loop::DecisionLoop;
use metrics::MetricsCollector;

#[derive(Parser)]
#[command(name = "asi-decision-loop")]
#[command(about = "High-performance real-time decision loop for ASI Decision Engine")]
struct Cli {
    /// Configuration file path
    #[arg(short, long, default_value = "config.yaml")]
    config: String,

    /// Log level
    #[arg(short, long, default_value = "info")]
    log_level: String,

    /// Enable performance profiling
    #[arg(long)]
    profile: bool,

    /// Number of worker threads
    #[arg(short, long)]
    workers: Option<usize>,

    /// Enable metrics collection
    #[arg(long, default_value = "true")]
    metrics: bool,
}

#[tokio::main]
async fn main() -> Result<()> {
    let cli = Cli::parse();

    // Initialize tracing
    init_tracing(&cli.log_level)?;

    info!("Starting ASI Decision Loop");

    // Load configuration
    let config = Config::load(&cli.config).await?;
    info!("Configuration loaded from {}", cli.config);

    // Override config with CLI arguments
    let mut config = config;
    if let Some(workers) = cli.workers {
        config.performance.worker_threads = workers;
    }
    config.monitoring.enable_metrics = cli.metrics;
    config.performance.enable_profiling = cli.profile;

    // Validate configuration
    config.validate()?;

    // Initialize metrics collector
    let metrics = Arc::new(MetricsCollector::new(&config)?);

    // Initialize decision loop
    let decision_loop = Arc::new(DecisionLoop::new(config.clone(), metrics.clone()).await?);

    // Start the decision loop
    let loop_handle = {
        let decision_loop = decision_loop.clone();
        tokio::spawn(async move {
            if let Err(e) = decision_loop.run().await {
                error!("Decision loop error: {}", e);
            }
        })
    };

    // Start metrics server if enabled
    let metrics_handle = if config.monitoring.enable_metrics {
        let metrics = metrics.clone();
        let port = config.monitoring.metrics_port;
        Some(tokio::spawn(async move {
            if let Err(e) = start_metrics_server(metrics, port).await {
                error!("Metrics server error: {}", e);
            }
        }))
    } else {
        None
    };

    // Wait for shutdown signal
    info!("Decision loop started, waiting for shutdown signal...");
    match signal::ctrl_c().await {
        Ok(()) => {
            info!("Received shutdown signal");
        }
        Err(err) => {
            error!("Unable to listen for shutdown signal: {}", err);
        }
    }

    // Graceful shutdown
    info!("Shutting down decision loop...");
    decision_loop.shutdown().await?;

    // Cancel tasks
    loop_handle.abort();
    if let Some(handle) = metrics_handle {
        handle.abort();
    }

    info!("Decision loop shutdown complete");
    Ok(())
}

fn init_tracing(log_level: &str) -> Result<()> {
    use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt, EnvFilter};

    let filter = EnvFilter::try_from_default_env()
        .unwrap_or_else(|_| EnvFilter::new(log_level));

    tracing_subscriber::registry()
        .with(
            tracing_subscriber::fmt::layer()
                .json()
                .with_target(true)
                .with_thread_ids(true)
                .with_file(true)
                .with_line_number(true),
        )
        .with(filter)
        .init();

    Ok(())
}

async fn start_metrics_server(
    metrics: Arc<MetricsCollector>,
    port: u16,
) -> Result<()> {
    use hyper::{
        service::{make_service_fn, service_fn},
        Body, Request, Response, Server,
    };
    use std::convert::Infallible;
    use std::net::SocketAddr;

    let make_svc = make_service_fn(move |_conn| {
        let metrics = metrics.clone();
        async move {
            Ok::<_, Infallible>(service_fn(move |req: Request<Body>| {
                let metrics = metrics.clone();
                async move {
                    match req.uri().path() {
                        "/metrics" => {
                            let metrics_output = metrics.export_prometheus().await;
                            Ok::<_, Infallible>(
                                Response::builder()
                                    .header("content-type", "text/plain; version=0.0.4")
                                    .body(Body::from(metrics_output))
                                    .unwrap(),
                            )
                        }
                        "/health" => {
                            let health_status = serde_json::json!({
                                "status": "healthy",
                                "timestamp": chrono::Utc::now().to_rfc3339(),
                                "service": "asi-decision-loop"
                            });
                            Ok::<_, Infallible>(
                                Response::builder()
                                    .header("content-type", "application/json")
                                    .body(Body::from(health_status.to_string()))
                                    .unwrap(),
                            )
                        }
                        _ => Ok::<_, Infallible>(
                            Response::builder()
                                .status(404)
                                .body(Body::from("Not Found"))
                                .unwrap(),
                        ),
                    }
                }
            }))
        }
    });

    let addr = SocketAddr::from(([0, 0, 0, 0], port));
    let server = Server::bind(&addr).serve(make_svc);

    info!("Metrics server listening on http://{}", addr);

    if let Err(e) = server.await {
        error!("Metrics server error: {}", e);
    }

    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_config_loading() {
        // Test configuration loading
        let config = Config::default();
        assert!(config.validate().is_ok());
    }

    #[test]
    fn test_cli_parsing() {
        use clap::Parser;
        
        let cli = Cli::try_parse_from(&[
            "asi-decision-loop",
            "--config", "test.yaml",
            "--log-level", "debug",
            "--workers", "4",
            "--profile"
        ]).unwrap();
        
        assert_eq!(cli.config, "test.yaml");
        assert_eq!(cli.log_level, "debug");
        assert_eq!(cli.workers, Some(4));
        assert!(cli.profile);
    }
}
