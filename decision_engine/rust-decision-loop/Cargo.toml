[package]
name = "asi-decision-loop"
version = "1.0.0"
edition = "2021"
authors = ["ASI System Team <<EMAIL>>"]
description = "High-performance real-time decision loop for the ASI Decision Engine"
license = "MIT"
repository = "https://github.com/asi-system/decision-engine"
keywords = ["ai", "decision", "real-time", "asi"]
categories = ["science", "algorithms"]

[dependencies]
# Async runtime
tokio = { version = "1.28", features = ["full"] }
tokio-util = "0.7"
futures = "0.3"

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
serde_yaml = "0.9"

# gRPC and networking
tonic = "0.9"
prost = "0.11"
tower = "0.4"
hyper = "0.14"

# Logging and tracing
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["json", "env-filter"] }
tracing-opentelemetry = "0.19"
opentelemetry = "0.19"
opentelemetry-jaeger = "0.18"

# Metrics
prometheus = "0.13"
metrics = "0.21"
metrics-prometheus = "0.6"

# Configuration
config = "0.13"
clap = { version = "4.3", features = ["derive"] }

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Data structures
dashmap = "5.4"
crossbeam = "0.8"
parking_lot = "0.12"

# Time and scheduling
chrono = { version = "0.4", features = ["serde"] }
cron = "0.12"

# Performance
rayon = "1.7"
num_cpus = "1.15"

# Memory management
bytes = "1.4"
smallvec = "1.10"

# Hashing
ahash = "0.8"
fnv = "1.0"

# UUID generation
uuid = { version = "1.3", features = ["v4", "serde"] }

# HTTP client
reqwest = { version = "0.11", features = ["json"] }

# Database (optional)
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "postgres", "chrono", "uuid"], optional = true }

# Redis (optional)
redis = { version = "0.23", features = ["tokio-comp"], optional = true }

# Kafka (optional)
rdkafka = { version = "0.33", features = ["cmake-build"], optional = true }

[build-dependencies]
tonic-build = "0.9"

[features]
default = ["metrics", "tracing"]
database = ["sqlx"]
cache = ["redis"]
kafka = ["rdkafka"]
full = ["database", "cache", "kafka"]

[profile.release]
lto = true
codegen-units = 1
panic = "abort"
strip = true

[profile.dev]
debug = true
opt-level = 0

[[bin]]
name = "decision-loop"
path = "src/main.rs"

[lib]
name = "asi_decision_loop"
path = "src/lib.rs"
