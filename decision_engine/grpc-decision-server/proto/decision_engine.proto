syntax = "proto3";

package asi.decision_engine;

import "google/protobuf/timestamp.proto";
import "google/protobuf/any.proto";
import "google/protobuf/struct.proto";

option go_package = "github.com/asi-system/decision-engine/proto";
option java_package = "com.asi.decision_engine";
option java_outer_classname = "DecisionEngineProto";

// Decision Engine Service for ASI System
service DecisionEngineService {
  // Make a decision based on context and rules
  rpc MakeDecision(DecisionRequest) returns (DecisionResponse);

  // Stream decision requests for real-time processing
  rpc StreamDecisions(stream StreamDecisionRequest) returns (stream StreamDecisionResponse);

  // Evaluate rules against context
  rpc EvaluateRules(RuleEvaluationRequest) returns (RuleEvaluationResponse);

  // Manage decision rules
  rpc CreateRule(CreateRuleRequest) returns (CreateRuleResponse);
  rpc UpdateRule(UpdateRuleRequest) returns (UpdateRuleResponse);
  rpc DeleteRule(DeleteRuleRequest) returns (DeleteRuleResponse);
  rpc ListRules(ListRulesRequest) returns (ListRulesResponse);

  // Get decision history and analytics
  rpc GetDecisionHistory(DecisionHistoryRequest) returns (DecisionHistoryResponse);
  rpc GetDecisionMetrics(DecisionMetricsRequest) returns (DecisionMetricsResponse);

  // Health check
  rpc HealthCheck(HealthCheckRequest) returns (HealthCheckResponse);
}

// Decision types supported by the engine
enum DecisionType {
  DECISION_TYPE_UNSPECIFIED = 0;
  DECISION_TYPE_CLASSIFICATION = 1;
  DECISION_TYPE_REGRESSION = 2;
  DECISION_TYPE_RECOMMENDATION = 3;
  DECISION_TYPE_OPTIMIZATION = 4;
  DECISION_TYPE_CONTROL = 5;
  DECISION_TYPE_PLANNING = 6;
}

// Decision priority levels
enum DecisionPriority {
  PRIORITY_UNSPECIFIED = 0;
  PRIORITY_LOW = 1;
  PRIORITY_NORMAL = 2;
  PRIORITY_HIGH = 3;
  PRIORITY_CRITICAL = 4;
  PRIORITY_EMERGENCY = 5;
}

// Decision request
message DecisionRequest {
  string request_id = 1;
  DecisionType decision_type = 2;
  DecisionPriority priority = 3;
  DecisionContext context = 4;
  DecisionConfig config = 5;
  map<string, string> metadata = 6;
  google.protobuf.Timestamp timestamp = 7;
}

// Decision context containing all relevant information
message DecisionContext {
  // Input data for decision making
  map<string, google.protobuf.Any> input_data = 1;

  // Historical context
  repeated HistoricalDecision history = 2;

  // Environmental context
  EnvironmentalContext environment = 3;

  // User/agent context
  AgentContext agent = 4;

  // System state
  SystemState system_state = 5;

  // Constraints and requirements
  repeated Constraint constraints = 6;
}

// Environmental context
message EnvironmentalContext {
  google.protobuf.Timestamp timestamp = 1;
  string location = 2;
  map<string, float> sensor_data = 3;
  map<string, string> external_conditions = 4;
  float uncertainty_level = 5;
}

// Agent context
message AgentContext {
  string agent_id = 1;
  string agent_type = 2;
  map<string, float> capabilities = 3;
  map<string, string> preferences = 4;
  float trust_level = 5;
  repeated string roles = 6;
}

// System state
message SystemState {
  float cpu_usage = 1;
  float memory_usage = 2;
  float network_latency = 3;
  int32 active_connections = 4;
  map<string, string> service_status = 5;
  repeated string active_models = 6;
}

// Constraint definition
message Constraint {
  string name = 1;
  ConstraintType type = 2;
  google.protobuf.Any value = 3;
  float weight = 4;
  bool is_hard_constraint = 5;
}

// Constraint types
enum ConstraintType {
  CONSTRAINT_TYPE_UNSPECIFIED = 0;
  CONSTRAINT_TYPE_NUMERIC = 1;
  CONSTRAINT_TYPE_CATEGORICAL = 2;
  CONSTRAINT_TYPE_TEMPORAL = 3;
  CONSTRAINT_TYPE_LOGICAL = 4;
  CONSTRAINT_TYPE_RESOURCE = 5;
}

// Decision configuration
message DecisionConfig {
  bool use_neural_models = 1;
  bool use_rule_engine = 2;
  bool use_fallback_logic = 3;
  float confidence_threshold = 4;
  int32 max_processing_time_ms = 5;
  repeated string preferred_models = 6;
  map<string, google.protobuf.Any> model_params = 7;
  bool enable_explanation = 8;
  bool enable_logging = 9;
}

// Decision response
message DecisionResponse {
  string request_id = 1;
  bool success = 2;
  DecisionResult result = 3;
  string error_message = 4;
  DecisionMetrics metrics = 5;
  DecisionExplanation explanation = 6;
}

// Decision result
message DecisionResult {
  DecisionType decision_type = 1;
  google.protobuf.Any decision_value = 2;
  float confidence = 3;
  repeated Alternative alternatives = 4;
  map<string, float> scores = 5;
  repeated string applied_rules = 6;
  repeated string consulted_models = 7;
  bool used_fallback = 8;
  google.protobuf.Timestamp timestamp = 9;
}

// Alternative decision options
message Alternative {
  string name = 1;
  google.protobuf.Any value = 2;
  float score = 3;
  float confidence = 4;
  string reasoning = 5;
}

// Decision metrics
message DecisionMetrics {
  int64 processing_time_ms = 1;
  int64 rule_evaluation_time_ms = 2;
  int64 model_inference_time_ms = 3;
  int32 rules_evaluated = 4;
  int32 models_consulted = 5;
  float memory_usage_mb = 6;
  float cpu_usage_percent = 7;
  google.protobuf.Timestamp timestamp = 8;
}

// Decision explanation for transparency
message DecisionExplanation {
  string summary = 1;
  repeated ReasoningStep reasoning_steps = 2;
  repeated RuleContribution rule_contributions = 3;
  repeated ModelContribution model_contributions = 4;
  map<string, float> feature_importance = 5;
  string confidence_explanation = 6;
}

// Reasoning step in decision process
message ReasoningStep {
  int32 step_number = 1;
  string description = 2;
  string component = 3;
  google.protobuf.Any input = 4;
  google.protobuf.Any output = 5;
  float confidence = 6;
}

// Rule contribution to decision
message RuleContribution {
  string rule_id = 1;
  string rule_name = 2;
  bool triggered = 3;
  float weight = 4;
  float contribution_score = 5;
  string explanation = 6;
}

// Model contribution to decision
message ModelContribution {
  string model_id = 1;
  string model_type = 2;
  google.protobuf.Any prediction = 3;
  float confidence = 4;
  float weight = 5;
  int64 inference_time_ms = 6;
}

// Historical decision for context
message HistoricalDecision {
  string decision_id = 1;
  DecisionType decision_type = 2;
  google.protobuf.Any decision_value = 3;
  float confidence = 4;
  google.protobuf.Timestamp timestamp = 5;
  string outcome = 6;
  float outcome_score = 7;
}

// Stream decision request
message StreamDecisionRequest {
  string stream_id = 1;
  DecisionRequest decision_request = 2;
  bool is_final = 3;
}

// Stream decision response
message StreamDecisionResponse {
  string stream_id = 1;
  DecisionResponse decision_response = 2;
  StreamStatus status = 3;
  string message = 4;
}

// Stream status
enum StreamStatus {
  STREAM_STATUS_UNSPECIFIED = 0;
  STREAM_STATUS_PROCESSING = 1;
  STREAM_STATUS_COMPLETED = 2;
  STREAM_STATUS_ERROR = 3;
  STREAM_STATUS_CANCELLED = 4;
}

// Rule evaluation request
message RuleEvaluationRequest {
  repeated string rule_ids = 1;
  DecisionContext context = 2;
  bool explain_evaluation = 3;
}

// Rule evaluation response
message RuleEvaluationResponse {
  bool success = 1;
  repeated RuleEvaluationResult results = 2;
  string error_message = 3;
}

// Rule evaluation result
message RuleEvaluationResult {
  string rule_id = 1;
  bool triggered = 2;
  float confidence = 3;
  google.protobuf.Any output = 4;
  string explanation = 5;
  int64 evaluation_time_ms = 6;
}

// Health check request
message HealthCheckRequest {
  string service = 1;
}

// Health check response
message HealthCheckResponse {
  bool healthy = 1;
  string status = 2;
  map<string, string> details = 3;
  google.protobuf.Timestamp timestamp = 4;
}

// Rule management messages
message Rule {
  string rule_id = 1;
  string name = 2;
  string description = 3;
  RuleType type = 4;
  string condition = 5;
  google.protobuf.Any action = 6;
  float weight = 7;
  bool enabled = 8;
  int32 priority = 9;
  repeated string tags = 10;
  google.protobuf.Timestamp created_at = 11;
  google.protobuf.Timestamp updated_at = 12;
  string author = 13;
  int32 version = 14;
}

enum RuleType {
  RULE_TYPE_UNSPECIFIED = 0;
  RULE_TYPE_CONDITIONAL = 1;
  RULE_TYPE_THRESHOLD = 2;
  RULE_TYPE_PATTERN = 3;
  RULE_TYPE_TEMPORAL = 4;
  RULE_TYPE_LOGICAL = 5;
  RULE_TYPE_ML_BASED = 6;
}

message CreateRuleRequest {
  Rule rule = 1;
  bool validate_only = 2;
}

message CreateRuleResponse {
  bool success = 1;
  string rule_id = 2;
  string error_message = 3;
  repeated string validation_errors = 4;
}

message UpdateRuleRequest {
  string rule_id = 1;
  Rule rule = 2;
  bool validate_only = 3;
}

message UpdateRuleResponse {
  bool success = 1;
  string error_message = 2;
  repeated string validation_errors = 3;
}

message DeleteRuleRequest {
  string rule_id = 1;
  bool force = 2;
}

message DeleteRuleResponse {
  bool success = 1;
  string error_message = 2;
}

message ListRulesRequest {
  repeated RuleType rule_types = 1;
  repeated string tags = 2;
  bool enabled_only = 3;
  int32 page_size = 4;
  string page_token = 5;
}

message ListRulesResponse {
  repeated Rule rules = 1;
  string next_page_token = 2;
  int32 total_count = 3;
}

// Decision history and metrics
message DecisionHistoryRequest {
  string agent_id = 1;
  DecisionType decision_type = 2;
  google.protobuf.Timestamp start_time = 3;
  google.protobuf.Timestamp end_time = 4;
  int32 page_size = 5;
  string page_token = 6;
}

message DecisionHistoryResponse {
  repeated HistoricalDecision decisions = 1;
  string next_page_token = 2;
  int32 total_count = 3;
}

message DecisionMetricsRequest {
  google.protobuf.Timestamp start_time = 1;
  google.protobuf.Timestamp end_time = 2;
  repeated string metric_types = 3;
  string aggregation = 4;
}

message DecisionMetricsResponse {
  map<string, MetricValue> metrics = 1;
  google.protobuf.Timestamp timestamp = 2;
}

message MetricValue {
  double value = 1;
  string unit = 2;
  map<string, string> labels = 3;
}
