"""
Learning Engine integration for the ASI Decision Engine.

Provides gRPC client for communicating with the Learning Engine,
model inference requests, and neural model consultation for hybrid reasoning.
"""

import asyncio
import json
import time
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass
from datetime import datetime

import grpc
from grpc import aio as grpc_aio

from ..utils.logger import get_logger, PerformanceLogger
from ..utils.metrics import ModelMetrics, get_metrics_collector
from ..utils.config import Config

logger = get_logger(__name__)


@dataclass
class ModelPrediction:
    """Represents a prediction from a neural model."""
    model_id: str
    model_type: str
    prediction: Any
    confidence: float
    inference_time_ms: float
    metadata: Dict[str, Any]
    timestamp: datetime


@dataclass
class InferenceRequest:
    """Request for model inference."""
    model_id: str
    model_version: Optional[str] = None
    input_data: Dict[str, Any] = None
    config: Dict[str, Any] = None
    timeout_ms: int = 5000


class LearningEngineClient:
    """gRPC client for the Learning Engine."""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = get_logger(__name__)
        self.metrics = get_metrics_collector()
        
        # Connection settings
        self.endpoint = config.grpc.learning_engine_endpoint
        self.timeout_ms = config.grpc.learning_engine_timeout_ms
        self.retry_attempts = config.grpc.learning_engine_retry_attempts
        
        # gRPC channel and stub
        self.channel: Optional[grpc_aio.Channel] = None
        self.stub = None
        
        # Connection state
        self.connected = False
        self.last_health_check = None
        
        self.logger.info(f"Learning Engine client initialized for {self.endpoint}")
    
    async def connect(self) -> bool:
        """Establish connection to the Learning Engine."""
        try:
            # Create gRPC channel
            self.channel = grpc_aio.insecure_channel(
                self.endpoint,
                options=[
                    ('grpc.keepalive_time_ms', self.config.grpc.keepalive_time_ms),
                    ('grpc.keepalive_timeout_ms', self.config.grpc.keepalive_timeout_ms),
                    ('grpc.keepalive_permit_without_calls', self.config.grpc.keepalive_permit_without_calls),
                    ('grpc.max_receive_message_length', self.config.grpc.max_message_length),
                    ('grpc.max_send_message_length', self.config.grpc.max_message_length),
                ]
            )
            
            # Create stub (in real implementation, this would use generated protobuf classes)
            # self.stub = learning_engine_pb2_grpc.LearningEngineServiceStub(self.channel)
            
            # Test connection with health check
            health_ok = await self.health_check()
            if health_ok:
                self.connected = True
                self.logger.info("Successfully connected to Learning Engine")
                return True
            else:
                self.logger.error("Health check failed after connection")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to connect to Learning Engine: {e}")
            return False
    
    async def disconnect(self):
        """Close connection to the Learning Engine."""
        if self.channel:
            await self.channel.close()
            self.connected = False
            self.logger.info("Disconnected from Learning Engine")
    
    async def health_check(self) -> bool:
        """Check if the Learning Engine is healthy."""
        try:
            # In real implementation, this would call the actual health check RPC
            # request = learning_engine_pb2.HealthCheckRequest(service="learning_engine")
            # response = await self.stub.HealthCheck(request, timeout=5.0)
            # return response.healthy
            
            # Placeholder implementation
            self.last_health_check = datetime.utcnow()
            return True
            
        except Exception as e:
            self.logger.warning(f"Health check failed: {e}")
            return False
    
    async def get_inference(self, request: InferenceRequest) -> Optional[ModelPrediction]:
        """Get inference from a specific model."""
        if not self.connected:
            if not await self.connect():
                return None
        
        start_time = time.time()
        
        try:
            with PerformanceLogger(self.logger, f"model_inference_{request.model_id}"):
                # In real implementation, this would create the actual protobuf request
                # grpc_request = learning_engine_pb2.InferenceRequest(
                #     model_id=request.model_id,
                #     model_version=request.model_version or "latest",
                #     input_data=self._convert_to_inference_data(request.input_data),
                #     config=self._convert_to_inference_config(request.config)
                # )
                
                # response = await self.stub.Inference(
                #     grpc_request,
                #     timeout=request.timeout_ms / 1000.0
                # )
                
                # Placeholder implementation
                await asyncio.sleep(0.01)  # Simulate inference time
                
                inference_time_ms = (time.time() - start_time) * 1000
                
                # Create mock prediction
                prediction = ModelPrediction(
                    model_id=request.model_id,
                    model_type="nlp_transformer",  # Would come from response
                    prediction={"class": "positive", "score": 0.85},  # Mock prediction
                    confidence=0.85,
                    inference_time_ms=inference_time_ms,
                    metadata={"model_version": "1.0", "framework": "pytorch"},
                    timestamp=datetime.utcnow()
                )
                
                # Record metrics
                self.metrics.record_model_consultation(ModelMetrics(
                    model_id=request.model_id,
                    model_type=prediction.model_type,
                    inference_time_ms=inference_time_ms,
                    confidence=prediction.confidence,
                    success=True
                ))
                
                self.logger.debug(f"Got inference from {request.model_id}: confidence={prediction.confidence}")
                return prediction
                
        except Exception as e:
            inference_time_ms = (time.time() - start_time) * 1000
            
            # Record error metrics
            self.metrics.record_model_consultation(ModelMetrics(
                model_id=request.model_id,
                model_type="unknown",
                inference_time_ms=inference_time_ms,
                confidence=0.0,
                success=False
            ))
            
            self.logger.error(f"Error getting inference from {request.model_id}: {e}")
            return None
    
    async def get_batch_inference(self, requests: List[InferenceRequest]) -> List[Optional[ModelPrediction]]:
        """Get batch inference from multiple models."""
        if not requests:
            return []
        
        # For now, process sequentially. In real implementation, this could be optimized
        # to use the batch inference RPC or process in parallel
        results = []
        for request in requests:
            result = await self.get_inference(request)
            results.append(result)
        
        return results
    
    async def list_available_models(self) -> List[Dict[str, Any]]:
        """List available models in the Learning Engine."""
        if not self.connected:
            if not await self.connect():
                return []
        
        try:
            # In real implementation:
            # request = learning_engine_pb2.ListModelsRequest()
            # response = await self.stub.ListModels(request, timeout=10.0)
            # return [self._convert_model_info(model) for model in response.models]
            
            # Placeholder implementation
            return [
                {
                    "model_id": "nlp_sentiment_v1",
                    "model_type": "nlp_transformer",
                    "status": "deployed",
                    "accuracy": 0.92,
                    "description": "Sentiment analysis model"
                },
                {
                    "model_id": "vision_classifier_v2",
                    "model_type": "vision_cnn",
                    "status": "deployed",
                    "accuracy": 0.89,
                    "description": "Image classification model"
                },
                {
                    "model_id": "rl_agent_v1",
                    "model_type": "rl_agent",
                    "status": "training",
                    "accuracy": 0.76,
                    "description": "Reinforcement learning agent"
                }
            ]
            
        except Exception as e:
            self.logger.error(f"Error listing models: {e}")
            return []
    
    async def get_model_info(self, model_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed information about a specific model."""
        if not self.connected:
            if not await self.connect():
                return None
        
        try:
            # In real implementation:
            # request = learning_engine_pb2.GetModelInfoRequest(model_id=model_id)
            # response = await self.stub.GetModelInfo(request, timeout=5.0)
            # return self._convert_model_info(response.model_info)
            
            # Placeholder implementation
            models = await self.list_available_models()
            for model in models:
                if model["model_id"] == model_id:
                    return model
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting model info for {model_id}: {e}")
            return None
    
    async def get_training_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """Get training status for a job."""
        if not self.connected:
            if not await self.connect():
                return None
        
        try:
            # In real implementation:
            # request = learning_engine_pb2.GetTrainingStatusRequest(job_id=job_id)
            # response = await self.stub.GetTrainingStatus(request, timeout=5.0)
            # return self._convert_training_status(response)
            
            # Placeholder implementation
            return {
                "job_id": job_id,
                "status": "running",
                "progress_percentage": 75.5,
                "current_epoch": 15,
                "total_epochs": 20,
                "metrics": {
                    "loss": 0.23,
                    "accuracy": 0.87
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error getting training status for {job_id}: {e}")
            return None
    
    def _convert_to_inference_data(self, input_data: Dict[str, Any]) -> Any:
        """Convert input data to protobuf format."""
        # In real implementation, this would convert to InferenceData protobuf
        return input_data
    
    def _convert_to_inference_config(self, config: Dict[str, Any]) -> Any:
        """Convert config to protobuf format."""
        # In real implementation, this would convert to InferenceConfig protobuf
        return config
    
    def _convert_model_info(self, model_info: Any) -> Dict[str, Any]:
        """Convert protobuf model info to dictionary."""
        # In real implementation, this would convert from protobuf
        return {}


class ModelConsultationService:
    """Service for consulting multiple models and combining predictions."""
    
    def __init__(self, learning_client: LearningEngineClient, config: Config):
        self.learning_client = learning_client
        self.config = config
        self.logger = get_logger(__name__)
        
        # Model selection and weighting
        self.model_weights = {}
        self.model_preferences = config.decision.preferred_models if hasattr(config.decision, 'preferred_models') else []
    
    async def consult_models(self, context: Dict[str, Any], model_ids: Optional[List[str]] = None) -> List[ModelPrediction]:
        """Consult multiple models for predictions."""
        if not model_ids:
            # Get available models and select appropriate ones
            available_models = await self.learning_client.list_available_models()
            model_ids = [model["model_id"] for model in available_models if model["status"] == "deployed"]
        
        # Create inference requests
        requests = []
        for model_id in model_ids:
            request = InferenceRequest(
                model_id=model_id,
                input_data=context,
                timeout_ms=self.config.decision.neural_model_timeout_ms
            )
            requests.append(request)
        
        # Get predictions
        predictions = await self.learning_client.get_batch_inference(requests)
        
        # Filter out failed predictions
        successful_predictions = [p for p in predictions if p is not None]
        
        self.logger.debug(f"Consulted {len(model_ids)} models, got {len(successful_predictions)} successful predictions")
        return successful_predictions
    
    def combine_predictions(self, predictions: List[ModelPrediction], method: str = "weighted_average") -> Optional[Dict[str, Any]]:
        """Combine multiple model predictions into a single result."""
        if not predictions:
            return None
        
        if method == "weighted_average":
            return self._weighted_average_combination(predictions)
        elif method == "voting":
            return self._voting_combination(predictions)
        elif method == "confidence_weighted":
            return self._confidence_weighted_combination(predictions)
        else:
            self.logger.warning(f"Unknown combination method: {method}, using weighted_average")
            return self._weighted_average_combination(predictions)
    
    def _weighted_average_combination(self, predictions: List[ModelPrediction]) -> Dict[str, Any]:
        """Combine predictions using weighted average."""
        total_weight = 0
        weighted_confidence = 0
        combined_prediction = {}
        
        for prediction in predictions:
            weight = self.model_weights.get(prediction.model_id, 1.0)
            total_weight += weight
            weighted_confidence += prediction.confidence * weight
            
            # Combine prediction values (simplified)
            if isinstance(prediction.prediction, dict):
                for key, value in prediction.prediction.items():
                    if key not in combined_prediction:
                        combined_prediction[key] = 0
                    if isinstance(value, (int, float)):
                        combined_prediction[key] += value * weight
        
        # Normalize
        if total_weight > 0:
            weighted_confidence /= total_weight
            for key in combined_prediction:
                combined_prediction[key] /= total_weight
        
        return {
            "prediction": combined_prediction,
            "confidence": weighted_confidence,
            "method": "weighted_average",
            "num_models": len(predictions)
        }
    
    def _voting_combination(self, predictions: List[ModelPrediction]) -> Dict[str, Any]:
        """Combine predictions using majority voting."""
        # Simplified voting implementation
        votes = {}
        total_confidence = 0
        
        for prediction in predictions:
            # Extract the main prediction (simplified)
            if isinstance(prediction.prediction, dict) and "class" in prediction.prediction:
                vote = prediction.prediction["class"]
                if vote not in votes:
                    votes[vote] = 0
                votes[vote] += 1
                total_confidence += prediction.confidence
        
        if votes:
            winning_vote = max(votes, key=votes.get)
            confidence = total_confidence / len(predictions)
            
            return {
                "prediction": {"class": winning_vote},
                "confidence": confidence,
                "method": "voting",
                "votes": votes,
                "num_models": len(predictions)
            }
        
        return None
    
    def _confidence_weighted_combination(self, predictions: List[ModelPrediction]) -> Dict[str, Any]:
        """Combine predictions weighted by their confidence scores."""
        total_confidence = sum(p.confidence for p in predictions)
        if total_confidence == 0:
            return None
        
        combined_prediction = {}
        weighted_confidence = 0
        
        for prediction in predictions:
            weight = prediction.confidence / total_confidence
            weighted_confidence += prediction.confidence * weight
            
            # Combine prediction values
            if isinstance(prediction.prediction, dict):
                for key, value in prediction.prediction.items():
                    if key not in combined_prediction:
                        combined_prediction[key] = 0
                    if isinstance(value, (int, float)):
                        combined_prediction[key] += value * weight
        
        return {
            "prediction": combined_prediction,
            "confidence": weighted_confidence,
            "method": "confidence_weighted",
            "num_models": len(predictions)
        }
