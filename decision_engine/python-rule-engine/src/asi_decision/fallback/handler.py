"""
Fallback handler for the ASI Decision Engine.

Provides graceful degradation and edge case handling when primary
decision-making components fail or produce low-confidence results.
"""

import json
import time
from dataclasses import dataclass
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union
from enum import Enum

from ..utils.logger import get_logger
from ..utils.config import Config

logger = get_logger(__name__)


class FallbackStrategy(Enum):
    """Available fallback strategies."""
    DEFAULT_VALUE = "default_value"
    HISTORICAL_AVERAGE = "historical_average"
    CONSERVATIVE_CHOICE = "conservative_choice"
    RANDOM_SELECTION = "random_selection"
    ESCALATION = "escalation"
    SAFE_MODE = "safe_mode"


@dataclass
class FallbackResult:
    """Result from fallback handling."""
    decision_value: Any
    confidence: float
    strategy_used: FallbackStrategy
    explanation: str
    metadata: Dict[str, Any]


class FallbackHandler:
    """Handles fallback scenarios for decision making."""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = get_logger(__name__)
        
        # Fallback configuration
        self.fallback_confidence = config.decision.fallback_confidence
        self.enable_fallback = config.decision.enable_fallback_logic
        
        # Historical data for fallback decisions
        self.decision_history: List[Dict[str, Any]] = []
        self.error_history: List[Dict[str, Any]] = []
        
        # Default values for different decision types
        self.default_values = {
            "classification": "unknown",
            "regression": 0.0,
            "recommendation": [],
            "optimization": None,
            "control": "maintain",
            "planning": "wait"
        }
        
        # Conservative choices for different scenarios
        self.conservative_choices = {
            "risk_assessment": "high_risk",
            "resource_allocation": "minimal",
            "action_selection": "no_action",
            "approval_decision": "deny",
            "priority_assignment": "low"
        }
        
        self.logger.info("Fallback handler initialized")
    
    async def handle_low_confidence(self, request, reasoning_result, current_confidence: float) -> Optional[FallbackResult]:
        """Handle decisions with low confidence scores."""
        if not self.enable_fallback:
            return None
        
        self.logger.warning(
            f"Low confidence decision detected: {current_confidence:.3f} < {self.fallback_confidence:.3f}",
            extra={'request_id': request.request_id, 'confidence': current_confidence}
        )
        
        # Determine appropriate fallback strategy
        strategy = self._select_fallback_strategy(request, reasoning_result, "low_confidence")
        
        # Apply the selected strategy
        return await self._apply_fallback_strategy(strategy, request, reasoning_result, "low_confidence")
    
    async def handle_error(self, request, error_message: str) -> Optional[FallbackResult]:
        """Handle errors in decision making."""
        if not self.enable_fallback:
            return None
        
        self.logger.error(
            f"Decision error, applying fallback: {error_message}",
            extra={'request_id': request.request_id}
        )
        
        # Record error for analysis
        self.error_history.append({
            "timestamp": datetime.utcnow(),
            "request_id": request.request_id,
            "error": error_message,
            "decision_type": request.decision_type.value
        })
        
        # Determine appropriate fallback strategy
        strategy = self._select_fallback_strategy(request, None, "error")
        
        # Apply the selected strategy
        return await self._apply_fallback_strategy(strategy, request, None, "error")
    
    async def handle_timeout(self, request, elapsed_time_ms: float) -> Optional[FallbackResult]:
        """Handle decision timeouts."""
        if not self.enable_fallback:
            return None
        
        self.logger.warning(
            f"Decision timeout after {elapsed_time_ms:.1f}ms, applying fallback",
            extra={'request_id': request.request_id, 'elapsed_time_ms': elapsed_time_ms}
        )
        
        # Determine appropriate fallback strategy
        strategy = self._select_fallback_strategy(request, None, "timeout")
        
        # Apply the selected strategy
        return await self._apply_fallback_strategy(strategy, request, None, "timeout")
    
    async def handle_missing_data(self, request, missing_fields: List[str]) -> Optional[FallbackResult]:
        """Handle decisions with missing required data."""
        if not self.enable_fallback:
            return None
        
        self.logger.warning(
            f"Missing required data fields: {missing_fields}",
            extra={'request_id': request.request_id, 'missing_fields': missing_fields}
        )
        
        # Determine appropriate fallback strategy
        strategy = self._select_fallback_strategy(request, None, "missing_data")
        
        # Apply the selected strategy
        return await self._apply_fallback_strategy(strategy, request, None, "missing_data")
    
    def _select_fallback_strategy(self, request, reasoning_result, scenario: str) -> FallbackStrategy:
        """Select the most appropriate fallback strategy."""
        decision_type = request.decision_type.value
        priority = request.priority.value
        
        # High priority decisions use conservative approaches
        if priority >= 4:  # CRITICAL or EMERGENCY
            if scenario == "error" or scenario == "timeout":
                return FallbackStrategy.SAFE_MODE
            else:
                return FallbackStrategy.CONSERVATIVE_CHOICE
        
        # For classification tasks
        if decision_type == "classification":
            if scenario == "low_confidence":
                return FallbackStrategy.HISTORICAL_AVERAGE
            else:
                return FallbackStrategy.DEFAULT_VALUE
        
        # For regression tasks
        elif decision_type == "regression":
            return FallbackStrategy.HISTORICAL_AVERAGE
        
        # For recommendations
        elif decision_type == "recommendation":
            if scenario == "error":
                return FallbackStrategy.DEFAULT_VALUE
            else:
                return FallbackStrategy.HISTORICAL_AVERAGE
        
        # For control decisions
        elif decision_type == "control":
            return FallbackStrategy.CONSERVATIVE_CHOICE
        
        # Default strategy
        return FallbackStrategy.DEFAULT_VALUE
    
    async def _apply_fallback_strategy(self, strategy: FallbackStrategy, request, reasoning_result, scenario: str) -> FallbackResult:
        """Apply the selected fallback strategy."""
        decision_type = request.decision_type.value
        
        if strategy == FallbackStrategy.DEFAULT_VALUE:
            return self._apply_default_value(decision_type, request, scenario)
        
        elif strategy == FallbackStrategy.HISTORICAL_AVERAGE:
            return self._apply_historical_average(decision_type, request, scenario)
        
        elif strategy == FallbackStrategy.CONSERVATIVE_CHOICE:
            return self._apply_conservative_choice(decision_type, request, scenario)
        
        elif strategy == FallbackStrategy.RANDOM_SELECTION:
            return self._apply_random_selection(decision_type, request, scenario)
        
        elif strategy == FallbackStrategy.ESCALATION:
            return self._apply_escalation(decision_type, request, scenario)
        
        elif strategy == FallbackStrategy.SAFE_MODE:
            return self._apply_safe_mode(decision_type, request, scenario)
        
        else:
            # Unknown strategy, use default
            return self._apply_default_value(decision_type, request, scenario)
    
    def _apply_default_value(self, decision_type: str, request, scenario: str) -> FallbackResult:
        """Apply default value strategy."""
        default_value = self.default_values.get(decision_type, None)
        
        return FallbackResult(
            decision_value=default_value,
            confidence=self.fallback_confidence,
            strategy_used=FallbackStrategy.DEFAULT_VALUE,
            explanation=f"Used default value for {decision_type} due to {scenario}",
            metadata={
                "scenario": scenario,
                "decision_type": decision_type,
                "default_value": default_value
            }
        )
    
    def _apply_historical_average(self, decision_type: str, request, scenario: str) -> FallbackResult:
        """Apply historical average strategy."""
        # Filter relevant historical decisions
        relevant_decisions = [
            d for d in self.decision_history
            if d.get("decision_type") == decision_type
            and d.get("timestamp", datetime.min) > datetime.utcnow() - timedelta(days=30)
        ]
        
        if relevant_decisions:
            # Calculate average for numeric values
            if decision_type == "regression":
                values = [d.get("decision_value", 0) for d in relevant_decisions if isinstance(d.get("decision_value"), (int, float))]
                if values:
                    avg_value = sum(values) / len(values)
                    return FallbackResult(
                        decision_value=avg_value,
                        confidence=self.fallback_confidence,
                        strategy_used=FallbackStrategy.HISTORICAL_AVERAGE,
                        explanation=f"Used historical average of {len(values)} decisions",
                        metadata={
                            "scenario": scenario,
                            "sample_size": len(values),
                            "average_value": avg_value
                        }
                    )
            
            # For categorical values, use most common
            else:
                value_counts = {}
                for d in relevant_decisions:
                    value = d.get("decision_value")
                    if value is not None:
                        value_str = str(value)
                        value_counts[value_str] = value_counts.get(value_str, 0) + 1
                
                if value_counts:
                    most_common = max(value_counts, key=value_counts.get)
                    return FallbackResult(
                        decision_value=most_common,
                        confidence=self.fallback_confidence,
                        strategy_used=FallbackStrategy.HISTORICAL_AVERAGE,
                        explanation=f"Used most common historical value: {most_common}",
                        metadata={
                            "scenario": scenario,
                            "value_counts": value_counts,
                            "most_common": most_common
                        }
                    )
        
        # Fallback to default if no historical data
        return self._apply_default_value(decision_type, request, scenario)
    
    def _apply_conservative_choice(self, decision_type: str, request, scenario: str) -> FallbackResult:
        """Apply conservative choice strategy."""
        # Try to infer conservative choice from context
        context_data = request.context.input_data
        
        # Look for risk-related context
        if "risk" in context_data or "safety" in context_data:
            conservative_value = self.conservative_choices.get("risk_assessment", "high_risk")
        elif "resource" in context_data or "allocation" in context_data:
            conservative_value = self.conservative_choices.get("resource_allocation", "minimal")
        elif "action" in context_data:
            conservative_value = self.conservative_choices.get("action_selection", "no_action")
        elif "approval" in context_data or "permission" in context_data:
            conservative_value = self.conservative_choices.get("approval_decision", "deny")
        else:
            conservative_value = self.conservative_choices.get("priority_assignment", "low")
        
        return FallbackResult(
            decision_value=conservative_value,
            confidence=self.fallback_confidence,
            strategy_used=FallbackStrategy.CONSERVATIVE_CHOICE,
            explanation=f"Used conservative choice due to {scenario}",
            metadata={
                "scenario": scenario,
                "conservative_value": conservative_value,
                "context_keys": list(context_data.keys())
            }
        )
    
    def _apply_random_selection(self, decision_type: str, request, scenario: str) -> FallbackResult:
        """Apply random selection strategy."""
        import random
        
        # Define possible values based on decision type
        if decision_type == "classification":
            possible_values = ["class_a", "class_b", "class_c", "unknown"]
        elif decision_type == "regression":
            possible_values = [random.uniform(0, 1) for _ in range(1)]
        elif decision_type == "recommendation":
            possible_values = [[], ["item_1"], ["item_1", "item_2"]]
        else:
            possible_values = [None, "default", "random_choice"]
        
        selected_value = random.choice(possible_values)
        
        return FallbackResult(
            decision_value=selected_value,
            confidence=self.fallback_confidence * 0.5,  # Lower confidence for random
            strategy_used=FallbackStrategy.RANDOM_SELECTION,
            explanation=f"Used random selection due to {scenario}",
            metadata={
                "scenario": scenario,
                "possible_values": len(possible_values),
                "selected_value": selected_value
            }
        )
    
    def _apply_escalation(self, decision_type: str, request, scenario: str) -> FallbackResult:
        """Apply escalation strategy."""
        # In a real system, this would escalate to human operators or higher-level systems
        escalation_message = f"Decision escalated: {decision_type} request {request.request_id} due to {scenario}"
        
        self.logger.warning(escalation_message)
        
        return FallbackResult(
            decision_value="ESCALATED",
            confidence=0.0,  # No confidence in escalated decisions
            strategy_used=FallbackStrategy.ESCALATION,
            explanation=f"Escalated decision due to {scenario}",
            metadata={
                "scenario": scenario,
                "escalation_message": escalation_message,
                "escalation_time": datetime.utcnow().isoformat()
            }
        )
    
    def _apply_safe_mode(self, decision_type: str, request, scenario: str) -> FallbackResult:
        """Apply safe mode strategy."""
        # Safe mode provides the safest possible decision
        safe_values = {
            "classification": "safe",
            "regression": 0.0,
            "recommendation": [],
            "optimization": None,
            "control": "stop",
            "planning": "abort"
        }
        
        safe_value = safe_values.get(decision_type, "SAFE_MODE")
        
        return FallbackResult(
            decision_value=safe_value,
            confidence=self.fallback_confidence,
            strategy_used=FallbackStrategy.SAFE_MODE,
            explanation=f"Entered safe mode due to {scenario}",
            metadata={
                "scenario": scenario,
                "safe_value": safe_value,
                "safe_mode_activated": True
            }
        )
    
    def record_decision(self, decision_result):
        """Record a decision for historical analysis."""
        self.decision_history.append({
            "timestamp": datetime.utcnow(),
            "decision_id": decision_result.decision_id,
            "decision_type": decision_result.decision_type.value,
            "decision_value": decision_result.decision_value,
            "confidence": decision_result.confidence,
            "used_fallback": decision_result.used_fallback
        })
        
        # Keep only recent history
        cutoff_time = datetime.utcnow() - timedelta(days=90)
        self.decision_history = [
            d for d in self.decision_history
            if d.get("timestamp", datetime.min) > cutoff_time
        ]
    
    def get_fallback_statistics(self) -> Dict[str, Any]:
        """Get statistics about fallback usage."""
        recent_decisions = [
            d for d in self.decision_history
            if d.get("timestamp", datetime.min) > datetime.utcnow() - timedelta(days=7)
        ]
        
        fallback_decisions = [d for d in recent_decisions if d.get("used_fallback", False)]
        
        return {
            "total_decisions": len(recent_decisions),
            "fallback_decisions": len(fallback_decisions),
            "fallback_rate": len(fallback_decisions) / len(recent_decisions) if recent_decisions else 0.0,
            "recent_errors": len([
                e for e in self.error_history
                if e.get("timestamp", datetime.min) > datetime.utcnow() - timedelta(days=7)
            ]),
            "timestamp": datetime.utcnow().isoformat()
        }
