"""
Advanced Fallback Handler for ASI Decision Engine.

Provides sophisticated fallback mechanisms for edge cases, uncertainty handling,
and graceful degradation when primary decision-making systems fail or produce
low-confidence results.
"""

import asyncio
import time
import random
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union, Callable
from enum import Enum
import logging
import numpy as np

from ..utils.logger import get_logger, PerformanceLogger
from ..utils.metrics import get_metrics_collector, FallbackMetrics
from ..utils.config import Config

logger = get_logger(__name__)


class FallbackStrategy(Enum):
    """Fallback strategies for different scenarios."""
    CONSERVATIVE = "conservative"      # Choose safest option
    AGGRESSIVE = "aggressive"         # Choose highest potential reward
    RANDOM = "random"                 # Random selection
    HISTORICAL = "historical"         # Based on historical patterns
    EXPERT_SYSTEM = "expert_system"   # Rule-based expert knowledge
    ENSEMBLE = "ensemble"             # Combine multiple strategies
    LEARNED = "learned"               # ML-based fallback decisions


class UncertaintyType(Enum):
    """Types of uncertainty in decision making."""
    LOW_CONFIDENCE = "low_confidence"         # All options have low confidence
    CONFLICTING = "conflicting"               # Multiple high-confidence conflicting options
    MISSING_DATA = "missing_data"             # Insufficient input data
    MODEL_FAILURE = "model_failure"           # Neural models failed
    RULE_FAILURE = "rule_failure"             # Rule engine failed
    TIMEOUT = "timeout"                       # Processing timeout
    UNKNOWN_SCENARIO = "unknown_scenario"     # Novel/unseen scenario


@dataclass
class FallbackContext:
    """Context for fallback decision making."""
    request_id: str
    original_input: Dict[str, Any]
    uncertainty_type: UncertaintyType
    failed_components: List[str] = field(default_factory=list)
    available_data: Dict[str, Any] = field(default_factory=dict)
    constraints: Dict[str, Any] = field(default_factory=dict)
    risk_tolerance: float = 0.5  # 0.0 = very conservative, 1.0 = very aggressive
    timeout_ms: int = 1000
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class FallbackDecision:
    """Result from fallback decision making."""
    decision: str
    confidence: float
    strategy_used: FallbackStrategy
    explanation: str
    risk_assessment: Dict[str, float]
    alternatives: List[Dict[str, Any]] = field(default_factory=list)
    processing_time_ms: float = 0.0
    timestamp: datetime = field(default_factory=datetime.utcnow)


class HistoricalPatternAnalyzer:
    """Analyzes historical patterns for fallback decisions."""
    
    def __init__(self, max_history: int = 10000):
        self.max_history = max_history
        self.decision_history: List[Dict[str, Any]] = []
        self.pattern_cache: Dict[str, Any] = {}
        
    def record_decision(self, context: Dict[str, Any], decision: str, outcome: Optional[str] = None):
        """Record a decision for pattern analysis."""
        record = {
            'context': context,
            'decision': decision,
            'outcome': outcome,
            'timestamp': datetime.utcnow()
        }
        
        self.decision_history.append(record)
        
        # Maintain history size
        if len(self.decision_history) > self.max_history:
            self.decision_history.pop(0)
    
    def find_similar_patterns(self, context: Dict[str, Any], top_k: int = 5) -> List[Dict[str, Any]]:
        """Find similar historical patterns."""
        if not self.decision_history:
            return []
        
        # Simple similarity based on context keys (can be enhanced with ML)
        similarities = []
        
        for record in self.decision_history:
            similarity = self._calculate_similarity(context, record['context'])
            similarities.append((similarity, record))
        
        # Sort by similarity and return top-k
        similarities.sort(key=lambda x: x[0], reverse=True)
        return [record for _, record in similarities[:top_k]]
    
    def _calculate_similarity(self, context1: Dict[str, Any], context2: Dict[str, Any]) -> float:
        """Calculate similarity between two contexts."""
        common_keys = set(context1.keys()) & set(context2.keys())
        if not common_keys:
            return 0.0
        
        similarity_sum = 0.0
        for key in common_keys:
            val1, val2 = context1[key], context2[key]
            
            if isinstance(val1, (int, float)) and isinstance(val2, (int, float)):
                # Numerical similarity
                max_val = max(abs(val1), abs(val2), 1.0)
                similarity_sum += 1.0 - abs(val1 - val2) / max_val
            elif val1 == val2:
                # Exact match
                similarity_sum += 1.0
            else:
                # No similarity
                similarity_sum += 0.0
        
        return similarity_sum / len(common_keys)


class ExpertSystemFallback:
    """Expert system for fallback decisions based on domain knowledge."""
    
    def __init__(self):
        self.expert_rules = self._initialize_expert_rules()
    
    def _initialize_expert_rules(self) -> Dict[str, Callable]:
        """Initialize expert system rules."""
        return {
            'safety_critical': self._safety_critical_rule,
            'financial_risk': self._financial_risk_rule,
            'time_sensitive': self._time_sensitive_rule,
            'data_quality': self._data_quality_rule,
            'user_impact': self._user_impact_rule
        }
    
    def evaluate(self, context: FallbackContext) -> Optional[FallbackDecision]:
        """Evaluate expert rules for fallback decision."""
        for rule_name, rule_func in self.expert_rules.items():
            try:
                decision = rule_func(context)
                if decision:
                    return decision
            except Exception as e:
                logger.warning(f"Expert rule {rule_name} failed: {e}")
        
        return None
    
    def _safety_critical_rule(self, context: FallbackContext) -> Optional[FallbackDecision]:
        """Rule for safety-critical scenarios."""
        if context.metadata.get('safety_critical', False):
            return FallbackDecision(
                decision="safe_mode",
                confidence=0.9,
                strategy_used=FallbackStrategy.EXPERT_SYSTEM,
                explanation="Safety-critical scenario detected, defaulting to safe mode",
                risk_assessment={'safety': 0.1, 'performance': 0.7}
            )
        return None
    
    def _financial_risk_rule(self, context: FallbackContext) -> Optional[FallbackDecision]:
        """Rule for financial risk scenarios."""
        financial_impact = context.metadata.get('financial_impact', 0)
        if financial_impact > 10000:  # High financial impact
            return FallbackDecision(
                decision="conservative",
                confidence=0.8,
                strategy_used=FallbackStrategy.EXPERT_SYSTEM,
                explanation=f"High financial impact (${financial_impact}), choosing conservative option",
                risk_assessment={'financial': 0.2, 'opportunity': 0.6}
            )
        return None
    
    def _time_sensitive_rule(self, context: FallbackContext) -> Optional[FallbackDecision]:
        """Rule for time-sensitive scenarios."""
        deadline = context.metadata.get('deadline')
        if deadline and isinstance(deadline, datetime):
            time_remaining = (deadline - datetime.utcnow()).total_seconds()
            if time_remaining < 300:  # Less than 5 minutes
                return FallbackDecision(
                    decision="fast_track",
                    confidence=0.7,
                    strategy_used=FallbackStrategy.EXPERT_SYSTEM,
                    explanation=f"Time-critical scenario ({time_remaining:.0f}s remaining), fast-tracking decision",
                    risk_assessment={'time': 0.9, 'quality': 0.4}
                )
        return None
    
    def _data_quality_rule(self, context: FallbackContext) -> Optional[FallbackDecision]:
        """Rule for data quality issues."""
        if context.uncertainty_type == UncertaintyType.MISSING_DATA:
            data_completeness = len(context.available_data) / max(len(context.original_input), 1)
            if data_completeness < 0.5:
                return FallbackDecision(
                    decision="request_more_data",
                    confidence=0.6,
                    strategy_used=FallbackStrategy.EXPERT_SYSTEM,
                    explanation=f"Insufficient data quality ({data_completeness:.1%}), requesting more information",
                    risk_assessment={'data_quality': 0.3, 'delay': 0.7}
                )
        return None
    
    def _user_impact_rule(self, context: FallbackContext) -> Optional[FallbackDecision]:
        """Rule for user impact scenarios."""
        user_count = context.metadata.get('affected_users', 0)
        if user_count > 1000:  # High user impact
            return FallbackDecision(
                decision="gradual_rollout",
                confidence=0.8,
                strategy_used=FallbackStrategy.EXPERT_SYSTEM,
                explanation=f"High user impact ({user_count} users), implementing gradual rollout",
                risk_assessment={'user_impact': 0.3, 'rollback_complexity': 0.5}
            )
        return None


class AdvancedFallbackHandler:
    """
    Advanced fallback handler with multiple strategies and uncertainty handling.
    """
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = get_logger(__name__)
        self.metrics = get_metrics_collector()
        
        # Initialize components
        self.pattern_analyzer = HistoricalPatternAnalyzer()
        self.expert_system = ExpertSystemFallback()
        
        # Configuration
        self.default_strategy = FallbackStrategy(config.decision.fallback_strategy)
        self.confidence_threshold = config.decision.fallback_confidence_threshold
        self.max_alternatives = config.decision.max_fallback_alternatives
        
        # Strategy weights for ensemble
        self.strategy_weights = {
            FallbackStrategy.CONSERVATIVE: 0.3,
            FallbackStrategy.HISTORICAL: 0.25,
            FallbackStrategy.EXPERT_SYSTEM: 0.25,
            FallbackStrategy.RANDOM: 0.1,
            FallbackStrategy.AGGRESSIVE: 0.1
        }
        
        self.logger.info("Advanced fallback handler initialized")
    
    async def handle_fallback(self, context: FallbackContext) -> FallbackDecision:
        """
        Handle fallback decision making for uncertain scenarios.
        """
        start_time = time.time()
        request_id = context.request_id
        
        self.logger.info(f"Handling fallback for request {request_id}, uncertainty: {context.uncertainty_type.value}")
        
        try:
            # Choose fallback strategy based on context
            strategy = self._choose_strategy(context)
            
            # Execute fallback strategy
            if strategy == FallbackStrategy.CONSERVATIVE:
                decision = await self._conservative_fallback(context)
            elif strategy == FallbackStrategy.AGGRESSIVE:
                decision = await self._aggressive_fallback(context)
            elif strategy == FallbackStrategy.RANDOM:
                decision = await self._random_fallback(context)
            elif strategy == FallbackStrategy.HISTORICAL:
                decision = await self._historical_fallback(context)
            elif strategy == FallbackStrategy.EXPERT_SYSTEM:
                decision = await self._expert_system_fallback(context)
            elif strategy == FallbackStrategy.ENSEMBLE:
                decision = await self._ensemble_fallback(context)
            else:
                decision = await self._conservative_fallback(context)  # Default
            
            # Record metrics
            processing_time_ms = (time.time() - start_time) * 1000
            decision.processing_time_ms = processing_time_ms
            
            self.metrics.record_fallback(FallbackMetrics(
                request_id=request_id,
                uncertainty_type=context.uncertainty_type.value,
                strategy_used=strategy.value,
                processing_time_ms=processing_time_ms,
                confidence=decision.confidence,
                success=True
            ))
            
            self.logger.info(f"Fallback completed for {request_id}: {decision.decision} (strategy: {strategy.value}, confidence: {decision.confidence:.3f})")
            return decision
            
        except Exception as e:
            processing_time_ms = (time.time() - start_time) * 1000
            self.logger.error(f"Fallback handling failed for {request_id}: {e}")
            
            self.metrics.record_fallback(FallbackMetrics(
                request_id=request_id,
                uncertainty_type=context.uncertainty_type.value,
                strategy_used="error",
                processing_time_ms=processing_time_ms,
                confidence=0.0,
                success=False
            ))
            
            # Return emergency fallback
            return FallbackDecision(
                decision="emergency_fallback",
                confidence=0.1,
                strategy_used=FallbackStrategy.CONSERVATIVE,
                explanation=f"Emergency fallback due to error: {str(e)}",
                risk_assessment={'error': 1.0},
                processing_time_ms=processing_time_ms
            )
    
    def _choose_strategy(self, context: FallbackContext) -> FallbackStrategy:
        """Choose appropriate fallback strategy based on context."""
        # Strategy selection based on uncertainty type
        if context.uncertainty_type == UncertaintyType.LOW_CONFIDENCE:
            return FallbackStrategy.ENSEMBLE
        elif context.uncertainty_type == UncertaintyType.CONFLICTING:
            return FallbackStrategy.EXPERT_SYSTEM
        elif context.uncertainty_type == UncertaintyType.MISSING_DATA:
            return FallbackStrategy.HISTORICAL
        elif context.uncertainty_type == UncertaintyType.MODEL_FAILURE:
            return FallbackStrategy.EXPERT_SYSTEM
        elif context.uncertainty_type == UncertaintyType.TIMEOUT:
            return FallbackStrategy.CONSERVATIVE
        else:
            return self.default_strategy
    
    async def _conservative_fallback(self, context: FallbackContext) -> FallbackDecision:
        """Conservative fallback strategy - choose safest option."""
        # Define conservative options based on domain
        conservative_options = ["maintain_status_quo", "safe_mode", "manual_review", "defer"]
        
        # Choose based on risk tolerance
        if context.risk_tolerance < 0.3:
            decision = "safe_mode"
            confidence = 0.8
        elif context.risk_tolerance < 0.6:
            decision = "maintain_status_quo"
            confidence = 0.7
        else:
            decision = "manual_review"
            confidence = 0.6
        
        return FallbackDecision(
            decision=decision,
            confidence=confidence,
            strategy_used=FallbackStrategy.CONSERVATIVE,
            explanation=f"Conservative fallback chosen due to {context.uncertainty_type.value}",
            risk_assessment={'safety': 0.9, 'performance': 0.4, 'cost': 0.6}
        )
    
    async def _aggressive_fallback(self, context: FallbackContext) -> FallbackDecision:
        """Aggressive fallback strategy - choose highest potential reward."""
        aggressive_options = ["proceed_anyway", "best_guess", "optimistic_path"]
        
        decision = random.choice(aggressive_options)
        confidence = min(0.6, context.risk_tolerance)
        
        return FallbackDecision(
            decision=decision,
            confidence=confidence,
            strategy_used=FallbackStrategy.AGGRESSIVE,
            explanation=f"Aggressive fallback chosen with risk tolerance {context.risk_tolerance}",
            risk_assessment={'opportunity': 0.8, 'safety': 0.3, 'uncertainty': 0.7}
        )
    
    async def _random_fallback(self, context: FallbackContext) -> FallbackDecision:
        """Random fallback strategy."""
        options = ["option_a", "option_b", "option_c", "random_choice"]
        decision = random.choice(options)
        confidence = 0.3  # Low confidence for random choice
        
        return FallbackDecision(
            decision=decision,
            confidence=confidence,
            strategy_used=FallbackStrategy.RANDOM,
            explanation="Random fallback due to lack of better alternatives",
            risk_assessment={'randomness': 1.0, 'predictability': 0.0}
        )
    
    async def _historical_fallback(self, context: FallbackContext) -> FallbackDecision:
        """Historical pattern-based fallback."""
        # Find similar historical patterns
        similar_patterns = self.pattern_analyzer.find_similar_patterns(context.original_input)
        
        if similar_patterns:
            # Use most similar pattern's decision
            best_pattern = similar_patterns[0]
            decision = best_pattern['decision']
            confidence = 0.6  # Moderate confidence for historical patterns
            explanation = f"Historical fallback based on {len(similar_patterns)} similar patterns"
        else:
            # No historical patterns found
            decision = "no_historical_data"
            confidence = 0.2
            explanation = "No similar historical patterns found"
        
        return FallbackDecision(
            decision=decision,
            confidence=confidence,
            strategy_used=FallbackStrategy.HISTORICAL,
            explanation=explanation,
            risk_assessment={'historical_accuracy': 0.6, 'novelty': 0.4}
        )
    
    async def _expert_system_fallback(self, context: FallbackContext) -> FallbackDecision:
        """Expert system-based fallback."""
        expert_decision = self.expert_system.evaluate(context)
        
        if expert_decision:
            return expert_decision
        else:
            # No expert rules matched
            return FallbackDecision(
                decision="expert_system_no_match",
                confidence=0.3,
                strategy_used=FallbackStrategy.EXPERT_SYSTEM,
                explanation="No expert system rules matched the scenario",
                risk_assessment={'expert_coverage': 0.0, 'uncertainty': 0.8}
            )
    
    async def _ensemble_fallback(self, context: FallbackContext) -> FallbackDecision:
        """Ensemble fallback combining multiple strategies."""
        # Run multiple strategies
        strategies_to_run = [
            FallbackStrategy.CONSERVATIVE,
            FallbackStrategy.HISTORICAL,
            FallbackStrategy.EXPERT_SYSTEM
        ]
        
        decisions = []
        for strategy in strategies_to_run:
            try:
                if strategy == FallbackStrategy.CONSERVATIVE:
                    decision = await self._conservative_fallback(context)
                elif strategy == FallbackStrategy.HISTORICAL:
                    decision = await self._historical_fallback(context)
                elif strategy == FallbackStrategy.EXPERT_SYSTEM:
                    decision = await self._expert_system_fallback(context)
                
                decisions.append((decision, self.strategy_weights.get(strategy, 0.1)))
            except Exception as e:
                self.logger.warning(f"Strategy {strategy} failed in ensemble: {e}")
        
        if not decisions:
            return await self._conservative_fallback(context)
        
        # Weighted voting
        decision_votes = {}
        total_weight = 0
        
        for decision, weight in decisions:
            if decision.decision not in decision_votes:
                decision_votes[decision.decision] = 0
            decision_votes[decision.decision] += decision.confidence * weight
            total_weight += weight
        
        # Choose decision with highest weighted vote
        if decision_votes:
            best_decision = max(decision_votes.items(), key=lambda x: x[1])[0]
            ensemble_confidence = decision_votes[best_decision] / total_weight if total_weight > 0 else 0.3
        else:
            best_decision = "ensemble_failed"
            ensemble_confidence = 0.1
        
        return FallbackDecision(
            decision=best_decision,
            confidence=ensemble_confidence,
            strategy_used=FallbackStrategy.ENSEMBLE,
            explanation=f"Ensemble fallback from {len(decisions)} strategies",
            risk_assessment={'ensemble_agreement': ensemble_confidence, 'diversity': len(decisions) / len(strategies_to_run)},
            alternatives=[d[0].decision for d in decisions]
        )
