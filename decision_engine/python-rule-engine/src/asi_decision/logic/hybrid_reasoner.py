"""
Hybrid Symbolic/Neural Reasoning Engine for ASI Decision Engine.

Combines rule-based symbolic reasoning with neural model outputs to make
intelligent decisions with confidence scoring and explanation generation.
"""

import asyncio
import time
import numpy as np
from dataclasses import dataclass, field
from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Tuple
from enum import Enum
import logging

from ..rules.engine import RuleEngine, RuleEvaluationResult
from ..integration.learning_engine import LearningEngineClient, ModelPrediction, InferenceRequest
from ..utils.logger import get_logger, PerformanceLogger
from ..utils.metrics import get_metrics_collector, HybridReasoningMetrics
from ..utils.config import Config

logger = get_logger(__name__)


class ReasoningStrategy(Enum):
    """Reasoning strategy for combining symbolic and neural outputs."""
    SYMBOLIC_FIRST = "symbolic_first"  # Apply rules first, then neural
    NEURAL_FIRST = "neural_first"      # Apply neural first, then rules
    PARALLEL = "parallel"              # Apply both in parallel and combine
    WEIGHTED = "weighted"              # Weighted combination based on confidence
    ENSEMBLE = "ensemble"              # Ensemble voting approach


@dataclass
class ReasoningContext:
    """Context for hybrid reasoning."""
    request_id: str
    input_data: Dict[str, Any]
    metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.utcnow)
    timeout_ms: int = 5000
    strategy: ReasoningStrategy = ReasoningStrategy.PARALLEL
    
    # Model consultation preferences
    preferred_models: List[str] = field(default_factory=list)
    model_weights: Dict[str, float] = field(default_factory=dict)
    
    # Rule evaluation preferences
    rule_tags: List[str] = field(default_factory=list)
    rule_priorities: Dict[str, int] = field(default_factory=dict)


@dataclass
class SymbolicReasoning:
    """Results from symbolic reasoning."""
    triggered_rules: List[RuleEvaluationResult]
    overall_confidence: float
    decision: Optional[str]
    explanation: str
    evaluation_time_ms: float


@dataclass
class NeuralReasoning:
    """Results from neural reasoning."""
    model_predictions: List[ModelPrediction]
    aggregated_prediction: Dict[str, Any]
    overall_confidence: float
    decision: Optional[str]
    explanation: str
    inference_time_ms: float


@dataclass
class HybridDecision:
    """Final hybrid decision combining symbolic and neural reasoning."""
    decision: str
    confidence: float
    explanation: str
    reasoning_breakdown: Dict[str, Any]
    
    # Component results
    symbolic_result: Optional[SymbolicReasoning]
    neural_result: Optional[NeuralReasoning]
    
    # Metadata
    strategy_used: ReasoningStrategy
    total_time_ms: float
    timestamp: datetime = field(default_factory=datetime.utcnow)


class HybridReasoner:
    """
    Hybrid reasoning engine that combines symbolic rules with neural model outputs.
    """
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = get_logger(__name__)
        self.metrics = get_metrics_collector()
        
        # Initialize components
        self.rule_engine = RuleEngine(
            max_rules=config.decision.max_rules,
            enable_caching=config.decision.enable_rule_caching
        )
        self.learning_client = LearningEngineClient(config.learning_engine)
        
        # Reasoning configuration
        self.default_strategy = ReasoningStrategy(config.decision.default_reasoning_strategy)
        self.symbolic_weight = config.decision.symbolic_weight
        self.neural_weight = config.decision.neural_weight
        self.confidence_threshold = config.decision.confidence_threshold
        
        self.logger.info("Hybrid reasoner initialized")
    
    async def reason(self, context: ReasoningContext) -> HybridDecision:
        """
        Perform hybrid reasoning combining symbolic and neural approaches.
        """
        start_time = time.time()
        request_id = context.request_id
        
        self.logger.info(f"Starting hybrid reasoning for request {request_id}")
        
        try:
            # Choose reasoning strategy
            strategy = context.strategy or self.default_strategy
            
            # Execute reasoning based on strategy
            if strategy == ReasoningStrategy.SYMBOLIC_FIRST:
                decision = await self._symbolic_first_reasoning(context)
            elif strategy == ReasoningStrategy.NEURAL_FIRST:
                decision = await self._neural_first_reasoning(context)
            elif strategy == ReasoningStrategy.PARALLEL:
                decision = await self._parallel_reasoning(context)
            elif strategy == ReasoningStrategy.WEIGHTED:
                decision = await self._weighted_reasoning(context)
            elif strategy == ReasoningStrategy.ENSEMBLE:
                decision = await self._ensemble_reasoning(context)
            else:
                raise ValueError(f"Unsupported reasoning strategy: {strategy}")
            
            # Record metrics
            total_time_ms = (time.time() - start_time) * 1000
            decision.total_time_ms = total_time_ms
            
            self.metrics.record_hybrid_reasoning(HybridReasoningMetrics(
                request_id=request_id,
                strategy=strategy.value,
                total_time_ms=total_time_ms,
                confidence=decision.confidence,
                success=True
            ))
            
            self.logger.info(f"Hybrid reasoning completed for {request_id}: {decision.decision} (confidence: {decision.confidence:.3f})")
            return decision
            
        except Exception as e:
            total_time_ms = (time.time() - start_time) * 1000
            self.logger.error(f"Hybrid reasoning failed for {request_id}: {e}")
            
            self.metrics.record_hybrid_reasoning(HybridReasoningMetrics(
                request_id=request_id,
                strategy=strategy.value if 'strategy' in locals() else "unknown",
                total_time_ms=total_time_ms,
                confidence=0.0,
                success=False
            ))
            
            # Return fallback decision
            return HybridDecision(
                decision="fallback",
                confidence=0.1,
                explanation=f"Reasoning failed: {str(e)}",
                reasoning_breakdown={"error": str(e)},
                symbolic_result=None,
                neural_result=None,
                strategy_used=strategy if 'strategy' in locals() else ReasoningStrategy.PARALLEL,
                total_time_ms=total_time_ms
            )
    
    async def _symbolic_first_reasoning(self, context: ReasoningContext) -> HybridDecision:
        """Apply symbolic reasoning first, then neural if needed."""
        # Perform symbolic reasoning
        symbolic_result = await self._perform_symbolic_reasoning(context)
        
        # If symbolic reasoning is confident enough, use it
        if symbolic_result.overall_confidence >= self.confidence_threshold:
            return HybridDecision(
                decision=symbolic_result.decision,
                confidence=symbolic_result.overall_confidence,
                explanation=symbolic_result.explanation,
                reasoning_breakdown={
                    "primary": "symbolic",
                    "symbolic_confidence": symbolic_result.overall_confidence,
                    "rules_triggered": len(symbolic_result.triggered_rules)
                },
                symbolic_result=symbolic_result,
                neural_result=None,
                strategy_used=ReasoningStrategy.SYMBOLIC_FIRST,
                total_time_ms=0  # Will be set by caller
            )
        
        # Otherwise, consult neural models
        neural_result = await self._perform_neural_reasoning(context)
        
        # Combine results with neural taking precedence
        combined_confidence = (symbolic_result.overall_confidence * 0.3 + 
                             neural_result.overall_confidence * 0.7)
        
        decision = neural_result.decision or symbolic_result.decision or "uncertain"
        
        explanation = f"Symbolic reasoning (confidence: {symbolic_result.overall_confidence:.3f}) " \
                     f"was below threshold, neural reasoning (confidence: {neural_result.overall_confidence:.3f}) " \
                     f"provided final decision: {decision}"
        
        return HybridDecision(
            decision=decision,
            confidence=combined_confidence,
            explanation=explanation,
            reasoning_breakdown={
                "primary": "neural",
                "symbolic_confidence": symbolic_result.overall_confidence,
                "neural_confidence": neural_result.overall_confidence,
                "combined_confidence": combined_confidence
            },
            symbolic_result=symbolic_result,
            neural_result=neural_result,
            strategy_used=ReasoningStrategy.SYMBOLIC_FIRST,
            total_time_ms=0
        )
    
    async def _neural_first_reasoning(self, context: ReasoningContext) -> HybridDecision:
        """Apply neural reasoning first, then symbolic validation."""
        # Perform neural reasoning
        neural_result = await self._perform_neural_reasoning(context)
        
        # Perform symbolic validation
        symbolic_result = await self._perform_symbolic_reasoning(context)
        
        # Check if symbolic rules contradict neural decision
        contradiction = self._check_contradiction(neural_result, symbolic_result)
        
        if contradiction:
            # Use symbolic rules to override if there's a strong contradiction
            decision = symbolic_result.decision or neural_result.decision
            confidence = min(neural_result.overall_confidence, symbolic_result.overall_confidence)
            explanation = f"Neural decision '{neural_result.decision}' was overridden by symbolic rules due to contradiction"
        else:
            # Use neural decision with symbolic validation
            decision = neural_result.decision
            confidence = min(neural_result.overall_confidence * 1.1, 1.0)  # Boost confidence if validated
            explanation = f"Neural decision '{neural_result.decision}' validated by symbolic rules"
        
        return HybridDecision(
            decision=decision,
            confidence=confidence,
            explanation=explanation,
            reasoning_breakdown={
                "primary": "neural",
                "validation": "symbolic",
                "contradiction": contradiction,
                "neural_confidence": neural_result.overall_confidence,
                "symbolic_confidence": symbolic_result.overall_confidence
            },
            symbolic_result=symbolic_result,
            neural_result=neural_result,
            strategy_used=ReasoningStrategy.NEURAL_FIRST,
            total_time_ms=0
        )
    
    async def _parallel_reasoning(self, context: ReasoningContext) -> HybridDecision:
        """Perform symbolic and neural reasoning in parallel."""
        # Run both reasoning approaches concurrently
        symbolic_task = asyncio.create_task(self._perform_symbolic_reasoning(context))
        neural_task = asyncio.create_task(self._perform_neural_reasoning(context))
        
        symbolic_result, neural_result = await asyncio.gather(symbolic_task, neural_task)
        
        # Combine results using weighted average
        combined_confidence = (symbolic_result.overall_confidence * self.symbolic_weight + 
                             neural_result.overall_confidence * self.neural_weight)
        
        # Choose decision based on higher confidence
        if symbolic_result.overall_confidence > neural_result.overall_confidence:
            primary_decision = symbolic_result.decision
            primary_source = "symbolic"
        else:
            primary_decision = neural_result.decision
            primary_source = "neural"
        
        explanation = f"Parallel reasoning: symbolic (confidence: {symbolic_result.overall_confidence:.3f}), " \
                     f"neural (confidence: {neural_result.overall_confidence:.3f}). " \
                     f"Primary decision from {primary_source}: {primary_decision}"
        
        return HybridDecision(
            decision=primary_decision,
            confidence=combined_confidence,
            explanation=explanation,
            reasoning_breakdown={
                "approach": "parallel",
                "symbolic_confidence": symbolic_result.overall_confidence,
                "neural_confidence": neural_result.overall_confidence,
                "combined_confidence": combined_confidence,
                "primary_source": primary_source
            },
            symbolic_result=symbolic_result,
            neural_result=neural_result,
            strategy_used=ReasoningStrategy.PARALLEL,
            total_time_ms=0
        )
    
    async def _weighted_reasoning(self, context: ReasoningContext) -> HybridDecision:
        """Combine symbolic and neural reasoning using dynamic weights."""
        # Perform both types of reasoning
        symbolic_result = await self._perform_symbolic_reasoning(context)
        neural_result = await self._perform_neural_reasoning(context)
        
        # Calculate dynamic weights based on confidence and context
        symbolic_weight = self._calculate_dynamic_weight(symbolic_result, context, "symbolic")
        neural_weight = self._calculate_dynamic_weight(neural_result, context, "neural")
        
        # Normalize weights
        total_weight = symbolic_weight + neural_weight
        if total_weight > 0:
            symbolic_weight /= total_weight
            neural_weight /= total_weight
        else:
            symbolic_weight = neural_weight = 0.5
        
        # Weighted combination
        combined_confidence = (symbolic_result.overall_confidence * symbolic_weight + 
                             neural_result.overall_confidence * neural_weight)
        
        # Choose decision based on weighted confidence
        if symbolic_result.overall_confidence * symbolic_weight > neural_result.overall_confidence * neural_weight:
            decision = symbolic_result.decision
        else:
            decision = neural_result.decision
        
        explanation = f"Weighted reasoning: symbolic (weight: {symbolic_weight:.3f}, confidence: {symbolic_result.overall_confidence:.3f}), " \
                     f"neural (weight: {neural_weight:.3f}, confidence: {neural_result.overall_confidence:.3f}). " \
                     f"Final decision: {decision}"
        
        return HybridDecision(
            decision=decision,
            confidence=combined_confidence,
            explanation=explanation,
            reasoning_breakdown={
                "approach": "weighted",
                "symbolic_weight": symbolic_weight,
                "neural_weight": neural_weight,
                "symbolic_confidence": symbolic_result.overall_confidence,
                "neural_confidence": neural_result.overall_confidence,
                "combined_confidence": combined_confidence
            },
            symbolic_result=symbolic_result,
            neural_result=neural_result,
            strategy_used=ReasoningStrategy.WEIGHTED,
            total_time_ms=0
        )
    
    async def _ensemble_reasoning(self, context: ReasoningContext) -> HybridDecision:
        """Use ensemble voting approach for decision making."""
        # Perform both types of reasoning
        symbolic_result = await self._perform_symbolic_reasoning(context)
        neural_result = await self._perform_neural_reasoning(context)
        
        # Collect all decisions with their confidence scores
        decisions = []
        
        if symbolic_result.decision:
            decisions.append((symbolic_result.decision, symbolic_result.overall_confidence, "symbolic"))
        
        if neural_result.decision:
            decisions.append((neural_result.decision, neural_result.overall_confidence, "neural"))
        
        # Ensemble voting
        if not decisions:
            final_decision = "uncertain"
            final_confidence = 0.0
        elif len(decisions) == 1:
            final_decision = decisions[0][0]
            final_confidence = decisions[0][1]
        else:
            # Vote based on confidence-weighted decisions
            decision_votes = {}
            for decision, confidence, source in decisions:
                if decision not in decision_votes:
                    decision_votes[decision] = 0
                decision_votes[decision] += confidence
            
            # Choose decision with highest vote
            final_decision = max(decision_votes.items(), key=lambda x: x[1])[0]
            final_confidence = decision_votes[final_decision] / len(decisions)
        
        explanation = f"Ensemble voting from {len(decisions)} sources. Final decision: {final_decision}"
        
        return HybridDecision(
            decision=final_decision,
            confidence=final_confidence,
            explanation=explanation,
            reasoning_breakdown={
                "approach": "ensemble",
                "num_voters": len(decisions),
                "votes": {d[0]: d[1] for d in decisions},
                "final_decision": final_decision,
                "final_confidence": final_confidence
            },
            symbolic_result=symbolic_result,
            neural_result=neural_result,
            strategy_used=ReasoningStrategy.ENSEMBLE,
            total_time_ms=0
        )
    
    async def _perform_symbolic_reasoning(self, context: ReasoningContext) -> SymbolicReasoning:
        """Perform symbolic rule-based reasoning."""
        start_time = time.time()
        
        # Evaluate rules
        rule_results = await self.rule_engine.evaluate_rules_async(
            context.input_data,
            tags=context.rule_tags
        )
        
        # Calculate overall confidence and decision
        triggered_rules = [r for r in rule_results if r.triggered]
        
        if triggered_rules:
            # Use highest confidence rule for decision
            best_rule = max(triggered_rules, key=lambda r: r.confidence)
            decision = str(best_rule.output) if best_rule.output else "triggered"
            overall_confidence = best_rule.confidence
            explanation = f"Rule '{best_rule.rule_id}' triggered with confidence {best_rule.confidence:.3f}"
        else:
            decision = None
            overall_confidence = 0.0
            explanation = "No rules triggered"
        
        evaluation_time_ms = (time.time() - start_time) * 1000
        
        return SymbolicReasoning(
            triggered_rules=triggered_rules,
            overall_confidence=overall_confidence,
            decision=decision,
            explanation=explanation,
            evaluation_time_ms=evaluation_time_ms
        )
    
    async def _perform_neural_reasoning(self, context: ReasoningContext) -> NeuralReasoning:
        """Perform neural model-based reasoning."""
        start_time = time.time()
        
        # Consult neural models
        model_predictions = []
        
        models_to_consult = context.preferred_models or ["default_classifier"]
        
        for model_id in models_to_consult:
            try:
                request = InferenceRequest(
                    model_id=model_id,
                    input_data=context.input_data,
                    timeout_ms=context.timeout_ms // len(models_to_consult)
                )
                
                prediction = await self.learning_client.get_inference(request)
                model_predictions.append(prediction)
                
            except Exception as e:
                self.logger.warning(f"Failed to get prediction from {model_id}: {e}")
        
        # Aggregate predictions
        if model_predictions:
            # Use highest confidence prediction
            best_prediction = max(model_predictions, key=lambda p: p.confidence)
            
            aggregated_prediction = best_prediction.prediction
            overall_confidence = best_prediction.confidence
            decision = str(aggregated_prediction.get("class", "unknown"))
            explanation = f"Neural model '{best_prediction.model_id}' predicted '{decision}' with confidence {overall_confidence:.3f}"
        else:
            aggregated_prediction = {}
            overall_confidence = 0.0
            decision = None
            explanation = "No neural predictions available"
        
        inference_time_ms = (time.time() - start_time) * 1000
        
        return NeuralReasoning(
            model_predictions=model_predictions,
            aggregated_prediction=aggregated_prediction,
            overall_confidence=overall_confidence,
            decision=decision,
            explanation=explanation,
            inference_time_ms=inference_time_ms
        )
    
    def _check_contradiction(self, neural_result: NeuralReasoning, symbolic_result: SymbolicReasoning) -> bool:
        """Check if neural and symbolic results contradict each other."""
        if not neural_result.decision or not symbolic_result.decision:
            return False
        
        # Simple contradiction check - can be made more sophisticated
        return neural_result.decision != symbolic_result.decision
    
    def _calculate_dynamic_weight(self, result: Union[SymbolicReasoning, NeuralReasoning], 
                                 context: ReasoningContext, source: str) -> float:
        """Calculate dynamic weight based on confidence and context."""
        base_weight = self.symbolic_weight if source == "symbolic" else self.neural_weight
        
        # Adjust weight based on confidence
        confidence_factor = result.overall_confidence
        
        # Adjust weight based on context (can be extended)
        context_factor = 1.0
        
        return base_weight * confidence_factor * context_factor
