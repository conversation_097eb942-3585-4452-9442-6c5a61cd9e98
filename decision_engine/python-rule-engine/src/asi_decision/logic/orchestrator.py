"""
Decision orchestrator for the ASI Decision Engine.

Coordinates hybrid symbolic/neural reasoning by combining rule-based logic
with neural model outputs to make context-aware decisions.
"""

import asyncio
import time
import uuid
from dataclasses import dataclass, field
from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from enum import Enum

from ..rules.engine import RuleEngine, RuleEvaluationResult
from ..integration.learning_engine import LearningEngineClient, ModelConsultationService, ModelPrediction
from ..fallback.handler import <PERSON>back<PERSON><PERSON><PERSON>
from ..utils.logger import get_logger, PerformanceLogger, LoggingContext
from ..utils.metrics import DecisionMetrics, get_metrics_collector
from ..utils.config import Config

logger = get_logger(__name__)


class DecisionType(Enum):
    """Types of decisions the engine can make."""
    CLASSIFICATION = "classification"
    REGRESSION = "regression"
    RECOMMENDATION = "recommendation"
    OPTIMIZATION = "optimization"
    CONTROL = "control"
    PLANNING = "planning"


class DecisionPriority(Enum):
    """Priority levels for decisions."""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4
    EMERGENCY = 5


@dataclass
class DecisionContext:
    """Context information for decision making."""
    input_data: Dict[str, Any]
    historical_decisions: List[Dict[str, Any]] = field(default_factory=list)
    environmental_context: Dict[str, Any] = field(default_factory=dict)
    agent_context: Dict[str, Any] = field(default_factory=dict)
    system_state: Dict[str, Any] = field(default_factory=dict)
    constraints: List[Dict[str, Any]] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class DecisionRequest:
    """Request for making a decision."""
    request_id: str
    decision_type: DecisionType
    priority: DecisionPriority
    context: DecisionContext
    config: Dict[str, Any] = field(default_factory=dict)
    timeout_ms: int = 10000
    timestamp: datetime = field(default_factory=datetime.utcnow)


@dataclass
class DecisionResult:
    """Result of a decision."""
    request_id: str
    decision_id: str
    decision_type: DecisionType
    decision_value: Any
    confidence: float
    alternatives: List[Dict[str, Any]] = field(default_factory=list)
    applied_rules: List[str] = field(default_factory=list)
    consulted_models: List[str] = field(default_factory=list)
    used_fallback: bool = False
    explanation: str = ""
    processing_time_ms: float = 0
    timestamp: datetime = field(default_factory=datetime.utcnow)


@dataclass
class HybridReasoningResult:
    """Result of hybrid reasoning combining rules and models."""
    rule_results: List[RuleEvaluationResult]
    model_predictions: List[ModelPrediction]
    combined_confidence: float
    reasoning_explanation: str
    rule_weight: float
    model_weight: float


class DecisionOrchestrator:
    """Main orchestrator for hybrid decision making."""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = get_logger(__name__)
        self.metrics = get_metrics_collector()
        
        # Initialize components
        self.rule_engine = RuleEngine(
            max_rules=config.rule_engine.max_rules,
            enable_caching=config.rule_engine.enable_rule_caching
        )
        
        self.learning_client = LearningEngineClient(config)
        self.model_service = ModelConsultationService(self.learning_client, config)
        self.fallback_handler = FallbackHandler(config)
        
        # Decision state
        self.active_decisions: Dict[str, DecisionRequest] = {}
        
        self.logger.info("Decision orchestrator initialized")
    
    async def initialize(self) -> bool:
        """Initialize the orchestrator and its components."""
        try:
            # Connect to Learning Engine
            if self.config.decision.use_neural_models:
                connected = await self.learning_client.connect()
                if not connected:
                    self.logger.warning("Failed to connect to Learning Engine, neural models disabled")
                    self.config.decision.use_neural_models = False
            
            self.logger.info("Decision orchestrator initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize decision orchestrator: {e}")
            return False
    
    async def make_decision(self, request: DecisionRequest) -> DecisionResult:
        """Make a decision using hybrid reasoning."""
        decision_id = str(uuid.uuid4())
        start_time = time.time()
        
        with LoggingContext(self.logger, decision_id=decision_id, request_id=request.request_id):
            try:
                # Track active decision
                self.active_decisions[decision_id] = request
                
                with PerformanceLogger(self.logger, f"decision_{request.decision_type.value}"):
                    # Perform hybrid reasoning
                    reasoning_result = await self._perform_hybrid_reasoning(request.context, request.config)
                    
                    # Make final decision
                    decision_value, confidence, explanation = self._make_final_decision(
                        reasoning_result, request.decision_type, request.context
                    )
                    
                    # Check if fallback is needed
                    used_fallback = False
                    if confidence < self.config.decision.default_confidence_threshold:
                        if self.config.decision.enable_fallback_logic:
                            fallback_result = await self.fallback_handler.handle_low_confidence(
                                request, reasoning_result, confidence
                            )
                            if fallback_result:
                                decision_value = fallback_result.decision_value
                                confidence = fallback_result.confidence
                                explanation = fallback_result.explanation
                                used_fallback = True
                    
                    processing_time_ms = (time.time() - start_time) * 1000
                    
                    # Create result
                    result = DecisionResult(
                        request_id=request.request_id,
                        decision_id=decision_id,
                        decision_type=request.decision_type,
                        decision_value=decision_value,
                        confidence=confidence,
                        applied_rules=[r.rule_id for r in reasoning_result.rule_results if r.triggered],
                        consulted_models=[p.model_id for p in reasoning_result.model_predictions],
                        used_fallback=used_fallback,
                        explanation=explanation,
                        processing_time_ms=processing_time_ms
                    )
                    
                    # Record metrics
                    self.metrics.record_decision(DecisionMetrics(
                        decision_id=decision_id,
                        decision_type=request.decision_type.value,
                        processing_time_ms=processing_time_ms,
                        confidence=confidence,
                        rules_evaluated=len(reasoning_result.rule_results),
                        models_consulted=len(reasoning_result.model_predictions),
                        used_fallback=used_fallback,
                        memory_usage_mb=self._get_memory_usage()
                    ))
                    
                    self.logger.info(
                        f"Decision completed: {decision_id}",
                        extra={
                            'confidence': confidence,
                            'processing_time_ms': processing_time_ms,
                            'used_fallback': used_fallback
                        }
                    )
                    
                    return result
                    
            except Exception as e:
                processing_time_ms = (time.time() - start_time) * 1000
                
                self.logger.error(f"Error making decision {decision_id}: {e}")
                
                # Try fallback for errors
                if self.config.decision.enable_fallback_logic:
                    fallback_result = await self.fallback_handler.handle_error(request, str(e))
                    if fallback_result:
                        return DecisionResult(
                            request_id=request.request_id,
                            decision_id=decision_id,
                            decision_type=request.decision_type,
                            decision_value=fallback_result.decision_value,
                            confidence=fallback_result.confidence,
                            used_fallback=True,
                            explanation=f"Fallback due to error: {e}",
                            processing_time_ms=processing_time_ms
                        )
                
                # Return error result
                return DecisionResult(
                    request_id=request.request_id,
                    decision_id=decision_id,
                    decision_type=request.decision_type,
                    decision_value=None,
                    confidence=0.0,
                    explanation=f"Error: {str(e)}",
                    processing_time_ms=processing_time_ms
                )
                
            finally:
                # Clean up
                if decision_id in self.active_decisions:
                    del self.active_decisions[decision_id]
    
    async def _perform_hybrid_reasoning(self, context: DecisionContext, config: Dict[str, Any]) -> HybridReasoningResult:
        """Perform hybrid reasoning combining rules and neural models."""
        rule_results = []
        model_predictions = []
        
        # Evaluate rules if enabled
        if self.config.decision.use_rule_engine:
            with PerformanceLogger(self.logger, "rule_evaluation"):
                # Convert context to rule evaluation format
                rule_context = self._prepare_rule_context(context)
                rule_results = self.rule_engine.evaluate_rules(rule_context)
        
        # Consult neural models if enabled
        if self.config.decision.use_neural_models:
            with PerformanceLogger(self.logger, "model_consultation"):
                # Convert context to model input format
                model_context = self._prepare_model_context(context)
                model_ids = config.get('preferred_models', [])
                model_predictions = await self.model_service.consult_models(model_context, model_ids)
        
        # Combine results
        combined_confidence = self._calculate_combined_confidence(rule_results, model_predictions)
        explanation = self._generate_reasoning_explanation(rule_results, model_predictions)
        
        return HybridReasoningResult(
            rule_results=rule_results,
            model_predictions=model_predictions,
            combined_confidence=combined_confidence,
            reasoning_explanation=explanation,
            rule_weight=self.config.decision.rule_engine_weight,
            model_weight=self.config.decision.neural_model_weight
        )
    
    def _make_final_decision(self, reasoning_result: HybridReasoningResult, decision_type: DecisionType, context: DecisionContext) -> tuple:
        """Make the final decision based on hybrid reasoning results."""
        if self.config.decision.hybrid_combination_method == "weighted_average":
            return self._weighted_average_decision(reasoning_result, decision_type)
        elif self.config.decision.hybrid_combination_method == "voting":
            return self._voting_decision(reasoning_result, decision_type)
        elif self.config.decision.hybrid_combination_method == "stacking":
            return self._stacking_decision(reasoning_result, decision_type, context)
        else:
            return self._weighted_average_decision(reasoning_result, decision_type)
    
    def _weighted_average_decision(self, reasoning_result: HybridReasoningResult, decision_type: DecisionType) -> tuple:
        """Make decision using weighted average of rules and models."""
        rule_weight = reasoning_result.rule_weight
        model_weight = reasoning_result.model_weight
        
        # Get rule-based decision
        rule_decision, rule_confidence = self._extract_rule_decision(reasoning_result.rule_results)
        
        # Get model-based decision
        model_decision, model_confidence = self._extract_model_decision(reasoning_result.model_predictions)
        
        # Combine decisions
        if rule_decision is not None and model_decision is not None:
            # Both available - weighted combination
            combined_confidence = rule_confidence * rule_weight + model_confidence * model_weight
            
            # For classification, use confidence-weighted voting
            if decision_type == DecisionType.CLASSIFICATION:
                if rule_confidence * rule_weight > model_confidence * model_weight:
                    decision_value = rule_decision
                else:
                    decision_value = model_decision
            else:
                # For numeric decisions, weighted average
                if isinstance(rule_decision, (int, float)) and isinstance(model_decision, (int, float)):
                    decision_value = rule_decision * rule_weight + model_decision * model_weight
                else:
                    decision_value = rule_decision if rule_confidence > model_confidence else model_decision
            
            explanation = f"Hybrid decision: rules ({rule_confidence:.2f}) + models ({model_confidence:.2f}) = {combined_confidence:.2f}"
            
        elif rule_decision is not None:
            # Only rules available
            decision_value = rule_decision
            combined_confidence = rule_confidence
            explanation = f"Rule-based decision: {rule_confidence:.2f}"
            
        elif model_decision is not None:
            # Only models available
            decision_value = model_decision
            combined_confidence = model_confidence
            explanation = f"Model-based decision: {model_confidence:.2f}"
            
        else:
            # No decisions available
            decision_value = None
            combined_confidence = 0.0
            explanation = "No valid decisions from rules or models"
        
        return decision_value, combined_confidence, explanation
    
    def _voting_decision(self, reasoning_result: HybridReasoningResult, decision_type: DecisionType) -> tuple:
        """Make decision using voting between rules and models."""
        # Simplified voting implementation
        votes = {}
        total_confidence = 0
        
        # Add rule votes
        for rule_result in reasoning_result.rule_results:
            if rule_result.triggered and rule_result.output is not None:
                vote = str(rule_result.output)
                if vote not in votes:
                    votes[vote] = 0
                votes[vote] += reasoning_result.rule_weight
                total_confidence += rule_result.confidence * reasoning_result.rule_weight
        
        # Add model votes
        for prediction in reasoning_result.model_predictions:
            if prediction.prediction is not None:
                vote = str(prediction.prediction)
                if vote not in votes:
                    votes[vote] = 0
                votes[vote] += reasoning_result.model_weight
                total_confidence += prediction.confidence * reasoning_result.model_weight
        
        if votes:
            winning_vote = max(votes, key=votes.get)
            confidence = total_confidence / (reasoning_result.rule_weight + reasoning_result.model_weight)
            explanation = f"Voting decision: {winning_vote} (votes: {votes})"
            return winning_vote, confidence, explanation
        
        return None, 0.0, "No votes available"
    
    def _stacking_decision(self, reasoning_result: HybridReasoningResult, decision_type: DecisionType, context: DecisionContext) -> tuple:
        """Make decision using stacking (meta-learning) approach."""
        # Simplified stacking - in real implementation, this would use a trained meta-model
        # For now, use weighted average with dynamic weights based on historical performance
        
        # Get base decisions
        rule_decision, rule_confidence = self._extract_rule_decision(reasoning_result.rule_results)
        model_decision, model_confidence = self._extract_model_decision(reasoning_result.model_predictions)
        
        # Dynamic weight adjustment (simplified)
        if rule_confidence > 0.8:
            rule_weight = 0.7
            model_weight = 0.3
        elif model_confidence > 0.8:
            rule_weight = 0.3
            model_weight = 0.7
        else:
            rule_weight = 0.5
            model_weight = 0.5
        
        # Apply weighted combination
        if rule_decision is not None and model_decision is not None:
            combined_confidence = rule_confidence * rule_weight + model_confidence * model_weight
            
            if isinstance(rule_decision, (int, float)) and isinstance(model_decision, (int, float)):
                decision_value = rule_decision * rule_weight + model_decision * model_weight
            else:
                decision_value = rule_decision if rule_confidence > model_confidence else model_decision
            
            explanation = f"Stacking decision: dynamic weights R:{rule_weight:.2f} M:{model_weight:.2f}"
            return decision_value, combined_confidence, explanation
        
        # Fallback to single source
        if rule_decision is not None:
            return rule_decision, rule_confidence, "Stacking: rule-only"
        elif model_decision is not None:
            return model_decision, model_confidence, "Stacking: model-only"
        
        return None, 0.0, "Stacking: no decisions available"
    
    def _prepare_rule_context(self, context: DecisionContext) -> Dict[str, Any]:
        """Prepare context for rule evaluation."""
        return {
            **context.input_data,
            "environment": context.environmental_context,
            "agent": context.agent_context,
            "system": context.system_state,
            "constraints": context.constraints,
            "metadata": context.metadata
        }
    
    def _prepare_model_context(self, context: DecisionContext) -> Dict[str, Any]:
        """Prepare context for model inference."""
        return context.input_data
    
    def _calculate_combined_confidence(self, rule_results: List[RuleEvaluationResult], model_predictions: List[ModelPrediction]) -> float:
        """Calculate combined confidence from rules and models."""
        rule_confidences = [r.confidence for r in rule_results if r.triggered]
        model_confidences = [p.confidence for p in model_predictions]
        
        all_confidences = rule_confidences + model_confidences
        return sum(all_confidences) / len(all_confidences) if all_confidences else 0.0
    
    def _generate_reasoning_explanation(self, rule_results: List[RuleEvaluationResult], model_predictions: List[ModelPrediction]) -> str:
        """Generate explanation of the reasoning process."""
        explanations = []
        
        triggered_rules = [r for r in rule_results if r.triggered]
        if triggered_rules:
            explanations.append(f"Triggered {len(triggered_rules)} rules")
        
        if model_predictions:
            explanations.append(f"Consulted {len(model_predictions)} models")
        
        return "; ".join(explanations) if explanations else "No reasoning components active"
    
    def _extract_rule_decision(self, rule_results: List[RuleEvaluationResult]) -> tuple:
        """Extract decision and confidence from rule results."""
        triggered_rules = [r for r in rule_results if r.triggered and r.output is not None]
        
        if not triggered_rules:
            return None, 0.0
        
        # Use highest priority/confidence rule
        best_rule = max(triggered_rules, key=lambda r: r.confidence)
        return best_rule.output, best_rule.confidence
    
    def _extract_model_decision(self, model_predictions: List[ModelPrediction]) -> tuple:
        """Extract decision and confidence from model predictions."""
        if not model_predictions:
            return None, 0.0
        
        # Combine predictions
        combined = self.model_service.combine_predictions(
            model_predictions, 
            self.config.decision.hybrid_combination_method
        )
        
        if combined:
            return combined["prediction"], combined["confidence"]
        
        return None, 0.0
    
    def _get_memory_usage(self) -> float:
        """Get current memory usage in MB."""
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except:
            return 0.0
    
    async def shutdown(self):
        """Shutdown the orchestrator and cleanup resources."""
        await self.learning_client.disconnect()
        self.logger.info("Decision orchestrator shutdown complete")
