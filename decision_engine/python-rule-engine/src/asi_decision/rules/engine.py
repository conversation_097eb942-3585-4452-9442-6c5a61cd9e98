"""
Rule-based decision engine for the ASI Decision Engine.

Provides rule definition, management, evaluation, and caching with support
for complex logic trees, conditional expressions, and dynamic rule updates.
"""

import ast
import json
import time
import threading
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Set, Union, Callable
from enum import Enum
import re
import operator

from ..utils.logger import get_logger, PerformanceLogger
from ..utils.metrics import MetricsCollector, RuleMetrics, get_metrics_collector

logger = get_logger(__name__)


class RuleType(Enum):
    """Types of rules supported by the engine."""
    CONDITIONAL = "conditional"
    THRESHOLD = "threshold"
    PATTERN = "pattern"
    TEMPORAL = "temporal"
    LOGICAL = "logical"
    ML_BASED = "ml_based"


class RuleStatus(Enum):
    """Rule execution status."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    DISABLED = "disabled"
    ERROR = "error"


@dataclass
class RuleCondition:
    """Represents a rule condition."""
    field: str
    operator: str
    value: Any
    weight: float = 1.0
    
    def evaluate(self, context: Dict[str, Any]) -> bool:
        """Evaluate the condition against context."""
        try:
            field_value = self._get_field_value(context, self.field)
            return self._apply_operator(field_value, self.operator, self.value)
        except Exception as e:
            logger.warning(f"Error evaluating condition {self.field} {self.operator} {self.value}: {e}")
            return False
    
    def _get_field_value(self, context: Dict[str, Any], field_path: str) -> Any:
        """Get field value from context using dot notation."""
        keys = field_path.split('.')
        value = context
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                raise KeyError(f"Field {field_path} not found in context")
        return value
    
    def _apply_operator(self, field_value: Any, op: str, target_value: Any) -> bool:
        """Apply comparison operator."""
        operators = {
            '==': operator.eq,
            '!=': operator.ne,
            '<': operator.lt,
            '<=': operator.le,
            '>': operator.gt,
            '>=': operator.ge,
            'in': lambda x, y: x in y,
            'not_in': lambda x, y: x not in y,
            'contains': lambda x, y: y in x,
            'startswith': lambda x, y: str(x).startswith(str(y)),
            'endswith': lambda x, y: str(x).endswith(str(y)),
            'regex': lambda x, y: bool(re.match(y, str(x))),
            'exists': lambda x, y: x is not None,
            'not_exists': lambda x, y: x is None,
        }
        
        if op not in operators:
            raise ValueError(f"Unsupported operator: {op}")
        
        return operators[op](field_value, target_value)


@dataclass
class RuleAction:
    """Represents a rule action."""
    action_type: str
    parameters: Dict[str, Any] = field(default_factory=dict)
    
    def execute(self, context: Dict[str, Any]) -> Any:
        """Execute the action."""
        if self.action_type == "return_value":
            return self.parameters.get("value")
        elif self.action_type == "set_field":
            field = self.parameters.get("field")
            value = self.parameters.get("value")
            if field:
                self._set_field_value(context, field, value)
            return value
        elif self.action_type == "call_function":
            function_name = self.parameters.get("function")
            args = self.parameters.get("args", [])
            kwargs = self.parameters.get("kwargs", {})
            # In a real implementation, you'd have a registry of callable functions
            return f"Called {function_name} with {args}, {kwargs}"
        else:
            logger.warning(f"Unknown action type: {self.action_type}")
            return None
    
    def _set_field_value(self, context: Dict[str, Any], field_path: str, value: Any):
        """Set field value in context using dot notation."""
        keys = field_path.split('.')
        current = context
        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        current[keys[-1]] = value


@dataclass
class Rule:
    """Represents a decision rule."""
    rule_id: str
    name: str
    description: str
    rule_type: RuleType
    conditions: List[RuleCondition]
    action: RuleAction
    priority: int = 0
    weight: float = 1.0
    enabled: bool = True
    tags: List[str] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    author: str = "system"
    version: int = 1
    
    # Execution settings
    timeout_ms: int = 1000
    max_retries: int = 0
    
    # Logic settings
    condition_logic: str = "AND"  # AND, OR, CUSTOM
    custom_logic: Optional[str] = None  # For complex logic expressions
    
    def evaluate(self, context: Dict[str, Any]) -> 'RuleEvaluationResult':
        """Evaluate the rule against the given context."""
        start_time = time.time()
        
        try:
            # Check if rule is enabled
            if not self.enabled:
                return RuleEvaluationResult(
                    rule_id=self.rule_id,
                    triggered=False,
                    confidence=0.0,
                    output=None,
                    explanation="Rule is disabled",
                    evaluation_time_ms=0
                )
            
            # Evaluate conditions
            triggered = self._evaluate_conditions(context)
            
            # Execute action if triggered
            output = None
            if triggered:
                output = self.action.execute(context)
            
            evaluation_time_ms = (time.time() - start_time) * 1000
            
            return RuleEvaluationResult(
                rule_id=self.rule_id,
                triggered=triggered,
                confidence=1.0 if triggered else 0.0,
                output=output,
                explanation=self._generate_explanation(context, triggered),
                evaluation_time_ms=evaluation_time_ms
            )
            
        except Exception as e:
            evaluation_time_ms = (time.time() - start_time) * 1000
            logger.error(f"Error evaluating rule {self.rule_id}: {e}")
            
            return RuleEvaluationResult(
                rule_id=self.rule_id,
                triggered=False,
                confidence=0.0,
                output=None,
                explanation=f"Error: {str(e)}",
                evaluation_time_ms=evaluation_time_ms
            )
    
    def _evaluate_conditions(self, context: Dict[str, Any]) -> bool:
        """Evaluate all conditions based on the logic setting."""
        if not self.conditions:
            return True
        
        condition_results = [condition.evaluate(context) for condition in self.conditions]
        
        if self.condition_logic == "AND":
            return all(condition_results)
        elif self.condition_logic == "OR":
            return any(condition_results)
        elif self.condition_logic == "CUSTOM" and self.custom_logic:
            return self._evaluate_custom_logic(condition_results)
        else:
            logger.warning(f"Unknown condition logic: {self.condition_logic}")
            return all(condition_results)  # Default to AND
    
    def _evaluate_custom_logic(self, condition_results: List[bool]) -> bool:
        """Evaluate custom logic expression."""
        try:
            # Replace condition references with actual results
            expression = self.custom_logic
            for i, result in enumerate(condition_results):
                expression = expression.replace(f"C{i}", str(result))
            
            # Safely evaluate the expression
            return self._safe_eval_bool(expression)
        except Exception as e:
            logger.error(f"Error evaluating custom logic '{self.custom_logic}': {e}")
            return False
    
    def _safe_eval_bool(self, expression: str) -> bool:
        """Safely evaluate a boolean expression."""
        # Only allow safe operations
        allowed_names = {
            "True": True,
            "False": False,
            "and": operator.and_,
            "or": operator.or_,
            "not": operator.not_,
        }
        
        try:
            # Parse the expression
            node = ast.parse(expression, mode='eval')
            
            # Evaluate with restricted names
            result = eval(compile(node, '<string>', 'eval'), {"__builtins__": {}}, allowed_names)
            return bool(result)
        except Exception as e:
            logger.error(f"Error in safe_eval_bool: {e}")
            return False
    
    def _generate_explanation(self, context: Dict[str, Any], triggered: bool) -> str:
        """Generate human-readable explanation of rule evaluation."""
        if not self.conditions:
            return "No conditions to evaluate"
        
        explanations = []
        for i, condition in enumerate(self.conditions):
            try:
                field_value = condition._get_field_value(context, condition.field)
                result = condition.evaluate(context)
                explanations.append(
                    f"Condition {i+1}: {condition.field} ({field_value}) {condition.operator} {condition.value} = {result}"
                )
            except Exception as e:
                explanations.append(f"Condition {i+1}: Error - {e}")
        
        logic_explanation = f"Logic: {self.condition_logic}"
        if self.custom_logic:
            logic_explanation += f" ({self.custom_logic})"
        
        result_explanation = f"Result: {'TRIGGERED' if triggered else 'NOT TRIGGERED'}"
        
        return f"{logic_explanation}\n" + "\n".join(explanations) + f"\n{result_explanation}"


@dataclass
class RuleEvaluationResult:
    """Result of rule evaluation."""
    rule_id: str
    triggered: bool
    confidence: float
    output: Any
    explanation: str
    evaluation_time_ms: float


class RuleEngine:
    """Main rule engine for evaluating rules against context."""
    
    def __init__(self, max_rules: int = 10000, enable_caching: bool = True):
        self.max_rules = max_rules
        self.enable_caching = enable_caching
        self.logger = get_logger(__name__)
        self.metrics = get_metrics_collector()
        
        # Rule storage
        self.rules: Dict[str, Rule] = {}
        self.rules_by_tag: Dict[str, Set[str]] = {}
        self.rules_by_type: Dict[RuleType, Set[str]] = {}
        
        # Caching
        self.cache: Dict[str, Any] = {}
        self.cache_ttl: Dict[str, datetime] = {}
        self.cache_size_limit = 1000
        
        # Thread safety
        self._lock = threading.RLock()
        
        self.logger.info("Rule engine initialized")
    
    def add_rule(self, rule: Rule) -> bool:
        """Add a rule to the engine."""
        with self._lock:
            if len(self.rules) >= self.max_rules:
                self.logger.error(f"Cannot add rule {rule.rule_id}: maximum rules limit reached")
                return False
            
            if rule.rule_id in self.rules:
                self.logger.warning(f"Rule {rule.rule_id} already exists, updating")
            
            self.rules[rule.rule_id] = rule
            
            # Update indexes
            for tag in rule.tags:
                if tag not in self.rules_by_tag:
                    self.rules_by_tag[tag] = set()
                self.rules_by_tag[tag].add(rule.rule_id)
            
            if rule.rule_type not in self.rules_by_type:
                self.rules_by_type[rule.rule_type] = set()
            self.rules_by_type[rule.rule_type].add(rule.rule_id)
            
            self.logger.info(f"Added rule {rule.rule_id}: {rule.name}")
            return True
    
    def remove_rule(self, rule_id: str) -> bool:
        """Remove a rule from the engine."""
        with self._lock:
            if rule_id not in self.rules:
                self.logger.warning(f"Rule {rule_id} not found")
                return False
            
            rule = self.rules[rule_id]
            
            # Remove from indexes
            for tag in rule.tags:
                if tag in self.rules_by_tag:
                    self.rules_by_tag[tag].discard(rule_id)
                    if not self.rules_by_tag[tag]:
                        del self.rules_by_tag[tag]
            
            if rule.rule_type in self.rules_by_type:
                self.rules_by_type[rule.rule_type].discard(rule_id)
                if not self.rules_by_type[rule.rule_type]:
                    del self.rules_by_type[rule.rule_type]
            
            del self.rules[rule_id]
            
            # Clear related cache entries
            self._clear_cache_for_rule(rule_id)
            
            self.logger.info(f"Removed rule {rule_id}")
            return True
    
    def evaluate_rules(self, context: Dict[str, Any], rule_ids: Optional[List[str]] = None) -> List[RuleEvaluationResult]:
        """Evaluate rules against the given context."""
        with PerformanceLogger(self.logger, "rule_evaluation"):
            with self._lock:
                # Determine which rules to evaluate
                if rule_ids is None:
                    rules_to_evaluate = list(self.rules.values())
                else:
                    rules_to_evaluate = [self.rules[rid] for rid in rule_ids if rid in self.rules]
                
                # Sort by priority (higher priority first)
                rules_to_evaluate.sort(key=lambda r: r.priority, reverse=True)
                
                results = []
                for rule in rules_to_evaluate:
                    if rule.enabled:
                        result = rule.evaluate(context)
                        results.append(result)
                        
                        # Record metrics
                        self.metrics.record_rule_evaluation(RuleMetrics(
                            rule_id=rule.rule_id,
                            rule_name=rule.name,
                            evaluation_time_ms=result.evaluation_time_ms,
                            triggered=result.triggered,
                            confidence=result.confidence
                        ))
                
                self.logger.debug(f"Evaluated {len(results)} rules")
                return results
    
    def get_rules_by_tag(self, tag: str) -> List[Rule]:
        """Get all rules with a specific tag."""
        with self._lock:
            rule_ids = self.rules_by_tag.get(tag, set())
            return [self.rules[rid] for rid in rule_ids if rid in self.rules]
    
    def get_rules_by_type(self, rule_type: RuleType) -> List[Rule]:
        """Get all rules of a specific type."""
        with self._lock:
            rule_ids = self.rules_by_type.get(rule_type, set())
            return [self.rules[rid] for rid in rule_ids if rid in self.rules]
    
    def get_rule(self, rule_id: str) -> Optional[Rule]:
        """Get a specific rule by ID."""
        with self._lock:
            return self.rules.get(rule_id)
    
    def list_rules(self, enabled_only: bool = False) -> List[Rule]:
        """List all rules."""
        with self._lock:
            rules = list(self.rules.values())
            if enabled_only:
                rules = [r for r in rules if r.enabled]
            return rules
    
    def _clear_cache_for_rule(self, rule_id: str):
        """Clear cache entries related to a specific rule."""
        if not self.enable_caching:
            return
        
        keys_to_remove = [key for key in self.cache.keys() if rule_id in key]
        for key in keys_to_remove:
            del self.cache[key]
            if key in self.cache_ttl:
                del self.cache_ttl[key]
    
    def clear_cache(self):
        """Clear all cache entries."""
        with self._lock:
            self.cache.clear()
            self.cache_ttl.clear()
            self.logger.info("Rule engine cache cleared")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get engine statistics."""
        with self._lock:
            enabled_rules = sum(1 for rule in self.rules.values() if rule.enabled)
            
            return {
                'total_rules': len(self.rules),
                'enabled_rules': enabled_rules,
                'disabled_rules': len(self.rules) - enabled_rules,
                'rules_by_type': {
                    rule_type.value: len(rule_ids)
                    for rule_type, rule_ids in self.rules_by_type.items()
                },
                'cache_size': len(self.cache),
                'cache_hit_rate': getattr(self, '_cache_hit_rate', 0.0),
                'timestamp': datetime.utcnow().isoformat()
            }
