"""
Logging utilities for the ASI Decision Engine.

Provides structured logging with JSON formatting, correlation IDs,
and integration with monitoring systems.
"""

import json
import logging
import logging.config
import os
import sys
import time
from datetime import datetime
from typing import Any, Dict, Optional
import uuid

import structlog
from pythonjsonlogger import jsonlogger


class CorrelationFilter(logging.Filter):
    """Add correlation ID to log records."""
    
    def filter(self, record):
        if not hasattr(record, 'correlation_id'):
            record.correlation_id = getattr(self, '_correlation_id', 'unknown')
        return True


class ASIDecisionFormatter(jsonlogger.JsonFormatter):
    """Custom JSON formatter for ASI Decision Engine logs."""
    
    def add_fields(self, log_record: Dict[str, Any], record: logging.LogRecord, message_dict: Dict[str, Any]) -> None:
        super().add_fields(log_record, record, message_dict)
        
        # Add standard fields
        log_record['timestamp'] = datetime.utcnow().isoformat()
        log_record['service'] = 'asi-decision-engine'
        log_record['module'] = record.module
        log_record['function'] = record.funcName
        log_record['line'] = record.lineno
        
        # Add correlation ID if available
        if hasattr(record, 'correlation_id'):
            log_record['correlation_id'] = record.correlation_id
        
        # Add decision context if available
        if hasattr(record, 'decision_id'):
            log_record['decision_id'] = record.decision_id
        
        if hasattr(record, 'rule_id'):
            log_record['rule_id'] = record.rule_id
        
        # Add performance metrics if available
        if hasattr(record, 'duration_ms'):
            log_record['duration_ms'] = record.duration_ms
        
        if hasattr(record, 'memory_mb'):
            log_record['memory_mb'] = record.memory_mb


def get_logger(name: str, correlation_id: Optional[str] = None) -> logging.Logger:
    """
    Get a configured logger instance.
    
    Args:
        name: Logger name (usually __name__)
        correlation_id: Optional correlation ID for request tracking
    
    Returns:
        Configured logger instance
    """
    logger = logging.getLogger(name)
    
    # Configure logger if not already configured
    if not logger.handlers:
        # Set log level from environment
        log_level = os.getenv('LOG_LEVEL', 'INFO').upper()
        logger.setLevel(getattr(logging, log_level))
        
        # Create handler
        if os.getenv('LOG_FORMAT', 'json').lower() == 'json':
            handler = logging.StreamHandler(sys.stdout)
            formatter = ASIDecisionFormatter(
                fmt='%(timestamp)s %(level)s %(service)s %(module)s %(message)s'
            )
            handler.setFormatter(formatter)
        else:
            handler = logging.StreamHandler(sys.stdout)
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
        
        # Add correlation filter
        correlation_filter = CorrelationFilter()
        if correlation_id:
            correlation_filter._correlation_id = correlation_id
        handler.addFilter(correlation_filter)
        
        logger.addHandler(handler)
        
        # Prevent duplicate logs
        logger.propagate = False
    
    return logger


def configure_structlog():
    """Configure structlog for structured logging."""
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )


class LoggingContext:
    """Context manager for adding context to logs."""
    
    def __init__(self, logger: logging.Logger, **context):
        self.logger = logger
        self.context = context
        self.old_context = {}
    
    def __enter__(self):
        # Store old context
        for key, value in self.context.items():
            if hasattr(self.logger, key):
                self.old_context[key] = getattr(self.logger, key)
            setattr(self.logger, key, value)
        return self.logger
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        # Restore old context
        for key in self.context.keys():
            if key in self.old_context:
                setattr(self.logger, key, self.old_context[key])
            else:
                delattr(self.logger, key)


class PerformanceLogger:
    """Logger for performance metrics."""
    
    def __init__(self, logger: logging.Logger, operation: str):
        self.logger = logger
        self.operation = operation
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        self.logger.info(f"Starting {self.operation}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        duration_ms = (time.time() - self.start_time) * 1000
        
        if exc_type is None:
            self.logger.info(
                f"Completed {self.operation}",
                extra={'duration_ms': duration_ms}
            )
        else:
            self.logger.error(
                f"Failed {self.operation}: {exc_val}",
                extra={'duration_ms': duration_ms}
            )


# Initialize structlog
configure_structlog()

# Export main functions
__all__ = [
    'get_logger',
    'LoggingContext',
    'PerformanceLogger',
    'configure_structlog',
]
