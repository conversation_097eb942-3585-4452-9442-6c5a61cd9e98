"""
Configuration management for the ASI Decision Engine.

Provides hierarchical configuration loading from files, environment variables,
and command-line arguments with validation and type conversion.
"""

import os
import yaml
import json
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass, field
from datetime import timed<PERSON><PERSON>

from .logger import get_logger

logger = get_logger(__name__)


@dataclass
class KafkaConfig:
    """Kafka configuration."""
    bootstrap_servers: List[str] = field(default_factory=lambda: ["localhost:9092"])
    consumer_group: str = "asi-decision-engine"
    topics: List[str] = field(default_factory=lambda: ["asi-decision-engine", "asi-processed-data"])
    auto_offset_reset: str = "latest"
    enable_auto_commit: bool = False
    max_poll_records: int = 500
    session_timeout_ms: int = 30000
    heartbeat_interval_ms: int = 3000
    max_poll_interval_ms: int = 300000
    security_protocol: str = "PLAINTEXT"
    sasl_mechanism: Optional[str] = None
    sasl_username: Optional[str] = None
    sasl_password: Optional[str] = None


@dataclass
class GrpcConfig:
    """gRPC configuration."""
    server_port: int = 50070
    max_workers: int = 10
    max_message_length: int = 4 * 1024 * 1024  # 4MB
    keepalive_time_ms: int = 30000
    keepalive_timeout_ms: int = 5000
    keepalive_permit_without_calls: bool = True
    max_connection_idle_ms: int = 300000
    max_connection_age_ms: int = 600000
    
    # Learning Engine client configuration
    learning_engine_endpoint: str = "localhost:50060"
    learning_engine_timeout_ms: int = 5000
    learning_engine_retry_attempts: int = 3


@dataclass
class RuleEngineConfig:
    """Rule engine configuration."""
    max_rules: int = 10000
    max_rule_depth: int = 10
    rule_evaluation_timeout_ms: int = 1000
    enable_rule_caching: bool = True
    cache_size: int = 1000
    cache_ttl_seconds: int = 300
    enable_rule_validation: bool = True
    enable_rule_versioning: bool = True
    rule_storage_backend: str = "memory"  # memory, redis, database
    
    # Rule execution settings
    parallel_execution: bool = True
    max_parallel_rules: int = 100
    execution_timeout_ms: int = 5000


@dataclass
class DecisionConfig:
    """Decision processing configuration."""
    default_confidence_threshold: float = 0.7
    max_processing_time_ms: int = 10000
    enable_fallback_logic: bool = True
    fallback_confidence: float = 0.5
    enable_explanation: bool = True
    enable_decision_logging: bool = True
    
    # Neural model integration
    use_neural_models: bool = True
    neural_model_timeout_ms: int = 2000
    neural_model_weight: float = 0.6
    
    # Rule-based logic
    use_rule_engine: bool = True
    rule_engine_weight: float = 0.4
    
    # Hybrid reasoning
    enable_hybrid_reasoning: bool = True
    hybrid_combination_method: str = "weighted_average"  # weighted_average, voting, stacking


@dataclass
class MonitoringConfig:
    """Monitoring and observability configuration."""
    enable_metrics: bool = True
    metrics_port: int = 8080
    metrics_path: str = "/metrics"
    enable_tracing: bool = True
    tracing_endpoint: str = "http://localhost:14268/api/traces"
    tracing_service_name: str = "asi-decision-engine"
    
    # Health check configuration
    health_check_interval_seconds: int = 30
    health_check_timeout_seconds: int = 5
    
    # Performance monitoring
    enable_performance_logging: bool = True
    slow_decision_threshold_ms: int = 1000
    memory_usage_threshold_mb: int = 1000


@dataclass
class SecurityConfig:
    """Security configuration."""
    enable_authentication: bool = False
    enable_authorization: bool = False
    jwt_secret_key: Optional[str] = None
    jwt_expiration_hours: int = 24
    
    # API rate limiting
    enable_rate_limiting: bool = True
    rate_limit_requests_per_minute: int = 1000
    rate_limit_burst: int = 100
    
    # Data encryption
    enable_encryption: bool = False
    encryption_key: Optional[str] = None


@dataclass
class Config:
    """Main configuration class."""
    kafka: KafkaConfig = field(default_factory=KafkaConfig)
    grpc: GrpcConfig = field(default_factory=GrpcConfig)
    rule_engine: RuleEngineConfig = field(default_factory=RuleEngineConfig)
    decision: DecisionConfig = field(default_factory=DecisionConfig)
    monitoring: MonitoringConfig = field(default_factory=MonitoringConfig)
    security: SecurityConfig = field(default_factory=SecurityConfig)
    
    # General settings
    debug: bool = False
    log_level: str = "INFO"
    environment: str = "development"
    
    @classmethod
    def load(cls, config_path: Optional[str] = None) -> 'Config':
        """
        Load configuration from file and environment variables.
        
        Args:
            config_path: Path to configuration file
            
        Returns:
            Loaded configuration instance
        """
        config_data = {}
        
        # Load from file if provided
        if config_path and Path(config_path).exists():
            logger.info(f"Loading configuration from {config_path}")
            with open(config_path, 'r') as f:
                if config_path.endswith('.yaml') or config_path.endswith('.yml'):
                    config_data = yaml.safe_load(f)
                elif config_path.endswith('.json'):
                    config_data = json.load(f)
                else:
                    raise ValueError(f"Unsupported config file format: {config_path}")
        
        # Override with environment variables
        config_data = cls._load_from_env(config_data)
        
        # Create configuration instance
        config = cls._create_from_dict(config_data)
        
        logger.info("Configuration loaded successfully")
        return config
    
    @classmethod
    def _load_from_env(cls, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """Load configuration from environment variables."""
        env_mappings = {
            'KAFKA_BOOTSTRAP_SERVERS': ('kafka', 'bootstrap_servers'),
            'KAFKA_CONSUMER_GROUP': ('kafka', 'consumer_group'),
            'GRPC_SERVER_PORT': ('grpc', 'server_port'),
            'LEARNING_ENGINE_ENDPOINT': ('grpc', 'learning_engine_endpoint'),
            'RULE_ENGINE_MAX_RULES': ('rule_engine', 'max_rules'),
            'DECISION_CONFIDENCE_THRESHOLD': ('decision', 'default_confidence_threshold'),
            'MONITORING_ENABLE_METRICS': ('monitoring', 'enable_metrics'),
            'SECURITY_ENABLE_AUTH': ('security', 'enable_authentication'),
            'DEBUG': ('debug',),
            'LOG_LEVEL': ('log_level',),
            'ENVIRONMENT': ('environment',),
        }
        
        for env_var, config_path in env_mappings.items():
            value = os.getenv(env_var)
            if value is not None:
                cls._set_nested_value(config_data, config_path, cls._convert_env_value(value))
        
        return config_data
    
    @staticmethod
    def _set_nested_value(data: Dict[str, Any], path: tuple, value: Any):
        """Set a nested value in a dictionary."""
        current = data
        for key in path[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        current[path[-1]] = value
    
    @staticmethod
    def _convert_env_value(value: str) -> Any:
        """Convert environment variable string to appropriate type."""
        # Boolean conversion
        if value.lower() in ('true', 'false'):
            return value.lower() == 'true'
        
        # Integer conversion
        try:
            return int(value)
        except ValueError:
            pass
        
        # Float conversion
        try:
            return float(value)
        except ValueError:
            pass
        
        # List conversion (comma-separated)
        if ',' in value:
            return [item.strip() for item in value.split(',')]
        
        # Return as string
        return value
    
    @classmethod
    def _create_from_dict(cls, data: Dict[str, Any]) -> 'Config':
        """Create configuration instance from dictionary."""
        # Extract nested configurations
        kafka_config = KafkaConfig(**data.get('kafka', {}))
        grpc_config = GrpcConfig(**data.get('grpc', {}))
        rule_engine_config = RuleEngineConfig(**data.get('rule_engine', {}))
        decision_config = DecisionConfig(**data.get('decision', {}))
        monitoring_config = MonitoringConfig(**data.get('monitoring', {}))
        security_config = SecurityConfig(**data.get('security', {}))
        
        # Create main config
        return cls(
            kafka=kafka_config,
            grpc=grpc_config,
            rule_engine=rule_engine_config,
            decision=decision_config,
            monitoring=monitoring_config,
            security=security_config,
            debug=data.get('debug', False),
            log_level=data.get('log_level', 'INFO'),
            environment=data.get('environment', 'development'),
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return {
            'kafka': self.kafka.__dict__,
            'grpc': self.grpc.__dict__,
            'rule_engine': self.rule_engine.__dict__,
            'decision': self.decision.__dict__,
            'monitoring': self.monitoring.__dict__,
            'security': self.security.__dict__,
            'debug': self.debug,
            'log_level': self.log_level,
            'environment': self.environment,
        }
    
    def validate(self) -> List[str]:
        """Validate configuration and return list of errors."""
        errors = []
        
        # Validate Kafka configuration
        if not self.kafka.bootstrap_servers:
            errors.append("Kafka bootstrap servers cannot be empty")
        
        if not self.kafka.topics:
            errors.append("Kafka topics cannot be empty")
        
        # Validate gRPC configuration
        if not (1024 <= self.grpc.server_port <= 65535):
            errors.append("gRPC server port must be between 1024 and 65535")
        
        # Validate decision configuration
        if not (0.0 <= self.decision.default_confidence_threshold <= 1.0):
            errors.append("Confidence threshold must be between 0.0 and 1.0")
        
        if self.decision.neural_model_weight + self.decision.rule_engine_weight != 1.0:
            errors.append("Neural model weight and rule engine weight must sum to 1.0")
        
        return errors
