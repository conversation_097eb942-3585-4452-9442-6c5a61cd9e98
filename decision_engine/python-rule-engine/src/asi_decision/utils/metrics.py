"""
Metrics collection and monitoring for the ASI Decision Engine.

Provides comprehensive metrics collection for decision processing,
rule evaluation, model integration, and system performance.
"""

import time
import threading
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union
import psutil
import json

from prometheus_client import Counter, Histogram, Gauge, Summary, CollectorRegistry, generate_latest
from .logger import get_logger

logger = get_logger(__name__)


@dataclass
class DecisionMetrics:
    """Metrics for a single decision."""
    decision_id: str
    decision_type: str
    processing_time_ms: float
    confidence: float
    rules_evaluated: int
    models_consulted: int
    used_fallback: bool
    memory_usage_mb: float
    timestamp: datetime = field(default_factory=datetime.utcnow)


@dataclass
class RuleMetrics:
    """Metrics for rule evaluation."""
    rule_id: str
    rule_name: str
    evaluation_time_ms: float
    triggered: bool
    confidence: float
    timestamp: datetime = field(default_factory=datetime.utcnow)


@dataclass
class ModelMetrics:
    """Metrics for model consultation."""
    model_id: str
    model_type: str
    inference_time_ms: float
    confidence: float
    success: bool
    timestamp: datetime = field(default_factory=datetime.utcnow)


class MetricsCollector:
    """Comprehensive metrics collector for the Decision Engine."""
    
    def __init__(self, registry: Optional[CollectorRegistry] = None):
        self.registry = registry or CollectorRegistry()
        self.logger = get_logger(__name__)
        self._lock = threading.Lock()
        
        # Initialize Prometheus metrics
        self._init_prometheus_metrics()
        
        # In-memory metrics storage
        self.decision_history = deque(maxlen=10000)
        self.rule_history = deque(maxlen=50000)
        self.model_history = deque(maxlen=20000)
        
        # Performance tracking
        self.performance_stats = defaultdict(list)
        self.error_counts = defaultdict(int)
        
        # System metrics
        self.system_metrics = {}
        self._start_system_monitoring()
        
        self.logger.info("Metrics collector initialized")
    
    def _init_prometheus_metrics(self):
        """Initialize Prometheus metrics."""
        # Decision metrics
        self.decisions_total = Counter(
            'asi_decisions_total',
            'Total number of decisions made',
            ['decision_type', 'status'],
            registry=self.registry
        )
        
        self.decision_duration = Histogram(
            'asi_decision_duration_seconds',
            'Time spent processing decisions',
            ['decision_type'],
            registry=self.registry
        )
        
        self.decision_confidence = Histogram(
            'asi_decision_confidence',
            'Confidence scores of decisions',
            ['decision_type'],
            buckets=[0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0],
            registry=self.registry
        )
        
        # Rule metrics
        self.rules_evaluated = Counter(
            'asi_rules_evaluated_total',
            'Total number of rules evaluated',
            ['rule_id', 'triggered'],
            registry=self.registry
        )
        
        self.rule_evaluation_duration = Histogram(
            'asi_rule_evaluation_duration_seconds',
            'Time spent evaluating rules',
            ['rule_id'],
            registry=self.registry
        )
        
        # Model metrics
        self.model_consultations = Counter(
            'asi_model_consultations_total',
            'Total number of model consultations',
            ['model_id', 'model_type', 'status'],
            registry=self.registry
        )
        
        self.model_inference_duration = Histogram(
            'asi_model_inference_duration_seconds',
            'Time spent on model inference',
            ['model_id', 'model_type'],
            registry=self.registry
        )
        
        # System metrics
        self.memory_usage = Gauge(
            'asi_memory_usage_bytes',
            'Memory usage in bytes',
            registry=self.registry
        )
        
        self.cpu_usage = Gauge(
            'asi_cpu_usage_percent',
            'CPU usage percentage',
            registry=self.registry
        )
        
        self.active_decisions = Gauge(
            'asi_active_decisions',
            'Number of currently active decisions',
            registry=self.registry
        )
        
        # Error metrics
        self.errors_total = Counter(
            'asi_errors_total',
            'Total number of errors',
            ['error_type', 'component'],
            registry=self.registry
        )
    
    def record_decision(self, metrics: DecisionMetrics):
        """Record metrics for a decision."""
        with self._lock:
            # Store in history
            self.decision_history.append(metrics)
            
            # Update Prometheus metrics
            status = 'success' if not metrics.used_fallback else 'fallback'
            self.decisions_total.labels(
                decision_type=metrics.decision_type,
                status=status
            ).inc()
            
            self.decision_duration.labels(
                decision_type=metrics.decision_type
            ).observe(metrics.processing_time_ms / 1000.0)
            
            self.decision_confidence.labels(
                decision_type=metrics.decision_type
            ).observe(metrics.confidence)
            
            # Update performance stats
            self.performance_stats['decision_times'].append(metrics.processing_time_ms)
            self.performance_stats['confidence_scores'].append(metrics.confidence)
            
            self.logger.debug(
                f"Recorded decision metrics: {metrics.decision_id}",
                extra={
                    'decision_id': metrics.decision_id,
                    'processing_time_ms': metrics.processing_time_ms,
                    'confidence': metrics.confidence
                }
            )
    
    def record_rule_evaluation(self, metrics: RuleMetrics):
        """Record metrics for rule evaluation."""
        with self._lock:
            # Store in history
            self.rule_history.append(metrics)
            
            # Update Prometheus metrics
            self.rules_evaluated.labels(
                rule_id=metrics.rule_id,
                triggered=str(metrics.triggered).lower()
            ).inc()
            
            self.rule_evaluation_duration.labels(
                rule_id=metrics.rule_id
            ).observe(metrics.evaluation_time_ms / 1000.0)
            
            # Update performance stats
            self.performance_stats['rule_times'].append(metrics.evaluation_time_ms)
    
    def record_model_consultation(self, metrics: ModelMetrics):
        """Record metrics for model consultation."""
        with self._lock:
            # Store in history
            self.model_history.append(metrics)
            
            # Update Prometheus metrics
            status = 'success' if metrics.success else 'error'
            self.model_consultations.labels(
                model_id=metrics.model_id,
                model_type=metrics.model_type,
                status=status
            ).inc()
            
            if metrics.success:
                self.model_inference_duration.labels(
                    model_id=metrics.model_id,
                    model_type=metrics.model_type
                ).observe(metrics.inference_time_ms / 1000.0)
            
            # Update performance stats
            self.performance_stats['model_times'].append(metrics.inference_time_ms)
    
    def record_error(self, error_type: str, component: str, details: Optional[str] = None):
        """Record an error occurrence."""
        with self._lock:
            self.error_counts[f"{component}:{error_type}"] += 1
            self.errors_total.labels(
                error_type=error_type,
                component=component
            ).inc()
            
            self.logger.error(
                f"Error recorded: {error_type} in {component}",
                extra={
                    'error_type': error_type,
                    'component': component,
                    'details': details
                }
            )
    
    def get_performance_summary(self, window_minutes: int = 60) -> Dict[str, Any]:
        """Get performance summary for the specified time window."""
        cutoff_time = datetime.utcnow() - timedelta(minutes=window_minutes)
        
        with self._lock:
            # Filter recent decisions
            recent_decisions = [
                d for d in self.decision_history
                if d.timestamp >= cutoff_time
            ]
            
            # Filter recent rules
            recent_rules = [
                r for r in self.rule_history
                if r.timestamp >= cutoff_time
            ]
            
            # Filter recent models
            recent_models = [
                m for m in self.model_history
                if m.timestamp >= cutoff_time
            ]
            
            summary = {
                'time_window_minutes': window_minutes,
                'timestamp': datetime.utcnow().isoformat(),
                'decisions': {
                    'total': len(recent_decisions),
                    'avg_processing_time_ms': self._safe_avg([d.processing_time_ms for d in recent_decisions]),
                    'avg_confidence': self._safe_avg([d.confidence for d in recent_decisions]),
                    'fallback_rate': self._safe_rate([d.used_fallback for d in recent_decisions]),
                    'by_type': self._group_by_type(recent_decisions, 'decision_type')
                },
                'rules': {
                    'total_evaluations': len(recent_rules),
                    'avg_evaluation_time_ms': self._safe_avg([r.evaluation_time_ms for r in recent_rules]),
                    'trigger_rate': self._safe_rate([r.triggered for r in recent_rules]),
                    'unique_rules': len(set(r.rule_id for r in recent_rules))
                },
                'models': {
                    'total_consultations': len(recent_models),
                    'avg_inference_time_ms': self._safe_avg([m.inference_time_ms for m in recent_models]),
                    'success_rate': self._safe_rate([m.success for m in recent_models]),
                    'by_type': self._group_by_type(recent_models, 'model_type')
                },
                'system': self.system_metrics.copy(),
                'errors': dict(self.error_counts)
            }
            
            return summary
    
    def _safe_avg(self, values: List[Union[int, float]]) -> float:
        """Safely calculate average, returning 0.0 for empty lists."""
        return sum(values) / len(values) if values else 0.0
    
    def _safe_rate(self, boolean_values: List[bool]) -> float:
        """Safely calculate rate of True values."""
        if not boolean_values:
            return 0.0
        return sum(boolean_values) / len(boolean_values)
    
    def _group_by_type(self, items: List[Any], type_attr: str) -> Dict[str, int]:
        """Group items by type attribute."""
        groups = defaultdict(int)
        for item in items:
            groups[getattr(item, type_attr)] += 1
        return dict(groups)
    
    def _start_system_monitoring(self):
        """Start background system monitoring."""
        def monitor():
            while True:
                try:
                    # Update system metrics
                    process = psutil.Process()
                    memory_info = process.memory_info()
                    cpu_percent = process.cpu_percent()
                    
                    self.system_metrics.update({
                        'memory_rss_mb': memory_info.rss / 1024 / 1024,
                        'memory_vms_mb': memory_info.vms / 1024 / 1024,
                        'cpu_percent': cpu_percent,
                        'num_threads': process.num_threads(),
                        'timestamp': datetime.utcnow().isoformat()
                    })
                    
                    # Update Prometheus gauges
                    self.memory_usage.set(memory_info.rss)
                    self.cpu_usage.set(cpu_percent)
                    
                    time.sleep(30)  # Update every 30 seconds
                    
                except Exception as e:
                    self.logger.error(f"Error in system monitoring: {e}")
                    time.sleep(60)  # Wait longer on error
        
        monitor_thread = threading.Thread(target=monitor, daemon=True)
        monitor_thread.start()
    
    def export_prometheus_metrics(self) -> str:
        """Export metrics in Prometheus format."""
        return generate_latest(self.registry).decode('utf-8')
    
    def reset_metrics(self):
        """Reset all metrics (useful for testing)."""
        with self._lock:
            self.decision_history.clear()
            self.rule_history.clear()
            self.model_history.clear()
            self.performance_stats.clear()
            self.error_counts.clear()
            
            self.logger.info("Metrics reset")


# Global metrics collector instance
_metrics_collector: Optional[MetricsCollector] = None


def get_metrics_collector() -> MetricsCollector:
    """Get the global metrics collector instance."""
    global _metrics_collector
    if _metrics_collector is None:
        _metrics_collector = MetricsCollector()
    return _metrics_collector


def init_metrics_collector(registry: Optional[CollectorRegistry] = None) -> MetricsCollector:
    """Initialize the global metrics collector."""
    global _metrics_collector
    _metrics_collector = MetricsCollector(registry)
    return _metrics_collector
