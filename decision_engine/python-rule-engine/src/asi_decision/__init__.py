"""
ASI Decision Engine

Context-aware decision-making platform for the Artificial Super Intelligence (ASI) System.
Combines rule-based logic, neural model outputs, and reinforcement learning agents to make
intelligent decisions with hybrid symbolic/neural reasoning and fallback logic for edge cases.

Modules:
    rules: Rule definition, management, and evaluation
    logic: Logic trees and symbolic reasoning
    integration: Learning Engine and external system integration
    fallback: Edge case handling and graceful degradation
    utils: Shared utilities and helpers
"""

__version__ = "1.0.0"
__author__ = "ASI System Team"
__email__ = "<EMAIL>"

from .utils.logger import get_logger
from .utils.config import Config
from .utils.metrics import MetricsCollector

# Initialize global logger
logger = get_logger(__name__)

# Package-level exports
__all__ = [
    "logger",
    "Config",
    "MetricsCollector",
]
