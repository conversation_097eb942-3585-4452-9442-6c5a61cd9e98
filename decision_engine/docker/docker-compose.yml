version: '3.8'

services:
  # Python Rule Engine
  python-rule-engine:
    build:
      context: ../python-rule-engine
      dockerfile: Dockerfile
    container_name: asi-python-rule-engine
    environment:
      - LOG_LEVEL=INFO
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
      - GRPC_SERVER_PORT=50070
      - LEARNING_ENGINE_ENDPOINT=learning-engine:50060
      - REDIS_URL=redis://redis:6379/0
      - POSTGRES_URL=************************************************/asi_decisions
    ports:
      - "50070:50070"
      - "8080:8080"
    depends_on:
      - kafka
      - redis
      - postgres
    volumes:
      - ../configs:/app/configs:ro
      - ../logs:/app/logs
    networks:
      - asi-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Rust Decision Loop
  rust-decision-loop:
    build:
      context: ../rust-decision-loop
      dockerfile: Dockerfile
    container_name: asi-rust-decision-loop
    environment:
      - RUST_LOG=info
      - KAFKA_BROKERS=kafka:9092
      - GRPC_ENDPOINT=python-rule-engine:50070
      - METRICS_PORT=8081
    ports:
      - "8081:8081"
    depends_on:
      - kafka
      - python-rule-engine
    volumes:
      - ../configs:/app/configs:ro
      - ../logs:/app/logs
    networks:
      - asi-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # C++ Edge Processor
  cpp-edge-processor:
    build:
      context: ../cpp-edge-processor
      dockerfile: Dockerfile
    container_name: asi-cpp-edge-processor
    environment:
      - LOG_LEVEL=INFO
      - DECISION_LOOP_ENDPOINT=rust-decision-loop:8081
      - METRICS_PORT=8082
    ports:
      - "8082:8082"
    depends_on:
      - rust-decision-loop
    volumes:
      - ../configs:/app/configs:ro
      - ../logs:/app/logs
    networks:
      - asi-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8082/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # gRPC Decision Server
  grpc-decision-server:
    build:
      context: ../grpc-decision-server
      dockerfile: Dockerfile
    container_name: asi-grpc-decision-server
    environment:
      - LOG_LEVEL=INFO
      - GRPC_PORT=50071
      - PYTHON_ENGINE_ENDPOINT=python-rule-engine:50070
    ports:
      - "50071:50071"
    depends_on:
      - python-rule-engine
    volumes:
      - ../configs:/app/configs:ro
      - ../logs:/app/logs
    networks:
      - asi-network
    restart: unless-stopped

  # Apache Kafka
  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: asi-kafka
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092,PLAINTEXT_HOST://localhost:29092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
      KAFKA_LOG_RETENTION_HOURS: 168
      KAFKA_LOG_SEGMENT_BYTES: 1073741824
      KAFKA_LOG_RETENTION_CHECK_INTERVAL_MS: 300000
    ports:
      - "29092:29092"
    depends_on:
      - zookeeper
    volumes:
      - kafka-data:/var/lib/kafka/data
    networks:
      - asi-network
    restart: unless-stopped

  # Apache Zookeeper
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: asi-zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    volumes:
      - zookeeper-data:/var/lib/zookeeper/data
      - zookeeper-logs:/var/lib/zookeeper/log
    networks:
      - asi-network
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7.0-alpine
    container_name: asi-redis
    command: redis-server --appendonly yes --maxmemory 1gb --maxmemory-policy allkeys-lru
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - asi-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: asi-postgres
    environment:
      POSTGRES_DB: asi_decisions
      POSTGRES_USER: asi_user
      POSTGRES_PASSWORD: asi_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ../sql/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - asi-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U asi_user -d asi_decisions"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Learning Engine (from existing module)
  learning-engine:
    image: asi-learning-engine:latest
    container_name: asi-learning-engine
    environment:
      - LOG_LEVEL=INFO
      - GRPC_PORT=50060
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
    ports:
      - "50060:50060"
    depends_on:
      - kafka
    networks:
      - asi-network
    restart: unless-stopped

  # Prometheus Metrics
  prometheus:
    image: prom/prometheus:v2.45.0
    container_name: asi-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    networks:
      - asi-network
    restart: unless-stopped

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:10.0.0
    container_name: asi-grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    ports:
      - "3000:3000"
    volumes:
      - grafana-data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    networks:
      - asi-network
    restart: unless-stopped

  # Jaeger Tracing
  jaeger:
    image: jaegertracing/all-in-one:1.47
    container_name: asi-jaeger
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    ports:
      - "16686:16686"
      - "14268:14268"
      - "14250:14250"
    networks:
      - asi-network
    restart: unless-stopped

  # Nginx Load Balancer
  nginx:
    image: nginx:1.25-alpine
    container_name: asi-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - python-rule-engine
      - grpc-decision-server
    networks:
      - asi-network
    restart: unless-stopped

volumes:
  kafka-data:
    driver: local
  zookeeper-data:
    driver: local
  zookeeper-logs:
    driver: local
  redis-data:
    driver: local
  postgres-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local

networks:
  asi-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
