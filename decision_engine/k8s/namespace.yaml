apiVersion: v1
kind: Namespace
metadata:
  name: asi-system
  labels:
    name: asi-system
    component: decision-engine
    version: v1.0.0
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: asi-decision-engine-quota
  namespace: asi-system
spec:
  hard:
    requests.cpu: "8"
    requests.memory: 16Gi
    limits.cpu: "16"
    limits.memory: 32Gi
    persistentvolumeclaims: "10"
    services: "20"
    secrets: "10"
    configmaps: "10"
---
apiVersion: v1
kind: LimitRange
metadata:
  name: asi-decision-engine-limits
  namespace: asi-system
spec:
  limits:
  - default:
      cpu: "1"
      memory: "2Gi"
    defaultRequest:
      cpu: "100m"
      memory: "256Mi"
    type: Container
  - default:
      storage: "10Gi"
    type: PersistentVolumeClaim
