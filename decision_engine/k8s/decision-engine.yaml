apiVersion: apps/v1
kind: Deployment
metadata:
  name: python-rule-engine
  namespace: asi-system
  labels:
    app: python-rule-engine
    component: decision-engine
    version: v1.0.0
spec:
  replicas: 3
  selector:
    matchLabels:
      app: python-rule-engine
  template:
    metadata:
      labels:
        app: python-rule-engine
        component: decision-engine
        version: v1.0.0
    spec:
      containers:
      - name: python-rule-engine
        image: asi-python-rule-engine:latest
        ports:
        - containerPort: 50070
          name: grpc
        - containerPort: 8080
          name: metrics
        env:
        - name: LOG_LEVEL
          value: "INFO"
        - name: KAFKA_BOOTSTRAP_SERVERS
          value: "kafka:9092"
        - name: GRPC_SERVER_PORT
          value: "50070"
        - name: LEARNING_ENGINE_ENDPOINT
          value: "learning-engine:50060"
        - name: REDIS_URL
          value: "redis://redis:6379/0"
        - name: POSTGRES_URL
          value: "************************************************/asi_decisions"
        resources:
          requests:
            cpu: 500m
            memory: 1Gi
          limits:
            cpu: 2
            memory: 4Gi
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: config
          mountPath: /app/configs
          readOnly: true
        - name: logs
          mountPath: /app/logs
      volumes:
      - name: config
        configMap:
          name: decision-engine-config
      - name: logs
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: python-rule-engine
  namespace: asi-system
  labels:
    app: python-rule-engine
    component: decision-engine
spec:
  selector:
    app: python-rule-engine
  ports:
  - name: grpc
    port: 50070
    targetPort: 50070
    protocol: TCP
  - name: metrics
    port: 8080
    targetPort: 8080
    protocol: TCP
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: rust-decision-loop
  namespace: asi-system
  labels:
    app: rust-decision-loop
    component: decision-engine
    version: v1.0.0
spec:
  replicas: 2
  selector:
    matchLabels:
      app: rust-decision-loop
  template:
    metadata:
      labels:
        app: rust-decision-loop
        component: decision-engine
        version: v1.0.0
    spec:
      containers:
      - name: rust-decision-loop
        image: asi-rust-decision-loop:latest
        ports:
        - containerPort: 8081
          name: metrics
        env:
        - name: RUST_LOG
          value: "info"
        - name: KAFKA_BROKERS
          value: "kafka:9092"
        - name: GRPC_ENDPOINT
          value: "python-rule-engine:50070"
        - name: METRICS_PORT
          value: "8081"
        resources:
          requests:
            cpu: 1
            memory: 2Gi
          limits:
            cpu: 4
            memory: 8Gi
        livenessProbe:
          httpGet:
            path: /health
            port: 8081
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8081
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: config
          mountPath: /app/configs
          readOnly: true
        - name: logs
          mountPath: /app/logs
      volumes:
      - name: config
        configMap:
          name: decision-engine-config
      - name: logs
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: rust-decision-loop
  namespace: asi-system
  labels:
    app: rust-decision-loop
    component: decision-engine
spec:
  selector:
    app: rust-decision-loop
  ports:
  - name: metrics
    port: 8081
    targetPort: 8081
    protocol: TCP
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grpc-decision-server
  namespace: asi-system
  labels:
    app: grpc-decision-server
    component: decision-engine
    version: v1.0.0
spec:
  replicas: 2
  selector:
    matchLabels:
      app: grpc-decision-server
  template:
    metadata:
      labels:
        app: grpc-decision-server
        component: decision-engine
        version: v1.0.0
    spec:
      containers:
      - name: grpc-decision-server
        image: asi-grpc-decision-server:latest
        ports:
        - containerPort: 50071
          name: grpc
        env:
        - name: LOG_LEVEL
          value: "INFO"
        - name: GRPC_PORT
          value: "50071"
        - name: PYTHON_ENGINE_ENDPOINT
          value: "python-rule-engine:50070"
        resources:
          requests:
            cpu: 200m
            memory: 512Mi
          limits:
            cpu: 1
            memory: 2Gi
        volumeMounts:
        - name: config
          mountPath: /app/configs
          readOnly: true
        - name: logs
          mountPath: /app/logs
      volumes:
      - name: config
        configMap:
          name: decision-engine-config
      - name: logs
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: grpc-decision-server
  namespace: asi-system
  labels:
    app: grpc-decision-server
    component: decision-engine
spec:
  selector:
    app: grpc-decision-server
  ports:
  - name: grpc
    port: 50071
    targetPort: 50071
    protocol: TCP
  type: LoadBalancer
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: decision-engine-config
  namespace: asi-system
  labels:
    app: decision-engine
    component: config
data:
  decision_engine_config.yaml: |
    debug: false
    log_level: "INFO"
    environment: "production"
    
    kafka:
      bootstrap_servers:
        - "kafka:9092"
      consumer_group: "asi-decision-engine"
      topics:
        - "asi-decision-engine"
        - "asi-processed-data"
        - "asi-learning-engine"
    
    grpc:
      server_port: 50070
      learning_engine_endpoint: "learning-engine:50060"
    
    decision:
      default_confidence_threshold: 0.7
      enable_fallback_logic: true
      use_neural_models: true
      use_rule_engine: true
    
    monitoring:
      enable_metrics: true
      metrics_port: 8080
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: python-rule-engine-hpa
  namespace: asi-system
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: python-rule-engine
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: rust-decision-loop-hpa
  namespace: asi-system
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: rust-decision-loop
  minReplicas: 1
  maxReplicas: 5
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 80
