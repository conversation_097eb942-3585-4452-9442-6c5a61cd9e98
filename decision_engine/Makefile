# ASI Decision Engine - Build and Deployment Automation
# ====================================================

.PHONY: help build test clean install start stop restart logs status deploy

# Default target
help:
	@echo "ASI Decision Engine - Available Commands:"
	@echo "  build          - Build all components (Python, Rust, C++)"
	@echo "  test           - Run all tests"
	@echo "  clean          - Clean build artifacts"
	@echo "  install        - Install dependencies"
	@echo "  start          - Start all services"
	@echo "  stop           - Stop all services"
	@echo "  restart        - Restart all services"
	@echo "  logs           - Show service logs"
	@echo "  status         - Show service status"
	@echo "  deploy         - Deploy to production"
	@echo "  check-deps     - Check system dependencies"
	@echo "  format         - Format code"
	@echo "  lint           - Run linting"
	@echo "  security       - Run security checks"

# Variables
PYTHON_DIR = python-rule-engine
RUST_DIR = rust-decision-loop
CPP_DIR = cpp-edge-processor
GRPC_DIR = grpc-decision-server
DOCKER_COMPOSE = docker/docker-compose.yml

# Check system dependencies
check-deps:
	@echo "Checking system dependencies..."
	@command -v python3 >/dev/null 2>&1 || { echo "Python 3 is required but not installed."; exit 1; }
	@command -v cargo >/dev/null 2>&1 || { echo "Rust/Cargo is required but not installed."; exit 1; }
	@command -v cmake >/dev/null 2>&1 || { echo "CMake is required but not installed."; exit 1; }
	@command -v docker >/dev/null 2>&1 || { echo "Docker is required but not installed."; exit 1; }
	@command -v docker-compose >/dev/null 2>&1 || { echo "Docker Compose is required but not installed."; exit 1; }
	@echo "All dependencies are available."

# Install dependencies
install: check-deps
	@echo "Installing dependencies..."
	
	# Python dependencies
	cd $(PYTHON_DIR) && python3 -m pip install -r requirements.txt
	
	# Rust dependencies (handled by Cargo)
	cd $(RUST_DIR) && cargo fetch
	
	# C++ dependencies (handled by CMake)
	mkdir -p $(CPP_DIR)/build
	cd $(CPP_DIR)/build && cmake .. && make dependencies
	
	@echo "Dependencies installed successfully."

# Build all components
build: install
	@echo "Building all components..."
	
	# Build Python rule engine
	@echo "Building Python rule engine..."
	cd $(PYTHON_DIR) && python3 -m pip install -e .
	
	# Build Rust decision loop
	@echo "Building Rust decision loop..."
	cd $(RUST_DIR) && cargo build --release
	
	# Build C++ edge processor
	@echo "Building C++ edge processor..."
	cd $(CPP_DIR)/build && make -j$(shell nproc)
	
	# Build gRPC server
	@echo "Building gRPC server..."
	cd $(GRPC_DIR) && python3 -m grpc_tools.protoc -I proto --python_out=src --grpc_python_out=src proto/*.proto
	
	@echo "Build completed successfully."

# Build Docker images
build-docker:
	@echo "Building Docker images..."
	docker-compose -f $(DOCKER_COMPOSE) build
	@echo "Docker images built successfully."

# Run tests
test:
	@echo "Running tests..."
	
	# Python tests
	cd $(PYTHON_DIR) && python3 -m pytest tests/ -v --cov=asi_decision --cov-report=html
	
	# Rust tests
	cd $(RUST_DIR) && cargo test --release
	
	# C++ tests
	cd $(CPP_DIR)/build && make test
	
	# Integration tests
	python3 tests/integration_test.py
	
	@echo "All tests completed."

# Run benchmarks
benchmark:
	@echo "Running performance benchmarks..."
	
	# Rust benchmarks
	cd $(RUST_DIR) && cargo bench
	
	# Python benchmarks
	cd $(PYTHON_DIR) && python3 -m pytest tests/benchmarks/ -v
	
	# C++ benchmarks
	cd $(CPP_DIR)/build && ./benchmarks/decision_benchmark
	
	@echo "Benchmarks completed."

# Format code
format:
	@echo "Formatting code..."
	
	# Python formatting
	cd $(PYTHON_DIR) && black src/ tests/
	cd $(PYTHON_DIR) && isort src/ tests/
	
	# Rust formatting
	cd $(RUST_DIR) && cargo fmt
	
	# C++ formatting (if clang-format is available)
	@if command -v clang-format >/dev/null 2>&1; then \
		find $(CPP_DIR) -name "*.cpp" -o -name "*.h" | xargs clang-format -i; \
	fi
	
	@echo "Code formatting completed."

# Run linting
lint:
	@echo "Running linting..."
	
	# Python linting
	cd $(PYTHON_DIR) && flake8 src/ tests/
	cd $(PYTHON_DIR) && mypy src/
	
	# Rust linting
	cd $(RUST_DIR) && cargo clippy -- -D warnings
	
	@echo "Linting completed."

# Security checks
security:
	@echo "Running security checks..."
	
	# Python security
	cd $(PYTHON_DIR) && safety check
	cd $(PYTHON_DIR) && bandit -r src/
	
	# Rust security
	cd $(RUST_DIR) && cargo audit
	
	@echo "Security checks completed."

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	
	# Python cleanup
	cd $(PYTHON_DIR) && find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	cd $(PYTHON_DIR) && find . -type f -name "*.pyc" -delete 2>/dev/null || true
	cd $(PYTHON_DIR) && rm -rf build/ dist/ *.egg-info/ .coverage htmlcov/
	
	# Rust cleanup
	cd $(RUST_DIR) && cargo clean
	
	# C++ cleanup
	rm -rf $(CPP_DIR)/build/
	
	# Docker cleanup
	docker-compose -f $(DOCKER_COMPOSE) down --volumes --remove-orphans
	docker system prune -f
	
	@echo "Cleanup completed."

# Start services
start:
	@echo "Starting ASI Decision Engine services..."
	docker-compose -f $(DOCKER_COMPOSE) up -d
	@echo "Services started. Use 'make logs' to view logs."

# Stop services
stop:
	@echo "Stopping ASI Decision Engine services..."
	docker-compose -f $(DOCKER_COMPOSE) down
	@echo "Services stopped."

# Restart services
restart: stop start

# Show logs
logs:
	docker-compose -f $(DOCKER_COMPOSE) logs -f

# Show service status
status:
	@echo "ASI Decision Engine Service Status:"
	@echo "=================================="
	docker-compose -f $(DOCKER_COMPOSE) ps
	@echo ""
	@echo "Health Checks:"
	@echo "-------------"
	@curl -s http://localhost:8080/health 2>/dev/null | jq . || echo "Decision Engine: Not responding"
	@curl -s http://localhost:50070/health 2>/dev/null || echo "gRPC Server: Not responding"

# Development server
dev:
	@echo "Starting development environment..."
	
	# Start dependencies
	docker-compose -f $(DOCKER_COMPOSE) up -d kafka redis postgres
	
	# Start Python rule engine in development mode
	cd $(PYTHON_DIR) && python3 -m asi_decision.main --config ../configs/decision_engine_config.yaml --debug &
	
	# Start Rust decision loop in development mode
	cd $(RUST_DIR) && cargo run -- --config ../configs/decision_engine_config.yaml --log-level debug &
	
	@echo "Development environment started."

# Production deployment
deploy:
	@echo "Deploying to production..."
	
	# Build production images
	docker-compose -f $(DOCKER_COMPOSE) -f docker/docker-compose.prod.yml build
	
	# Deploy to Kubernetes
	kubectl apply -f k8s/
	
	# Wait for deployment
	kubectl rollout status deployment/decision-engine
	
	@echo "Production deployment completed."

# Kubernetes operations
k8s-deploy:
	@echo "Deploying to Kubernetes..."
	kubectl apply -f k8s/namespace.yaml
	kubectl apply -f k8s/
	kubectl rollout status deployment/decision-engine -n asi-system

k8s-status:
	kubectl get pods -n asi-system
	kubectl get services -n asi-system

k8s-logs:
	kubectl logs -f deployment/decision-engine -n asi-system

k8s-clean:
	kubectl delete -f k8s/
	kubectl delete namespace asi-system

# Performance testing
perf-test:
	@echo "Running performance tests..."
	
	# Load testing with Apache Bench
	ab -n 10000 -c 100 http://localhost:50070/decision
	
	# Memory profiling
	cd $(PYTHON_DIR) && python3 -m memory_profiler tests/performance/memory_test.py
	
	# CPU profiling
	cd $(RUST_DIR) && cargo run --release --bin perf-test
	
	@echo "Performance testing completed."

# Database operations
db-migrate:
	@echo "Running database migrations..."
	cd $(PYTHON_DIR) && alembic upgrade head

db-seed:
	@echo "Seeding database with test data..."
	cd $(PYTHON_DIR) && python3 scripts/seed_database.py

db-backup:
	@echo "Creating database backup..."
	docker exec asi-postgres pg_dump -U asi_user asi_decisions > backups/decisions_$(shell date +%Y%m%d_%H%M%S).sql

# Monitoring setup
monitoring-setup:
	@echo "Setting up monitoring stack..."
	docker-compose -f docker/monitoring.yml up -d
	@echo "Monitoring available at:"
	@echo "  Prometheus: http://localhost:9090"
	@echo "  Grafana: http://localhost:3000"
	@echo "  Jaeger: http://localhost:16686"

# Documentation
docs:
	@echo "Generating documentation..."
	cd $(PYTHON_DIR) && sphinx-build -b html docs/ docs/_build/html/
	cd $(RUST_DIR) && cargo doc --no-deps
	@echo "Documentation generated."

# Release
release:
	@echo "Creating release..."
	
	# Tag version
	git tag -a v$(VERSION) -m "Release version $(VERSION)"
	
	# Build release artifacts
	make build
	make test
	
	# Create release package
	tar -czf asi-decision-engine-$(VERSION).tar.gz \
		$(PYTHON_DIR)/dist/ \
		$(RUST_DIR)/target/release/ \
		$(CPP_DIR)/build/ \
		configs/ \
		docker/ \
		k8s/ \
		README.md
	
	@echo "Release $(VERSION) created."

# Quick development setup
quick-start: install build start
	@echo "Quick start completed. Decision Engine is running!"
	@echo "Access points:"
	@echo "  gRPC API: localhost:50070"
	@echo "  Metrics: http://localhost:8080/metrics"
	@echo "  Health: http://localhost:8080/health"
