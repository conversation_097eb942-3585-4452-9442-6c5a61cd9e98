# ASI Decision Engine Configuration
# ================================

# General settings
debug: false
log_level: "INFO"
environment: "development"

# Kafka configuration for data streaming
kafka:
  bootstrap_servers:
    - "localhost:9092"
  consumer_group: "asi-decision-engine"
  topics:
    - "asi-decision-engine"
    - "asi-processed-data"
    - "asi-learning-engine"
  auto_offset_reset: "latest"
  enable_auto_commit: false
  max_poll_records: 500
  session_timeout_ms: 30000
  heartbeat_interval_ms: 3000
  max_poll_interval_ms: 300000
  security_protocol: "PLAINTEXT"

# gRPC configuration
grpc:
  server_port: 50070
  max_workers: 10
  max_message_length: 4194304  # 4MB
  keepalive_time_ms: 30000
  keepalive_timeout_ms: 5000
  keepalive_permit_without_calls: true
  max_connection_idle_ms: 300000
  max_connection_age_ms: 600000
  
  # Learning Engine client configuration
  learning_engine_endpoint: "localhost:50060"
  learning_engine_timeout_ms: 5000
  learning_engine_retry_attempts: 3

# Rule engine configuration
rule_engine:
  max_rules: 10000
  max_rule_depth: 10
  rule_evaluation_timeout_ms: 1000
  enable_rule_caching: true
  cache_size: 1000
  cache_ttl_seconds: 300
  enable_rule_validation: true
  enable_rule_versioning: true
  rule_storage_backend: "memory"  # memory, redis, database
  
  # Rule execution settings
  parallel_execution: true
  max_parallel_rules: 100
  execution_timeout_ms: 5000

# Decision processing configuration
decision:
  default_confidence_threshold: 0.7
  max_processing_time_ms: 10000
  enable_fallback_logic: true
  fallback_confidence: 0.5
  enable_explanation: true
  enable_decision_logging: true
  
  # Neural model integration
  use_neural_models: true
  neural_model_timeout_ms: 2000
  neural_model_weight: 0.6
  preferred_models:
    - "nlp_sentiment_v1"
    - "vision_classifier_v2"
  
  # Rule-based logic
  use_rule_engine: true
  rule_engine_weight: 0.4
  
  # Hybrid reasoning
  enable_hybrid_reasoning: true
  hybrid_combination_method: "weighted_average"  # weighted_average, voting, stacking

# Monitoring and observability
monitoring:
  enable_metrics: true
  metrics_port: 8080
  metrics_path: "/metrics"
  enable_tracing: true
  tracing_endpoint: "http://localhost:14268/api/traces"
  tracing_service_name: "asi-decision-engine"
  
  # Health check configuration
  health_check_interval_seconds: 30
  health_check_timeout_seconds: 5
  
  # Performance monitoring
  enable_performance_logging: true
  slow_decision_threshold_ms: 1000
  memory_usage_threshold_mb: 1000

# Security configuration
security:
  enable_authentication: false
  enable_authorization: false
  jwt_secret_key: null
  jwt_expiration_hours: 24
  
  # API rate limiting
  enable_rate_limiting: true
  rate_limit_requests_per_minute: 1000
  rate_limit_burst: 100
  
  # Data encryption
  enable_encryption: false
  encryption_key: null

# Rust decision loop configuration
rust_decision_loop:
  worker_threads: 4
  max_concurrent_decisions: 1000
  decision_queue_size: 10000
  enable_profiling: false
  memory_pool_size: "1GB"
  
  # Performance tuning
  cpu_affinity: []
  numa_aware: false
  lock_free_queues: true
  batch_processing: true
  batch_size: 100

# C++ edge processor configuration
cpp_edge_processor:
  enable_edge_processing: true
  edge_decision_timeout_ms: 1
  max_edge_decisions_per_second: 100000
  memory_limit_mb: 512
  
  # Hardware optimization
  use_simd: true
  use_gpu_acceleration: false
  cpu_optimization_level: "O3"

# Integration endpoints
integration:
  self_improvement_endpoint: "localhost:50080"
  ui_endpoint: "localhost:3000"
  
  # External APIs
  external_apis:
    weather_service: "https://api.weather.com/v1"
    financial_service: "https://api.financial.com/v2"
  
  # Webhook configurations
  webhooks:
    decision_completed: "http://localhost:8000/webhooks/decision"
    fallback_triggered: "http://localhost:8000/webhooks/fallback"

# Data storage configuration
storage:
  # Decision history storage
  decision_history:
    backend: "postgresql"  # postgresql, mongodb, redis
    connection_string: "postgresql://asi_user:asi_password@localhost:5432/asi_decisions"
    retention_days: 90
    
  # Rule storage
  rule_storage:
    backend: "postgresql"
    connection_string: "postgresql://asi_user:asi_password@localhost:5432/asi_rules"
    enable_versioning: true
    
  # Cache configuration
  cache:
    backend: "redis"
    connection_string: "redis://localhost:6379/0"
    default_ttl_seconds: 3600
    max_memory: "1GB"

# Development and testing
development:
  enable_mock_learning_engine: false
  mock_decision_latency_ms: 10
  enable_decision_replay: false
  replay_file_path: "test_data/decision_replay.json"
  
  # Load testing
  load_testing:
    enable: false
    requests_per_second: 100
    duration_seconds: 60
    concurrent_users: 10

# Production optimizations
production:
  enable_jit_compilation: true
  precompile_rules: true
  warm_up_models: true
  enable_connection_pooling: true
  
  # Resource limits
  max_memory_usage_mb: 8192
  max_cpu_usage_percent: 80
  max_open_files: 65536
  
  # Scaling configuration
  auto_scaling:
    enable: true
    min_instances: 2
    max_instances: 10
    scale_up_threshold: 70
    scale_down_threshold: 30
    cooldown_seconds: 300
