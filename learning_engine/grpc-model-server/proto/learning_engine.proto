syntax = "proto3";

package asi.learning_engine;

import "google/protobuf/timestamp.proto";
import "google/protobuf/any.proto";

option go_package = "github.com/asi/learning_engine/proto";
option java_package = "com.asi.learning_engine.proto";
option java_outer_classname = "LearningEngineProto";

// Learning Engine Service for ASI System
service LearningEngineService {
  // Train models with provided data
  rpc TrainModel(TrainModelRequest) returns (TrainModelResponse);
  
  // Stream training data for continuous learning
  rpc StreamTrainData(stream StreamTrainRequest) returns (stream StreamTrainResponse);
  
  // Perform inference on input data
  rpc Inference(InferenceRequest) returns (InferenceResponse);
  
  // Batch inference for multiple inputs
  rpc BatchInference(BatchInferenceRequest) returns (BatchInferenceResponse);
  
  // Get model information and metadata
  rpc GetModelInfo(GetModelInfoRequest) returns (ModelInfoResponse);
  
  // List available models
  rpc ListModels(ListModelsRequest) returns (ListModelsResponse);
  
  // Deploy model to production
  rpc DeployModel(DeployModelRequest) returns (DeployModelResponse);
  
  // Get training status and metrics
  rpc GetTrainingStatus(GetTrainingStatusRequest) returns (TrainingStatusResponse);
  
  // Health check
  rpc HealthCheck(HealthCheckRequest) returns (HealthCheckResponse);
}

// Model types supported by the learning engine
enum ModelType {
  MODEL_TYPE_UNSPECIFIED = 0;
  MODEL_TYPE_NLP_TRANSFORMER = 1;
  MODEL_TYPE_VISION_CNN = 2;
  MODEL_TYPE_RL_AGENT = 3;
  MODEL_TYPE_ABSTRACTION = 4;
  MODEL_TYPE_MULTIMODAL = 5;
}

// Training request
message TrainModelRequest {
  string model_id = 1;
  ModelType model_type = 2;
  TrainingConfig config = 3;
  repeated TrainingData data = 4;
  map<string, string> metadata = 5;
}

// Training configuration
message TrainingConfig {
  int32 epochs = 1;
  float learning_rate = 2;
  int32 batch_size = 3;
  string optimizer = 4;
  string loss_function = 5;
  map<string, google.protobuf.Any> hyperparameters = 6;
  bool use_gpu = 7;
  int32 num_gpus = 8;
  string checkpoint_path = 9;
  int32 save_frequency = 10;
}

// Training data
message TrainingData {
  string data_id = 1;
  DataType data_type = 2;
  bytes data_payload = 3;
  bytes label = 4;
  map<string, string> annotations = 5;
  google.protobuf.Timestamp timestamp = 6;
}

// Data types
enum DataType {
  DATA_TYPE_UNSPECIFIED = 0;
  DATA_TYPE_TEXT = 1;
  DATA_TYPE_IMAGE = 2;
  DATA_TYPE_VIDEO = 3;
  DATA_TYPE_AUDIO = 4;
  DATA_TYPE_TABULAR = 5;
  DATA_TYPE_TIME_SERIES = 6;
  DATA_TYPE_GRAPH = 7;
}

// Training response
message TrainModelResponse {
  bool success = 1;
  string job_id = 2;
  string model_version = 3;
  string error_message = 4;
  TrainingMetrics initial_metrics = 5;
}

// Stream training request
message StreamTrainRequest {
  string job_id = 1;
  TrainingData data = 2;
  bool is_final = 3;
}

// Stream training response
message StreamTrainResponse {
  string job_id = 1;
  TrainingMetrics metrics = 2;
  TrainingStatus status = 3;
  string message = 4;
}

// Training metrics
message TrainingMetrics {
  float loss = 1;
  float accuracy = 2;
  float precision = 3;
  float recall = 4;
  float f1_score = 5;
  map<string, float> custom_metrics = 6;
  int32 epoch = 7;
  int32 step = 8;
  google.protobuf.Timestamp timestamp = 9;
}

// Training status
enum TrainingStatus {
  TRAINING_STATUS_UNSPECIFIED = 0;
  TRAINING_STATUS_PENDING = 1;
  TRAINING_STATUS_RUNNING = 2;
  TRAINING_STATUS_COMPLETED = 3;
  TRAINING_STATUS_FAILED = 4;
  TRAINING_STATUS_CANCELLED = 5;
}

// Inference request
message InferenceRequest {
  string model_id = 1;
  string model_version = 2;
  InferenceData input_data = 3;
  InferenceConfig config = 4;
}

// Inference data
message InferenceData {
  DataType data_type = 1;
  bytes data_payload = 2;
  map<string, string> metadata = 3;
}

// Inference configuration
message InferenceConfig {
  bool use_gpu = 1;
  int32 max_tokens = 2;
  float temperature = 3;
  float top_p = 4;
  int32 beam_size = 5;
  map<string, google.protobuf.Any> model_params = 6;
}

// Inference response
message InferenceResponse {
  bool success = 1;
  InferenceResult result = 2;
  string error_message = 3;
  InferenceMetrics metrics = 4;
}

// Inference result
message InferenceResult {
  DataType output_type = 1;
  bytes output_data = 2;
  float confidence = 3;
  repeated Prediction predictions = 4;
  map<string, google.protobuf.Any> additional_outputs = 5;
}

// Prediction
message Prediction {
  string label = 1;
  float confidence = 2;
  map<string, float> scores = 3;
  bytes raw_output = 4;
}

// Inference metrics
message InferenceMetrics {
  int64 latency_ms = 1;
  int64 memory_usage_mb = 2;
  float gpu_utilization = 3;
  int32 tokens_processed = 4;
  google.protobuf.Timestamp timestamp = 5;
}

// Batch inference request
message BatchInferenceRequest {
  string model_id = 1;
  string model_version = 2;
  repeated InferenceData inputs = 3;
  InferenceConfig config = 4;
}

// Batch inference response
message BatchInferenceResponse {
  bool success = 1;
  repeated InferenceResult results = 2;
  string error_message = 3;
  BatchInferenceMetrics metrics = 4;
}

// Batch inference metrics
message BatchInferenceMetrics {
  int64 total_latency_ms = 1;
  int64 avg_latency_ms = 2;
  int32 batch_size = 3;
  int32 successful_inferences = 4;
  int32 failed_inferences = 5;
  InferenceMetrics aggregate_metrics = 6;
}

// Get model info request
message GetModelInfoRequest {
  string model_id = 1;
  string model_version = 2;
}

// Model info response
message ModelInfoResponse {
  ModelInfo model_info = 1;
  string error_message = 2;
}

// Model information
message ModelInfo {
  string model_id = 1;
  string model_version = 2;
  ModelType model_type = 3;
  string name = 4;
  string description = 5;
  ModelStatus status = 6;
  google.protobuf.Timestamp created_at = 7;
  google.protobuf.Timestamp updated_at = 8;
  ModelMetadata metadata = 9;
  ModelPerformance performance = 10;
}

// Model status
enum ModelStatus {
  MODEL_STATUS_UNSPECIFIED = 0;
  MODEL_STATUS_TRAINING = 1;
  MODEL_STATUS_READY = 2;
  MODEL_STATUS_DEPLOYED = 3;
  MODEL_STATUS_DEPRECATED = 4;
  MODEL_STATUS_FAILED = 5;
}

// Model metadata
message ModelMetadata {
  string framework = 1;
  string architecture = 2;
  int64 parameters_count = 3;
  int64 model_size_mb = 4;
  repeated string input_types = 5;
  repeated string output_types = 6;
  map<string, string> tags = 7;
  string author = 8;
  string license = 9;
}

// Model performance
message ModelPerformance {
  float accuracy = 1;
  float precision = 2;
  float recall = 3;
  float f1_score = 4;
  int64 avg_inference_time_ms = 5;
  int64 memory_usage_mb = 6;
  map<string, float> benchmark_scores = 7;
}

// List models request
message ListModelsRequest {
  ModelType model_type = 1;
  ModelStatus status = 2;
  int32 page_size = 3;
  string page_token = 4;
  map<string, string> filters = 5;
}

// List models response
message ListModelsResponse {
  repeated ModelInfo models = 1;
  string next_page_token = 2;
  int32 total_count = 3;
}

// Deploy model request
message DeployModelRequest {
  string model_id = 1;
  string model_version = 2;
  DeploymentConfig deployment_config = 3;
}

// Deployment configuration
message DeploymentConfig {
  string environment = 1;
  int32 replicas = 2;
  ResourceRequirements resources = 3;
  map<string, string> environment_variables = 4;
  bool auto_scaling = 5;
  int32 min_replicas = 6;
  int32 max_replicas = 7;
}

// Resource requirements
message ResourceRequirements {
  string cpu = 1;
  string memory = 2;
  string gpu = 3;
  string storage = 4;
}

// Deploy model response
message DeployModelResponse {
  bool success = 1;
  string deployment_id = 2;
  string endpoint_url = 3;
  string error_message = 4;
}

// Get training status request
message GetTrainingStatusRequest {
  string job_id = 1;
}

// Training status response
message TrainingStatusResponse {
  string job_id = 1;
  TrainingStatus status = 2;
  TrainingMetrics current_metrics = 3;
  float progress_percentage = 4;
  google.protobuf.Timestamp started_at = 5;
  google.protobuf.Timestamp estimated_completion = 6;
  string error_message = 7;
}

// Health check request
message HealthCheckRequest {
  string service = 1;
}

// Health check response
message HealthCheckResponse {
  enum ServingStatus {
    UNKNOWN = 0;
    SERVING = 1;
    NOT_SERVING = 2;
    SERVICE_UNKNOWN = 3;
  }
  ServingStatus status = 1;
  map<string, string> details = 2;
}
