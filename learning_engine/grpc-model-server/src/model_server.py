"""
gRPC Model Server implementation for the ASI Learning Engine.

Provides high-performance model serving with training and inference capabilities
through a gRPC interface.
"""

import asyncio
import logging
import signal
import sys
import time
from concurrent import futures
from pathlib import Path
from typing import Dict, List, Optional, Any

import grpc
from grpc_reflection.v1alpha import reflection
import yaml

# Add the parent directory to the path to import from asi_learning
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "python-training-engine" / "src"))

from asi_learning.training.orchestrator import TrainingOrchestrator
from asi_learning.nlp.trainer import NLPTrainer
from asi_learning.vision.trainer import VisionTrainer
from asi_learning.rl.trainer import RLTrainer
from asi_learning.abstraction.trainer import AbstractionTrainer
from asi_learning.utils.config import Config
from asi_learning.utils.logger import get_logger
from asi_learning.utils.metrics import MetricsCollector

# Import generated protobuf classes (these would be generated from the .proto file)
# For now, we'll create placeholder classes
class LearningEngineServicer:
    """gRPC servicer implementation for the Learning Engine."""

    def __init__(self, config_path: str = "configs/grpc_server_config.yaml"):
        self.logger = get_logger(__name__)
        self.config = Config.load(config_path)

        # Initialize components
        self.orchestrator = TrainingOrchestrator(config_path)
        self.trainers = {
            "nlp": NLPTrainer(self.config.nlp),
            "vision": VisionTrainer(self.config.vision),
            "rl": RLTrainer(self.config.rl),
            "abstraction": AbstractionTrainer(self.config.abstraction),
        }

        self.metrics_collector = MetricsCollector(self.config.monitoring)

        # Model registry
        self.models = {}
        self.training_jobs = {}

        self.logger.info("Learning Engine gRPC servicer initialized")

    async def TrainModel(self, request, context):
        """Train a model with the provided configuration and data."""
        try:
            self.logger.info(f"Received training request for model: {request.model_id}")

            # Convert gRPC request to internal format
            training_config = {
                "model_type": self._convert_model_type(request.model_type),
                "epochs": request.config.epochs,
                "learning_rate": request.config.learning_rate,
                "batch_size": request.config.batch_size,
                "optimizer": request.config.optimizer,
                "use_gpu": request.config.use_gpu,
                "data": self._convert_training_data(request.data),
                "model_save_path": f"./models/{request.model_id}",
            }

            # Submit training job to orchestrator
            job_id = self.orchestrator.submit_training_job(training_config)

            # Store job information
            self.training_jobs[job_id] = {
                "model_id": request.model_id,
                "status": "TRAINING_STATUS_PENDING",
                "start_time": time.time(),
            }

            # Create response
            response = TrainModelResponse()
            response.success = True
            response.job_id = job_id
            response.model_version = "1.0.0"

            self.logger.info(f"Training job submitted: {job_id}")
            return response

        except Exception as e:
            self.logger.error(f"Training request failed: {e}")
            response = TrainModelResponse()
            response.success = False
            response.error_message = str(e)
            return response

    async def StreamTrainData(self, request_iterator, context):
        """Stream training data for continuous learning."""
        try:
            async for request in request_iterator:
                job_id = request.job_id

                if job_id not in self.training_jobs:
                    response = StreamTrainResponse()
                    response.job_id = job_id
                    response.status = "TRAINING_STATUS_FAILED"
                    response.message = "Job not found"
                    yield response
                    continue

                # Process streaming data
                data = self._convert_training_data([request.data])

                # Send to appropriate trainer for online learning
                model_type = self.training_jobs[job_id].get("model_type", "nlp")
                if model_type in self.trainers:
                    await self.trainers[model_type].process_streaming_data(data[0])

                # Create response with current metrics
                response = StreamTrainResponse()
                response.job_id = job_id
                response.status = "TRAINING_STATUS_RUNNING"
                response.message = "Data processed"

                # Add dummy metrics (in real implementation, get from trainer)
                response.metrics.loss = 0.5
                response.metrics.accuracy = 0.85
                response.metrics.step = 1

                yield response

        except Exception as e:
            self.logger.error(f"Stream training failed: {e}")
            response = StreamTrainResponse()
            response.status = "TRAINING_STATUS_FAILED"
            response.message = str(e)
            yield response

    async def Inference(self, request, context):
        """Perform inference on input data."""
        try:
            self.logger.debug(f"Inference request for model: {request.model_id}")

            model_id = request.model_id
            if model_id not in self.models:
                response = InferenceResponse()
                response.success = False
                response.error_message = f"Model not found: {model_id}"
                return response

            # Convert input data
            input_data = self._convert_inference_data(request.input_data)

            # Perform inference (placeholder implementation)
            start_time = time.time()

            # In real implementation, route to appropriate model
            predictions = [0.8, 0.2]  # Dummy predictions
            confidence = max(predictions)

            inference_time = (time.time() - start_time) * 1000  # Convert to ms

            # Create response
            response = InferenceResponse()
            response.success = True
            response.result.confidence = confidence
            response.result.output_data = str(predictions).encode()
            response.metrics.latency_ms = int(inference_time)

            self.logger.debug(f"Inference completed in {inference_time:.2f}ms")
            return response

        except Exception as e:
            self.logger.error(f"Inference failed: {e}")
            response = InferenceResponse()
            response.success = False
            response.error_message = str(e)
            return response

    async def BatchInference(self, request, context):
        """Perform batch inference on multiple inputs."""
        try:
            self.logger.debug(f"Batch inference request for model: {request.model_id}")

            model_id = request.model_id
            if model_id not in self.models:
                response = BatchInferenceResponse()
                response.success = False
                response.error_message = f"Model not found: {model_id}"
                return response

            start_time = time.time()
            results = []

            # Process each input
            for input_data in request.inputs:
                # Dummy inference result
                result = InferenceResult()
                result.confidence = 0.85
                result.output_data = b"dummy_output"
                results.append(result)

            total_time = (time.time() - start_time) * 1000

            # Create response
            response = BatchInferenceResponse()
            response.success = True
            response.results.extend(results)
            response.metrics.total_latency_ms = int(total_time)
            response.metrics.avg_latency_ms = int(total_time / len(request.inputs))
            response.metrics.batch_size = len(request.inputs)
            response.metrics.successful_inferences = len(results)

            return response

        except Exception as e:
            self.logger.error(f"Batch inference failed: {e}")
            response = BatchInferenceResponse()
            response.success = False
            response.error_message = str(e)
            return response

    async def GetModelInfo(self, request, context):
        """Get model information and metadata."""
        try:
            model_id = request.model_id

            if model_id not in self.models:
                response = ModelInfoResponse()
                response.error_message = f"Model not found: {model_id}"
                return response

            model_info = self.models[model_id]

            response = ModelInfoResponse()
            response.model_info.model_id = model_id
            response.model_info.model_version = model_info.get("version", "1.0.0")
            response.model_info.name = model_info.get("name", model_id)
            response.model_info.status = "MODEL_STATUS_READY"

            return response

        except Exception as e:
            self.logger.error(f"Get model info failed: {e}")
            response = ModelInfoResponse()
            response.error_message = str(e)
            return response

    async def ListModels(self, request, context):
        """List available models."""
        try:
            response = ListModelsResponse()

            for model_id, model_info in self.models.items():
                model = ModelInfo()
                model.model_id = model_id
                model.model_version = model_info.get("version", "1.0.0")
                model.name = model_info.get("name", model_id)
                model.status = "MODEL_STATUS_READY"
                response.models.append(model)

            response.total_count = len(self.models)
            return response

        except Exception as e:
            self.logger.error(f"List models failed: {e}")
            response = ListModelsResponse()
            return response

    async def DeployModel(self, request, context):
        """Deploy model to production."""
        try:
            model_id = request.model_id

            # Placeholder deployment logic
            deployment_id = f"deploy_{model_id}_{int(time.time())}"
            endpoint_url = f"http://localhost:8080/models/{model_id}"

            response = DeployModelResponse()
            response.success = True
            response.deployment_id = deployment_id
            response.endpoint_url = endpoint_url

            self.logger.info(f"Model deployed: {model_id} -> {deployment_id}")
            return response

        except Exception as e:
            self.logger.error(f"Model deployment failed: {e}")
            response = DeployModelResponse()
            response.success = False
            response.error_message = str(e)
            return response

    async def GetTrainingStatus(self, request, context):
        """Get training status and metrics."""
        try:
            job_id = request.job_id

            if job_id not in self.training_jobs:
                response = TrainingStatusResponse()
                response.job_id = job_id
                response.status = "TRAINING_STATUS_FAILED"
                response.error_message = "Job not found"
                return response

            # Get status from orchestrator
            job_status = self.orchestrator.get_job_status(job_id)

            response = TrainingStatusResponse()
            response.job_id = job_id

            if job_status:
                response.status = self._convert_training_status(job_status["status"])
                response.progress_percentage = job_status.get("progress", 0.0)

                # Add current metrics if available
                if "metrics" in job_status:
                    metrics = job_status["metrics"]
                    response.current_metrics.loss = metrics.get("loss", 0.0)
                    response.current_metrics.accuracy = metrics.get("accuracy", 0.0)
            else:
                response.status = "TRAINING_STATUS_FAILED"
                response.error_message = "Status not available"

            return response

        except Exception as e:
            self.logger.error(f"Get training status failed: {e}")
            response = TrainingStatusResponse()
            response.job_id = request.job_id
            response.status = "TRAINING_STATUS_FAILED"
            response.error_message = str(e)
            return response

    async def HealthCheck(self, request, context):
        """Health check for the service."""
        try:
            # Check if orchestrator is healthy
            is_healthy = True  # Placeholder

            response = HealthCheckResponse()
            if is_healthy:
                response.status = "SERVING"
                response.details["orchestrator"] = "healthy"
                response.details["models_loaded"] = str(len(self.models))
            else:
                response.status = "NOT_SERVING"
                response.details["error"] = "Service unhealthy"

            return response

        except Exception as e:
            self.logger.error(f"Health check failed: {e}")
            response = HealthCheckResponse()
            response.status = "NOT_SERVING"
            response.details["error"] = str(e)
            return response

    def _convert_model_type(self, grpc_model_type):
        """Convert gRPC model type to internal format."""
        mapping = {
            1: "nlp",      # MODEL_TYPE_NLP_TRANSFORMER
            2: "vision",   # MODEL_TYPE_VISION_CNN
            3: "rl",       # MODEL_TYPE_RL_AGENT
            4: "abstraction",  # MODEL_TYPE_ABSTRACTION
            5: "multimodal",   # MODEL_TYPE_MULTIMODAL
        }
        return mapping.get(grpc_model_type, "nlp")

    def _convert_training_data(self, grpc_data):
        """Convert gRPC training data to internal format."""
        converted_data = []
        for data in grpc_data:
            converted_data.append({
                "data_id": data.data_id,
                "data_type": data.data_type,
                "data_payload": data.data_payload,
                "label": data.label,
                "annotations": dict(data.annotations),
            })
        return converted_data

    def _convert_inference_data(self, grpc_data):
        """Convert gRPC inference data to internal format."""
        return {
            "data_type": grpc_data.data_type,
            "data_payload": grpc_data.data_payload,
            "metadata": dict(grpc_data.metadata),
        }

    def _convert_training_status(self, internal_status):
        """Convert internal training status to gRPC format."""
        mapping = {
            "pending": "TRAINING_STATUS_PENDING",
            "running": "TRAINING_STATUS_RUNNING",
            "completed": "TRAINING_STATUS_COMPLETED",
            "failed": "TRAINING_STATUS_FAILED",
            "cancelled": "TRAINING_STATUS_CANCELLED",
        }
        return mapping.get(internal_status, "TRAINING_STATUS_UNSPECIFIED")


# Placeholder classes for gRPC messages (these would be generated from proto)
class TrainModelResponse:
    def __init__(self):
        self.success = False
        self.job_id = ""
        self.model_version = ""
        self.error_message = ""

class StreamTrainResponse:
    def __init__(self):
        self.job_id = ""
        self.status = ""
        self.message = ""
        self.metrics = TrainingMetrics()

class TrainingMetrics:
    def __init__(self):
        self.loss = 0.0
        self.accuracy = 0.0
        self.step = 0

class InferenceResponse:
    def __init__(self):
        self.success = False
        self.result = InferenceResult()
        self.error_message = ""
        self.metrics = InferenceMetrics()

class InferenceResult:
    def __init__(self):
        self.confidence = 0.0
        self.output_data = b""

class InferenceMetrics:
    def __init__(self):
        self.latency_ms = 0

class BatchInferenceResponse:
    def __init__(self):
        self.success = False
        self.results = []
        self.error_message = ""
        self.metrics = BatchInferenceMetrics()

class BatchInferenceMetrics:
    def __init__(self):
        self.total_latency_ms = 0
        self.avg_latency_ms = 0
        self.batch_size = 0
        self.successful_inferences = 0

class ModelInfoResponse:
    def __init__(self):
        self.model_info = ModelInfo()
        self.error_message = ""

class ModelInfo:
    def __init__(self):
        self.model_id = ""
        self.model_version = ""
        self.name = ""
        self.status = ""

class ListModelsResponse:
    def __init__(self):
        self.models = []
        self.total_count = 0

class DeployModelResponse:
    def __init__(self):
        self.success = False
        self.deployment_id = ""
        self.endpoint_url = ""
        self.error_message = ""

class TrainingStatusResponse:
    def __init__(self):
        self.job_id = ""
        self.status = ""
        self.current_metrics = TrainingMetrics()
        self.progress_percentage = 0.0
        self.error_message = ""

class HealthCheckResponse:
    def __init__(self):
        self.status = ""
        self.details = {}


async def serve():
    """Start the gRPC server."""
    logger = get_logger(__name__)

    # Load configuration
    config_path = "configs/grpc_server_config.yaml"
    if len(sys.argv) > 1:
        config_path = sys.argv[1]

    try:
        # Create servicer
        servicer = LearningEngineServicer(config_path)

        # Create server
        server = grpc.aio.server(futures.ThreadPoolExecutor(max_workers=10))

        # Add servicer to server (this would use generated code in real implementation)
        # learning_engine_pb2_grpc.add_LearningEngineServiceServicer_to_server(servicer, server)

        # Enable reflection for debugging
        SERVICE_NAMES = (
            "asi.learning_engine.LearningEngineService",
            reflection.SERVICE_NAME,
        )
        reflection.enable_server_reflection(SERVICE_NAMES, server)

        # Configure server address
        listen_addr = "[::]:50060"
        server.add_insecure_port(listen_addr)

        # Start server
        logger.info(f"Starting gRPC server on {listen_addr}")
        await server.start()

        # Setup graceful shutdown
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, shutting down...")
            asyncio.create_task(server.stop(5))

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

        logger.info("gRPC server started successfully")

        # Wait for termination
        await server.wait_for_termination()

    except Exception as e:
        logger.error(f"Failed to start gRPC server: {e}")
        sys.exit(1)


def main():
    """Main entry point."""
    asyncio.run(serve())


if __name__ == "__main__":
    main()
