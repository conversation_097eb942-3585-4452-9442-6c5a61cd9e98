apiVersion: apps/v1
kind: Deployment
metadata:
  name: training-engine
  namespace: asi-learning-engine
  labels:
    app: training-engine
    component: learning-engine
    tier: training
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: training-engine
  template:
    metadata:
      labels:
        app: training-engine
        component: learning-engine
        tier: training
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8090"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: asi-learning-engine
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      containers:
      - name: training-engine
        image: asi-learning-training:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8090
          name: metrics
          protocol: TCP
        - containerPort: 8093
          name: health
          protocol: TCP
        env:
        - name: CUDA_VISIBLE_DEVICES
          value: "0"
        - name: PYTHONPATH
          value: "/app/src"
        - name: ASI_KAFKA_BOOTSTRAP_SERVERS
          value: "kafka:9092"
        - name: ASI_DATA_INTEGRATION_ENDPOINT
          value: "data-integration:50055"
        - name: ASI_MONITORING_ENABLED
          value: "true"
        - name: ASI_MONITORING_PROMETHEUS_PORT
          value: "8090"
        - name: ASI_MLFLOW_TRACKING_URI
          value: "http://mlflow:5000"
        - name: ASI_MAX_CONCURRENT_JOBS
          value: "2"
        - name: ASI_LOG_LEVEL
          value: "INFO"
        - name: ASI_ENVIRONMENT
          value: "production"
        resources:
          requests:
            cpu: "2"
            memory: "8Gi"
            nvidia.com/gpu: "1"
          limits:
            cpu: "4"
            memory: "16Gi"
            nvidia.com/gpu: "1"
        volumeMounts:
        - name: config-volume
          mountPath: /app/configs
          readOnly: true
        - name: models-volume
          mountPath: /app/models
        - name: checkpoints-volume
          mountPath: /app/checkpoints
        - name: logs-volume
          mountPath: /app/logs
        - name: data-volume
          mountPath: /app/data
        livenessProbe:
          httpGet:
            path: /health
            port: 8093
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8093
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health
            port: 8093
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 10
      volumes:
      - name: config-volume
        configMap:
          name: learning-engine-config
      - name: models-volume
        persistentVolumeClaim:
          claimName: models-pvc
      - name: checkpoints-volume
        persistentVolumeClaim:
          claimName: checkpoints-pvc
      - name: logs-volume
        persistentVolumeClaim:
          claimName: logs-pvc
      - name: data-volume
        persistentVolumeClaim:
          claimName: training-data-pvc
      nodeSelector:
        accelerator: nvidia-tesla-v100
      tolerations:
      - key: nvidia.com/gpu
        operator: Exists
        effect: NoSchedule
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - training-engine
              topologyKey: kubernetes.io/hostname
---
apiVersion: v1
kind: Service
metadata:
  name: training-engine
  namespace: asi-learning-engine
  labels:
    app: training-engine
    component: learning-engine
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8090"
    prometheus.io/path: "/metrics"
spec:
  type: ClusterIP
  ports:
  - port: 8090
    targetPort: 8090
    protocol: TCP
    name: metrics
  - port: 8093
    targetPort: 8093
    protocol: TCP
    name: health
  selector:
    app: training-engine
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: training-engine-hpa
  namespace: asi-learning-engine
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: training-engine
  minReplicas: 1
  maxReplicas: 4
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 60
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: training-engine-pdb
  namespace: asi-learning-engine
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: training-engine
