apiVersion: v1
kind: Namespace
metadata:
  name: asi-learning-engine
  labels:
    name: asi-learning-engine
    component: learning-engine
    part-of: asi-system
    version: v1.0.0
  annotations:
    description: "ASI Learning Engine namespace for ML/DL training and inference"
    contact: "<EMAIL>"
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: asi-learning-engine-quota
  namespace: asi-learning-engine
spec:
  hard:
    requests.cpu: "20"
    requests.memory: 64Gi
    requests.nvidia.com/gpu: "4"
    limits.cpu: "40"
    limits.memory: 128Gi
    limits.nvidia.com/gpu: "4"
    persistentvolumeclaims: "10"
    services: "10"
    secrets: "20"
    configmaps: "20"
---
apiVersion: v1
kind: LimitRange
metadata:
  name: asi-learning-engine-limits
  namespace: asi-learning-engine
spec:
  limits:
  - default:
      cpu: "2"
      memory: "4Gi"
      nvidia.com/gpu: "1"
    defaultRequest:
      cpu: "500m"
      memory: "1Gi"
    type: Container
  - max:
      cpu: "8"
      memory: "16Gi"
      nvidia.com/gpu: "2"
    min:
      cpu: "100m"
      memory: "128Mi"
    type: Container
