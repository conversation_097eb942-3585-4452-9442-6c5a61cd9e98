# Multi-stage build for ASI Learning Engine Training Service
FROM nvidia/cuda:11.8-devel-ubuntu20.04 as base

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV CUDA_HOME=/usr/local/cuda
ENV PATH=${CUDA_HOME}/bin:${PATH}
ENV LD_LIBRARY_PATH=${CUDA_HOME}/lib64:${LD_LIBRARY_PATH}

# Install system dependencies
RUN apt-get update && apt-get install -y \
    python3.9 \
    python3.9-dev \
    python3-pip \
    python3.9-venv \
    build-essential \
    cmake \
    git \
    wget \
    curl \
    unzip \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    libgoogle-perftools4 \
    libtcmalloc-minimal4 \
    && rm -rf /var/lib/apt/lists/*

# Create symbolic link for python
RUN ln -s /usr/bin/python3.9 /usr/bin/python

# Upgrade pip
RUN python -m pip install --upgrade pip setuptools wheel

# Install PyTorch with CUDA support
RUN pip install torch==2.0.1+cu118 torchvision==0.15.2+cu118 torchaudio==2.0.2+cu118 \
    --index-url https://download.pytorch.org/whl/cu118

# Development stage
FROM base as development

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Install additional development tools
RUN pip install --no-cache-dir \
    jupyter \
    ipython \
    notebook \
    jupyterlab \
    pytest-xdist \
    pytest-benchmark \
    memory-profiler \
    line-profiler

# Copy source code
COPY . .

# Install package in development mode
RUN pip install -e .

# Create necessary directories
RUN mkdir -p /app/logs /app/checkpoints /app/models /app/data

# Set environment variables for development
ENV PYTHONPATH=/app/src
ENV ASI_LOG_LEVEL=DEBUG
ENV ASI_ENVIRONMENT=development

# Expose ports
EXPOSE 8090 8093

# Development command
CMD ["python", "-m", "asi_learning.training.orchestrator"]

# Production stage
FROM base as production

# Install production Python dependencies only
COPY requirements.txt /tmp/
RUN pip install --no-cache-dir -r /tmp/requirements.txt && \
    rm /tmp/requirements.txt

# Create non-root user
RUN groupadd -r asi && useradd -r -g asi asi

# Set working directory
WORKDIR /app

# Copy source code
COPY --chown=asi:asi src/ ./src/
COPY --chown=asi:asi configs/ ./configs/

# Create necessary directories with proper permissions
RUN mkdir -p /app/logs /app/checkpoints /app/models /app/data && \
    chown -R asi:asi /app

# Install package
RUN pip install --no-cache-dir ./src

# Set environment variables for production
ENV PYTHONPATH=/app/src
ENV ASI_LOG_LEVEL=INFO
ENV ASI_ENVIRONMENT=production
ENV CUDA_LAUNCH_BLOCKING=0
ENV TORCH_CUDNN_V8_API_ENABLED=1

# Switch to non-root user
USER asi

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8093/health || exit 1

# Expose ports
EXPOSE 8090 8093

# Production command
CMD ["python", "-m", "asi_learning.training.orchestrator", "--config", "configs/production.yaml"]

# Testing stage
FROM development as testing

# Install additional testing dependencies
RUN pip install --no-cache-dir \
    pytest-cov \
    pytest-mock \
    pytest-asyncio \
    pytest-timeout \
    coverage[toml] \
    bandit \
    safety

# Copy test files
COPY tests/ ./tests/

# Run tests
RUN python -m pytest tests/ -v --cov=src/asi_learning --cov-report=html --cov-report=term

# Security scanning
RUN bandit -r src/ -f json -o bandit-report.json || true
RUN safety check --json --output safety-report.json || true

# Benchmark stage
FROM production as benchmark

# Install benchmarking tools
RUN pip install --no-cache-dir \
    pytest-benchmark \
    memory-profiler \
    psutil \
    gpustat

# Copy benchmark scripts
COPY benchmarks/ ./benchmarks/

# Set benchmark environment
ENV ASI_BENCHMARK_MODE=true
ENV CUDA_VISIBLE_DEVICES=0

# Benchmark command
CMD ["python", "-m", "benchmarks.run_benchmarks"]

# Final production image
FROM production as final

# Add labels
LABEL maintainer="ASI System Team <<EMAIL>>"
LABEL version="1.0.0"
LABEL description="ASI Learning Engine Training Service"
LABEL org.opencontainers.image.source="https://github.com/asi-system/learning-engine"
LABEL org.opencontainers.image.documentation="https://docs.asi-system.com/learning-engine"
LABEL org.opencontainers.image.licenses="MIT"

# Add build info
ARG BUILD_DATE
ARG VCS_REF
ARG VERSION
LABEL org.opencontainers.image.created=$BUILD_DATE
LABEL org.opencontainers.image.revision=$VCS_REF
LABEL org.opencontainers.image.version=$VERSION

# Final setup
WORKDIR /app

# Ensure proper permissions
USER asi

# Default command
CMD ["python", "-m", "asi_learning.training.orchestrator"]
