"""
Abstraction Trainer for the ASI Learning Engine.

Provides meta-learning and abstract reasoning model training,
including few-shot learning, transfer learning, and reasoning networks.
"""

import os
import json
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable, Tuple
import asyncio

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import numpy as np
from sklearn.metrics import accuracy_score

from ..utils.logger import get_logger, TrainingLogger
from ..utils.config import AbstractionConfig
from ..utils.metrics import MetricsCollector

logger = get_logger(__name__)


class AbstractionDataset(Dataset):
    """Dataset for abstraction learning tasks."""

    def __init__(
        self,
        data: List[Dict[str, Any]],
        task_type: str = "few_shot_classification"
    ):
        self.data = data
        self.task_type = task_type

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        item = self.data[idx]

        if self.task_type == "few_shot_classification":
            return {
                'support_set': torch.tensor(item['support_set'], dtype=torch.float32),
                'support_labels': torch.tensor(item['support_labels'], dtype=torch.long),
                'query_set': torch.tensor(item['query_set'], dtype=torch.float32),
                'query_labels': torch.tensor(item['query_labels'], dtype=torch.long),
            }
        elif self.task_type == "reasoning":
            return {
                'context': torch.tensor(item['context'], dtype=torch.float32),
                'question': torch.tensor(item['question'], dtype=torch.float32),
                'answer': torch.tensor(item['answer'], dtype=torch.long),
            }
        else:
            return item


class MetaLearningNetwork(nn.Module):
    """Meta-learning network for few-shot learning."""

    def __init__(
        self,
        input_dim: int,
        hidden_dim: int = 512,
        num_layers: int = 4,
        num_heads: int = 8
    ):
        super().__init__()
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim

        # Embedding layers
        self.support_encoder = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
        )

        self.query_encoder = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
        )

        # Attention mechanism
        self.attention = nn.MultiheadAttention(
            embed_dim=hidden_dim,
            num_heads=num_heads,
            batch_first=True
        )

        # Classification head
        self.classifier = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim // 2, 1),
        )

    def forward(self, support_set, support_labels, query_set):
        batch_size, n_support, input_dim = support_set.shape
        n_query = query_set.shape[1]

        # Encode support and query sets
        support_encoded = self.support_encoder(support_set.view(-1, input_dim))
        support_encoded = support_encoded.view(batch_size, n_support, self.hidden_dim)

        query_encoded = self.query_encoder(query_set.view(-1, input_dim))
        query_encoded = query_encoded.view(batch_size, n_query, self.hidden_dim)

        # Apply attention
        attended_query, _ = self.attention(
            query_encoded, support_encoded, support_encoded
        )

        # Classify
        logits = self.classifier(attended_query)
        return logits.squeeze(-1)


class ReasoningNetwork(nn.Module):
    """Neural network for abstract reasoning tasks."""

    def __init__(
        self,
        context_dim: int,
        question_dim: int,
        hidden_dim: int = 512,
        num_layers: int = 6,
        num_heads: int = 8,
        num_classes: int = 10
    ):
        super().__init__()
        self.hidden_dim = hidden_dim

        # Encoders
        self.context_encoder = nn.Linear(context_dim, hidden_dim)
        self.question_encoder = nn.Linear(question_dim, hidden_dim)

        # Transformer layers
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=hidden_dim,
            nhead=num_heads,
            dim_feedforward=hidden_dim * 4,
            dropout=0.1,
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers)

        # Reasoning head
        self.reasoning_head = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, num_classes),
        )

    def forward(self, context, question):
        # Encode inputs
        context_encoded = self.context_encoder(context)
        question_encoded = self.question_encoder(question)

        # Combine context and question
        combined = torch.cat([context_encoded, question_encoded], dim=1)

        # Apply transformer
        transformed = self.transformer(combined)

        # Pool and classify
        pooled = torch.cat([
            transformed.mean(dim=1),
            transformed.max(dim=1)[0]
        ], dim=1)

        logits = self.reasoning_head(pooled)
        return logits


class AbstractionTrainer:
    """
    Advanced abstraction trainer for the ASI Learning Engine.

    Supports:
    - Meta-learning and few-shot learning
    - Abstract reasoning tasks
    - Transfer learning
    - Continual learning
    """

    def __init__(self, config: AbstractionConfig):
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = None
        self.optimizer = None
        self.scheduler = None
        self.criterion = None
        self.training_logger = None

        logger.info(f"Abstraction Trainer initialized with device: {self.device}")

    def setup_model(self, task_type: str = "meta_learning", **kwargs) -> None:
        """Setup model for specific abstraction task."""
        try:
            if task_type == "meta_learning":
                self.model = MetaLearningNetwork(
                    input_dim=kwargs.get('input_dim', 784),
                    hidden_dim=self.config.hidden_size,
                    num_layers=self.config.num_layers,
                    num_heads=self.config.num_heads
                )
                self.criterion = nn.BCEWithLogitsLoss()

            elif task_type == "reasoning":
                self.model = ReasoningNetwork(
                    context_dim=kwargs.get('context_dim', 512),
                    question_dim=kwargs.get('question_dim', 256),
                    hidden_dim=self.config.hidden_size,
                    num_layers=self.config.num_layers,
                    num_heads=self.config.num_heads,
                    num_classes=kwargs.get('num_classes', 10)
                )
                self.criterion = nn.CrossEntropyLoss()

            else:
                raise ValueError(f"Unsupported task type: {task_type}")

            # Move model to device
            self.model.to(self.device)

            logger.info(f"Abstraction model setup completed for task: {task_type}")

        except Exception as e:
            logger.error(f"Failed to setup model: {e}")
            raise

    def setup_optimizer(self, learning_rate: float = None) -> None:
        """Setup optimizer and scheduler."""
        learning_rate = learning_rate or self.config.training.learning_rate

        self.optimizer = optim.Adam(
            self.model.parameters(),
            lr=learning_rate,
            weight_decay=1e-4
        )

        self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer,
            T_max=self.config.training.epochs
        )

    def prepare_data(self, data: Dict[str, Any], task_type: str) -> Tuple[DataLoader, DataLoader]:
        """Prepare training and validation data loaders."""
        try:
            # Extract data
            train_data = data.get('train_data', [])
            val_data = data.get('val_data', [])

            # Create datasets
            train_dataset = AbstractionDataset(train_data, task_type)
            val_dataset = AbstractionDataset(val_data, task_type) if val_data else None

            # Create data loaders
            train_loader = DataLoader(
                train_dataset,
                batch_size=self.config.training.batch_size,
                shuffle=True,
                num_workers=2
            )

            val_loader = None
            if val_dataset:
                val_loader = DataLoader(
                    val_dataset,
                    batch_size=self.config.training.batch_size,
                    shuffle=False,
                    num_workers=2
                )

            logger.info(f"Prepared data loaders - Train: {len(train_dataset)}, Val: {len(val_dataset) if val_dataset else 0}")

            return train_loader, val_loader

        except Exception as e:
            logger.error(f"Failed to prepare data: {e}")
            raise

    def train_epoch(
        self,
        train_loader: DataLoader,
        epoch: int,
        task_type: str,
        hooks: Dict[str, Callable] = None
    ) -> Dict[str, float]:
        """Train for one epoch."""
        self.model.train()
        running_loss = 0.0
        correct = 0
        total = 0

        if hooks and 'on_epoch_start' in hooks:
            hooks['on_epoch_start'](epoch)

        for batch_idx, batch in enumerate(train_loader):
            # Move batch to device
            for key in batch:
                if isinstance(batch[key], torch.Tensor):
                    batch[key] = batch[key].to(self.device)

            # Zero gradients
            self.optimizer.zero_grad()

            # Forward pass
            if task_type == "meta_learning":
                outputs = self.model(
                    batch['support_set'],
                    batch['support_labels'],
                    batch['query_set']
                )
                targets = batch['query_labels'].float()
                loss = self.criterion(outputs, targets)

                # Calculate accuracy
                predictions = (torch.sigmoid(outputs) > 0.5).float()
                correct += (predictions == targets).sum().item()
                total += targets.numel()

            elif task_type == "reasoning":
                outputs = self.model(batch['context'], batch['question'])
                targets = batch['answer']
                loss = self.criterion(outputs, targets)

                # Calculate accuracy
                _, predicted = outputs.max(1)
                correct += predicted.eq(targets).sum().item()
                total += targets.size(0)

            # Backward pass
            loss.backward()

            # Gradient clipping
            if self.config.training.gradient_clip_norm > 0:
                torch.nn.utils.clip_grad_norm_(
                    self.model.parameters(),
                    self.config.training.gradient_clip_norm
                )

            self.optimizer.step()

            # Statistics
            running_loss += loss.item()

            # Log batch metrics
            if hooks and 'on_batch_end' in hooks and batch_idx % 50 == 0:
                hooks['on_batch_end'](batch_idx, loss.item())

        # Calculate epoch metrics
        epoch_loss = running_loss / len(train_loader)
        epoch_acc = 100. * correct / total

        metrics = {
            'train_loss': epoch_loss,
            'train_accuracy': epoch_acc,
        }

        if hooks and 'on_epoch_end' in hooks:
            hooks['on_epoch_end'](epoch, metrics)

        return metrics

    def validate_epoch(
        self,
        val_loader: DataLoader,
        task_type: str,
        hooks: Dict[str, Callable] = None
    ) -> Dict[str, float]:
        """Validate for one epoch."""
        self.model.eval()
        running_loss = 0.0
        correct = 0
        total = 0

        with torch.no_grad():
            for batch in val_loader:
                # Move batch to device
                for key in batch:
                    if isinstance(batch[key], torch.Tensor):
                        batch[key] = batch[key].to(self.device)

                # Forward pass
                if task_type == "meta_learning":
                    outputs = self.model(
                        batch['support_set'],
                        batch['support_labels'],
                        batch['query_set']
                    )
                    targets = batch['query_labels'].float()
                    loss = self.criterion(outputs, targets)

                    # Calculate accuracy
                    predictions = (torch.sigmoid(outputs) > 0.5).float()
                    correct += (predictions == targets).sum().item()
                    total += targets.numel()

                elif task_type == "reasoning":
                    outputs = self.model(batch['context'], batch['question'])
                    targets = batch['answer']
                    loss = self.criterion(outputs, targets)

                    # Calculate accuracy
                    _, predicted = outputs.max(1)
                    correct += predicted.eq(targets).sum().item()
                    total += targets.size(0)

                running_loss += loss.item()

        # Calculate metrics
        val_loss = running_loss / len(val_loader)
        val_acc = 100. * correct / total

        metrics = {
            'val_loss': val_loss,
            'val_accuracy': val_acc,
        }

        if hooks and 'on_validation' in hooks:
            hooks['on_validation'](metrics)

        return metrics

    def train(
        self,
        config: Dict[str, Any],
        hooks: Dict[str, Callable] = None,
        job_id: str = None
    ) -> Dict[str, Any]:
        """Train the abstraction model."""
        try:
            # Setup training logger
            if job_id:
                self.training_logger = TrainingLogger(job_id, "abstraction")

            # Setup model
            task_type = config.get('task_type', 'meta_learning')
            self.setup_model(task_type, **config.get('model_params', {}))

            # Setup optimizer
            self.setup_optimizer(config.get('learning_rate'))

            # Prepare data
            train_loader, val_loader = self.prepare_data(config['data'], task_type)

            # Training loop
            best_val_acc = 0.0
            patience_counter = 0

            for epoch in range(self.config.training.epochs):
                if self.training_logger:
                    self.training_logger.log_epoch_start(epoch, self.config.training.epochs)

                # Train epoch
                train_metrics = self.train_epoch(train_loader, epoch, task_type, hooks)

                # Validate epoch
                val_metrics = {}
                if val_loader:
                    val_metrics = self.validate_epoch(val_loader, task_type, hooks)

                    # Early stopping
                    if val_metrics['val_accuracy'] > best_val_acc:
                        best_val_acc = val_metrics['val_accuracy']
                        patience_counter = 0
                        # Save best model
                        self.save_checkpoint(config.get('checkpoint_dir', './checkpoints'), epoch, is_best=True)
                    else:
                        patience_counter += 1
                        if patience_counter >= self.config.training.early_stopping_patience:
                            logger.info(f"Early stopping at epoch {epoch}")
                            break

                # Update scheduler
                if self.scheduler:
                    self.scheduler.step()

                # Log metrics
                all_metrics = {**train_metrics, **val_metrics}
                if self.training_logger:
                    self.training_logger.log_epoch_end(epoch, all_metrics)

                # Save checkpoint
                if epoch % self.config.training.checkpoint_frequency == 0:
                    self.save_checkpoint(config.get('checkpoint_dir', './checkpoints'), epoch)

            # Save final model
            model_path = Path(config.get('model_save_path', './models/abstraction_model'))
            model_path.mkdir(parents=True, exist_ok=True)
            self.save_model(str(model_path))

            if self.training_logger:
                self.training_logger.log_model_save(str(model_path), epoch)

            results = {
                'best_val_accuracy': best_val_acc,
                'final_epoch': epoch,
                'model_path': str(model_path),
            }

            logger.info(f"Abstraction training completed successfully: {results}")
            return results

        except Exception as e:
            if self.training_logger:
                self.training_logger.log_error(e, "training")
            logger.error(f"Abstraction training failed: {e}")
            raise

    def predict(self, data: Dict[str, Any], task_type: str = "meta_learning") -> List[Dict[str, Any]]:
        """Make predictions with the trained model."""
        try:
            if not self.model:
                raise ValueError("Model not loaded. Call setup_model() first.")

            self.model.eval()
            predictions = []

            with torch.no_grad():
                if task_type == "meta_learning":
                    support_set = torch.tensor(data['support_set'], dtype=torch.float32).to(self.device)
                    support_labels = torch.tensor(data['support_labels'], dtype=torch.long).to(self.device)
                    query_set = torch.tensor(data['query_set'], dtype=torch.float32).to(self.device)

                    outputs = self.model(support_set.unsqueeze(0), support_labels.unsqueeze(0), query_set.unsqueeze(0))
                    probabilities = torch.sigmoid(outputs).squeeze(0)
                    predicted_labels = (probabilities > 0.5).float()

                    for i, (prob, pred) in enumerate(zip(probabilities, predicted_labels)):
                        predictions.append({
                            'query_index': i,
                            'predicted_label': pred.item(),
                            'confidence': prob.item(),
                        })

                elif task_type == "reasoning":
                    context = torch.tensor(data['context'], dtype=torch.float32).to(self.device)
                    question = torch.tensor(data['question'], dtype=torch.float32).to(self.device)

                    outputs = self.model(context.unsqueeze(0), question.unsqueeze(0))
                    probabilities = torch.softmax(outputs, dim=1)
                    predicted_class = torch.argmax(outputs, dim=1).item()
                    confidence = probabilities[0][predicted_class].item()

                    predictions.append({
                        'predicted_class': predicted_class,
                        'confidence': confidence,
                        'probabilities': probabilities[0].cpu().numpy().tolist()
                    })

            return predictions

        except Exception as e:
            logger.error(f"Prediction failed: {e}")
            return []

    def save_checkpoint(self, checkpoint_dir: str, epoch: int, is_best: bool = False) -> None:
        """Save model checkpoint."""
        try:
            checkpoint_path = Path(checkpoint_dir)
            checkpoint_path.mkdir(parents=True, exist_ok=True)

            checkpoint = {
                'epoch': epoch,
                'model_state_dict': self.model.state_dict(),
                'optimizer_state_dict': self.optimizer.state_dict(),
                'scheduler_state_dict': self.scheduler.state_dict() if self.scheduler else None,
                'config': self.config,
            }

            # Save regular checkpoint
            torch.save(checkpoint, checkpoint_path / f'checkpoint_epoch_{epoch}.pth')

            # Save best checkpoint
            if is_best:
                torch.save(checkpoint, checkpoint_path / 'best_model.pth')

            logger.info(f"Abstraction checkpoint saved: epoch {epoch}")

        except Exception as e:
            logger.error(f"Failed to save checkpoint: {e}")

    def save_model(self, path: str) -> None:
        """Save the trained model."""
        try:
            model_path = Path(path)
            model_path.mkdir(parents=True, exist_ok=True)

            # Save model state dict
            torch.save(self.model.state_dict(), model_path / 'model.pth')

            # Save config
            with open(model_path / 'config.json', 'w') as f:
                json.dump(self.config.__dict__, f, indent=2)

            logger.info(f"Abstraction model saved to {path}")

        except Exception as e:
            logger.error(f"Failed to save model: {e}")

    def load_model(self, path: str, task_type: str = "meta_learning") -> None:
        """Load a trained model."""
        try:
            model_path = Path(path)

            # Load config
            with open(model_path / 'config.json', 'r') as f:
                config_dict = json.load(f)

            # Setup model
            self.setup_model(task_type, **config_dict.get('model_params', {}))

            # Load state dict
            self.model.load_state_dict(torch.load(model_path / 'model.pth', map_location=self.device))
            self.model.eval()

            logger.info(f"Abstraction model loaded from {path}")

        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            raise

    async def process_streaming_data(self, data: Dict[str, Any]) -> None:
        """Process streaming data for continuous learning."""
        try:
            # Extract abstraction task data from streaming input
            task_data = data.get('task_data')
            task_type = data.get('task_type', 'meta_learning')

            if not task_data:
                return

            # For now, just log the data
            # In a full implementation, this would update the model
            logger.debug(f"Received streaming abstraction data for task: {task_type}")

        except Exception as e:
            logger.error(f"Failed to process streaming data: {e}")

    async def health_check(self) -> bool:
        """Check if the trainer is healthy."""
        try:
            # Check if model is loaded
            if self.model is None:
                return False

            # Check if model is on correct device
            if next(self.model.parameters()).device != self.device:
                return False

            # Try a simple forward pass
            if isinstance(self.model, MetaLearningNetwork):
                dummy_support = torch.randn(1, 5, 784).to(self.device)
                dummy_support_labels = torch.randint(0, 2, (1, 5)).to(self.device)
                dummy_query = torch.randn(1, 3, 784).to(self.device)
                with torch.no_grad():
                    _ = self.model(dummy_support, dummy_support_labels, dummy_query)
            elif isinstance(self.model, ReasoningNetwork):
                dummy_context = torch.randn(1, 512).to(self.device)
                dummy_question = torch.randn(1, 256).to(self.device)
                with torch.no_grad():
                    _ = self.model(dummy_context, dummy_question)

            return True

        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return False
