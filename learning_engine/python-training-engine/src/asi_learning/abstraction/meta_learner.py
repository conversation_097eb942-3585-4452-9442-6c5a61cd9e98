"""
Meta-Learning and Abstract Reasoning Engine for ASI Learning Engine.

Provides advanced meta-learning capabilities including:
- Few-shot learning and adaptation
- Model-agnostic meta-learning (MAML)
- Neural architecture search (NAS)
- Abstract reasoning and pattern recognition
- Transfer learning and domain adaptation
- Continual learning and catastrophic forgetting prevention
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader, Dataset
import numpy as np
from typing import Dict, List, Any, Optional, Tuple, Callable
import logging
from dataclasses import dataclass
from collections import defaultdict, deque
import copy
import random

logger = logging.getLogger(__name__)


@dataclass
class MetaLearningConfig:
    """Configuration for meta-learning algorithms."""
    algorithm: str = "MAML"  # MAML, Reptile, ProtoNet, MatchingNet
    inner_lr: float = 0.01
    outer_lr: float = 0.001
    inner_steps: int = 5
    meta_batch_size: int = 16
    support_size: int = 5
    query_size: int = 15
    num_ways: int = 5  # Number of classes in few-shot learning
    adaptation_steps: int = 10
    temperature: float = 1.0
    use_first_order: bool = False  # First-order MAML
    device: str = "cuda" if torch.cuda.is_available() else "cpu"


class MAMLModel(nn.Module):
    """
    Model-Agnostic Meta-Learning (MAML) implementation.
    """
    
    def __init__(self, input_dim: int, hidden_dims: List[int], output_dim: int):
        super().__init__()
        
        layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.1)
            ])
            prev_dim = hidden_dim
        
        layers.append(nn.Linear(prev_dim, output_dim))
        
        self.network = nn.Sequential(*layers)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return self.network(x)
    
    def clone(self):
        """Create a deep copy of the model."""
        return copy.deepcopy(self)


class PrototypicalNetwork(nn.Module):
    """
    Prototypical Networks for few-shot learning.
    """
    
    def __init__(self, input_dim: int, hidden_dims: List[int], embedding_dim: int):
        super().__init__()
        
        layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU(),
                nn.BatchNorm1d(hidden_dim),
                nn.Dropout(0.1)
            ])
            prev_dim = hidden_dim
        
        layers.append(nn.Linear(prev_dim, embedding_dim))
        
        self.encoder = nn.Sequential(*layers)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return self.encoder(x)
    
    def compute_prototypes(self, support_embeddings: torch.Tensor, support_labels: torch.Tensor) -> torch.Tensor:
        """Compute class prototypes from support set."""
        unique_labels = torch.unique(support_labels)
        prototypes = []
        
        for label in unique_labels:
            mask = support_labels == label
            prototype = support_embeddings[mask].mean(dim=0)
            prototypes.append(prototype)
        
        return torch.stack(prototypes)
    
    def classify(self, query_embeddings: torch.Tensor, prototypes: torch.Tensor, temperature: float = 1.0) -> torch.Tensor:
        """Classify query examples using prototypes."""
        distances = torch.cdist(query_embeddings, prototypes)
        logits = -distances / temperature
        return F.softmax(logits, dim=1)


class MatchingNetwork(nn.Module):
    """
    Matching Networks for one-shot learning.
    """
    
    def __init__(self, input_dim: int, hidden_dims: List[int], embedding_dim: int):
        super().__init__()
        
        # Feature encoder
        encoder_layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_dims:
            encoder_layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.1)
            ])
            prev_dim = hidden_dim
        
        encoder_layers.append(nn.Linear(prev_dim, embedding_dim))
        self.encoder = nn.Sequential(*encoder_layers)
        
        # Attention mechanism
        self.attention = nn.MultiheadAttention(embedding_dim, num_heads=8, batch_first=True)
        
    def forward(self, support_set: torch.Tensor, support_labels: torch.Tensor, 
                query_set: torch.Tensor) -> torch.Tensor:
        """Forward pass for matching networks."""
        # Encode support and query sets
        support_embeddings = self.encoder(support_set)
        query_embeddings = self.encoder(query_set)
        
        # Apply attention
        attended_queries, attention_weights = self.attention(
            query_embeddings.unsqueeze(1),
            support_embeddings.unsqueeze(0).repeat(query_embeddings.size(0), 1, 1),
            support_embeddings.unsqueeze(0).repeat(query_embeddings.size(0), 1, 1)
        )
        
        # Compute similarities
        similarities = F.cosine_similarity(
            attended_queries.squeeze(1).unsqueeze(1),
            support_embeddings.unsqueeze(0),
            dim=2
        )
        
        # Weight by support labels
        num_classes = len(torch.unique(support_labels))
        class_similarities = torch.zeros(query_embeddings.size(0), num_classes, device=query_embeddings.device)
        
        for i, label in enumerate(torch.unique(support_labels)):
            mask = support_labels == label
            class_similarities[:, i] = similarities[:, mask].mean(dim=1)
        
        return F.softmax(class_similarities, dim=1)


class MetaLearner:
    """
    Meta-learning orchestrator supporting multiple algorithms.
    """
    
    def __init__(self, config: MetaLearningConfig):
        self.config = config
        self.device = torch.device(config.device)
        self.model = None
        self.meta_optimizer = None
        self.task_history = deque(maxlen=1000)
        
        logger.info(f"MetaLearner initialized with algorithm: {config.algorithm}")
    
    def setup_model(self, input_dim: int, output_dim: int, hidden_dims: List[int] = [256, 128]):
        """Setup the meta-learning model."""
        if self.config.algorithm == "MAML":
            self.model = MAMLModel(input_dim, hidden_dims, output_dim)
        elif self.config.algorithm == "ProtoNet":
            self.model = PrototypicalNetwork(input_dim, hidden_dims, hidden_dims[-1])
        elif self.config.algorithm == "MatchingNet":
            self.model = MatchingNetwork(input_dim, hidden_dims, hidden_dims[-1])
        else:
            raise ValueError(f"Unsupported algorithm: {self.config.algorithm}")
        
        self.model.to(self.device)
        self.meta_optimizer = optim.Adam(self.model.parameters(), lr=self.config.outer_lr)
        
        logger.info(f"Model setup complete for {self.config.algorithm}")
    
    def meta_train_step(self, tasks: List[Dict[str, torch.Tensor]]) -> Dict[str, float]:
        """Perform one meta-training step."""
        if self.config.algorithm == "MAML":
            return self._maml_train_step(tasks)
        elif self.config.algorithm == "ProtoNet":
            return self._prototypical_train_step(tasks)
        elif self.config.algorithm == "MatchingNet":
            return self._matching_train_step(tasks)
        else:
            raise ValueError(f"Unsupported algorithm: {self.config.algorithm}")
    
    def _maml_train_step(self, tasks: List[Dict[str, torch.Tensor]]) -> Dict[str, float]:
        """MAML training step."""
        meta_loss = 0.0
        meta_accuracy = 0.0
        
        self.meta_optimizer.zero_grad()
        
        for task in tasks:
            # Get task data
            support_x = task['support_x'].to(self.device)
            support_y = task['support_y'].to(self.device)
            query_x = task['query_x'].to(self.device)
            query_y = task['query_y'].to(self.device)
            
            # Clone model for inner loop
            fast_model = self.model.clone()
            fast_optimizer = optim.SGD(fast_model.parameters(), lr=self.config.inner_lr)
            
            # Inner loop adaptation
            for _ in range(self.config.inner_steps):
                support_pred = fast_model(support_x)
                support_loss = F.cross_entropy(support_pred, support_y)
                
                fast_optimizer.zero_grad()
                support_loss.backward()
                fast_optimizer.step()
            
            # Compute query loss
            query_pred = fast_model(query_x)
            query_loss = F.cross_entropy(query_pred, query_y)
            
            # Accumulate meta loss
            meta_loss += query_loss
            
            # Compute accuracy
            with torch.no_grad():
                pred_labels = torch.argmax(query_pred, dim=1)
                accuracy = (pred_labels == query_y).float().mean()
                meta_accuracy += accuracy
        
        # Average over tasks
        meta_loss /= len(tasks)
        meta_accuracy /= len(tasks)
        
        # Meta update
        meta_loss.backward()
        self.meta_optimizer.step()
        
        return {
            'meta_loss': meta_loss.item(),
            'meta_accuracy': meta_accuracy.item()
        }
    
    def _prototypical_train_step(self, tasks: List[Dict[str, torch.Tensor]]) -> Dict[str, float]:
        """Prototypical Networks training step."""
        total_loss = 0.0
        total_accuracy = 0.0
        
        self.meta_optimizer.zero_grad()
        
        for task in tasks:
            # Get task data
            support_x = task['support_x'].to(self.device)
            support_y = task['support_y'].to(self.device)
            query_x = task['query_x'].to(self.device)
            query_y = task['query_y'].to(self.device)
            
            # Encode support and query sets
            support_embeddings = self.model(support_x)
            query_embeddings = self.model(query_x)
            
            # Compute prototypes
            prototypes = self.model.compute_prototypes(support_embeddings, support_y)
            
            # Classify queries
            query_probs = self.model.classify(query_embeddings, prototypes, self.config.temperature)
            
            # Compute loss
            loss = F.cross_entropy(query_probs, query_y)
            total_loss += loss
            
            # Compute accuracy
            with torch.no_grad():
                pred_labels = torch.argmax(query_probs, dim=1)
                accuracy = (pred_labels == query_y).float().mean()
                total_accuracy += accuracy
        
        # Average over tasks
        total_loss /= len(tasks)
        total_accuracy /= len(tasks)
        
        # Update model
        total_loss.backward()
        self.meta_optimizer.step()
        
        return {
            'meta_loss': total_loss.item(),
            'meta_accuracy': total_accuracy.item()
        }
    
    def _matching_train_step(self, tasks: List[Dict[str, torch.Tensor]]) -> Dict[str, float]:
        """Matching Networks training step."""
        total_loss = 0.0
        total_accuracy = 0.0
        
        self.meta_optimizer.zero_grad()
        
        for task in tasks:
            # Get task data
            support_x = task['support_x'].to(self.device)
            support_y = task['support_y'].to(self.device)
            query_x = task['query_x'].to(self.device)
            query_y = task['query_y'].to(self.device)
            
            # Forward pass
            query_probs = self.model(support_x, support_y, query_x)
            
            # Compute loss
            loss = F.cross_entropy(query_probs, query_y)
            total_loss += loss
            
            # Compute accuracy
            with torch.no_grad():
                pred_labels = torch.argmax(query_probs, dim=1)
                accuracy = (pred_labels == query_y).float().mean()
                total_accuracy += accuracy
        
        # Average over tasks
        total_loss /= len(tasks)
        total_accuracy /= len(tasks)
        
        # Update model
        total_loss.backward()
        self.meta_optimizer.step()
        
        return {
            'meta_loss': total_loss.item(),
            'meta_accuracy': total_accuracy.item()
        }
    
    def adapt_to_task(self, support_x: torch.Tensor, support_y: torch.Tensor, 
                      adaptation_steps: int = None) -> nn.Module:
        """Adapt the model to a new task."""
        adaptation_steps = adaptation_steps or self.config.adaptation_steps
        
        if self.config.algorithm == "MAML":
            # Clone model for adaptation
            adapted_model = self.model.clone()
            optimizer = optim.SGD(adapted_model.parameters(), lr=self.config.inner_lr)
            
            # Adaptation loop
            for _ in range(adaptation_steps):
                pred = adapted_model(support_x)
                loss = F.cross_entropy(pred, support_y)
                
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()
            
            return adapted_model
        
        else:
            # For other algorithms, return the base model
            return self.model
    
    def evaluate_task(self, support_x: torch.Tensor, support_y: torch.Tensor,
                      query_x: torch.Tensor, query_y: torch.Tensor) -> Dict[str, float]:
        """Evaluate on a single task."""
        with torch.no_grad():
            if self.config.algorithm == "MAML":
                # Adapt model
                adapted_model = self.adapt_to_task(support_x, support_y)
                
                # Evaluate on query set
                query_pred = adapted_model(query_x)
                
            elif self.config.algorithm == "ProtoNet":
                # Encode support and query sets
                support_embeddings = self.model(support_x)
                query_embeddings = self.model(query_x)
                
                # Compute prototypes and classify
                prototypes = self.model.compute_prototypes(support_embeddings, support_y)
                query_pred = self.model.classify(query_embeddings, prototypes, self.config.temperature)
                
            elif self.config.algorithm == "MatchingNet":
                query_pred = self.model(support_x, support_y, query_x)
            
            # Compute metrics
            loss = F.cross_entropy(query_pred, query_y)
            pred_labels = torch.argmax(query_pred, dim=1)
            accuracy = (pred_labels == query_y).float().mean()
            
            return {
                'loss': loss.item(),
                'accuracy': accuracy.item()
            }
    
    def save_model(self, path: str):
        """Save the meta-learned model."""
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.meta_optimizer.state_dict(),
            'config': self.config
        }, path)
        logger.info(f"Model saved to {path}")
    
    def load_model(self, path: str):
        """Load a meta-learned model."""
        checkpoint = torch.load(path, map_location=self.device)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.meta_optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        logger.info(f"Model loaded from {path}")
