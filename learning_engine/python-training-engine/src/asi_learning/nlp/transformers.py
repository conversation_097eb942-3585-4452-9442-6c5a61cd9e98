"""
Advanced Transformer Models for ASI Learning Engine.

Provides state-of-the-art transformer architectures for various NLP tasks including:
- Text classification and sentiment analysis
- Named entity recognition
- Question answering
- Text generation and summarization
- Multi-modal text-image understanding
"""

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from transformers import (
    AutoTokenizer, AutoModel, AutoModelForSequenceClassification,
    AutoModelForTokenClassification, AutoModelForQuestionAnswering,
    AutoModelForCausalLM, AutoModelForSeq2SeqLM,
    BertModel, RobertaModel, GPT2LMHeadModel, T5ForConditionalGeneration,
    pipeline, Pipeline
)
from typing import Dict, List, Optional, Any, Union, Tuple
import numpy as np
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)


@dataclass
class TransformerConfig:
    """Configuration for transformer models."""
    model_name: str = "bert-base-uncased"
    task_type: str = "classification"  # classification, ner, qa, generation
    num_labels: int = 2
    max_length: int = 512
    learning_rate: float = 2e-5
    batch_size: int = 16
    num_epochs: int = 3
    warmup_steps: int = 500
    weight_decay: float = 0.01
    dropout_rate: float = 0.1
    use_cuda: bool = True
    mixed_precision: bool = True
    gradient_checkpointing: bool = False


class MultiTaskTransformer(nn.Module):
    """
    Multi-task transformer model that can handle multiple NLP tasks simultaneously.
    """
    
    def __init__(self, config: TransformerConfig, task_configs: Dict[str, Dict]):
        super().__init__()
        self.config = config
        self.task_configs = task_configs
        
        # Load base transformer
        self.backbone = AutoModel.from_pretrained(config.model_name)
        self.hidden_size = self.backbone.config.hidden_size
        
        # Task-specific heads
        self.task_heads = nn.ModuleDict()
        for task_name, task_config in task_configs.items():
            self.task_heads[task_name] = self._create_task_head(
                task_config["type"], 
                task_config.get("num_labels", 2)
            )
        
        # Shared dropout
        self.dropout = nn.Dropout(config.dropout_rate)
        
    def _create_task_head(self, task_type: str, num_labels: int) -> nn.Module:
        """Create task-specific head."""
        if task_type == "classification":
            return nn.Linear(self.hidden_size, num_labels)
        elif task_type == "token_classification":
            return nn.Linear(self.hidden_size, num_labels)
        elif task_type == "regression":
            return nn.Linear(self.hidden_size, 1)
        else:
            raise ValueError(f"Unsupported task type: {task_type}")
    
    def forward(self, input_ids, attention_mask=None, task_name=None, **kwargs):
        """Forward pass for multi-task learning."""
        # Get backbone features
        outputs = self.backbone(
            input_ids=input_ids,
            attention_mask=attention_mask,
            **kwargs
        )
        
        # Apply dropout
        pooled_output = self.dropout(outputs.pooler_output)
        sequence_output = self.dropout(outputs.last_hidden_state)
        
        # Task-specific predictions
        if task_name:
            task_head = self.task_heads[task_name]
            if task_name in ["classification", "regression"]:
                logits = task_head(pooled_output)
            else:  # token classification
                logits = task_head(sequence_output)
            return {"logits": logits, "hidden_states": outputs.hidden_states}
        else:
            # Return all task predictions
            predictions = {}
            for task_name, task_head in self.task_heads.items():
                if self.task_configs[task_name]["type"] in ["classification", "regression"]:
                    predictions[task_name] = task_head(pooled_output)
                else:
                    predictions[task_name] = task_head(sequence_output)
            return predictions


class AdvancedTextGenerator:
    """
    Advanced text generation with multiple strategies and fine-tuning capabilities.
    """
    
    def __init__(self, model_name: str = "gpt2-medium"):
        self.model_name = model_name
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        self.model = AutoModelForCausalLM.from_pretrained(model_name)
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model.to(self.device)
        
        # Add padding token if not present
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
    
    def generate_text(
        self, 
        prompt: str, 
        max_length: int = 100,
        temperature: float = 0.8,
        top_k: int = 50,
        top_p: float = 0.9,
        num_return_sequences: int = 1,
        do_sample: bool = True
    ) -> List[str]:
        """Generate text with advanced sampling strategies."""
        try:
            # Tokenize input
            inputs = self.tokenizer.encode(prompt, return_tensors='pt').to(self.device)
            
            # Generate
            with torch.no_grad():
                outputs = self.model.generate(
                    inputs,
                    max_length=max_length,
                    temperature=temperature,
                    top_k=top_k,
                    top_p=top_p,
                    num_return_sequences=num_return_sequences,
                    do_sample=do_sample,
                    pad_token_id=self.tokenizer.eos_token_id,
                    eos_token_id=self.tokenizer.eos_token_id
                )
            
            # Decode outputs
            generated_texts = []
            for output in outputs:
                text = self.tokenizer.decode(output, skip_special_tokens=True)
                # Remove the original prompt
                generated_text = text[len(prompt):].strip()
                generated_texts.append(generated_text)
            
            return generated_texts
            
        except Exception as e:
            logger.error(f"Text generation failed: {e}")
            return []
    
    def fine_tune(self, texts: List[str], epochs: int = 3, learning_rate: float = 5e-5):
        """Fine-tune the model on custom text data."""
        from torch.utils.data import Dataset, DataLoader
        from transformers import AdamW, get_linear_schedule_with_warmup
        
        class TextDataset(Dataset):
            def __init__(self, texts, tokenizer, max_length=512):
                self.texts = texts
                self.tokenizer = tokenizer
                self.max_length = max_length
            
            def __len__(self):
                return len(self.texts)
            
            def __getitem__(self, idx):
                text = self.texts[idx]
                encoding = self.tokenizer(
                    text,
                    truncation=True,
                    padding='max_length',
                    max_length=self.max_length,
                    return_tensors='pt'
                )
                return {
                    'input_ids': encoding['input_ids'].flatten(),
                    'attention_mask': encoding['attention_mask'].flatten(),
                    'labels': encoding['input_ids'].flatten()
                }
        
        # Create dataset and dataloader
        dataset = TextDataset(texts, self.tokenizer)
        dataloader = DataLoader(dataset, batch_size=4, shuffle=True)
        
        # Setup optimizer and scheduler
        optimizer = AdamW(self.model.parameters(), lr=learning_rate)
        total_steps = len(dataloader) * epochs
        scheduler = get_linear_schedule_with_warmup(
            optimizer, num_warmup_steps=0, num_training_steps=total_steps
        )
        
        # Training loop
        self.model.train()
        for epoch in range(epochs):
            total_loss = 0
            for batch in dataloader:
                # Move to device
                input_ids = batch['input_ids'].to(self.device)
                attention_mask = batch['attention_mask'].to(self.device)
                labels = batch['labels'].to(self.device)
                
                # Forward pass
                outputs = self.model(
                    input_ids=input_ids,
                    attention_mask=attention_mask,
                    labels=labels
                )
                loss = outputs.loss
                
                # Backward pass
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()
                scheduler.step()
                
                total_loss += loss.item()
            
            avg_loss = total_loss / len(dataloader)
            logger.info(f"Epoch {epoch + 1}/{epochs}, Average Loss: {avg_loss:.4f}")


class SemanticSearchEngine:
    """
    Semantic search engine using transformer embeddings.
    """
    
    def __init__(self, model_name: str = "sentence-transformers/all-MiniLM-L6-v2"):
        from sentence_transformers import SentenceTransformer
        self.model = SentenceTransformer(model_name)
        self.document_embeddings = None
        self.documents = []
    
    def index_documents(self, documents: List[str]) -> None:
        """Index documents for semantic search."""
        self.documents = documents
        self.document_embeddings = self.model.encode(documents)
        logger.info(f"Indexed {len(documents)} documents")
    
    def search(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """Search for semantically similar documents."""
        if self.document_embeddings is None:
            raise ValueError("No documents indexed. Call index_documents first.")
        
        # Encode query
        query_embedding = self.model.encode([query])
        
        # Calculate similarities
        from sklearn.metrics.pairwise import cosine_similarity
        similarities = cosine_similarity(query_embedding, self.document_embeddings)[0]
        
        # Get top-k results
        top_indices = np.argsort(similarities)[::-1][:top_k]
        
        results = []
        for idx in top_indices:
            results.append({
                'document': self.documents[idx],
                'similarity': float(similarities[idx]),
                'index': int(idx)
            })
        
        return results


class NLPPipelineManager:
    """
    Manager for multiple NLP pipelines and models.
    """
    
    def __init__(self):
        self.pipelines = {}
        self.models = {}
    
    def load_pipeline(self, task: str, model_name: str = None) -> Pipeline:
        """Load a Hugging Face pipeline for a specific task."""
        if task in self.pipelines:
            return self.pipelines[task]
        
        try:
            if model_name:
                pipe = pipeline(task, model=model_name)
            else:
                pipe = pipeline(task)
            
            self.pipelines[task] = pipe
            logger.info(f"Loaded pipeline for task: {task}")
            return pipe
            
        except Exception as e:
            logger.error(f"Failed to load pipeline for {task}: {e}")
            raise
    
    def sentiment_analysis(self, texts: List[str]) -> List[Dict[str, Any]]:
        """Perform sentiment analysis."""
        pipe = self.load_pipeline("sentiment-analysis")
        return pipe(texts)
    
    def named_entity_recognition(self, texts: List[str]) -> List[List[Dict[str, Any]]]:
        """Perform named entity recognition."""
        pipe = self.load_pipeline("ner", "dbmdz/bert-large-cased-finetuned-conll03-english")
        return [pipe(text) for text in texts]
    
    def question_answering(self, questions: List[str], contexts: List[str]) -> List[Dict[str, Any]]:
        """Perform question answering."""
        pipe = self.load_pipeline("question-answering")
        results = []
        for question, context in zip(questions, contexts):
            result = pipe(question=question, context=context)
            results.append(result)
        return results
    
    def text_summarization(self, texts: List[str], max_length: int = 150) -> List[str]:
        """Perform text summarization."""
        pipe = self.load_pipeline("summarization")
        results = pipe(texts, max_length=max_length, min_length=30, do_sample=False)
        return [result['summary_text'] for result in results]
    
    def text_classification(self, texts: List[str], labels: List[str]) -> List[Dict[str, Any]]:
        """Perform zero-shot text classification."""
        pipe = self.load_pipeline("zero-shot-classification")
        results = []
        for text in texts:
            result = pipe(text, labels)
            results.append(result)
        return results


# Factory function for creating transformer models
def create_transformer_model(config: TransformerConfig) -> Union[nn.Module, Pipeline]:
    """Factory function to create transformer models based on configuration."""
    if config.task_type == "classification":
        return AutoModelForSequenceClassification.from_pretrained(
            config.model_name, 
            num_labels=config.num_labels
        )
    elif config.task_type == "ner":
        return AutoModelForTokenClassification.from_pretrained(
            config.model_name,
            num_labels=config.num_labels
        )
    elif config.task_type == "qa":
        return AutoModelForQuestionAnswering.from_pretrained(config.model_name)
    elif config.task_type == "generation":
        return AutoModelForCausalLM.from_pretrained(config.model_name)
    else:
        raise ValueError(f"Unsupported task type: {config.task_type}")
