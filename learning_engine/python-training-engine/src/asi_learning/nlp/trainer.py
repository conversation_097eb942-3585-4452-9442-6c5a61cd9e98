"""
NLP Trainer for the ASI Learning Engine.

Provides comprehensive NLP model training with Hugging Face transformers,
including text classification, named entity recognition, question answering,
and text generation models.
"""

import os
import json
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable, Union
import asyncio

import torch
import torch.nn as nn
from torch.utils.data import DataLoader, Dataset
from transformers import (
    AutoTokenizer, AutoModel, AutoModelForSequenceClassification,
    AutoModelForTokenClassification, AutoModelForQuestionAnswering,
    AutoModelForCausalLM, TrainingArguments, Trainer,
    DataCollatorWithPadding, EarlyStoppingCallback
)
from datasets import Dataset as HFDataset
import numpy as np
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, confusion_matrix

from ..utils.logger import get_logger, TrainingLogger
from ..utils.config import NLPConfig
from ..utils.metrics import MetricsCollector

logger = get_logger(__name__)


class NLPDataset(Dataset):
    """Custom dataset for NLP tasks."""
    
    def __init__(self, texts: List[str], labels: List[int] = None, tokenizer=None, max_length: int = 512):
        self.texts = texts
        self.labels = labels
        self.tokenizer = tokenizer
        self.max_length = max_length
    
    def __len__(self):
        return len(self.texts)
    
    def __getitem__(self, idx):
        text = str(self.texts[idx])
        
        # Tokenize text
        encoding = self.tokenizer(
            text,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        item = {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
        }
        
        if self.labels is not None:
            item['labels'] = torch.tensor(self.labels[idx], dtype=torch.long)
        
        return item


class NLPTrainer:
    """
    Advanced NLP trainer for the ASI Learning Engine.
    
    Supports:
    - Multiple NLP tasks (classification, NER, QA, generation)
    - Hugging Face transformers integration
    - Distributed training
    - Custom evaluation metrics
    - Model checkpointing and versioning
    """
    
    def __init__(self, config: NLPConfig):
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.tokenizer = None
        self.model = None
        self.trainer = None
        self.training_logger = None
        
        logger.info(f"NLP Trainer initialized with device: {self.device}")
    
    def setup_model(self, task_type: str = "classification", num_labels: int = None) -> None:
        """Setup model and tokenizer for specific task."""
        try:
            # Load tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(self.config.model_name)
            
            # Add padding token if not present
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # Load model based on task type
            if task_type == "classification":
                self.model = AutoModelForSequenceClassification.from_pretrained(
                    self.config.model_name,
                    num_labels=num_labels or self.config.num_labels,
                    dropout=self.config.dropout_rate
                )
            elif task_type == "token_classification":
                self.model = AutoModelForTokenClassification.from_pretrained(
                    self.config.model_name,
                    num_labels=num_labels or self.config.num_labels,
                    dropout=self.config.dropout_rate
                )
            elif task_type == "question_answering":
                self.model = AutoModelForQuestionAnswering.from_pretrained(
                    self.config.model_name,
                    dropout=self.config.dropout_rate
                )
            elif task_type == "generation":
                self.model = AutoModelForCausalLM.from_pretrained(
                    self.config.model_name,
                    dropout=self.config.dropout_rate
                )
            else:
                raise ValueError(f"Unsupported task type: {task_type}")
            
            # Move model to device
            self.model.to(self.device)
            
            logger.info(f"Model setup completed for task: {task_type}")
            
        except Exception as e:
            logger.error(f"Failed to setup model: {e}")
            raise
    
    def prepare_data(self, data: Dict[str, Any]) -> tuple:
        """Prepare training and validation datasets."""
        try:
            # Extract data
            train_texts = data.get('train_texts', [])
            train_labels = data.get('train_labels', [])
            val_texts = data.get('val_texts', [])
            val_labels = data.get('val_labels', [])
            
            # Create datasets
            train_dataset = NLPDataset(
                texts=train_texts,
                labels=train_labels,
                tokenizer=self.tokenizer,
                max_length=self.config.max_sequence_length
            )
            
            val_dataset = None
            if val_texts:
                val_dataset = NLPDataset(
                    texts=val_texts,
                    labels=val_labels,
                    tokenizer=self.tokenizer,
                    max_length=self.config.max_sequence_length
                )
            
            logger.info(f"Prepared datasets - Train: {len(train_dataset)}, Val: {len(val_dataset) if val_dataset else 0}")
            
            return train_dataset, val_dataset
            
        except Exception as e:
            logger.error(f"Failed to prepare data: {e}")
            raise
    
    def compute_metrics(self, eval_pred) -> Dict[str, float]:
        """Compute evaluation metrics."""
        predictions, labels = eval_pred
        predictions = np.argmax(predictions, axis=1)
        
        # Calculate metrics
        accuracy = accuracy_score(labels, predictions)
        precision, recall, f1, _ = precision_recall_fscore_support(labels, predictions, average='weighted')
        
        return {
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1': f1,
        }
    
    def train(
        self,
        config: Dict[str, Any],
        hooks: Dict[str, Callable] = None,
        job_id: str = None
    ) -> Dict[str, Any]:
        """Train the NLP model."""
        try:
            # Setup training logger
            if job_id:
                self.training_logger = TrainingLogger(job_id, "nlp")
            
            # Setup model
            task_type = config.get('task_type', 'classification')
            num_labels = config.get('num_labels', self.config.num_labels)
            self.setup_model(task_type, num_labels)
            
            # Prepare data
            train_dataset, val_dataset = self.prepare_data(config['data'])
            
            # Setup training arguments
            training_args = TrainingArguments(
                output_dir=config.get('output_dir', './results'),
                num_train_epochs=config.get('epochs', self.config.training.epochs),
                per_device_train_batch_size=config.get('batch_size', self.config.training.batch_size),
                per_device_eval_batch_size=config.get('eval_batch_size', self.config.training.batch_size),
                learning_rate=config.get('learning_rate', self.config.training.learning_rate),
                weight_decay=self.config.weight_decay,
                warmup_steps=self.config.warmup_steps,
                logging_dir=config.get('logging_dir', './logs'),
                logging_steps=100,
                evaluation_strategy="steps" if val_dataset else "no",
                eval_steps=500 if val_dataset else None,
                save_strategy="steps",
                save_steps=1000,
                load_best_model_at_end=True if val_dataset else False,
                metric_for_best_model="f1" if val_dataset else None,
                greater_is_better=True,
                fp16=self.config.training.mixed_precision,
                dataloader_num_workers=4,
                remove_unused_columns=False,
                report_to=None,  # Disable default reporting
            )
            
            # Setup data collator
            data_collator = DataCollatorWithPadding(tokenizer=self.tokenizer)
            
            # Setup callbacks
            callbacks = []
            if val_dataset and config.get('early_stopping', True):
                callbacks.append(EarlyStoppingCallback(
                    early_stopping_patience=self.config.training.early_stopping_patience
                ))
            
            # Create trainer
            self.trainer = Trainer(
                model=self.model,
                args=training_args,
                train_dataset=train_dataset,
                eval_dataset=val_dataset,
                tokenizer=self.tokenizer,
                data_collator=data_collator,
                compute_metrics=self.compute_metrics if val_dataset else None,
                callbacks=callbacks,
            )
            
            # Add custom callbacks for hooks
            if hooks:
                self._add_training_hooks(hooks)
            
            # Start training
            if self.training_logger:
                self.training_logger.log_epoch_start(0, training_args.num_train_epochs)
            
            train_result = self.trainer.train()
            
            # Save model
            model_path = Path(config.get('model_save_path', './models/nlp_model'))
            model_path.mkdir(parents=True, exist_ok=True)
            self.trainer.save_model(str(model_path))
            
            if self.training_logger:
                self.training_logger.log_model_save(str(model_path), training_args.num_train_epochs)
            
            # Evaluate on test set if provided
            test_results = {}
            if 'test_texts' in config['data']:
                test_results = self.evaluate(config['data'])
            
            results = {
                'train_loss': train_result.training_loss,
                'train_runtime': train_result.metrics['train_runtime'],
                'train_samples_per_second': train_result.metrics['train_samples_per_second'],
                'model_path': str(model_path),
                **test_results
            }
            
            logger.info(f"Training completed successfully: {results}")
            return results
            
        except Exception as e:
            if self.training_logger:
                self.training_logger.log_error(e, "training")
            logger.error(f"Training failed: {e}")
            raise
    
    def evaluate(self, data: Dict[str, Any]) -> Dict[str, float]:
        """Evaluate the model on test data."""
        try:
            test_texts = data.get('test_texts', [])
            test_labels = data.get('test_labels', [])
            
            if not test_texts:
                return {}
            
            # Create test dataset
            test_dataset = NLPDataset(
                texts=test_texts,
                labels=test_labels,
                tokenizer=self.tokenizer,
                max_length=self.config.max_sequence_length
            )
            
            # Evaluate
            eval_results = self.trainer.evaluate(test_dataset)
            
            # Extract metrics
            metrics = {
                'test_loss': eval_results['eval_loss'],
                'test_accuracy': eval_results.get('eval_accuracy', 0),
                'test_precision': eval_results.get('eval_precision', 0),
                'test_recall': eval_results.get('eval_recall', 0),
                'test_f1': eval_results.get('eval_f1', 0),
            }
            
            if self.training_logger:
                self.training_logger.log_validation(metrics)
            
            return metrics
            
        except Exception as e:
            logger.error(f"Evaluation failed: {e}")
            return {}
    
    def predict(self, texts: List[str]) -> List[Dict[str, Any]]:
        """Make predictions on new texts."""
        try:
            if not self.model or not self.tokenizer:
                raise ValueError("Model not loaded. Call setup_model() first.")
            
            predictions = []
            
            for text in texts:
                # Tokenize
                inputs = self.tokenizer(
                    text,
                    truncation=True,
                    padding=True,
                    max_length=self.config.max_sequence_length,
                    return_tensors='pt'
                ).to(self.device)
                
                # Predict
                with torch.no_grad():
                    outputs = self.model(**inputs)
                    logits = outputs.logits
                    probabilities = torch.softmax(logits, dim=-1)
                    predicted_class = torch.argmax(logits, dim=-1).item()
                    confidence = probabilities[0][predicted_class].item()
                
                predictions.append({
                    'text': text,
                    'predicted_class': predicted_class,
                    'confidence': confidence,
                    'probabilities': probabilities[0].cpu().numpy().tolist()
                })
            
            return predictions
            
        except Exception as e:
            logger.error(f"Prediction failed: {e}")
            return []
    
    async def process_streaming_data(self, data: Dict[str, Any]) -> None:
        """Process streaming data for continuous learning."""
        try:
            # Extract text and label from streaming data
            text = data.get('text', '')
            label = data.get('label')
            
            if not text:
                return
            
            # For now, just log the data
            # In a full implementation, this would update the model
            logger.debug(f"Received streaming data: {text[:100]}...")
            
        except Exception as e:
            logger.error(f"Failed to process streaming data: {e}")
    
    async def health_check(self) -> bool:
        """Check if the trainer is healthy."""
        try:
            # Check if model and tokenizer are loaded
            if self.model is None or self.tokenizer is None:
                return False
            
            # Check if model is on correct device
            if next(self.model.parameters()).device != self.device:
                return False
            
            # Try a simple forward pass
            dummy_input = torch.randint(0, 1000, (1, 10)).to(self.device)
            with torch.no_grad():
                _ = self.model(dummy_input)
            
            return True
            
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return False
    
    def _add_training_hooks(self, hooks: Dict[str, Callable]) -> None:
        """Add custom training hooks."""
        # This would integrate with the Trainer's callback system
        # For now, we'll implement basic hook support
        pass
    
    def save_model(self, path: str) -> None:
        """Save the trained model."""
        if self.trainer:
            self.trainer.save_model(path)
            logger.info(f"Model saved to {path}")
    
    def load_model(self, path: str, task_type: str = "classification") -> None:
        """Load a trained model."""
        try:
            self.tokenizer = AutoTokenizer.from_pretrained(path)
            
            if task_type == "classification":
                self.model = AutoModelForSequenceClassification.from_pretrained(path)
            elif task_type == "token_classification":
                self.model = AutoModelForTokenClassification.from_pretrained(path)
            elif task_type == "question_answering":
                self.model = AutoModelForQuestionAnswering.from_pretrained(path)
            elif task_type == "generation":
                self.model = AutoModelForCausalLM.from_pretrained(path)
            
            self.model.to(self.device)
            logger.info(f"Model loaded from {path}")
            
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            raise
