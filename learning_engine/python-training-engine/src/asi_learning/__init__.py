"""
ASI Learning Engine

Multi-modal AI/ML learning platform for the Artificial Super Intelligence (ASI) System.
Provides NLP transformers, computer vision CNNs, reinforcement learning agents, 
and abstract reasoning models with production-ready training and inference capabilities.

Modules:
    nlp: Natural Language Processing with transformers
    vision: Computer Vision with CNNs and object detection
    rl: Reinforcement Learning agents
    abstraction: Abstract reasoning and meta-learning
    training: Training orchestration and management
    evaluation: Model evaluation and metrics
    utils: Shared utilities and helpers
"""

__version__ = "1.0.0"
__author__ = "ASI System Team"
__email__ = "<EMAIL>"

from .utils.logger import get_logger
from .utils.config import Config
from .utils.metrics import MetricsCollector

# Initialize global logger
logger = get_logger(__name__)

# Package-level exports
__all__ = [
    "logger",
    "Config",
    "MetricsCollector",
]
