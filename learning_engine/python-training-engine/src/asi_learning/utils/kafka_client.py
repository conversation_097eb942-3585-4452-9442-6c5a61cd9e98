"""
Kafka client for the ASI Learning Engine.

Provides high-performance Kafka integration for streaming data consumption
and model update publishing with automatic reconnection and error handling.
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
import time

from kafka import <PERSON>fkaConsumer as SyncKafkaConsumer, KafkaProducer
from kafka.errors import KafkaError, KafkaTimeoutError
import aiokafka

from .logger import get_logger
from .config import KafkaConfig

logger = get_logger(__name__)


@dataclass
class KafkaMessage:
    """Container for Kafka message with metadata."""
    topic: str
    partition: int
    offset: int
    key: Optional[str]
    value: Dict[str, Any]
    timestamp: int
    headers: Dict[str, str]


class KafkaConsumer:
    """
    Asynchronous Kafka consumer for the ASI Learning Engine.
    
    Features:
    - Async/await support
    - Automatic reconnection
    - Message batching
    - Error handling and retry logic
    - Metrics collection
    """
    
    def __init__(self, config: KafkaConfig):
        self.config = config
        self.consumer = None
        self.running = False
        self.message_handlers = {}
        self.error_count = 0
        self.last_error_time = 0
        
    async def start(self) -> None:
        """Start the Kafka consumer."""
        try:
            self.consumer = aiokafka.AIOKafkaConsumer(
                *self.config.training_topics,
                bootstrap_servers=self.config.bootstrap_servers,
                group_id=self.config.consumer_group,
                auto_offset_reset=self.config.auto_offset_reset,
                enable_auto_commit=self.config.enable_auto_commit,
                value_deserializer=self._deserialize_message,
                key_deserializer=lambda x: x.decode('utf-8') if x else None,
            )
            
            await self.consumer.start()
            self.running = True
            logger.info(f"Kafka consumer started for topics: {self.config.training_topics}")
            
        except Exception as e:
            logger.error(f"Failed to start Kafka consumer: {e}")
            raise
    
    async def stop(self) -> None:
        """Stop the Kafka consumer."""
        self.running = False
        if self.consumer:
            await self.consumer.stop()
            logger.info("Kafka consumer stopped")
    
    async def consume_batch(
        self,
        topics: List[str] = None,
        timeout: float = 1.0,
        max_records: int = 100
    ) -> List[KafkaMessage]:
        """Consume a batch of messages."""
        if not self.running or not self.consumer:
            return []
        
        try:
            messages = []
            start_time = time.time()
            
            while len(messages) < max_records and (time.time() - start_time) < timeout:
                try:
                    msg_batch = await asyncio.wait_for(
                        self.consumer.getmany(timeout_ms=int(timeout * 1000)),
                        timeout=timeout
                    )
                    
                    for topic_partition, msgs in msg_batch.items():
                        for msg in msgs:
                            if not topics or msg.topic in topics:
                                kafka_msg = KafkaMessage(
                                    topic=msg.topic,
                                    partition=msg.partition,
                                    offset=msg.offset,
                                    key=msg.key,
                                    value=msg.value,
                                    timestamp=msg.timestamp,
                                    headers={k: v.decode('utf-8') for k, v in msg.headers}
                                )
                                messages.append(kafka_msg)
                    
                    if not msg_batch:
                        break
                        
                except asyncio.TimeoutError:
                    break
            
            return messages
            
        except Exception as e:
            self._handle_error(e)
            return []
    
    async def consume_stream(
        self,
        message_handler: Callable[[KafkaMessage], None],
        topics: List[str] = None
    ) -> None:
        """Consume messages in streaming mode."""
        if not self.running or not self.consumer:
            return
        
        try:
            async for msg in self.consumer:
                if not topics or msg.topic in topics:
                    kafka_msg = KafkaMessage(
                        topic=msg.topic,
                        partition=msg.partition,
                        offset=msg.offset,
                        key=msg.key,
                        value=msg.value,
                        timestamp=msg.timestamp,
                        headers={k: v.decode('utf-8') for k, v in msg.headers}
                    )
                    
                    try:
                        await message_handler(kafka_msg)
                    except Exception as e:
                        logger.error(f"Error in message handler: {e}")
                
                if not self.running:
                    break
                    
        except Exception as e:
            self._handle_error(e)
    
    def _deserialize_message(self, raw_value: bytes) -> Dict[str, Any]:
        """Deserialize message value."""
        try:
            if raw_value is None:
                return {}
            return json.loads(raw_value.decode('utf-8'))
        except Exception as e:
            logger.error(f"Failed to deserialize message: {e}")
            return {}
    
    def _handle_error(self, error: Exception) -> None:
        """Handle consumer errors with exponential backoff."""
        self.error_count += 1
        current_time = time.time()
        
        # Log error with rate limiting
        if current_time - self.last_error_time > 60:  # Log at most once per minute
            logger.error(f"Kafka consumer error (count: {self.error_count}): {error}")
            self.last_error_time = current_time
        
        # Reset error count after successful period
        if current_time - self.last_error_time > 300:  # 5 minutes
            self.error_count = 0
    
    async def close(self) -> None:
        """Close the consumer connection."""
        await self.stop()


class KafkaProducerClient:
    """
    Kafka producer for publishing model updates and training events.
    
    Features:
    - Async message publishing
    - Automatic retries
    - Message compression
    - Delivery confirmation
    """
    
    def __init__(self, config: KafkaConfig):
        self.config = config
        self.producer = None
        
    async def start(self) -> None:
        """Start the Kafka producer."""
        try:
            self.producer = aiokafka.AIOKafkaProducer(
                bootstrap_servers=self.config.bootstrap_servers,
                value_serializer=self._serialize_message,
                key_serializer=lambda x: x.encode('utf-8') if x else None,
                compression_type='gzip',
                acks='all',
                retries=3,
                retry_backoff_ms=1000,
            )
            
            await self.producer.start()
            logger.info("Kafka producer started")
            
        except Exception as e:
            logger.error(f"Failed to start Kafka producer: {e}")
            raise
    
    async def stop(self) -> None:
        """Stop the Kafka producer."""
        if self.producer:
            await self.producer.stop()
            logger.info("Kafka producer stopped")
    
    async def publish_message(
        self,
        topic: str,
        message: Dict[str, Any],
        key: Optional[str] = None,
        headers: Dict[str, str] = None
    ) -> bool:
        """Publish a message to Kafka."""
        if not self.producer:
            logger.error("Producer not started")
            return False
        
        try:
            # Add timestamp if not present
            if 'timestamp' not in message:
                message['timestamp'] = int(time.time() * 1000)
            
            # Convert headers to bytes
            kafka_headers = []
            if headers:
                kafka_headers = [(k, v.encode('utf-8')) for k, v in headers.items()]
            
            # Send message
            await self.producer.send_and_wait(
                topic=topic,
                value=message,
                key=key,
                headers=kafka_headers
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to publish message: {e}")
            return False
    
    async def publish_training_event(
        self,
        job_id: str,
        model_type: str,
        event_type: str,
        data: Dict[str, Any]
    ) -> bool:
        """Publish a training event."""
        message = {
            'job_id': job_id,
            'model_type': model_type,
            'event_type': event_type,
            'data': data,
            'source': 'asi-learning-engine'
        }
        
        headers = {
            'job_id': job_id,
            'model_type': model_type,
            'event_type': event_type
        }
        
        return await self.publish_message(
            topic='training-events',
            message=message,
            key=job_id,
            headers=headers
        )
    
    def _serialize_message(self, message: Dict[str, Any]) -> bytes:
        """Serialize message to JSON bytes."""
        try:
            return json.dumps(message, default=str).encode('utf-8')
        except Exception as e:
            logger.error(f"Failed to serialize message: {e}")
            return b'{}'
