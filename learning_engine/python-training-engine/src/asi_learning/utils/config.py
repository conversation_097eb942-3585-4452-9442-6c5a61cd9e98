"""
Configuration management for the ASI Learning Engine.

Provides hierarchical configuration loading, validation, and environment-specific overrides.
Supports YAML, JSON, and environment variable configuration sources.
"""

import os
from pathlib import Path
from typing import Any, Dict, Optional, Union
import yaml
import json
from dataclasses import dataclass, field
from omegaconf import DictConfig, OmegaConf

from .logger import get_logger

logger = get_logger(__name__)


@dataclass
class KafkaConfig:
    """Kafka configuration."""
    bootstrap_servers: str = "localhost:9092"
    training_topics: list = field(default_factory=lambda: ["asi-learning-engine"])
    consumer_group: str = "asi-learning-engine"
    auto_offset_reset: str = "latest"
    enable_auto_commit: bool = False
    max_poll_records: int = 500
    session_timeout_ms: int = 30000


@dataclass
class DataIntegrationConfig:
    """Data integration service configuration."""
    endpoint: str = "localhost:50055"
    timeout: int = 30
    max_retries: int = 3
    retry_delay: float = 1.0


@dataclass
class MonitoringConfig:
    """Monitoring and metrics configuration."""
    enabled: bool = True
    prometheus_port: int = 8090
    report_interval: int = 30
    mlflow_tracking_uri: str = "http://localhost:5000"
    tensorboard_log_dir: str = "logs/tensorboard"


@dataclass
class TrainingConfig:
    """Base training configuration."""
    batch_size: int = 32
    epochs: int = 100
    learning_rate: float = 0.001
    optimizer: str = "adam"
    scheduler: str = "cosine"
    gradient_clip_norm: float = 1.0
    mixed_precision: bool = True
    checkpoint_frequency: int = 10
    validation_frequency: int = 5
    early_stopping_patience: int = 10


@dataclass
class NLPConfig:
    """NLP-specific configuration."""
    model_name: str = "bert-base-uncased"
    max_sequence_length: int = 512
    num_labels: int = 2
    dropout_rate: float = 0.1
    warmup_steps: int = 1000
    weight_decay: float = 0.01
    training: TrainingConfig = field(default_factory=TrainingConfig)


@dataclass
class VisionConfig:
    """Vision-specific configuration."""
    model_name: str = "resnet50"
    image_size: int = 224
    num_classes: int = 1000
    augmentation_strength: float = 0.5
    pretrained: bool = True
    freeze_backbone: bool = False
    training: TrainingConfig = field(default_factory=TrainingConfig)


@dataclass
class RLConfig:
    """Reinforcement Learning configuration."""
    algorithm: str = "PPO"
    environment: str = "CartPole-v1"
    total_timesteps: int = 1000000
    learning_rate: float = 0.0003
    n_steps: int = 2048
    batch_size: int = 64
    n_epochs: int = 10
    gamma: float = 0.99
    gae_lambda: float = 0.95


@dataclass
class AbstractionConfig:
    """Abstraction learning configuration."""
    model_type: str = "transformer"
    hidden_size: int = 512
    num_layers: int = 6
    num_heads: int = 8
    dropout_rate: float = 0.1
    max_context_length: int = 1024
    training: TrainingConfig = field(default_factory=TrainingConfig)


@dataclass
class EvaluationConfig:
    """Model evaluation configuration."""
    metrics: list = field(default_factory=lambda: ["accuracy", "precision", "recall", "f1"])
    test_batch_size: int = 64
    save_predictions: bool = True
    compute_confusion_matrix: bool = True
    benchmark_datasets: list = field(default_factory=list)


@dataclass
class OrchestratorConfig:
    """Orchestrator configuration."""
    max_concurrent_jobs: int = 4
    job_timeout: int = 86400  # 24 hours
    checkpoint_dir: str = "checkpoints"
    model_registry_dir: str = "models"
    temp_dir: str = "/tmp/asi_learning"


@dataclass
class ASILearningConfig:
    """Main configuration class for ASI Learning Engine."""
    orchestrator: OrchestratorConfig = field(default_factory=OrchestratorConfig)
    kafka: KafkaConfig = field(default_factory=KafkaConfig)
    data_integration: DataIntegrationConfig = field(default_factory=DataIntegrationConfig)
    monitoring: MonitoringConfig = field(default_factory=MonitoringConfig)
    nlp: NLPConfig = field(default_factory=NLPConfig)
    vision: VisionConfig = field(default_factory=VisionConfig)
    rl: RLConfig = field(default_factory=RLConfig)
    abstraction: AbstractionConfig = field(default_factory=AbstractionConfig)
    evaluation: EvaluationConfig = field(default_factory=EvaluationConfig)


class Config:
    """
    Configuration manager for the ASI Learning Engine.
    
    Supports:
    - Hierarchical configuration loading
    - Environment variable overrides
    - Configuration validation
    - Multiple file formats (YAML, JSON)
    """
    
    _instance: Optional['Config'] = None
    _config: Optional[DictConfig] = None
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize configuration manager."""
        if config_path:
            self.load_config(config_path)
    
    @classmethod
    def load(cls, config_path: str) -> DictConfig:
        """Load configuration from file."""
        if cls._instance is None:
            cls._instance = cls(config_path)
        return cls._instance._config
    
    def load_config(self, config_path: str) -> None:
        """Load configuration from file with environment overrides."""
        config_file = Path(config_path)
        
        if not config_file.exists():
            logger.warning(f"Config file {config_path} not found, using defaults")
            self._config = OmegaConf.structured(ASILearningConfig())
            return
        
        try:
            # Load base configuration
            if config_file.suffix.lower() == '.yaml' or config_file.suffix.lower() == '.yml':
                with open(config_file, 'r') as f:
                    config_dict = yaml.safe_load(f)
            elif config_file.suffix.lower() == '.json':
                with open(config_file, 'r') as f:
                    config_dict = json.load(f)
            else:
                raise ValueError(f"Unsupported config file format: {config_file.suffix}")
            
            # Create OmegaConf configuration
            self._config = OmegaConf.create(config_dict)
            
            # Merge with structured config for validation
            structured_config = OmegaConf.structured(ASILearningConfig())
            self._config = OmegaConf.merge(structured_config, self._config)
            
            # Apply environment variable overrides
            self._apply_env_overrides()
            
            # Validate configuration
            self._validate_config()
            
            logger.info(f"Configuration loaded from {config_path}")
            
        except Exception as e:
            logger.error(f"Failed to load configuration from {config_path}: {e}")
            # Fall back to default configuration
            self._config = OmegaConf.structured(ASILearningConfig())
    
    def _apply_env_overrides(self) -> None:
        """Apply environment variable overrides."""
        env_mappings = {
            'ASI_KAFKA_BOOTSTRAP_SERVERS': 'kafka.bootstrap_servers',
            'ASI_KAFKA_TRAINING_TOPICS': 'kafka.training_topics',
            'ASI_DATA_INTEGRATION_ENDPOINT': 'data_integration.endpoint',
            'ASI_MONITORING_ENABLED': 'monitoring.enabled',
            'ASI_MONITORING_PROMETHEUS_PORT': 'monitoring.prometheus_port',
            'ASI_MLFLOW_TRACKING_URI': 'monitoring.mlflow_tracking_uri',
            'ASI_MAX_CONCURRENT_JOBS': 'orchestrator.max_concurrent_jobs',
            'ASI_CHECKPOINT_DIR': 'orchestrator.checkpoint_dir',
            'ASI_MODEL_REGISTRY_DIR': 'orchestrator.model_registry_dir',
        }
        
        for env_var, config_path in env_mappings.items():
            env_value = os.getenv(env_var)
            if env_value is not None:
                # Convert string values to appropriate types
                if env_value.lower() in ('true', 'false'):
                    env_value = env_value.lower() == 'true'
                elif env_value.isdigit():
                    env_value = int(env_value)
                elif ',' in env_value:
                    env_value = [item.strip() for item in env_value.split(',')]
                
                OmegaConf.set(self._config, config_path, env_value)
                logger.debug(f"Applied environment override: {env_var} -> {config_path} = {env_value}")
    
    def _validate_config(self) -> None:
        """Validate configuration values."""
        try:
            # Validate required fields
            required_fields = [
                'kafka.bootstrap_servers',
                'data_integration.endpoint',
                'orchestrator.checkpoint_dir',
                'orchestrator.model_registry_dir',
            ]
            
            for field in required_fields:
                if not OmegaConf.select(self._config, field):
                    raise ValueError(f"Required configuration field missing: {field}")
            
            # Validate numeric ranges
            if self._config.orchestrator.max_concurrent_jobs <= 0:
                raise ValueError("max_concurrent_jobs must be positive")
            
            if self._config.monitoring.prometheus_port <= 0 or self._config.monitoring.prometheus_port > 65535:
                raise ValueError("prometheus_port must be between 1 and 65535")
            
            # Create directories if they don't exist
            for dir_path in [
                self._config.orchestrator.checkpoint_dir,
                self._config.orchestrator.model_registry_dir,
                self._config.orchestrator.temp_dir,
                self._config.monitoring.tensorboard_log_dir,
            ]:
                Path(dir_path).mkdir(parents=True, exist_ok=True)
            
            logger.info("Configuration validation passed")
            
        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            raise
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value by key."""
        if self._config is None:
            return default
        return OmegaConf.select(self._config, key, default=default)
    
    def set(self, key: str, value: Any) -> None:
        """Set configuration value by key."""
        if self._config is None:
            self._config = OmegaConf.structured(ASILearningConfig())
        OmegaConf.set(self._config, key, value)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        if self._config is None:
            return {}
        return OmegaConf.to_container(self._config, resolve=True)
    
    def save(self, output_path: str) -> None:
        """Save configuration to file."""
        if self._config is None:
            return
        
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_file, 'w') as f:
            OmegaConf.save(self._config, f)
        
        logger.info(f"Configuration saved to {output_path}")


def load_config(config_path: str) -> DictConfig:
    """Convenience function to load configuration."""
    return Config.load(config_path)
