"""
Logging utilities for the ASI Learning Engine.

Provides structured logging with multiple outputs, log levels, and formatting options.
Integrates with monitoring systems and supports distributed logging.
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Optional, Dict, Any
import json
from datetime import datetime

from rich.logging import RichHandler
from rich.console import Console


class StructuredFormatter(logging.Formatter):
    """Custom formatter for structured JSON logging."""
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as structured JSON."""
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }
        
        # Add extra fields if present
        if hasattr(record, 'job_id'):
            log_entry['job_id'] = record.job_id
        if hasattr(record, 'model_type'):
            log_entry['model_type'] = record.model_type
        if hasattr(record, 'epoch'):
            log_entry['epoch'] = record.epoch
        if hasattr(record, 'batch'):
            log_entry['batch'] = record.batch
        
        # Add exception info if present
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        return json.dumps(log_entry)


class ASILogger:
    """
    Advanced logging system for the ASI Learning Engine.
    
    Features:
    - Multiple output handlers (console, file, remote)
    - Structured JSON logging
    - Rich console output with colors
    - Log rotation and compression
    - Integration with monitoring systems
    """
    
    _loggers: Dict[str, logging.Logger] = {}
    _configured = False
    
    @classmethod
    def configure(
        cls,
        log_level: str = "INFO",
        log_dir: Optional[str] = None,
        enable_console: bool = True,
        enable_file: bool = True,
        enable_structured: bool = True,
        max_file_size: int = 100 * 1024 * 1024,  # 100MB
        backup_count: int = 5,
    ) -> None:
        """Configure global logging settings."""
        if cls._configured:
            return
        
        # Create log directory if specified
        if log_dir:
            Path(log_dir).mkdir(parents=True, exist_ok=True)
        
        # Set global log level
        logging.getLogger().setLevel(getattr(logging, log_level.upper()))
        
        cls._log_level = log_level
        cls._log_dir = log_dir
        cls._enable_console = enable_console
        cls._enable_file = enable_file
        cls._enable_structured = enable_structured
        cls._max_file_size = max_file_size
        cls._backup_count = backup_count
        
        cls._configured = True
    
    @classmethod
    def get_logger(cls, name: str) -> logging.Logger:
        """Get or create a logger with the specified name."""
        if name in cls._loggers:
            return cls._loggers[name]
        
        # Ensure configuration is done
        if not cls._configured:
            cls.configure()
        
        logger = logging.getLogger(name)
        logger.setLevel(getattr(logging, cls._log_level.upper()))
        
        # Clear any existing handlers
        logger.handlers.clear()
        
        # Add console handler
        if cls._enable_console:
            console_handler = RichHandler(
                console=Console(stderr=True),
                show_time=True,
                show_path=True,
                markup=True,
                rich_tracebacks=True,
            )
            console_handler.setLevel(getattr(logging, cls._log_level.upper()))
            
            # Use structured format for console in production
            if cls._enable_structured:
                console_handler.setFormatter(StructuredFormatter())
            else:
                console_handler.setFormatter(
                    logging.Formatter(
                        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
                    )
                )
            
            logger.addHandler(console_handler)
        
        # Add file handler
        if cls._enable_file and cls._log_dir:
            file_path = Path(cls._log_dir) / f"{name.replace('.', '_')}.log"
            
            file_handler = logging.handlers.RotatingFileHandler(
                file_path,
                maxBytes=cls._max_file_size,
                backupCount=cls._backup_count,
                encoding='utf-8'
            )
            file_handler.setLevel(getattr(logging, cls._log_level.upper()))
            file_handler.setFormatter(StructuredFormatter())
            
            logger.addHandler(file_handler)
        
        # Prevent propagation to root logger
        logger.propagate = False
        
        cls._loggers[name] = logger
        return logger


def get_logger(name: str) -> logging.Logger:
    """Convenience function to get a logger."""
    return ASILogger.get_logger(name)


class LoggerMixin:
    """Mixin class to add logging capabilities to any class."""
    
    @property
    def logger(self) -> logging.Logger:
        """Get logger for this class."""
        return get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")


class TrainingLogger:
    """Specialized logger for training processes."""
    
    def __init__(self, job_id: str, model_type: str):
        self.job_id = job_id
        self.model_type = model_type
        self.logger = get_logger(f"training.{model_type}")
    
    def log_epoch_start(self, epoch: int, total_epochs: int) -> None:
        """Log epoch start."""
        self.logger.info(
            f"Starting epoch {epoch}/{total_epochs}",
            extra={"job_id": self.job_id, "model_type": self.model_type, "epoch": epoch}
        )
    
    def log_epoch_end(self, epoch: int, metrics: Dict[str, float]) -> None:
        """Log epoch completion with metrics."""
        metrics_str = ", ".join([f"{k}: {v:.4f}" for k, v in metrics.items()])
        self.logger.info(
            f"Epoch {epoch} completed - {metrics_str}",
            extra={
                "job_id": self.job_id,
                "model_type": self.model_type,
                "epoch": epoch,
                **metrics
            }
        )
    
    def log_batch_metrics(self, batch: int, loss: float, **kwargs) -> None:
        """Log batch-level metrics."""
        self.logger.debug(
            f"Batch {batch} - Loss: {loss:.4f}",
            extra={
                "job_id": self.job_id,
                "model_type": self.model_type,
                "batch": batch,
                "loss": loss,
                **kwargs
            }
        )
    
    def log_validation(self, metrics: Dict[str, float]) -> None:
        """Log validation metrics."""
        metrics_str = ", ".join([f"{k}: {v:.4f}" for k, v in metrics.items()])
        self.logger.info(
            f"Validation metrics - {metrics_str}",
            extra={
                "job_id": self.job_id,
                "model_type": self.model_type,
                "validation": True,
                **metrics
            }
        )
    
    def log_model_save(self, checkpoint_path: str, epoch: int) -> None:
        """Log model checkpoint save."""
        self.logger.info(
            f"Model checkpoint saved: {checkpoint_path}",
            extra={
                "job_id": self.job_id,
                "model_type": self.model_type,
                "epoch": epoch,
                "checkpoint_path": checkpoint_path
            }
        )
    
    def log_error(self, error: Exception, context: str = "") -> None:
        """Log training error."""
        self.logger.error(
            f"Training error{' in ' + context if context else ''}: {str(error)}",
            extra={"job_id": self.job_id, "model_type": self.model_type},
            exc_info=True
        )


# Configure logging on module import
ASILogger.configure(
    log_level="INFO",
    log_dir="logs",
    enable_console=True,
    enable_file=True,
    enable_structured=True,
)
