"""
gRPC client for the ASI Learning Engine.

Provides high-performance gRPC integration for communication with
the Data Integration module and other ASI system components.
"""

import asyncio
import grpc
from typing import Dict, List, Optional, Any, AsyncIterator
import json
import time
from dataclasses import dataclass

from .logger import get_logger
from .config import DataIntegrationConfig

logger = get_logger(__name__)


@dataclass
class DataRequest:
    """Request for training data."""
    model_type: str
    data_type: str
    batch_size: int
    filters: Dict[str, Any]
    format: str = "json"


@dataclass
class DataResponse:
    """Response containing training data."""
    data: List[Dict[str, Any]]
    metadata: Dict[str, Any]
    total_count: int
    has_more: bool


class DataIntegrationClient:
    """
    gRPC client for communicating with the Data Integration module.
    
    Features:
    - Async gRPC communication
    - Automatic reconnection
    - Request/response streaming
    - Error handling and retries
    - Connection pooling
    """
    
    def __init__(self, config: DataIntegrationConfig):
        self.config = config
        self.channel = None
        self.stub = None
        self.connected = False
        self.retry_count = 0
        self.max_retries = config.max_retries
        self.retry_delay = config.retry_delay
        
    async def connect(self) -> bool:
        """Establish gRPC connection."""
        try:
            # Create channel with options
            options = [
                ('grpc.keepalive_time_ms', 30000),
                ('grpc.keepalive_timeout_ms', 5000),
                ('grpc.keepalive_permit_without_calls', True),
                ('grpc.http2.max_pings_without_data', 0),
                ('grpc.http2.min_time_between_pings_ms', 10000),
                ('grpc.http2.min_ping_interval_without_data_ms', 300000),
            ]
            
            self.channel = grpc.aio.insecure_channel(
                self.config.endpoint,
                options=options
            )
            
            # Test connection
            await self._health_check()
            
            self.connected = True
            self.retry_count = 0
            logger.info(f"Connected to Data Integration service: {self.config.endpoint}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to Data Integration service: {e}")
            self.connected = False
            return False
    
    async def disconnect(self) -> None:
        """Close gRPC connection."""
        if self.channel:
            await self.channel.close()
            self.connected = False
            logger.info("Disconnected from Data Integration service")
    
    async def _health_check(self) -> bool:
        """Perform health check on the connection."""
        try:
            # Simple health check - attempt to create a stub and make a basic call
            # This would use the actual gRPC service definition in a real implementation
            await asyncio.wait_for(
                self.channel.channel_ready(),
                timeout=self.config.timeout
            )
            return True
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return False
    
    async def _ensure_connected(self) -> bool:
        """Ensure connection is established with retry logic."""
        if self.connected and await self._health_check():
            return True
        
        while self.retry_count < self.max_retries:
            logger.info(f"Attempting to reconnect (attempt {self.retry_count + 1}/{self.max_retries})")
            
            if await self.connect():
                return True
            
            self.retry_count += 1
            if self.retry_count < self.max_retries:
                await asyncio.sleep(self.retry_delay * (2 ** self.retry_count))  # Exponential backoff
        
        logger.error("Failed to establish connection after maximum retries")
        return False
    
    async def request_training_data(
        self,
        model_type: str,
        data_type: str,
        batch_size: int = 1000,
        filters: Dict[str, Any] = None
    ) -> Optional[DataResponse]:
        """Request training data from the Data Integration service."""
        if not await self._ensure_connected():
            return None
        
        try:
            request = DataRequest(
                model_type=model_type,
                data_type=data_type,
                batch_size=batch_size,
                filters=filters or {}
            )
            
            # In a real implementation, this would use the actual gRPC stub
            # For now, we'll simulate the response
            response_data = await self._simulate_data_request(request)
            
            return DataResponse(
                data=response_data.get('data', []),
                metadata=response_data.get('metadata', {}),
                total_count=response_data.get('total_count', 0),
                has_more=response_data.get('has_more', False)
            )
            
        except Exception as e:
            logger.error(f"Failed to request training data: {e}")
            self.connected = False
            return None
    
    async def stream_training_data(
        self,
        model_type: str,
        data_type: str,
        batch_size: int = 100
    ) -> AsyncIterator[DataResponse]:
        """Stream training data from the Data Integration service."""
        if not await self._ensure_connected():
            return
        
        try:
            # In a real implementation, this would use gRPC streaming
            # For now, we'll simulate streaming responses
            async for response_data in self._simulate_data_stream(model_type, data_type, batch_size):
                yield DataResponse(
                    data=response_data.get('data', []),
                    metadata=response_data.get('metadata', {}),
                    total_count=response_data.get('total_count', 0),
                    has_more=response_data.get('has_more', False)
                )
                
        except Exception as e:
            logger.error(f"Failed to stream training data: {e}")
            self.connected = False
    
    async def submit_model_update(
        self,
        job_id: str,
        model_type: str,
        model_path: str,
        metrics: Dict[str, float],
        metadata: Dict[str, Any] = None
    ) -> bool:
        """Submit model update to the Data Integration service."""
        if not await self._ensure_connected():
            return False
        
        try:
            update_data = {
                'job_id': job_id,
                'model_type': model_type,
                'model_path': model_path,
                'metrics': metrics,
                'metadata': metadata or {},
                'timestamp': int(time.time() * 1000)
            }
            
            # In a real implementation, this would use the actual gRPC stub
            success = await self._simulate_model_update(update_data)
            
            if success:
                logger.info(f"Model update submitted successfully: {job_id}")
            else:
                logger.error(f"Failed to submit model update: {job_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to submit model update: {e}")
            self.connected = False
            return False
    
    async def get_data_statistics(
        self,
        model_type: str,
        data_type: str
    ) -> Optional[Dict[str, Any]]:
        """Get statistics about available training data."""
        if not await self._ensure_connected():
            return None
        
        try:
            # In a real implementation, this would use the actual gRPC stub
            stats = await self._simulate_data_statistics(model_type, data_type)
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get data statistics: {e}")
            self.connected = False
            return None
    
    # Simulation methods (replace with actual gRPC calls in production)
    async def _simulate_data_request(self, request: DataRequest) -> Dict[str, Any]:
        """Simulate data request response."""
        await asyncio.sleep(0.1)  # Simulate network delay
        
        return {
            'data': [
                {'id': i, 'content': f'Sample data {i}', 'label': i % 2}
                for i in range(request.batch_size)
            ],
            'metadata': {
                'model_type': request.model_type,
                'data_type': request.data_type,
                'source': 'data_integration_service'
            },
            'total_count': request.batch_size,
            'has_more': False
        }
    
    async def _simulate_data_stream(
        self,
        model_type: str,
        data_type: str,
        batch_size: int
    ) -> AsyncIterator[Dict[str, Any]]:
        """Simulate streaming data responses."""
        for batch_num in range(3):  # Simulate 3 batches
            await asyncio.sleep(0.1)  # Simulate network delay
            
            yield {
                'data': [
                    {'id': batch_num * batch_size + i, 'content': f'Stream data {i}', 'label': i % 2}
                    for i in range(batch_size)
                ],
                'metadata': {
                    'model_type': model_type,
                    'data_type': data_type,
                    'batch_num': batch_num,
                    'source': 'data_integration_service'
                },
                'total_count': batch_size,
                'has_more': batch_num < 2
            }
    
    async def _simulate_model_update(self, update_data: Dict[str, Any]) -> bool:
        """Simulate model update submission."""
        await asyncio.sleep(0.05)  # Simulate network delay
        return True  # Always succeed in simulation
    
    async def _simulate_data_statistics(self, model_type: str, data_type: str) -> Dict[str, Any]:
        """Simulate data statistics response."""
        await asyncio.sleep(0.05)  # Simulate network delay
        
        return {
            'model_type': model_type,
            'data_type': data_type,
            'total_samples': 100000,
            'available_samples': 95000,
            'last_updated': int(time.time() * 1000),
            'data_quality_score': 0.95,
            'schema_version': '1.0'
        }
    
    async def close(self) -> None:
        """Close the gRPC client."""
        await self.disconnect()
