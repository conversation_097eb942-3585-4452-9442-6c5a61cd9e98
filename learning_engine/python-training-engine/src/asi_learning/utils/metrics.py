"""
Metrics collection and monitoring for the ASI Learning Engine.

Provides comprehensive metrics collection, aggregation, and reporting capabilities
with integration to Prometheus, MLflow, and TensorBoard.
"""

import time
from collections import defaultdict, deque
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Union
import threading
from datetime import datetime, timedelta
import json

import numpy as np
from prometheus_client import Counter, Histogram, Gauge, start_http_server, CollectorRegistry
import mlflow
import mlflow.pytorch
from torch.utils.tensorboard import SummaryWriter

from .logger import get_logger

logger = get_logger(__name__)


@dataclass
class MetricValue:
    """Container for a metric value with metadata."""
    value: Union[int, float]
    timestamp: datetime
    tags: Dict[str, str] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)


class MetricsBuffer:
    """Thread-safe buffer for storing metrics before aggregation."""
    
    def __init__(self, max_size: int = 10000):
        self.max_size = max_size
        self.buffer = deque(maxlen=max_size)
        self.lock = threading.Lock()
    
    def add(self, metric_name: str, value: MetricValue) -> None:
        """Add a metric to the buffer."""
        with self.lock:
            self.buffer.append((metric_name, value))
    
    def get_and_clear(self) -> List[tuple]:
        """Get all metrics and clear the buffer."""
        with self.lock:
            metrics = list(self.buffer)
            self.buffer.clear()
            return metrics
    
    def size(self) -> int:
        """Get current buffer size."""
        with self.lock:
            return len(self.buffer)


class PrometheusExporter:
    """Prometheus metrics exporter."""
    
    def __init__(self, port: int = 8090):
        self.port = port
        self.registry = CollectorRegistry()
        self.counters = {}
        self.histograms = {}
        self.gauges = {}
        self.started = False
    
    def start(self) -> None:
        """Start Prometheus HTTP server."""
        if not self.started:
            start_http_server(self.port, registry=self.registry)
            self.started = True
            logger.info(f"Prometheus metrics server started on port {self.port}")
    
    def record_counter(self, name: str, value: float, tags: Dict[str, str] = None) -> None:
        """Record a counter metric."""
        if name not in self.counters:
            self.counters[name] = Counter(
                name.replace('.', '_'),
                f'Counter metric: {name}',
                labelnames=list(tags.keys()) if tags else [],
                registry=self.registry
            )
        
        if tags:
            self.counters[name].labels(**tags).inc(value)
        else:
            self.counters[name].inc(value)
    
    def record_histogram(self, name: str, value: float, tags: Dict[str, str] = None) -> None:
        """Record a histogram metric."""
        if name not in self.histograms:
            self.histograms[name] = Histogram(
                name.replace('.', '_'),
                f'Histogram metric: {name}',
                labelnames=list(tags.keys()) if tags else [],
                registry=self.registry
            )
        
        if tags:
            self.histograms[name].labels(**tags).observe(value)
        else:
            self.histograms[name].observe(value)
    
    def record_gauge(self, name: str, value: float, tags: Dict[str, str] = None) -> None:
        """Record a gauge metric."""
        if name not in self.gauges:
            self.gauges[name] = Gauge(
                name.replace('.', '_'),
                f'Gauge metric: {name}',
                labelnames=list(tags.keys()) if tags else [],
                registry=self.registry
            )
        
        if tags:
            self.gauges[name].labels(**tags).set(value)
        else:
            self.gauges[name].set(value)


class MLflowTracker:
    """MLflow experiment tracking."""
    
    def __init__(self, tracking_uri: str = "http://localhost:5000"):
        self.tracking_uri = tracking_uri
        self.active_runs = {}
        self.setup_mlflow()
    
    def setup_mlflow(self) -> None:
        """Setup MLflow tracking."""
        try:
            mlflow.set_tracking_uri(self.tracking_uri)
            logger.info(f"MLflow tracking configured: {self.tracking_uri}")
        except Exception as e:
            logger.error(f"Failed to setup MLflow: {e}")
    
    def start_run(self, run_name: str, experiment_name: str = "ASI_Learning") -> str:
        """Start a new MLflow run."""
        try:
            # Set or create experiment
            try:
                experiment_id = mlflow.create_experiment(experiment_name)
            except Exception:
                experiment_id = mlflow.get_experiment_by_name(experiment_name).experiment_id
            
            mlflow.set_experiment(experiment_name)
            
            # Start run
            run = mlflow.start_run(run_name=run_name)
            run_id = run.info.run_id
            self.active_runs[run_name] = run_id
            
            logger.info(f"Started MLflow run: {run_name} ({run_id})")
            return run_id
            
        except Exception as e:
            logger.error(f"Failed to start MLflow run: {e}")
            return ""
    
    def log_metric(self, run_name: str, metric_name: str, value: float, step: int = None) -> None:
        """Log a metric to MLflow."""
        try:
            if run_name in self.active_runs:
                with mlflow.start_run(run_id=self.active_runs[run_name]):
                    mlflow.log_metric(metric_name, value, step=step)
        except Exception as e:
            logger.error(f"Failed to log MLflow metric: {e}")
    
    def log_params(self, run_name: str, params: Dict[str, Any]) -> None:
        """Log parameters to MLflow."""
        try:
            if run_name in self.active_runs:
                with mlflow.start_run(run_id=self.active_runs[run_name]):
                    mlflow.log_params(params)
        except Exception as e:
            logger.error(f"Failed to log MLflow params: {e}")
    
    def log_model(self, run_name: str, model, artifact_path: str) -> None:
        """Log a model to MLflow."""
        try:
            if run_name in self.active_runs:
                with mlflow.start_run(run_id=self.active_runs[run_name]):
                    mlflow.pytorch.log_model(model, artifact_path)
        except Exception as e:
            logger.error(f"Failed to log MLflow model: {e}")
    
    def end_run(self, run_name: str) -> None:
        """End an MLflow run."""
        try:
            if run_name in self.active_runs:
                with mlflow.start_run(run_id=self.active_runs[run_name]):
                    mlflow.end_run()
                del self.active_runs[run_name]
                logger.info(f"Ended MLflow run: {run_name}")
        except Exception as e:
            logger.error(f"Failed to end MLflow run: {e}")


class TensorBoardLogger:
    """TensorBoard logging integration."""
    
    def __init__(self, log_dir: str = "logs/tensorboard"):
        self.log_dir = log_dir
        self.writers = {}
    
    def get_writer(self, run_name: str) -> SummaryWriter:
        """Get or create a TensorBoard writer."""
        if run_name not in self.writers:
            self.writers[run_name] = SummaryWriter(f"{self.log_dir}/{run_name}")
        return self.writers[run_name]
    
    def log_scalar(self, run_name: str, tag: str, value: float, step: int) -> None:
        """Log a scalar value."""
        try:
            writer = self.get_writer(run_name)
            writer.add_scalar(tag, value, step)
        except Exception as e:
            logger.error(f"Failed to log TensorBoard scalar: {e}")
    
    def log_histogram(self, run_name: str, tag: str, values: np.ndarray, step: int) -> None:
        """Log a histogram."""
        try:
            writer = self.get_writer(run_name)
            writer.add_histogram(tag, values, step)
        except Exception as e:
            logger.error(f"Failed to log TensorBoard histogram: {e}")
    
    def close_writer(self, run_name: str) -> None:
        """Close a TensorBoard writer."""
        if run_name in self.writers:
            self.writers[run_name].close()
            del self.writers[run_name]


class MetricsCollector:
    """
    Central metrics collection system for the ASI Learning Engine.
    
    Features:
    - Thread-safe metric collection
    - Multiple export backends (Prometheus, MLflow, TensorBoard)
    - Automatic aggregation and buffering
    - Real-time monitoring capabilities
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.enabled = config.get('enabled', True)
        
        if not self.enabled:
            logger.info("Metrics collection disabled")
            return
        
        # Initialize components
        self.buffer = MetricsBuffer()
        self.prometheus = PrometheusExporter(config.get('prometheus_port', 8090))
        self.mlflow = MLflowTracker(config.get('mlflow_tracking_uri', 'http://localhost:5000'))
        self.tensorboard = TensorBoardLogger(config.get('tensorboard_log_dir', 'logs/tensorboard'))
        
        # Metric aggregations
        self.metric_history = defaultdict(list)
        self.metric_stats = defaultdict(dict)
        
        # Start background processing
        self.processing_thread = threading.Thread(target=self._process_metrics, daemon=True)
        self.running = True
        
        if self.enabled:
            self.prometheus.start()
            self.processing_thread.start()
            logger.info("Metrics collector initialized")
    
    def record_metric(
        self,
        name: str,
        value: Union[int, float],
        tags: Dict[str, str] = None,
        metadata: Dict[str, Any] = None
    ) -> None:
        """Record a metric value."""
        if not self.enabled:
            return
        
        metric_value = MetricValue(
            value=value,
            timestamp=datetime.utcnow(),
            tags=tags or {},
            metadata=metadata or {}
        )
        
        self.buffer.add(name, metric_value)
    
    def record_training_metric(
        self,
        job_id: str,
        model_type: str,
        metric_name: str,
        value: float,
        epoch: int = None,
        step: int = None
    ) -> None:
        """Record a training-specific metric."""
        tags = {
            'job_id': job_id,
            'model_type': model_type,
        }
        
        metadata = {}
        if epoch is not None:
            metadata['epoch'] = epoch
        if step is not None:
            metadata['step'] = step
        
        self.record_metric(f"training.{metric_name}", value, tags, metadata)
        
        # Also log to MLflow and TensorBoard
        if epoch is not None or step is not None:
            self.mlflow.log_metric(job_id, metric_name, value, step=step or epoch)
            self.tensorboard.log_scalar(job_id, metric_name, value, step or epoch)
    
    def start_training_run(self, job_id: str, model_type: str, config: Dict[str, Any]) -> None:
        """Start tracking a training run."""
        if not self.enabled:
            return
        
        # Start MLflow run
        self.mlflow.start_run(job_id, experiment_name=f"ASI_{model_type}")
        self.mlflow.log_params(job_id, config)
        
        logger.info(f"Started tracking training run: {job_id}")
    
    def end_training_run(self, job_id: str) -> None:
        """End tracking a training run."""
        if not self.enabled:
            return
        
        self.mlflow.end_run(job_id)
        self.tensorboard.close_writer(job_id)
        
        logger.info(f"Ended tracking training run: {job_id}")
    
    def get_metric_stats(self, name: str) -> Dict[str, float]:
        """Get statistics for a metric."""
        if name not in self.metric_history:
            return {}
        
        values = [mv.value for mv in self.metric_history[name]]
        if not values:
            return {}
        
        return {
            'count': len(values),
            'mean': np.mean(values),
            'std': np.std(values),
            'min': np.min(values),
            'max': np.max(values),
            'median': np.median(values),
            'p95': np.percentile(values, 95),
            'p99': np.percentile(values, 99),
        }
    
    def _process_metrics(self) -> None:
        """Background thread for processing metrics."""
        while self.running:
            try:
                # Get metrics from buffer
                metrics = self.buffer.get_and_clear()
                
                for metric_name, metric_value in metrics:
                    # Update history
                    self.metric_history[metric_name].append(metric_value)
                    
                    # Keep only recent history (last 1000 values)
                    if len(self.metric_history[metric_name]) > 1000:
                        self.metric_history[metric_name] = self.metric_history[metric_name][-1000:]
                    
                    # Export to Prometheus
                    if 'counter' in metric_name or 'count' in metric_name:
                        self.prometheus.record_counter(metric_name, metric_value.value, metric_value.tags)
                    elif 'latency' in metric_name or 'duration' in metric_name:
                        self.prometheus.record_histogram(metric_name, metric_value.value, metric_value.tags)
                    else:
                        self.prometheus.record_gauge(metric_name, metric_value.value, metric_value.tags)
                
                time.sleep(1)  # Process every second
                
            except Exception as e:
                logger.error(f"Error processing metrics: {e}")
                time.sleep(5)
    
    def shutdown(self) -> None:
        """Shutdown the metrics collector."""
        if not self.enabled:
            return
        
        self.running = False
        if self.processing_thread.is_alive():
            self.processing_thread.join(timeout=5)
        
        # Close all writers
        for run_name in list(self.tensorboard.writers.keys()):
            self.tensorboard.close_writer(run_name)
        
        logger.info("Metrics collector shutdown completed")
