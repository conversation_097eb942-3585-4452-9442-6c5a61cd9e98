"""
Training Orchestrator

Central orchestrator for managing multi-modal training pipelines in the ASI Learning Engine.
Coordinates NLP, Vision, RL, and Abstraction model training with hooks for logging and evaluation.
"""

import asyncio
import logging
import signal
import sys
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable

import torch
import torch.distributed as dist
from omegaconf import DictConfig, OmegaConf
from rich.console import Console
from rich.progress import Progress, TaskID

from ..utils.config import Config
from ..utils.logger import get_logger
from ..utils.metrics import MetricsCollector
from ..utils.kafka_client import KafkaConsumer
from ..utils.grpc_client import DataIntegrationClient
from ..nlp.trainer import NLPTrainer
from ..vision.trainer import VisionTrainer
from ..rl.trainer import RLTrainer
from ..abstraction.trainer import AbstractionTrainer
from ..evaluation.evaluator import ModelEvaluator

logger = get_logger(__name__)
console = Console()


@dataclass
class TrainingJob:
    """Training job configuration and state."""
    job_id: str
    model_type: str
    config: DictConfig
    status: str = "pending"
    progress: float = 0.0
    trainer: Optional[Any] = None
    metrics: Dict[str, float] = None
    error_message: Optional[str] = None

    def __post_init__(self):
        if self.metrics is None:
            self.metrics = {}


class TrainingOrchestrator:
    """
    Central training orchestrator for the ASI Learning Engine.
    
    Manages multi-modal training pipelines with:
    - Distributed training coordination
    - Real-time monitoring and logging
    - Model evaluation and validation
    - Integration with data sources
    - Fault tolerance and recovery
    """

    def __init__(self, config_path: str = "configs/orchestrator_config.yaml"):
        """Initialize the training orchestrator."""
        self.config = Config.load(config_path)
        self.metrics_collector = MetricsCollector(self.config.monitoring)
        
        # Training components
        self.trainers = {
            "nlp": NLPTrainer(self.config.nlp),
            "vision": VisionTrainer(self.config.vision),
            "rl": RLTrainer(self.config.rl),
            "abstraction": AbstractionTrainer(self.config.abstraction),
        }
        
        # Job management
        self.active_jobs: Dict[str, TrainingJob] = {}
        self.job_queue: List[TrainingJob] = []
        self.executor = ThreadPoolExecutor(max_workers=self.config.orchestrator.max_concurrent_jobs)
        
        # Data integration
        self.kafka_consumer = KafkaConsumer(self.config.kafka)
        self.data_integration_client = DataIntegrationClient(self.config.data_integration)
        
        # Model evaluation
        self.evaluator = ModelEvaluator(self.config.evaluation)
        
        # State management
        self.is_running = False
        self.shutdown_event = asyncio.Event()
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        logger.info("Training orchestrator initialized")

    def _signal_handler(self, signum: int, frame) -> None:
        """Handle shutdown signals gracefully."""
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        self.shutdown_event.set()

    async def start(self) -> None:
        """Start the training orchestrator."""
        logger.info("Starting training orchestrator...")
        self.is_running = True
        
        try:
            # Start background tasks
            tasks = [
                asyncio.create_task(self._job_scheduler()),
                asyncio.create_task(self._data_consumer()),
                asyncio.create_task(self._metrics_reporter()),
                asyncio.create_task(self._health_monitor()),
            ]
            
            # Wait for shutdown signal
            await self.shutdown_event.wait()
            
            # Cancel all tasks
            for task in tasks:
                task.cancel()
            
            await asyncio.gather(*tasks, return_exceptions=True)
            
        except Exception as e:
            logger.error(f"Error in orchestrator: {e}")
            raise
        finally:
            await self._cleanup()

    async def _job_scheduler(self) -> None:
        """Schedule and manage training jobs."""
        logger.info("Job scheduler started")
        
        while self.is_running:
            try:
                # Process job queue
                if self.job_queue and len(self.active_jobs) < self.config.orchestrator.max_concurrent_jobs:
                    job = self.job_queue.pop(0)
                    await self._start_training_job(job)
                
                # Check completed jobs
                completed_jobs = []
                for job_id, job in self.active_jobs.items():
                    if job.status in ["completed", "failed", "cancelled"]:
                        completed_jobs.append(job_id)
                
                # Clean up completed jobs
                for job_id in completed_jobs:
                    await self._cleanup_job(job_id)
                
                await asyncio.sleep(1)
                
            except Exception as e:
                logger.error(f"Error in job scheduler: {e}")
                await asyncio.sleep(5)

    async def _start_training_job(self, job: TrainingJob) -> None:
        """Start a training job."""
        try:
            logger.info(f"Starting training job {job.job_id} for {job.model_type}")
            
            # Get appropriate trainer
            trainer = self.trainers.get(job.model_type)
            if not trainer:
                raise ValueError(f"Unknown model type: {job.model_type}")
            
            job.trainer = trainer
            job.status = "running"
            self.active_jobs[job.job_id] = job
            
            # Submit training task to executor
            future = self.executor.submit(self._run_training, job)
            
            # Store future for monitoring
            job.future = future
            
        except Exception as e:
            logger.error(f"Failed to start job {job.job_id}: {e}")
            job.status = "failed"
            job.error_message = str(e)

    def _run_training(self, job: TrainingJob) -> None:
        """Run training in a separate thread."""
        try:
            # Setup training hooks
            hooks = self._create_training_hooks(job)
            
            # Start training
            job.trainer.train(
                config=job.config,
                hooks=hooks,
                job_id=job.job_id
            )
            
            job.status = "completed"
            logger.info(f"Training job {job.job_id} completed successfully")
            
        except Exception as e:
            logger.error(f"Training job {job.job_id} failed: {e}")
            job.status = "failed"
            job.error_message = str(e)

    def _create_training_hooks(self, job: TrainingJob) -> Dict[str, Callable]:
        """Create training hooks for logging and evaluation."""
        def on_epoch_start(epoch: int, **kwargs):
            logger.debug(f"Job {job.job_id}: Starting epoch {epoch}")
            self.metrics_collector.record_metric(
                f"training.{job.job_id}.epoch_start",
                epoch,
                tags={"job_id": job.job_id, "model_type": job.model_type}
            )

        def on_epoch_end(epoch: int, metrics: Dict[str, float], **kwargs):
            logger.info(f"Job {job.job_id}: Epoch {epoch} completed - {metrics}")
            job.metrics.update(metrics)
            job.progress = (epoch + 1) / job.config.training.epochs * 100
            
            # Record metrics
            for metric_name, value in metrics.items():
                self.metrics_collector.record_metric(
                    f"training.{job.job_id}.{metric_name}",
                    value,
                    tags={"job_id": job.job_id, "model_type": job.model_type, "epoch": epoch}
                )

        def on_batch_end(batch: int, loss: float, **kwargs):
            if batch % 100 == 0:  # Log every 100 batches
                self.metrics_collector.record_metric(
                    f"training.{job.job_id}.batch_loss",
                    loss,
                    tags={"job_id": job.job_id, "model_type": job.model_type, "batch": batch}
                )

        def on_validation(metrics: Dict[str, float], **kwargs):
            logger.info(f"Job {job.job_id}: Validation metrics - {metrics}")
            for metric_name, value in metrics.items():
                self.metrics_collector.record_metric(
                    f"validation.{job.job_id}.{metric_name}",
                    value,
                    tags={"job_id": job.job_id, "model_type": job.model_type}
                )

        return {
            "on_epoch_start": on_epoch_start,
            "on_epoch_end": on_epoch_end,
            "on_batch_end": on_batch_end,
            "on_validation": on_validation,
        }

    async def _data_consumer(self) -> None:
        """Consume training data from Kafka."""
        logger.info("Data consumer started")
        
        while self.is_running:
            try:
                # Consume messages from Kafka
                messages = await self.kafka_consumer.consume_batch(
                    topics=self.config.kafka.training_topics,
                    timeout=1.0
                )
                
                for message in messages:
                    await self._process_training_data(message)
                
            except Exception as e:
                logger.error(f"Error in data consumer: {e}")
                await asyncio.sleep(5)

    async def _process_training_data(self, message: Dict[str, Any]) -> None:
        """Process incoming training data."""
        try:
            # Parse message
            data_type = message.get("data_type")
            model_type = message.get("model_type")
            data_payload = message.get("data_payload")
            
            # Route to appropriate trainer
            if model_type in self.trainers:
                trainer = self.trainers[model_type]
                await trainer.process_streaming_data(data_payload)
            
        except Exception as e:
            logger.error(f"Error processing training data: {e}")

    async def _metrics_reporter(self) -> None:
        """Report metrics periodically."""
        logger.info("Metrics reporter started")
        
        while self.is_running:
            try:
                # Collect system metrics
                system_metrics = {
                    "active_jobs": len(self.active_jobs),
                    "queued_jobs": len(self.job_queue),
                    "gpu_utilization": self._get_gpu_utilization(),
                    "memory_usage": self._get_memory_usage(),
                }
                
                # Report metrics
                for metric_name, value in system_metrics.items():
                    self.metrics_collector.record_metric(
                        f"orchestrator.{metric_name}",
                        value,
                        tags={"component": "orchestrator"}
                    )
                
                await asyncio.sleep(self.config.monitoring.report_interval)
                
            except Exception as e:
                logger.error(f"Error in metrics reporter: {e}")
                await asyncio.sleep(30)

    async def _health_monitor(self) -> None:
        """Monitor system health."""
        logger.info("Health monitor started")
        
        while self.is_running:
            try:
                # Check trainer health
                for model_type, trainer in self.trainers.items():
                    health_status = await trainer.health_check()
                    self.metrics_collector.record_metric(
                        f"health.{model_type}",
                        1 if health_status else 0,
                        tags={"component": model_type}
                    )
                
                await asyncio.sleep(30)
                
            except Exception as e:
                logger.error(f"Error in health monitor: {e}")
                await asyncio.sleep(60)

    def _get_gpu_utilization(self) -> float:
        """Get GPU utilization percentage."""
        try:
            if torch.cuda.is_available():
                return torch.cuda.utilization()
            return 0.0
        except Exception:
            return 0.0

    def _get_memory_usage(self) -> float:
        """Get memory usage percentage."""
        try:
            import psutil
            return psutil.virtual_memory().percent
        except Exception:
            return 0.0

    async def _cleanup_job(self, job_id: str) -> None:
        """Clean up completed job."""
        job = self.active_jobs.pop(job_id, None)
        if job:
            logger.info(f"Cleaning up job {job_id}")
            # Additional cleanup logic here

    async def _cleanup(self) -> None:
        """Clean up resources."""
        logger.info("Cleaning up orchestrator resources...")
        
        # Shutdown executor
        self.executor.shutdown(wait=True)
        
        # Close connections
        await self.kafka_consumer.close()
        await self.data_integration_client.close()
        
        self.is_running = False
        logger.info("Orchestrator cleanup completed")

    # Public API methods
    def submit_training_job(self, job_config: Dict[str, Any]) -> str:
        """Submit a new training job."""
        job = TrainingJob(
            job_id=f"job_{len(self.active_jobs) + len(self.job_queue)}",
            model_type=job_config["model_type"],
            config=OmegaConf.create(job_config)
        )
        
        self.job_queue.append(job)
        logger.info(f"Submitted training job {job.job_id}")
        return job.job_id

    def get_job_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """Get status of a training job."""
        job = self.active_jobs.get(job_id)
        if job:
            return {
                "job_id": job.job_id,
                "status": job.status,
                "progress": job.progress,
                "metrics": job.metrics,
                "error_message": job.error_message,
            }
        return None

    def cancel_job(self, job_id: str) -> bool:
        """Cancel a training job."""
        job = self.active_jobs.get(job_id)
        if job and job.status == "running":
            job.status = "cancelled"
            if hasattr(job, 'future'):
                job.future.cancel()
            logger.info(f"Cancelled job {job_id}")
            return True
        return False


async def main():
    """Main entry point for the training orchestrator."""
    orchestrator = TrainingOrchestrator()
    
    try:
        await orchestrator.start()
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt, shutting down...")
    except Exception as e:
        logger.error(f"Orchestrator failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
