"""
Reinforcement Learning Trainer for the ASI Learning Engine.

Provides comprehensive RL agent training with Stable-Baselines3,
including PPO, A2C, SAC, TD3, and custom environments.
"""

import os
import json
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable, Union
import asyncio

import numpy as np
import torch
import gymnasium as gym
from stable_baselines3 import PPO, A2C, SAC, TD3, DQN
from stable_baselines3.common.env_util import make_vec_env
from stable_baselines3.common.callbacks import BaseCallback, EvalCallback, CheckpointCallback
from stable_baselines3.common.monitor import Monitor
from stable_baselines3.common.vec_env import DummyVecEnv, SubprocVecEnv
from stable_baselines3.common.evaluation import evaluate_policy
from stable_baselines3.common.logger import configure

from ..utils.logger import get_logger, TrainingLogger
from ..utils.config import RLConfig
from ..utils.metrics import MetricsCollector

logger = get_logger(__name__)


class RLMetricsCallback(BaseCallback):
    """Custom callback for RL training metrics."""

    def __init__(self, training_logger: TrainingLogger = None, hooks: Dict[str, Callable] = None):
        super().__init__()
        self.training_logger = training_logger
        self.hooks = hooks
        self.episode_rewards = []
        self.episode_lengths = []

    def _on_step(self) -> bool:
        # Log training metrics
        if len(self.model.ep_info_buffer) > 0:
            ep_info = self.model.ep_info_buffer[-1]
            episode_reward = ep_info.get('r', 0)
            episode_length = ep_info.get('l', 0)

            self.episode_rewards.append(episode_reward)
            self.episode_lengths.append(episode_length)

            # Log every 100 episodes
            if len(self.episode_rewards) % 100 == 0:
                mean_reward = np.mean(self.episode_rewards[-100:])
                mean_length = np.mean(self.episode_lengths[-100:])

                metrics = {
                    'mean_episode_reward': mean_reward,
                    'mean_episode_length': mean_length,
                    'total_timesteps': self.num_timesteps,
                }

                if self.training_logger:
                    self.training_logger.log_batch_metrics(
                        len(self.episode_rewards),
                        mean_reward,
                        **metrics
                    )

                if self.hooks and 'on_batch_end' in self.hooks:
                    self.hooks['on_batch_end'](len(self.episode_rewards), mean_reward)

        return True


class RLTrainer:
    """
    Advanced RL trainer for the ASI Learning Engine.

    Supports:
    - Multiple RL algorithms (PPO, A2C, SAC, TD3, DQN)
    - Custom and standard environments
    - Distributed training
    - Policy evaluation and testing
    - Model checkpointing and versioning
    """

    def __init__(self, config: RLConfig):
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = None
        self.env = None
        self.eval_env = None
        self.training_logger = None

        logger.info(f"RL Trainer initialized with device: {self.device}")

    def setup_environment(self, env_name: str = None, n_envs: int = 1) -> None:
        """Setup training and evaluation environments."""
        try:
            env_name = env_name or self.config.environment

            # Create training environment
            if n_envs > 1:
                self.env = make_vec_env(
                    env_name,
                    n_envs=n_envs,
                    vec_env_cls=SubprocVecEnv if n_envs > 4 else DummyVecEnv
                )
            else:
                self.env = gym.make(env_name)
                self.env = Monitor(self.env)
                self.env = DummyVecEnv([lambda: self.env])

            # Create evaluation environment
            self.eval_env = gym.make(env_name)
            self.eval_env = Monitor(self.eval_env)

            logger.info(f"Environment setup completed: {env_name} with {n_envs} parallel environments")

        except Exception as e:
            logger.error(f"Failed to setup environment: {e}")
            raise

    def setup_model(self, algorithm: str = None, **kwargs) -> None:
        """Setup RL model/algorithm."""
        try:
            algorithm = algorithm or self.config.algorithm

            # Common parameters
            common_params = {
                'learning_rate': self.config.learning_rate,
                'device': self.device,
                'verbose': 1,
                **kwargs
            }

            if algorithm.upper() == "PPO":
                self.model = PPO(
                    "MlpPolicy",
                    self.env,
                    n_steps=self.config.n_steps,
                    batch_size=self.config.batch_size,
                    n_epochs=self.config.n_epochs,
                    gamma=self.config.gamma,
                    gae_lambda=self.config.gae_lambda,
                    **common_params
                )

            elif algorithm.upper() == "A2C":
                self.model = A2C(
                    "MlpPolicy",
                    self.env,
                    n_steps=self.config.n_steps,
                    gamma=self.config.gamma,
                    gae_lambda=self.config.gae_lambda,
                    **common_params
                )

            elif algorithm.upper() == "SAC":
                self.model = SAC(
                    "MlpPolicy",
                    self.env,
                    batch_size=self.config.batch_size,
                    gamma=self.config.gamma,
                    **common_params
                )

            elif algorithm.upper() == "TD3":
                self.model = TD3(
                    "MlpPolicy",
                    self.env,
                    batch_size=self.config.batch_size,
                    gamma=self.config.gamma,
                    **common_params
                )

            elif algorithm.upper() == "DQN":
                self.model = DQN(
                    "MlpPolicy",
                    self.env,
                    batch_size=self.config.batch_size,
                    gamma=self.config.gamma,
                    **common_params
                )

            else:
                raise ValueError(f"Unsupported algorithm: {algorithm}")

            logger.info(f"Model setup completed: {algorithm}")

        except Exception as e:
            logger.error(f"Failed to setup model: {e}")
            raise

    def train(
        self,
        config: Dict[str, Any],
        hooks: Dict[str, Callable] = None,
        job_id: str = None
    ) -> Dict[str, Any]:
        """Train the RL agent."""
        try:
            # Setup training logger
            if job_id:
                self.training_logger = TrainingLogger(job_id, "rl")

            # Setup environment
            env_name = config.get('environment', self.config.environment)
            n_envs = config.get('n_envs', 1)
            self.setup_environment(env_name, n_envs)

            # Setup model
            algorithm = config.get('algorithm', self.config.algorithm)
            self.setup_model(algorithm, **config.get('model_params', {}))

            # Setup callbacks
            callbacks = []

            # Metrics callback
            if self.training_logger or hooks:
                metrics_callback = RLMetricsCallback(self.training_logger, hooks)
                callbacks.append(metrics_callback)

            # Evaluation callback
            eval_freq = config.get('eval_freq', 10000)
            if self.eval_env and eval_freq > 0:
                eval_callback = EvalCallback(
                    self.eval_env,
                    best_model_save_path=config.get('best_model_path', './models/rl_best'),
                    log_path=config.get('log_path', './logs/rl'),
                    eval_freq=eval_freq,
                    deterministic=True,
                    render=False
                )
                callbacks.append(eval_callback)

            # Checkpoint callback
            checkpoint_freq = config.get('checkpoint_freq', 50000)
            if checkpoint_freq > 0:
                checkpoint_callback = CheckpointCallback(
                    save_freq=checkpoint_freq,
                    save_path=config.get('checkpoint_path', './checkpoints/rl'),
                    name_prefix='rl_model'
                )
                callbacks.append(checkpoint_callback)

            # Configure logger
            log_dir = config.get('log_dir', './logs/rl')
            Path(log_dir).mkdir(parents=True, exist_ok=True)
            self.model.set_logger(configure(log_dir, ["stdout", "csv", "tensorboard"]))

            # Start training
            total_timesteps = config.get('total_timesteps', self.config.total_timesteps)

            if self.training_logger:
                self.training_logger.log_epoch_start(0, total_timesteps)

            if hooks and 'on_epoch_start' in hooks:
                hooks['on_epoch_start'](0)

            self.model.learn(
                total_timesteps=total_timesteps,
                callback=callbacks,
                progress_bar=True
            )

            # Save final model
            model_path = Path(config.get('model_save_path', './models/rl_model'))
            model_path.mkdir(parents=True, exist_ok=True)
            self.model.save(str(model_path / 'final_model'))

            if self.training_logger:
                self.training_logger.log_model_save(str(model_path), total_timesteps)

            # Evaluate final model
            final_eval = self.evaluate(n_eval_episodes=100)

            results = {
                'total_timesteps': total_timesteps,
                'model_path': str(model_path),
                'final_evaluation': final_eval,
            }

            if hooks and 'on_epoch_end' in hooks:
                hooks['on_epoch_end'](total_timesteps, results)

            logger.info(f"RL training completed successfully: {results}")
            return results

        except Exception as e:
            if self.training_logger:
                self.training_logger.log_error(e, "training")
            logger.error(f"RL training failed: {e}")
            raise

    def evaluate(self, n_eval_episodes: int = 100) -> Dict[str, float]:
        """Evaluate the trained agent."""
        try:
            if not self.model or not self.eval_env:
                raise ValueError("Model or evaluation environment not setup")

            # Evaluate policy
            mean_reward, std_reward = evaluate_policy(
                self.model,
                self.eval_env,
                n_eval_episodes=n_eval_episodes,
                deterministic=True,
                return_episode_rewards=False
            )

            metrics = {
                'mean_reward': mean_reward,
                'std_reward': std_reward,
                'n_eval_episodes': n_eval_episodes,
            }

            if self.training_logger:
                self.training_logger.log_validation(metrics)

            logger.info(f"Evaluation completed: {metrics}")
            return metrics

        except Exception as e:
            logger.error(f"Evaluation failed: {e}")
            return {}

    def predict(self, observations: List[np.ndarray], deterministic: bool = True) -> List[Dict[str, Any]]:
        """Make predictions with the trained agent."""
        try:
            if not self.model:
                raise ValueError("Model not loaded. Call setup_model() first.")

            predictions = []

            for obs in observations:
                action, _states = self.model.predict(obs, deterministic=deterministic)

                predictions.append({
                    'observation': obs.tolist(),
                    'action': action.tolist() if isinstance(action, np.ndarray) else action,
                    'deterministic': deterministic,
                })

            return predictions

        except Exception as e:
            logger.error(f"Prediction failed: {e}")
            return []

    def save_model(self, path: str) -> None:
        """Save the trained model."""
        try:
            model_path = Path(path)
            model_path.mkdir(parents=True, exist_ok=True)

            # Save model
            self.model.save(str(model_path / 'model'))

            # Save config
            with open(model_path / 'config.json', 'w') as f:
                json.dump(self.config.__dict__, f, indent=2)

            logger.info(f"RL model saved to {path}")

        except Exception as e:
            logger.error(f"Failed to save model: {e}")

    def load_model(self, path: str, algorithm: str = None) -> None:
        """Load a trained model."""
        try:
            model_path = Path(path)

            # Load config
            with open(model_path / 'config.json', 'r') as f:
                config_dict = json.load(f)

            algorithm = algorithm or config_dict.get('algorithm', self.config.algorithm)

            # Load model based on algorithm
            if algorithm.upper() == "PPO":
                self.model = PPO.load(str(model_path / 'model'))
            elif algorithm.upper() == "A2C":
                self.model = A2C.load(str(model_path / 'model'))
            elif algorithm.upper() == "SAC":
                self.model = SAC.load(str(model_path / 'model'))
            elif algorithm.upper() == "TD3":
                self.model = TD3.load(str(model_path / 'model'))
            elif algorithm.upper() == "DQN":
                self.model = DQN.load(str(model_path / 'model'))
            else:
                raise ValueError(f"Unsupported algorithm: {algorithm}")

            logger.info(f"RL model loaded from {path}")

        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            raise

    async def process_streaming_data(self, data: Dict[str, Any]) -> None:
        """Process streaming data for continuous learning."""
        try:
            # Extract environment data from streaming input
            observation = data.get('observation')
            action = data.get('action')
            reward = data.get('reward')
            done = data.get('done')

            if observation is None:
                return

            # For now, just log the data
            # In a full implementation, this would update the replay buffer
            logger.debug(f"Received streaming RL data: obs shape {np.array(observation).shape}, reward {reward}")

        except Exception as e:
            logger.error(f"Failed to process streaming data: {e}")

    async def health_check(self) -> bool:
        """Check if the trainer is healthy."""
        try:
            # Check if model is loaded
            if self.model is None:
                return False

            # Check if environment is setup
            if self.env is None:
                return False

            # Try a simple prediction
            obs = self.env.observation_space.sample()
            action, _states = self.model.predict(obs)

            return True

        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return False

    def create_custom_environment(self, env_config: Dict[str, Any]) -> gym.Env:
        """Create a custom environment from configuration."""
        try:
            # This is a placeholder for custom environment creation
            # In a real implementation, this would create environments
            # based on the ASI system's specific needs

            env_type = env_config.get('type', 'basic')

            if env_type == 'basic':
                # Create a basic custom environment
                return gym.make('CartPole-v1')
            else:
                raise ValueError(f"Unsupported custom environment type: {env_type}")

        except Exception as e:
            logger.error(f"Failed to create custom environment: {e}")
            raise

    def get_policy_info(self) -> Dict[str, Any]:
        """Get information about the current policy."""
        try:
            if not self.model:
                return {}

            info = {
                'algorithm': self.model.__class__.__name__,
                'policy_class': str(type(self.model.policy)),
                'learning_rate': self.model.learning_rate,
                'device': str(self.model.device),
            }

            # Add algorithm-specific info
            if hasattr(self.model, 'gamma'):
                info['gamma'] = self.model.gamma
            if hasattr(self.model, 'n_steps'):
                info['n_steps'] = self.model.n_steps
            if hasattr(self.model, 'batch_size'):
                info['batch_size'] = self.model.batch_size

            return info

        except Exception as e:
            logger.error(f"Failed to get policy info: {e}")
            return {}

    def close(self) -> None:
        """Close environments and clean up resources."""
        try:
            if self.env:
                self.env.close()
            if self.eval_env:
                self.eval_env.close()

            logger.info("RL trainer resources cleaned up")

        except Exception as e:
            logger.error(f"Failed to close RL trainer: {e}")
