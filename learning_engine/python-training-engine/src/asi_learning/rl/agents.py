"""
Advanced Reinforcement Learning Agents for ASI Learning Engine.

Provides state-of-the-art RL algorithms including:
- Deep Q-Networks (DQN) and variants
- Policy Gradient methods (PPO, A3C)
- Actor-Critic algorithms (SAC, TD3)
- Multi-agent reinforcement learning
- Hierarchical reinforcement learning
- Meta-learning and few-shot adaptation
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import gymnasium as gym
from stable_baselines3 import PPO, SAC, TD3, DQN, A2C
from stable_baselines3.common.env_util import make_vec_env
from stable_baselines3.common.callbacks import BaseCallback, EvalCallback
from stable_baselines3.common.monitor import Monitor
from stable_baselines3.common.vec_env import DummyVecEnv, SubprocVecEnv
from typing import Dict, List, Any, Optional, Tuple, Union, Callable
import logging
from dataclasses import dataclass
import pickle
import os
from collections import deque
import random

logger = logging.getLogger(__name__)


@dataclass
class RLConfig:
    """Configuration for RL agents."""
    algorithm: str = "PPO"  # PPO, SAC, TD3, <PERSON>Q<PERSON>, A2C
    total_timesteps: int = 1000000
    learning_rate: float = 3e-4
    batch_size: int = 64
    buffer_size: int = 1000000
    gamma: float = 0.99
    tau: float = 0.005
    exploration_fraction: float = 0.1
    exploration_final_eps: float = 0.02
    target_update_interval: int = 1
    train_freq: int = 4
    gradient_steps: int = 1
    n_episodes_rollout: int = -1
    policy_kwargs: Dict[str, Any] = None
    device: str = "auto"
    verbose: int = 1
    tensorboard_log: Optional[str] = None


class CustomDQN(nn.Module):
    """
    Custom Deep Q-Network implementation with advanced features.
    """
    
    def __init__(self, state_dim: int, action_dim: int, hidden_dims: List[int] = [256, 256]):
        super().__init__()
        self.state_dim = state_dim
        self.action_dim = action_dim
        
        # Build network layers
        layers = []
        input_dim = state_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(input_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.1)
            ])
            input_dim = hidden_dim
        
        layers.append(nn.Linear(input_dim, action_dim))
        
        self.network = nn.Sequential(*layers)
        
        # Dueling DQN components
        self.value_stream = nn.Sequential(
            nn.Linear(hidden_dims[-1], hidden_dims[-1] // 2),
            nn.ReLU(),
            nn.Linear(hidden_dims[-1] // 2, 1)
        )
        
        self.advantage_stream = nn.Sequential(
            nn.Linear(hidden_dims[-1], hidden_dims[-1] // 2),
            nn.ReLU(),
            nn.Linear(hidden_dims[-1] // 2, action_dim)
        )
    
    def forward(self, state: torch.Tensor, dueling: bool = True) -> torch.Tensor:
        """Forward pass through the network."""
        if dueling:
            # Extract features
            features = self.network[:-1](state)  # All layers except the last
            
            # Compute value and advantage
            value = self.value_stream(features)
            advantage = self.advantage_stream(features)
            
            # Combine using dueling architecture
            q_values = value + (advantage - advantage.mean(dim=1, keepdim=True))
            return q_values
        else:
            return self.network(state)


class ReplayBuffer:
    """
    Experience replay buffer for off-policy RL algorithms.
    """
    
    def __init__(self, capacity: int, state_dim: int, action_dim: int):
        self.capacity = capacity
        self.buffer = deque(maxlen=capacity)
        self.state_dim = state_dim
        self.action_dim = action_dim
    
    def push(self, state: np.ndarray, action: Union[int, np.ndarray], 
             reward: float, next_state: np.ndarray, done: bool):
        """Add experience to buffer."""
        experience = (state, action, reward, next_state, done)
        self.buffer.append(experience)
    
    def sample(self, batch_size: int) -> Tuple[torch.Tensor, ...]:
        """Sample a batch of experiences."""
        batch = random.sample(self.buffer, batch_size)
        
        states = torch.FloatTensor([e[0] for e in batch])
        actions = torch.LongTensor([e[1] for e in batch])
        rewards = torch.FloatTensor([e[2] for e in batch])
        next_states = torch.FloatTensor([e[3] for e in batch])
        dones = torch.BoolTensor([e[4] for e in batch])
        
        return states, actions, rewards, next_states, dones
    
    def __len__(self):
        return len(self.buffer)


class MultiAgentEnvironment:
    """
    Multi-agent environment wrapper for cooperative and competitive scenarios.
    """
    
    def __init__(self, env_name: str, num_agents: int = 2):
        self.env_name = env_name
        self.num_agents = num_agents
        self.agents = {}
        self.shared_reward = False
        
        # Create individual environments for each agent
        for i in range(num_agents):
            self.agents[f"agent_{i}"] = gym.make(env_name)
    
    def reset(self) -> Dict[str, np.ndarray]:
        """Reset all agents."""
        observations = {}
        for agent_id, env in self.agents.items():
            obs, _ = env.reset()
            observations[agent_id] = obs
        return observations
    
    def step(self, actions: Dict[str, Union[int, np.ndarray]]) -> Tuple[Dict, Dict, Dict, Dict]:
        """Step all agents."""
        observations = {}
        rewards = {}
        dones = {}
        infos = {}
        
        for agent_id, action in actions.items():
            obs, reward, terminated, truncated, info = self.agents[agent_id].step(action)
            observations[agent_id] = obs
            rewards[agent_id] = reward
            dones[agent_id] = terminated or truncated
            infos[agent_id] = info
        
        return observations, rewards, dones, infos
    
    def close(self):
        """Close all environments."""
        for env in self.agents.values():
            env.close()


class HierarchicalAgent:
    """
    Hierarchical Reinforcement Learning agent with high-level and low-level policies.
    """
    
    def __init__(self, state_dim: int, action_dim: int, goal_dim: int, config: RLConfig):
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.goal_dim = goal_dim
        self.config = config
        
        # High-level policy (goal selection)
        self.high_level_policy = PPO(
            "MlpPolicy",
            DummyVecEnv([lambda: gym.spaces.Box(low=-1, high=1, shape=(state_dim,))]),
            verbose=0
        )
        
        # Low-level policy (action execution)
        self.low_level_policy = SAC(
            "MlpPolicy",
            DummyVecEnv([lambda: gym.spaces.Box(low=-1, high=1, shape=(state_dim + goal_dim,))]),
            verbose=0
        )
        
        self.goal_buffer = deque(maxlen=10000)
        self.current_goal = None
        self.goal_achieved_threshold = 0.1
    
    def select_goal(self, state: np.ndarray) -> np.ndarray:
        """Select a high-level goal."""
        goal, _ = self.high_level_policy.predict(state, deterministic=False)
        return goal
    
    def select_action(self, state: np.ndarray, goal: np.ndarray) -> np.ndarray:
        """Select a low-level action given state and goal."""
        augmented_state = np.concatenate([state, goal])
        action, _ = self.low_level_policy.predict(augmented_state, deterministic=False)
        return action
    
    def train_hierarchical(self, env, total_timesteps: int):
        """Train the hierarchical agent."""
        state, _ = env.reset()
        episode_reward = 0
        episode_length = 0
        goal_length = 0
        max_goal_length = 50
        
        for timestep in range(total_timesteps):
            # Select new goal if needed
            if self.current_goal is None or goal_length >= max_goal_length:
                self.current_goal = self.select_goal(state)
                goal_length = 0
            
            # Select action
            action = self.select_action(state, self.current_goal)
            
            # Execute action
            next_state, reward, done, truncated, info = env.step(action)
            
            # Compute intrinsic reward for low-level policy
            intrinsic_reward = self._compute_intrinsic_reward(state, next_state, self.current_goal)
            
            # Store experience for low-level policy
            augmented_state = np.concatenate([state, self.current_goal])
            augmented_next_state = np.concatenate([next_state, self.current_goal])
            
            # Update policies (simplified - in practice, use proper training loops)
            episode_reward += reward
            episode_length += 1
            goal_length += 1
            
            state = next_state
            
            if done or truncated:
                state, _ = env.reset()
                episode_reward = 0
                episode_length = 0
                self.current_goal = None
    
    def _compute_intrinsic_reward(self, state: np.ndarray, next_state: np.ndarray, goal: np.ndarray) -> float:
        """Compute intrinsic reward based on goal achievement."""
        # Simple distance-based reward (can be more sophisticated)
        distance_to_goal = np.linalg.norm(next_state[:self.goal_dim] - goal)
        return -distance_to_goal


class MetaLearningAgent:
    """
    Meta-learning agent for few-shot adaptation to new tasks.
    """
    
    def __init__(self, state_dim: int, action_dim: int, config: RLConfig):
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.config = config
        
        # Meta-policy network
        self.meta_policy = nn.Sequential(
            nn.Linear(state_dim, 256),
            nn.ReLU(),
            nn.Linear(256, 256),
            nn.ReLU(),
            nn.Linear(256, action_dim)
        )
        
        # Task embedding network
        self.task_encoder = nn.Sequential(
            nn.Linear(state_dim + action_dim + 1, 128),  # state + action + reward
            nn.ReLU(),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Linear(64, 32)  # task embedding dimension
        )
        
        self.optimizer = optim.Adam(
            list(self.meta_policy.parameters()) + list(self.task_encoder.parameters()),
            lr=config.learning_rate
        )
        
        self.task_buffer = deque(maxlen=1000)
    
    def encode_task(self, experiences: List[Tuple]) -> torch.Tensor:
        """Encode task from a few experiences."""
        if not experiences:
            return torch.zeros(32)  # Default embedding
        
        embeddings = []
        for state, action, reward, next_state, done in experiences:
            experience_tensor = torch.cat([
                torch.FloatTensor(state),
                torch.FloatTensor([action]) if isinstance(action, (int, float)) else torch.FloatTensor(action),
                torch.FloatTensor([reward])
            ])
            embedding = self.task_encoder(experience_tensor)
            embeddings.append(embedding)
        
        # Average embeddings
        task_embedding = torch.stack(embeddings).mean(dim=0)
        return task_embedding
    
    def adapt_to_task(self, task_experiences: List[Tuple], adaptation_steps: int = 5):
        """Adapt to a new task using few-shot learning."""
        # Encode the task
        task_embedding = self.encode_task(task_experiences)
        
        # Fine-tune policy on task experiences
        for _ in range(adaptation_steps):
            for state, action, reward, next_state, done in task_experiences:
                # Augment state with task embedding
                augmented_state = torch.cat([
                    torch.FloatTensor(state),
                    task_embedding
                ])
                
                # Compute policy loss (simplified)
                predicted_action = self.meta_policy(augmented_state)
                target_action = torch.FloatTensor([action]) if isinstance(action, (int, float)) else torch.FloatTensor(action)
                
                loss = nn.MSELoss()(predicted_action, target_action)
                
                self.optimizer.zero_grad()
                loss.backward()
                self.optimizer.step()


class RLAgentManager:
    """
    Manager for multiple RL agents and training orchestration.
    """
    
    def __init__(self, config: RLConfig):
        self.config = config
        self.agents = {}
        self.environments = {}
        self.training_callbacks = []
    
    def create_agent(self, agent_id: str, env_name: str, algorithm: str = None) -> Any:
        """Create and register an RL agent."""
        algorithm = algorithm or self.config.algorithm
        
        # Create environment
        env = gym.make(env_name)
        env = Monitor(env)
        
        # Create agent based on algorithm
        if algorithm == "PPO":
            agent = PPO(
                "MlpPolicy",
                env,
                learning_rate=self.config.learning_rate,
                batch_size=self.config.batch_size,
                gamma=self.config.gamma,
                verbose=self.config.verbose,
                tensorboard_log=self.config.tensorboard_log,
                policy_kwargs=self.config.policy_kwargs or {}
            )
        elif algorithm == "SAC":
            agent = SAC(
                "MlpPolicy",
                env,
                learning_rate=self.config.learning_rate,
                buffer_size=self.config.buffer_size,
                batch_size=self.config.batch_size,
                gamma=self.config.gamma,
                tau=self.config.tau,
                verbose=self.config.verbose,
                tensorboard_log=self.config.tensorboard_log
            )
        elif algorithm == "TD3":
            agent = TD3(
                "MlpPolicy",
                env,
                learning_rate=self.config.learning_rate,
                buffer_size=self.config.buffer_size,
                batch_size=self.config.batch_size,
                gamma=self.config.gamma,
                tau=self.config.tau,
                verbose=self.config.verbose,
                tensorboard_log=self.config.tensorboard_log
            )
        elif algorithm == "DQN":
            agent = DQN(
                "MlpPolicy",
                env,
                learning_rate=self.config.learning_rate,
                buffer_size=self.config.buffer_size,
                batch_size=self.config.batch_size,
                gamma=self.config.gamma,
                exploration_fraction=self.config.exploration_fraction,
                exploration_final_eps=self.config.exploration_final_eps,
                target_update_interval=self.config.target_update_interval,
                verbose=self.config.verbose,
                tensorboard_log=self.config.tensorboard_log
            )
        else:
            raise ValueError(f"Unsupported algorithm: {algorithm}")
        
        self.agents[agent_id] = agent
        self.environments[agent_id] = env
        
        logger.info(f"Created {algorithm} agent: {agent_id}")
        return agent
    
    def train_agent(self, agent_id: str, total_timesteps: int = None) -> None:
        """Train a specific agent."""
        if agent_id not in self.agents:
            raise ValueError(f"Agent {agent_id} not found")
        
        agent = self.agents[agent_id]
        timesteps = total_timesteps or self.config.total_timesteps
        
        logger.info(f"Training agent {agent_id} for {timesteps} timesteps")
        
        # Setup callbacks
        eval_callback = EvalCallback(
            self.environments[agent_id],
            best_model_save_path=f"./models/{agent_id}/",
            log_path=f"./logs/{agent_id}/",
            eval_freq=10000,
            deterministic=True,
            render=False
        )
        
        # Train the agent
        agent.learn(
            total_timesteps=timesteps,
            callback=[eval_callback] + self.training_callbacks
        )
        
        logger.info(f"Training completed for agent {agent_id}")
    
    def evaluate_agent(self, agent_id: str, num_episodes: int = 10) -> Dict[str, float]:
        """Evaluate an agent's performance."""
        if agent_id not in self.agents:
            raise ValueError(f"Agent {agent_id} not found")
        
        agent = self.agents[agent_id]
        env = self.environments[agent_id]
        
        episode_rewards = []
        episode_lengths = []
        
        for episode in range(num_episodes):
            obs, _ = env.reset()
            episode_reward = 0
            episode_length = 0
            done = False
            
            while not done:
                action, _ = agent.predict(obs, deterministic=True)
                obs, reward, terminated, truncated, info = env.step(action)
                episode_reward += reward
                episode_length += 1
                done = terminated or truncated
            
            episode_rewards.append(episode_reward)
            episode_lengths.append(episode_length)
        
        return {
            'mean_reward': np.mean(episode_rewards),
            'std_reward': np.std(episode_rewards),
            'mean_length': np.mean(episode_lengths),
            'std_length': np.std(episode_lengths)
        }
    
    def save_agent(self, agent_id: str, path: str) -> None:
        """Save an agent's model."""
        if agent_id not in self.agents:
            raise ValueError(f"Agent {agent_id} not found")
        
        self.agents[agent_id].save(path)
        logger.info(f"Saved agent {agent_id} to {path}")
    
    def load_agent(self, agent_id: str, path: str, env_name: str) -> None:
        """Load an agent's model."""
        env = gym.make(env_name)
        env = Monitor(env)
        
        # Determine algorithm from path or config
        algorithm = self.config.algorithm
        
        if algorithm == "PPO":
            agent = PPO.load(path, env=env)
        elif algorithm == "SAC":
            agent = SAC.load(path, env=env)
        elif algorithm == "TD3":
            agent = TD3.load(path, env=env)
        elif algorithm == "DQN":
            agent = DQN.load(path, env=env)
        else:
            raise ValueError(f"Unsupported algorithm: {algorithm}")
        
        self.agents[agent_id] = agent
        self.environments[agent_id] = env
        
        logger.info(f"Loaded agent {agent_id} from {path}")
