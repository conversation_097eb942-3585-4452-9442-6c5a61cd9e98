"""
Advanced Computer Vision Processors for ASI Learning Engine.

Provides comprehensive computer vision capabilities including:
- Image preprocessing and augmentation
- Object detection with YOLOv8
- Image classification with CNNs
- Semantic segmentation
- Feature extraction and similarity search
- Real-time video processing
"""

import cv2
import numpy as np
import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
from torchvision import transforms, models
from ultralytics import YOLO
from PIL import Image, ImageDraw, ImageFont
from typing import List, Dict, Any, Optional, Tuple, Union
import logging
from dataclasses import dataclass
import albumentations as A
from albumentations.pytorch import ToTensorV2

logger = logging.getLogger(__name__)


@dataclass
class VisionConfig:
    """Configuration for vision processing."""
    input_size: Tuple[int, int] = (224, 224)
    batch_size: int = 32
    num_classes: int = 1000
    confidence_threshold: float = 0.5
    nms_threshold: float = 0.4
    device: str = "cuda" if torch.cuda.is_available() else "cpu"
    model_path: Optional[str] = None
    use_augmentation: bool = True


class ImagePreprocessor:
    """
    Advanced image preprocessing with augmentation capabilities.
    """
    
    def __init__(self, config: VisionConfig):
        self.config = config
        self.setup_transforms()
    
    def setup_transforms(self):
        """Setup image transformations."""
        # Basic transforms
        self.basic_transform = transforms.Compose([
            transforms.Resize(self.config.input_size),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        # Training transforms with augmentation
        if self.config.use_augmentation:
            self.train_transform = A.Compose([
                A.Resize(height=self.config.input_size[0], width=self.config.input_size[1]),
                A.HorizontalFlip(p=0.5),
                A.VerticalFlip(p=0.2),
                A.RandomRotate90(p=0.3),
                A.RandomBrightnessContrast(p=0.3),
                A.HueSaturationValue(p=0.3),
                A.GaussianBlur(blur_limit=3, p=0.2),
                A.CoarseDropout(max_holes=8, max_height=32, max_width=32, p=0.3),
                A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
                ToTensorV2()
            ])
        else:
            self.train_transform = A.Compose([
                A.Resize(height=self.config.input_size[0], width=self.config.input_size[1]),
                A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
                ToTensorV2()
            ])
        
        # Validation transforms
        self.val_transform = A.Compose([
            A.Resize(height=self.config.input_size[0], width=self.config.input_size[1]),
            A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
            ToTensorV2()
        ])
    
    def preprocess_image(self, image: Union[np.ndarray, Image.Image], training: bool = False) -> torch.Tensor:
        """Preprocess a single image."""
        if isinstance(image, Image.Image):
            image = np.array(image)
        
        if training and self.config.use_augmentation:
            transformed = self.train_transform(image=image)
            return transformed['image']
        else:
            transformed = self.val_transform(image=image)
            return transformed['image']
    
    def preprocess_batch(self, images: List[Union[np.ndarray, Image.Image]], training: bool = False) -> torch.Tensor:
        """Preprocess a batch of images."""
        processed_images = []
        for image in images:
            processed = self.preprocess_image(image, training)
            processed_images.append(processed)
        return torch.stack(processed_images)


class ObjectDetector:
    """
    Advanced object detection using YOLOv8.
    """
    
    def __init__(self, model_path: str = "yolov8n.pt", config: VisionConfig = None):
        self.config = config or VisionConfig()
        self.model = YOLO(model_path)
        self.class_names = self.model.names
        logger.info(f"Loaded YOLOv8 model: {model_path}")
    
    def detect_objects(self, image: Union[np.ndarray, str], return_crops: bool = False) -> Dict[str, Any]:
        """Detect objects in an image."""
        try:
            # Run inference
            results = self.model(image, conf=self.config.confidence_threshold)
            
            detections = []
            crops = []
            
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        # Extract box information
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        confidence = box.conf[0].cpu().numpy()
                        class_id = int(box.cls[0].cpu().numpy())
                        class_name = self.class_names[class_id]
                        
                        detection = {
                            'bbox': [float(x1), float(y1), float(x2), float(y2)],
                            'confidence': float(confidence),
                            'class_id': class_id,
                            'class_name': class_name
                        }
                        detections.append(detection)
                        
                        # Extract crop if requested
                        if return_crops and isinstance(image, np.ndarray):
                            crop = image[int(y1):int(y2), int(x1):int(x2)]
                            crops.append(crop)
            
            return {
                'detections': detections,
                'crops': crops if return_crops else None,
                'num_detections': len(detections)
            }
            
        except Exception as e:
            logger.error(f"Object detection failed: {e}")
            return {'detections': [], 'crops': [], 'num_detections': 0}
    
    def detect_batch(self, images: List[Union[np.ndarray, str]]) -> List[Dict[str, Any]]:
        """Detect objects in a batch of images."""
        results = []
        for image in images:
            result = self.detect_objects(image)
            results.append(result)
        return results
    
    def train_custom_model(self, dataset_path: str, epochs: int = 100, imgsz: int = 640):
        """Train a custom YOLOv8 model."""
        try:
            # Train the model
            results = self.model.train(
                data=dataset_path,
                epochs=epochs,
                imgsz=imgsz,
                device=self.config.device
            )
            logger.info("Custom model training completed")
            return results
        except Exception as e:
            logger.error(f"Training failed: {e}")
            raise


class ImageClassifier:
    """
    Advanced image classification with multiple CNN architectures.
    """
    
    def __init__(self, model_name: str = "resnet50", num_classes: int = 1000, pretrained: bool = True):
        self.model_name = model_name
        self.num_classes = num_classes
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Load model
        self.model = self._load_model(pretrained)
        self.model.to(self.device)
        self.model.eval()
        
        # Setup preprocessing
        self.preprocessor = ImagePreprocessor(VisionConfig())
        
        logger.info(f"Loaded {model_name} classifier with {num_classes} classes")
    
    def _load_model(self, pretrained: bool) -> nn.Module:
        """Load the specified model architecture."""
        if self.model_name == "resnet50":
            model = models.resnet50(pretrained=pretrained)
            if self.num_classes != 1000:
                model.fc = nn.Linear(model.fc.in_features, self.num_classes)
        elif self.model_name == "efficientnet_b0":
            model = models.efficientnet_b0(pretrained=pretrained)
            if self.num_classes != 1000:
                model.classifier[1] = nn.Linear(model.classifier[1].in_features, self.num_classes)
        elif self.model_name == "vit_b_16":
            model = models.vit_b_16(pretrained=pretrained)
            if self.num_classes != 1000:
                model.heads.head = nn.Linear(model.heads.head.in_features, self.num_classes)
        else:
            raise ValueError(f"Unsupported model: {self.model_name}")
        
        return model
    
    def classify_image(self, image: Union[np.ndarray, Image.Image]) -> Dict[str, Any]:
        """Classify a single image."""
        try:
            # Preprocess image
            input_tensor = self.preprocessor.preprocess_image(image, training=False)
            input_batch = input_tensor.unsqueeze(0).to(self.device)
            
            # Inference
            with torch.no_grad():
                outputs = self.model(input_batch)
                probabilities = torch.softmax(outputs, dim=1)
                confidence, predicted_class = torch.max(probabilities, 1)
            
            return {
                'predicted_class': predicted_class.item(),
                'confidence': confidence.item(),
                'probabilities': probabilities[0].cpu().numpy().tolist()
            }
            
        except Exception as e:
            logger.error(f"Image classification failed: {e}")
            return {'predicted_class': -1, 'confidence': 0.0, 'probabilities': []}
    
    def classify_batch(self, images: List[Union[np.ndarray, Image.Image]]) -> List[Dict[str, Any]]:
        """Classify a batch of images."""
        try:
            # Preprocess batch
            input_batch = self.preprocessor.preprocess_batch(images, training=False)
            input_batch = input_batch.to(self.device)
            
            # Inference
            with torch.no_grad():
                outputs = self.model(input_batch)
                probabilities = torch.softmax(outputs, dim=1)
                confidences, predicted_classes = torch.max(probabilities, 1)
            
            results = []
            for i in range(len(images)):
                results.append({
                    'predicted_class': predicted_classes[i].item(),
                    'confidence': confidences[i].item(),
                    'probabilities': probabilities[i].cpu().numpy().tolist()
                })
            
            return results
            
        except Exception as e:
            logger.error(f"Batch classification failed: {e}")
            return [{'predicted_class': -1, 'confidence': 0.0, 'probabilities': []} for _ in images]


class FeatureExtractor:
    """
    Extract deep features from images for similarity search and clustering.
    """
    
    def __init__(self, model_name: str = "resnet50"):
        self.model_name = model_name
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Load model and remove final classification layer
        if model_name == "resnet50":
            self.model = models.resnet50(pretrained=True)
            self.model = nn.Sequential(*list(self.model.children())[:-1])  # Remove FC layer
            self.feature_dim = 2048
        elif model_name == "efficientnet_b0":
            self.model = models.efficientnet_b0(pretrained=True)
            self.model = nn.Sequential(*list(self.model.children())[:-1])  # Remove classifier
            self.feature_dim = 1280
        else:
            raise ValueError(f"Unsupported model: {model_name}")
        
        self.model.to(self.device)
        self.model.eval()
        
        # Setup preprocessing
        self.preprocessor = ImagePreprocessor(VisionConfig())
        
        logger.info(f"Loaded {model_name} feature extractor")
    
    def extract_features(self, image: Union[np.ndarray, Image.Image]) -> np.ndarray:
        """Extract features from a single image."""
        try:
            # Preprocess image
            input_tensor = self.preprocessor.preprocess_image(image, training=False)
            input_batch = input_tensor.unsqueeze(0).to(self.device)
            
            # Extract features
            with torch.no_grad():
                features = self.model(input_batch)
                features = features.view(features.size(0), -1)  # Flatten
            
            return features.cpu().numpy()[0]
            
        except Exception as e:
            logger.error(f"Feature extraction failed: {e}")
            return np.zeros(self.feature_dim)
    
    def extract_batch_features(self, images: List[Union[np.ndarray, Image.Image]]) -> np.ndarray:
        """Extract features from a batch of images."""
        try:
            # Preprocess batch
            input_batch = self.preprocessor.preprocess_batch(images, training=False)
            input_batch = input_batch.to(self.device)
            
            # Extract features
            with torch.no_grad():
                features = self.model(input_batch)
                features = features.view(features.size(0), -1)  # Flatten
            
            return features.cpu().numpy()
            
        except Exception as e:
            logger.error(f"Batch feature extraction failed: {e}")
            return np.zeros((len(images), self.feature_dim))


class VideoProcessor:
    """
    Real-time video processing with object detection and tracking.
    """
    
    def __init__(self, config: VisionConfig = None):
        self.config = config or VisionConfig()
        self.detector = ObjectDetector()
        self.tracker = None  # Can be extended with tracking algorithms
    
    def process_video_file(self, video_path: str, output_path: str = None) -> List[Dict[str, Any]]:
        """Process a video file and return detection results."""
        cap = cv2.VideoCapture(video_path)
        
        if output_path:
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            fps = int(cap.get(cv2.CAP_PROP_FPS))
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
        
        frame_results = []
        frame_count = 0
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Detect objects in frame
            detections = self.detector.detect_objects(frame)
            
            # Draw bounding boxes
            annotated_frame = self._draw_detections(frame, detections['detections'])
            
            # Save frame result
            frame_results.append({
                'frame_number': frame_count,
                'detections': detections['detections'],
                'num_detections': detections['num_detections']
            })
            
            if output_path:
                out.write(annotated_frame)
            
            frame_count += 1
        
        cap.release()
        if output_path:
            out.release()
        
        logger.info(f"Processed {frame_count} frames")
        return frame_results
    
    def _draw_detections(self, frame: np.ndarray, detections: List[Dict[str, Any]]) -> np.ndarray:
        """Draw bounding boxes and labels on frame."""
        annotated_frame = frame.copy()
        
        for detection in detections:
            x1, y1, x2, y2 = detection['bbox']
            confidence = detection['confidence']
            class_name = detection['class_name']
            
            # Draw bounding box
            cv2.rectangle(annotated_frame, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 0), 2)
            
            # Draw label
            label = f"{class_name}: {confidence:.2f}"
            cv2.putText(annotated_frame, label, (int(x1), int(y1) - 10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
        
        return annotated_frame
