"""
Vision Trainer for the ASI Learning Engine.

Provides comprehensive computer vision model training with PyTorch,
including image classification, object detection with YOLOv8,
semantic segmentation, and custom CNN architectures.
"""

import os
import json
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable, Tuple
import asyncio

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import torchvision.transforms as transforms
import torchvision.models as models
from PIL import Image
import cv2
import numpy as np
from ultralytics import YOLO
import albumentations as A
from albumentations.pytorch import ToTensorV2
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, confusion_matrix

from ..utils.logger import get_logger, TrainingLogger
from ..utils.config import VisionConfig
from ..utils.metrics import MetricsCollector

logger = get_logger(__name__)


class VisionDataset(Dataset):
    """Custom dataset for vision tasks."""

    def __init__(
        self,
        image_paths: List[str],
        labels: List[int] = None,
        transform=None,
        task_type: str = "classification"
    ):
        self.image_paths = image_paths
        self.labels = labels
        self.transform = transform
        self.task_type = task_type

    def __len__(self):
        return len(self.image_paths)

    def __getitem__(self, idx):
        # Load image
        image_path = self.image_paths[idx]
        image = Image.open(image_path).convert('RGB')

        # Apply transforms
        if self.transform:
            if isinstance(self.transform, A.Compose):
                # Albumentations transform
                image_np = np.array(image)
                transformed = self.transform(image=image_np)
                image = transformed['image']
            else:
                # Torchvision transform
                image = self.transform(image)

        item = {'image': image}

        if self.labels is not None:
            item['label'] = torch.tensor(self.labels[idx], dtype=torch.long)

        return item


class VisionTrainer:
    """
    Advanced vision trainer for the ASI Learning Engine.

    Supports:
    - Image classification with CNNs
    - Object detection with YOLOv8
    - Semantic segmentation
    - Custom architectures
    - Data augmentation
    - Transfer learning
    """

    def __init__(self, config: VisionConfig):
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = None
        self.optimizer = None
        self.scheduler = None
        self.criterion = None
        self.training_logger = None

        logger.info(f"Vision Trainer initialized with device: {self.device}")

    def setup_model(self, task_type: str = "classification", num_classes: int = None) -> None:
        """Setup model for specific vision task."""
        try:
            num_classes = num_classes or self.config.num_classes

            if task_type == "classification":
                self._setup_classification_model(num_classes)
            elif task_type == "detection":
                self._setup_detection_model()
            elif task_type == "segmentation":
                self._setup_segmentation_model(num_classes)
            else:
                raise ValueError(f"Unsupported task type: {task_type}")

            # Move model to device
            self.model.to(self.device)

            logger.info(f"Model setup completed for task: {task_type}")

        except Exception as e:
            logger.error(f"Failed to setup model: {e}")
            raise

    def _setup_classification_model(self, num_classes: int) -> None:
        """Setup classification model."""
        if self.config.model_name == "resnet50":
            self.model = models.resnet50(pretrained=self.config.pretrained)
            if self.config.freeze_backbone:
                for param in self.model.parameters():
                    param.requires_grad = False
            self.model.fc = nn.Linear(self.model.fc.in_features, num_classes)

        elif self.config.model_name == "efficientnet_b0":
            self.model = models.efficientnet_b0(pretrained=self.config.pretrained)
            if self.config.freeze_backbone:
                for param in self.model.features.parameters():
                    param.requires_grad = False
            self.model.classifier[1] = nn.Linear(self.model.classifier[1].in_features, num_classes)

        elif self.config.model_name == "vit_b_16":
            self.model = models.vit_b_16(pretrained=self.config.pretrained)
            if self.config.freeze_backbone:
                for param in self.model.parameters():
                    param.requires_grad = False
            self.model.heads.head = nn.Linear(self.model.heads.head.in_features, num_classes)

        else:
            raise ValueError(f"Unsupported model: {self.config.model_name}")

        # Setup loss function
        self.criterion = nn.CrossEntropyLoss()

    def _setup_detection_model(self) -> None:
        """Setup object detection model with YOLOv8."""
        self.model = YOLO('yolov8n.pt')  # Start with nano model
        logger.info("YOLOv8 detection model loaded")

    def _setup_segmentation_model(self, num_classes: int) -> None:
        """Setup segmentation model."""
        # Use DeepLabV3 for segmentation
        self.model = models.segmentation.deeplabv3_resnet50(
            pretrained=self.config.pretrained,
            num_classes=num_classes
        )
        self.criterion = nn.CrossEntropyLoss()

    def setup_optimizer(self, learning_rate: float = None) -> None:
        """Setup optimizer and scheduler."""
        learning_rate = learning_rate or self.config.training.learning_rate

        if self.config.training.optimizer == "adam":
            self.optimizer = optim.Adam(
                self.model.parameters(),
                lr=learning_rate,
                weight_decay=1e-4
            )
        elif self.config.training.optimizer == "sgd":
            self.optimizer = optim.SGD(
                self.model.parameters(),
                lr=learning_rate,
                momentum=0.9,
                weight_decay=1e-4
            )
        else:
            raise ValueError(f"Unsupported optimizer: {self.config.training.optimizer}")

        # Setup scheduler
        if self.config.training.scheduler == "cosine":
            self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer,
                T_max=self.config.training.epochs
            )
        elif self.config.training.scheduler == "step":
            self.scheduler = optim.lr_scheduler.StepLR(
                self.optimizer,
                step_size=30,
                gamma=0.1
            )

    def get_transforms(self, is_training: bool = True) -> Any:
        """Get data transforms for training or validation."""
        if is_training:
            # Training transforms with augmentation
            transform = A.Compose([
                A.Resize(self.config.image_size, self.config.image_size),
                A.HorizontalFlip(p=0.5),
                A.RandomBrightnessContrast(p=0.3),
                A.ShiftScaleRotate(
                    shift_limit=0.1,
                    scale_limit=0.1,
                    rotate_limit=15,
                    p=0.3
                ),
                A.OneOf([
                    A.GaussNoise(p=1),
                    A.GaussianBlur(p=1),
                ], p=0.2),
                A.Normalize(
                    mean=[0.485, 0.456, 0.406],
                    std=[0.229, 0.224, 0.225]
                ),
                ToTensorV2(),
            ])
        else:
            # Validation transforms
            transform = A.Compose([
                A.Resize(self.config.image_size, self.config.image_size),
                A.Normalize(
                    mean=[0.485, 0.456, 0.406],
                    std=[0.229, 0.224, 0.225]
                ),
                ToTensorV2(),
            ])

        return transform

    def prepare_data(self, data: Dict[str, Any], task_type: str = "classification") -> Tuple[DataLoader, DataLoader]:
        """Prepare training and validation data loaders."""
        try:
            # Extract data
            train_images = data.get('train_images', [])
            train_labels = data.get('train_labels', [])
            val_images = data.get('val_images', [])
            val_labels = data.get('val_labels', [])

            # Get transforms
            train_transform = self.get_transforms(is_training=True)
            val_transform = self.get_transforms(is_training=False)

            # Create datasets
            train_dataset = VisionDataset(
                image_paths=train_images,
                labels=train_labels,
                transform=train_transform,
                task_type=task_type
            )

            val_dataset = None
            if val_images:
                val_dataset = VisionDataset(
                    image_paths=val_images,
                    labels=val_labels,
                    transform=val_transform,
                    task_type=task_type
                )

            # Create data loaders
            train_loader = DataLoader(
                train_dataset,
                batch_size=self.config.training.batch_size,
                shuffle=True,
                num_workers=4,
                pin_memory=True
            )

            val_loader = None
            if val_dataset:
                val_loader = DataLoader(
                    val_dataset,
                    batch_size=self.config.training.batch_size,
                    shuffle=False,
                    num_workers=4,
                    pin_memory=True
                )

            logger.info(f"Prepared data loaders - Train: {len(train_dataset)}, Val: {len(val_dataset) if val_dataset else 0}")

            return train_loader, val_loader

        except Exception as e:
            logger.error(f"Failed to prepare data: {e}")
            raise

    def train_epoch(self, train_loader: DataLoader, epoch: int, hooks: Dict[str, Callable] = None) -> Dict[str, float]:
        """Train for one epoch."""
        self.model.train()
        running_loss = 0.0
        correct = 0
        total = 0

        if hooks and 'on_epoch_start' in hooks:
            hooks['on_epoch_start'](epoch)

        for batch_idx, batch in enumerate(train_loader):
            images = batch['image'].to(self.device)
            labels = batch['label'].to(self.device)

            # Zero gradients
            self.optimizer.zero_grad()

            # Forward pass
            outputs = self.model(images)
            loss = self.criterion(outputs, labels)

            # Backward pass
            loss.backward()

            # Gradient clipping
            if self.config.training.gradient_clip_norm > 0:
                torch.nn.utils.clip_grad_norm_(
                    self.model.parameters(),
                    self.config.training.gradient_clip_norm
                )

            self.optimizer.step()

            # Statistics
            running_loss += loss.item()
            _, predicted = outputs.max(1)
            total += labels.size(0)
            correct += predicted.eq(labels).sum().item()

            # Log batch metrics
            if hooks and 'on_batch_end' in hooks and batch_idx % 100 == 0:
                hooks['on_batch_end'](batch_idx, loss.item())

        # Calculate epoch metrics
        epoch_loss = running_loss / len(train_loader)
        epoch_acc = 100. * correct / total

        metrics = {
            'train_loss': epoch_loss,
            'train_accuracy': epoch_acc,
        }

        if hooks and 'on_epoch_end' in hooks:
            hooks['on_epoch_end'](epoch, metrics)

        return metrics

    def validate_epoch(self, val_loader: DataLoader, hooks: Dict[str, Callable] = None) -> Dict[str, float]:
        """Validate for one epoch."""
        self.model.eval()
        running_loss = 0.0
        correct = 0
        total = 0
        all_predictions = []
        all_labels = []

        with torch.no_grad():
            for batch in val_loader:
                images = batch['image'].to(self.device)
                labels = batch['label'].to(self.device)

                outputs = self.model(images)
                loss = self.criterion(outputs, labels)

                running_loss += loss.item()
                _, predicted = outputs.max(1)
                total += labels.size(0)
                correct += predicted.eq(labels).sum().item()

                all_predictions.extend(predicted.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())

        # Calculate metrics
        val_loss = running_loss / len(val_loader)
        val_acc = 100. * correct / total

        # Calculate additional metrics
        precision, recall, f1, _ = precision_recall_fscore_support(
            all_labels, all_predictions, average='weighted', zero_division=0
        )

        metrics = {
            'val_loss': val_loss,
            'val_accuracy': val_acc,
            'val_precision': precision,
            'val_recall': recall,
            'val_f1': f1,
        }

        if hooks and 'on_validation' in hooks:
            hooks['on_validation'](metrics)

        return metrics

    def train(
        self,
        config: Dict[str, Any],
        hooks: Dict[str, Callable] = None,
        job_id: str = None
    ) -> Dict[str, Any]:
        """Train the vision model."""
        try:
            # Setup training logger
            if job_id:
                self.training_logger = TrainingLogger(job_id, "vision")

            # Setup model
            task_type = config.get('task_type', 'classification')
            num_classes = config.get('num_classes', self.config.num_classes)
            self.setup_model(task_type, num_classes)

            # Setup optimizer
            self.setup_optimizer(config.get('learning_rate'))

            # Prepare data
            train_loader, val_loader = self.prepare_data(config['data'], task_type)

            # Training loop
            best_val_acc = 0.0
            patience_counter = 0

            for epoch in range(self.config.training.epochs):
                if self.training_logger:
                    self.training_logger.log_epoch_start(epoch, self.config.training.epochs)

                # Train epoch
                train_metrics = self.train_epoch(train_loader, epoch, hooks)

                # Validate epoch
                val_metrics = {}
                if val_loader:
                    val_metrics = self.validate_epoch(val_loader, hooks)

                    # Early stopping
                    if val_metrics['val_accuracy'] > best_val_acc:
                        best_val_acc = val_metrics['val_accuracy']
                        patience_counter = 0
                        # Save best model
                        self.save_checkpoint(config.get('checkpoint_dir', './checkpoints'), epoch, is_best=True)
                    else:
                        patience_counter += 1
                        if patience_counter >= self.config.training.early_stopping_patience:
                            logger.info(f"Early stopping at epoch {epoch}")
                            break

                # Update scheduler
                if self.scheduler:
                    self.scheduler.step()

                # Log metrics
                all_metrics = {**train_metrics, **val_metrics}
                if self.training_logger:
                    self.training_logger.log_epoch_end(epoch, all_metrics)

                # Save checkpoint
                if epoch % self.config.training.checkpoint_frequency == 0:
                    self.save_checkpoint(config.get('checkpoint_dir', './checkpoints'), epoch)

            # Save final model
            model_path = Path(config.get('model_save_path', './models/vision_model'))
            model_path.mkdir(parents=True, exist_ok=True)
            self.save_model(str(model_path))

            if self.training_logger:
                self.training_logger.log_model_save(str(model_path), epoch)

            results = {
                'best_val_accuracy': best_val_acc,
                'final_epoch': epoch,
                'model_path': str(model_path),
            }

            logger.info(f"Training completed successfully: {results}")
            return results

        except Exception as e:
            if self.training_logger:
                self.training_logger.log_error(e, "training")
            logger.error(f"Training failed: {e}")
            raise

    def predict(self, image_paths: List[str]) -> List[Dict[str, Any]]:
        """Make predictions on new images."""
        try:
            if not self.model:
                raise ValueError("Model not loaded. Call setup_model() first.")

            self.model.eval()
            predictions = []
            transform = self.get_transforms(is_training=False)

            with torch.no_grad():
                for image_path in image_paths:
                    # Load and preprocess image
                    image = Image.open(image_path).convert('RGB')
                    image_np = np.array(image)
                    transformed = transform(image=image_np)
                    image_tensor = transformed['image'].unsqueeze(0).to(self.device)

                    # Predict
                    outputs = self.model(image_tensor)
                    probabilities = torch.softmax(outputs, dim=1)
                    predicted_class = torch.argmax(outputs, dim=1).item()
                    confidence = probabilities[0][predicted_class].item()

                    predictions.append({
                        'image_path': image_path,
                        'predicted_class': predicted_class,
                        'confidence': confidence,
                        'probabilities': probabilities[0].cpu().numpy().tolist()
                    })

            return predictions

        except Exception as e:
            logger.error(f"Prediction failed: {e}")
            return []

    def save_checkpoint(self, checkpoint_dir: str, epoch: int, is_best: bool = False) -> None:
        """Save model checkpoint."""
        try:
            checkpoint_path = Path(checkpoint_dir)
            checkpoint_path.mkdir(parents=True, exist_ok=True)

            checkpoint = {
                'epoch': epoch,
                'model_state_dict': self.model.state_dict(),
                'optimizer_state_dict': self.optimizer.state_dict(),
                'scheduler_state_dict': self.scheduler.state_dict() if self.scheduler else None,
                'config': self.config,
            }

            # Save regular checkpoint
            torch.save(checkpoint, checkpoint_path / f'checkpoint_epoch_{epoch}.pth')

            # Save best checkpoint
            if is_best:
                torch.save(checkpoint, checkpoint_path / 'best_model.pth')

            logger.info(f"Checkpoint saved: epoch {epoch}")

        except Exception as e:
            logger.error(f"Failed to save checkpoint: {e}")

    def load_checkpoint(self, checkpoint_path: str) -> int:
        """Load model checkpoint."""
        try:
            checkpoint = torch.load(checkpoint_path, map_location=self.device)

            self.model.load_state_dict(checkpoint['model_state_dict'])
            if self.optimizer:
                self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            if self.scheduler and checkpoint['scheduler_state_dict']:
                self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])

            epoch = checkpoint['epoch']
            logger.info(f"Checkpoint loaded: epoch {epoch}")
            return epoch

        except Exception as e:
            logger.error(f"Failed to load checkpoint: {e}")
            return 0

    def save_model(self, path: str) -> None:
        """Save the trained model."""
        try:
            model_path = Path(path)
            model_path.mkdir(parents=True, exist_ok=True)

            # Save model state dict
            torch.save(self.model.state_dict(), model_path / 'model.pth')

            # Save config
            with open(model_path / 'config.json', 'w') as f:
                json.dump(self.config.__dict__, f, indent=2)

            logger.info(f"Model saved to {path}")

        except Exception as e:
            logger.error(f"Failed to save model: {e}")

    def load_model(self, path: str, task_type: str = "classification") -> None:
        """Load a trained model."""
        try:
            model_path = Path(path)

            # Load config
            with open(model_path / 'config.json', 'r') as f:
                config_dict = json.load(f)

            # Setup model
            num_classes = config_dict.get('num_classes', self.config.num_classes)
            self.setup_model(task_type, num_classes)

            # Load state dict
            self.model.load_state_dict(torch.load(model_path / 'model.pth', map_location=self.device))
            self.model.eval()

            logger.info(f"Model loaded from {path}")

        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            raise

    async def process_streaming_data(self, data: Dict[str, Any]) -> None:
        """Process streaming data for continuous learning."""
        try:
            # Extract image data from streaming input
            image_data = data.get('image_data')
            label = data.get('label')

            if not image_data:
                return

            # For now, just log the data
            # In a full implementation, this would update the model
            logger.debug(f"Received streaming image data with label: {label}")

        except Exception as e:
            logger.error(f"Failed to process streaming data: {e}")

    async def health_check(self) -> bool:
        """Check if the trainer is healthy."""
        try:
            # Check if model is loaded
            if self.model is None:
                return False

            # Check if model is on correct device
            if next(self.model.parameters()).device != self.device:
                return False

            # Try a simple forward pass
            dummy_input = torch.randn(1, 3, self.config.image_size, self.config.image_size).to(self.device)
            with torch.no_grad():
                _ = self.model(dummy_input)

            return True

        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return False
