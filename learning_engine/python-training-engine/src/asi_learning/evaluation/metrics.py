"""
Specialized metrics computation for different model types in the ASI Learning Engine.

Provides domain-specific metrics for NLP, Vision, RL, and Abstraction models
with comprehensive evaluation capabilities.
"""

import numpy as np
from typing import Dict, List, Any, Optional, Union
from abc import ABC, abstractmethod
import torch
from sklearn.metrics import (
    accuracy_score, precision_recall_fscore_support, confusion_matrix,
    mean_squared_error, mean_absolute_error, r2_score,
    roc_auc_score, average_precision_score
)

from ..utils.logger import get_logger

logger = get_logger(__name__)


class BaseMetrics(ABC):
    """Base class for metrics computation."""
    
    @abstractmethod
    def compute_metrics(
        self,
        predictions: List[Any],
        targets: List[Any],
        outputs: List[Any] = None
    ) -> Dict[str, float]:
        """Compute metrics for the given predictions and targets."""
        pass


class ClassificationMetrics(BaseMetrics):
    """Standard classification metrics."""
    
    def compute_metrics(
        self,
        predictions: List[int],
        targets: List[int],
        outputs: List[Any] = None
    ) -> Dict[str, float]:
        """Compute classification metrics."""
        try:
            metrics = {}
            
            # Basic metrics
            metrics['accuracy'] = accuracy_score(targets, predictions)
            
            # Precision, recall, F1
            precision, recall, f1, _ = precision_recall_fscore_support(
                targets, predictions, average='weighted', zero_division=0
            )
            metrics['precision'] = precision
            metrics['recall'] = recall
            metrics['f1'] = f1
            
            # Macro averages
            precision_macro, recall_macro, f1_macro, _ = precision_recall_fscore_support(
                targets, predictions, average='macro', zero_division=0
            )
            metrics['precision_macro'] = precision_macro
            metrics['recall_macro'] = recall_macro
            metrics['f1_macro'] = f1_macro
            
            # Per-class metrics if binary classification
            unique_labels = np.unique(targets)
            if len(unique_labels) == 2:
                # Binary classification specific metrics
                if outputs is not None and len(outputs) > 0:
                    # Convert outputs to probabilities if needed
                    if isinstance(outputs[0], (list, np.ndarray)) and len(outputs[0]) > 1:
                        probs = [o[1] if len(o) > 1 else o[0] for o in outputs]
                    else:
                        probs = outputs
                    
                    try:
                        metrics['auc_roc'] = roc_auc_score(targets, probs)
                        metrics['auc_pr'] = average_precision_score(targets, probs)
                    except Exception as e:
                        logger.warning(f"Failed to compute AUC metrics: {e}")
            
            return metrics
            
        except Exception as e:
            logger.error(f"Failed to compute classification metrics: {e}")
            return {}


class RegressionMetrics(BaseMetrics):
    """Standard regression metrics."""
    
    def compute_metrics(
        self,
        predictions: List[float],
        targets: List[float],
        outputs: List[Any] = None
    ) -> Dict[str, float]:
        """Compute regression metrics."""
        try:
            metrics = {}
            
            # Convert to numpy arrays
            pred_array = np.array(predictions)
            target_array = np.array(targets)
            
            # Basic metrics
            metrics['mse'] = mean_squared_error(target_array, pred_array)
            metrics['rmse'] = np.sqrt(metrics['mse'])
            metrics['mae'] = mean_absolute_error(target_array, pred_array)
            metrics['r2'] = r2_score(target_array, pred_array)
            
            # Additional metrics
            metrics['mean_absolute_percentage_error'] = np.mean(
                np.abs((target_array - pred_array) / np.maximum(np.abs(target_array), 1e-8))
            ) * 100
            
            # Explained variance
            metrics['explained_variance'] = 1 - np.var(target_array - pred_array) / np.var(target_array)
            
            return metrics
            
        except Exception as e:
            logger.error(f"Failed to compute regression metrics: {e}")
            return {}


class VisionMetrics(BaseMetrics):
    """Computer vision specific metrics."""
    
    def compute_metrics(
        self,
        predictions: List[Any],
        targets: List[Any],
        outputs: List[Any] = None
    ) -> Dict[str, float]:
        """Compute vision-specific metrics."""
        try:
            metrics = {}
            
            # Start with standard classification metrics
            if all(isinstance(p, (int, np.integer)) for p in predictions):
                base_metrics = ClassificationMetrics().compute_metrics(predictions, targets, outputs)
                metrics.update(base_metrics)
            
            # Add vision-specific metrics
            if len(np.unique(targets)) > 2:  # Multi-class
                # Top-k accuracy
                if outputs is not None:
                    metrics.update(self._compute_topk_accuracy(outputs, targets, k_values=[1, 3, 5]))
            
            # Per-class accuracy for detailed analysis
            unique_classes = np.unique(targets)
            if len(unique_classes) <= 20:  # Only for reasonable number of classes
                per_class_acc = self._compute_per_class_accuracy(predictions, targets)
                for class_id, acc in per_class_acc.items():
                    metrics[f'accuracy_class_{class_id}'] = acc
            
            return metrics
            
        except Exception as e:
            logger.error(f"Failed to compute vision metrics: {e}")
            return {}
    
    def _compute_topk_accuracy(
        self,
        outputs: List[Any],
        targets: List[int],
        k_values: List[int] = [1, 3, 5]
    ) -> Dict[str, float]:
        """Compute top-k accuracy."""
        metrics = {}
        
        try:
            # Convert outputs to numpy array
            if isinstance(outputs[0], torch.Tensor):
                output_array = torch.stack(outputs).cpu().numpy()
            else:
                output_array = np.array(outputs)
            
            target_array = np.array(targets)
            
            for k in k_values:
                if k <= output_array.shape[1]:
                    # Get top-k predictions
                    topk_preds = np.argsort(output_array, axis=1)[:, -k:]
                    
                    # Check if target is in top-k
                    correct = 0
                    for i, target in enumerate(target_array):
                        if target in topk_preds[i]:
                            correct += 1
                    
                    metrics[f'top_{k}_accuracy'] = correct / len(target_array)
            
        except Exception as e:
            logger.warning(f"Failed to compute top-k accuracy: {e}")
        
        return metrics
    
    def _compute_per_class_accuracy(
        self,
        predictions: List[int],
        targets: List[int]
    ) -> Dict[int, float]:
        """Compute per-class accuracy."""
        per_class_acc = {}
        
        try:
            unique_classes = np.unique(targets)
            
            for class_id in unique_classes:
                class_mask = np.array(targets) == class_id
                class_predictions = np.array(predictions)[class_mask]
                class_targets = np.array(targets)[class_mask]
                
                if len(class_targets) > 0:
                    per_class_acc[int(class_id)] = accuracy_score(class_targets, class_predictions)
        
        except Exception as e:
            logger.warning(f"Failed to compute per-class accuracy: {e}")
        
        return per_class_acc


class NLPMetrics(BaseMetrics):
    """Natural Language Processing specific metrics."""
    
    def compute_metrics(
        self,
        predictions: List[Any],
        targets: List[Any],
        outputs: List[Any] = None
    ) -> Dict[str, float]:
        """Compute NLP-specific metrics."""
        try:
            metrics = {}
            
            # Determine task type based on prediction format
            if all(isinstance(p, (int, np.integer)) for p in predictions):
                # Classification task
                base_metrics = ClassificationMetrics().compute_metrics(predictions, targets, outputs)
                metrics.update(base_metrics)
                
                # Add NLP-specific classification metrics
                if len(np.unique(targets)) == 2:
                    # Binary classification (e.g., sentiment analysis)
                    metrics.update(self._compute_binary_nlp_metrics(predictions, targets))
            
            elif all(isinstance(p, str) for p in predictions):
                # Text generation task
                metrics.update(self._compute_generation_metrics(predictions, targets))
            
            elif all(isinstance(p, (list, np.ndarray)) for p in predictions):
                # Sequence labeling task (e.g., NER)
                metrics.update(self._compute_sequence_metrics(predictions, targets))
            
            return metrics
            
        except Exception as e:
            logger.error(f"Failed to compute NLP metrics: {e}")
            return {}
    
    def _compute_binary_nlp_metrics(
        self,
        predictions: List[int],
        targets: List[int]
    ) -> Dict[str, float]:
        """Compute binary classification NLP metrics."""
        metrics = {}
        
        try:
            # Confusion matrix for detailed analysis
            cm = confusion_matrix(targets, predictions)
            if cm.shape == (2, 2):
                tn, fp, fn, tp = cm.ravel()
                
                # Specificity and sensitivity
                metrics['specificity'] = tn / (tn + fp) if (tn + fp) > 0 else 0
                metrics['sensitivity'] = tp / (tp + fn) if (tp + fn) > 0 else 0
                
                # Balanced accuracy
                metrics['balanced_accuracy'] = (metrics['sensitivity'] + metrics['specificity']) / 2
        
        except Exception as e:
            logger.warning(f"Failed to compute binary NLP metrics: {e}")
        
        return metrics
    
    def _compute_generation_metrics(
        self,
        predictions: List[str],
        targets: List[str]
    ) -> Dict[str, float]:
        """Compute text generation metrics."""
        metrics = {}
        
        try:
            # BLEU score (simplified implementation)
            bleu_scores = []
            for pred, target in zip(predictions, targets):
                bleu = self._compute_bleu(pred, target)
                bleu_scores.append(bleu)
            
            metrics['bleu'] = np.mean(bleu_scores)
            
            # Exact match
            exact_matches = sum(1 for p, t in zip(predictions, targets) if p.strip() == t.strip())
            metrics['exact_match'] = exact_matches / len(predictions)
            
            # Average length ratio
            length_ratios = [len(p.split()) / max(len(t.split()), 1) for p, t in zip(predictions, targets)]
            metrics['length_ratio'] = np.mean(length_ratios)
        
        except Exception as e:
            logger.warning(f"Failed to compute generation metrics: {e}")
        
        return metrics
    
    def _compute_sequence_metrics(
        self,
        predictions: List[List[Any]],
        targets: List[List[Any]]
    ) -> Dict[str, float]:
        """Compute sequence labeling metrics."""
        metrics = {}
        
        try:
            # Flatten sequences for token-level evaluation
            flat_predictions = [item for seq in predictions for item in seq]
            flat_targets = [item for seq in targets for item in seq]
            
            # Token-level accuracy
            metrics['token_accuracy'] = accuracy_score(flat_targets, flat_predictions)
            
            # Sequence-level accuracy
            seq_correct = sum(1 for p, t in zip(predictions, targets) if p == t)
            metrics['sequence_accuracy'] = seq_correct / len(predictions)
        
        except Exception as e:
            logger.warning(f"Failed to compute sequence metrics: {e}")
        
        return metrics
    
    def _compute_bleu(self, prediction: str, target: str) -> float:
        """Compute simplified BLEU score."""
        try:
            pred_tokens = prediction.lower().split()
            target_tokens = target.lower().split()
            
            if not pred_tokens or not target_tokens:
                return 0.0
            
            # Simple 1-gram BLEU
            matches = sum(1 for token in pred_tokens if token in target_tokens)
            return matches / len(pred_tokens)
        
        except Exception:
            return 0.0


class RLMetrics(BaseMetrics):
    """Reinforcement Learning specific metrics."""
    
    def compute_metrics(
        self,
        predictions: List[Any],
        targets: List[Any],
        outputs: List[Any] = None
    ) -> Dict[str, float]:
        """Compute RL-specific metrics."""
        try:
            metrics = {}
            
            # Assume predictions are episode rewards or returns
            if all(isinstance(p, (int, float)) for p in predictions):
                rewards = np.array(predictions)
                
                # Basic statistics
                metrics['mean_reward'] = np.mean(rewards)
                metrics['std_reward'] = np.std(rewards)
                metrics['min_reward'] = np.min(rewards)
                metrics['max_reward'] = np.max(rewards)
                metrics['median_reward'] = np.median(rewards)
                
                # Success rate (assuming positive rewards indicate success)
                metrics['success_rate'] = np.mean(rewards > 0)
                
                # Stability metrics
                if len(rewards) > 1:
                    metrics['reward_stability'] = 1.0 / (1.0 + np.std(rewards))
            
            return metrics
            
        except Exception as e:
            logger.error(f"Failed to compute RL metrics: {e}")
            return {}


class AbstractionMetrics(BaseMetrics):
    """Abstraction and meta-learning specific metrics."""
    
    def compute_metrics(
        self,
        predictions: List[Any],
        targets: List[Any],
        outputs: List[Any] = None
    ) -> Dict[str, float]:
        """Compute abstraction-specific metrics."""
        try:
            metrics = {}
            
            # For meta-learning tasks, compute adaptation metrics
            if isinstance(predictions[0], dict) and 'adaptation_accuracy' in predictions[0]:
                # Meta-learning evaluation
                adaptation_accuracies = [p['adaptation_accuracy'] for p in predictions]
                metrics['mean_adaptation_accuracy'] = np.mean(adaptation_accuracies)
                metrics['std_adaptation_accuracy'] = np.std(adaptation_accuracies)
                
                # Few-shot learning metrics
                if 'few_shot_accuracy' in predictions[0]:
                    few_shot_accuracies = [p['few_shot_accuracy'] for p in predictions]
                    metrics['mean_few_shot_accuracy'] = np.mean(few_shot_accuracies)
            
            else:
                # Standard classification for abstraction tasks
                if all(isinstance(p, (int, np.integer)) for p in predictions):
                    base_metrics = ClassificationMetrics().compute_metrics(predictions, targets, outputs)
                    metrics.update(base_metrics)
            
            return metrics
            
        except Exception as e:
            logger.error(f"Failed to compute abstraction metrics: {e}")
            return {}
