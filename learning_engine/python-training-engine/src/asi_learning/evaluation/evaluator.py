"""
Central model evaluator for the ASI Learning Engine.

Provides unified evaluation interface for all model types with comprehensive
metrics computation, benchmark testing, and performance analysis.
"""

import asyncio
import json
import time
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass
import numpy as np
import torch
from torch.utils.data import DataLoader

from ..utils.logger import get_logger
from ..utils.config import EvaluationConfig
from ..utils.metrics import MetricsCollector
from .metrics import (
    ClassificationMetrics, RegressionMetrics, VisionMetrics,
    NLPMetrics, RLMetrics, AbstractionMetrics
)

logger = get_logger(__name__)


@dataclass
class EvaluationResult:
    """Container for evaluation results."""
    model_type: str
    model_path: str
    dataset_name: str
    metrics: Dict[str, float]
    predictions: Optional[List[Any]] = None
    confusion_matrix: Optional[np.ndarray] = None
    evaluation_time: float = 0.0
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class ModelEvaluator:
    """
    Central model evaluator for the ASI Learning Engine.
    
    Features:
    - Multi-modal model evaluation
    - Comprehensive metrics computation
    - Benchmark dataset testing
    - Performance profiling
    - Model comparison and analysis
    """
    
    def __init__(self, config: EvaluationConfig):
        self.config = config
        self.metrics_collector = None
        
        # Initialize metric calculators
        self.metric_calculators = {
            'classification': ClassificationMetrics(),
            'regression': RegressionMetrics(),
            'vision': VisionMetrics(),
            'nlp': NLPMetrics(),
            'rl': RLMetrics(),
            'abstraction': AbstractionMetrics(),
        }
        
        logger.info("Model evaluator initialized")
    
    def set_metrics_collector(self, metrics_collector: MetricsCollector) -> None:
        """Set metrics collector for reporting."""
        self.metrics_collector = metrics_collector
    
    async def evaluate_model(
        self,
        model: torch.nn.Module,
        dataloader: DataLoader,
        model_type: str,
        task_type: str = "classification",
        model_path: str = "",
        dataset_name: str = "test"
    ) -> EvaluationResult:
        """Evaluate a model on a dataset."""
        start_time = time.time()
        
        try:
            logger.info(f"Starting evaluation for {model_type} model on {dataset_name}")
            
            # Set model to evaluation mode
            model.eval()
            device = next(model.parameters()).device
            
            # Collect predictions and targets
            all_predictions = []
            all_targets = []
            all_outputs = []
            
            with torch.no_grad():
                for batch_idx, batch in enumerate(dataloader):
                    # Move batch to device
                    if isinstance(batch, (list, tuple)):
                        inputs, targets = batch[0].to(device), batch[1].to(device)
                    else:
                        inputs = batch['input'].to(device)
                        targets = batch['target'].to(device)
                    
                    # Forward pass
                    outputs = model(inputs)
                    
                    # Store results
                    if task_type == "classification":
                        predictions = torch.argmax(outputs, dim=1)
                        all_predictions.extend(predictions.cpu().numpy())
                        all_targets.extend(targets.cpu().numpy())
                    elif task_type == "regression":
                        all_predictions.extend(outputs.cpu().numpy())
                        all_targets.extend(targets.cpu().numpy())
                    
                    all_outputs.extend(outputs.cpu().numpy())
                    
                    # Log progress
                    if batch_idx % 100 == 0:
                        logger.debug(f"Evaluated batch {batch_idx}/{len(dataloader)}")
            
            # Compute metrics
            metrics = await self._compute_metrics(
                predictions=all_predictions,
                targets=all_targets,
                outputs=all_outputs,
                model_type=model_type,
                task_type=task_type
            )
            
            # Compute confusion matrix if requested
            confusion_matrix = None
            if self.config.compute_confusion_matrix and task_type == "classification":
                confusion_matrix = self._compute_confusion_matrix(all_predictions, all_targets)
            
            evaluation_time = time.time() - start_time
            
            # Create result
            result = EvaluationResult(
                model_type=model_type,
                model_path=model_path,
                dataset_name=dataset_name,
                metrics=metrics,
                predictions=all_predictions if self.config.save_predictions else None,
                confusion_matrix=confusion_matrix,
                evaluation_time=evaluation_time,
                metadata={
                    'task_type': task_type,
                    'num_samples': len(all_predictions),
                    'device': str(device),
                    'batch_size': dataloader.batch_size
                }
            )
            
            # Report metrics
            if self.metrics_collector:
                for metric_name, value in metrics.items():
                    self.metrics_collector.record_metric(
                        f"evaluation.{model_type}.{metric_name}",
                        value,
                        tags={
                            'model_type': model_type,
                            'dataset': dataset_name,
                            'task_type': task_type
                        }
                    )
            
            logger.info(f"Evaluation completed in {evaluation_time:.2f}s - {metrics}")
            return result
            
        except Exception as e:
            logger.error(f"Evaluation failed: {e}")
            raise
    
    async def _compute_metrics(
        self,
        predictions: List[Any],
        targets: List[Any],
        outputs: List[Any],
        model_type: str,
        task_type: str
    ) -> Dict[str, float]:
        """Compute evaluation metrics based on model and task type."""
        metrics = {}
        
        try:
            # Get appropriate metric calculator
            if model_type in self.metric_calculators:
                calculator = self.metric_calculators[model_type]
            elif task_type in self.metric_calculators:
                calculator = self.metric_calculators[task_type]
            else:
                calculator = self.metric_calculators['classification']  # Default
            
            # Compute metrics
            computed_metrics = calculator.compute_metrics(
                predictions=predictions,
                targets=targets,
                outputs=outputs
            )
            
            metrics.update(computed_metrics)
            
            # Add standard metrics if not already computed
            if task_type == "classification":
                if 'accuracy' not in metrics:
                    metrics['accuracy'] = self._compute_accuracy(predictions, targets)
                if 'precision' not in metrics:
                    metrics['precision'] = self._compute_precision(predictions, targets)
                if 'recall' not in metrics:
                    metrics['recall'] = self._compute_recall(predictions, targets)
                if 'f1' not in metrics:
                    metrics['f1'] = self._compute_f1(predictions, targets)
            
            elif task_type == "regression":
                if 'mse' not in metrics:
                    metrics['mse'] = self._compute_mse(predictions, targets)
                if 'mae' not in metrics:
                    metrics['mae'] = self._compute_mae(predictions, targets)
                if 'r2' not in metrics:
                    metrics['r2'] = self._compute_r2(predictions, targets)
            
            return metrics
            
        except Exception as e:
            logger.error(f"Failed to compute metrics: {e}")
            return {}
    
    def _compute_accuracy(self, predictions: List[int], targets: List[int]) -> float:
        """Compute classification accuracy."""
        correct = sum(p == t for p, t in zip(predictions, targets))
        return correct / len(predictions) if predictions else 0.0
    
    def _compute_precision(self, predictions: List[int], targets: List[int]) -> float:
        """Compute precision (macro average)."""
        from sklearn.metrics import precision_score
        try:
            return float(precision_score(targets, predictions, average='macro', zero_division=0))
        except Exception:
            return 0.0
    
    def _compute_recall(self, predictions: List[int], targets: List[int]) -> float:
        """Compute recall (macro average)."""
        from sklearn.metrics import recall_score
        try:
            return float(recall_score(targets, predictions, average='macro', zero_division=0))
        except Exception:
            return 0.0
    
    def _compute_f1(self, predictions: List[int], targets: List[int]) -> float:
        """Compute F1 score (macro average)."""
        from sklearn.metrics import f1_score
        try:
            return float(f1_score(targets, predictions, average='macro', zero_division=0))
        except Exception:
            return 0.0
    
    def _compute_mse(self, predictions: List[float], targets: List[float]) -> float:
        """Compute mean squared error."""
        return float(np.mean([(p - t) ** 2 for p, t in zip(predictions, targets)]))
    
    def _compute_mae(self, predictions: List[float], targets: List[float]) -> float:
        """Compute mean absolute error."""
        return float(np.mean([abs(p - t) for p, t in zip(predictions, targets)]))
    
    def _compute_r2(self, predictions: List[float], targets: List[float]) -> float:
        """Compute R-squared score."""
        try:
            from sklearn.metrics import r2_score
            return float(r2_score(targets, predictions))
        except Exception:
            return 0.0
    
    def _compute_confusion_matrix(self, predictions: List[int], targets: List[int]) -> np.ndarray:
        """Compute confusion matrix."""
        try:
            from sklearn.metrics import confusion_matrix
            return confusion_matrix(targets, predictions)
        except Exception as e:
            logger.error(f"Failed to compute confusion matrix: {e}")
            return np.array([])
    
    async def evaluate_on_benchmarks(
        self,
        model: torch.nn.Module,
        model_type: str,
        benchmark_datasets: List[str] = None
    ) -> List[EvaluationResult]:
        """Evaluate model on benchmark datasets."""
        if not benchmark_datasets:
            benchmark_datasets = self.config.benchmark_datasets
        
        if not benchmark_datasets:
            logger.info("No benchmark datasets configured")
            return []
        
        results = []
        
        for dataset_name in benchmark_datasets:
            try:
                logger.info(f"Evaluating on benchmark dataset: {dataset_name}")
                
                # Load benchmark dataset (implementation depends on dataset)
                dataloader = await self._load_benchmark_dataset(dataset_name, model_type)
                
                if dataloader:
                    result = await self.evaluate_model(
                        model=model,
                        dataloader=dataloader,
                        model_type=model_type,
                        dataset_name=dataset_name
                    )
                    results.append(result)
                
            except Exception as e:
                logger.error(f"Failed to evaluate on benchmark {dataset_name}: {e}")
        
        return results
    
    async def _load_benchmark_dataset(self, dataset_name: str, model_type: str) -> Optional[DataLoader]:
        """Load a benchmark dataset."""
        # This would be implemented based on available benchmark datasets
        # For now, return None to indicate no benchmark datasets available
        logger.warning(f"Benchmark dataset loading not implemented: {dataset_name}")
        return None
    
    def save_results(self, results: List[EvaluationResult], output_path: str) -> None:
        """Save evaluation results to file."""
        try:
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            # Convert results to serializable format
            serializable_results = []
            for result in results:
                result_dict = {
                    'model_type': result.model_type,
                    'model_path': result.model_path,
                    'dataset_name': result.dataset_name,
                    'metrics': result.metrics,
                    'evaluation_time': result.evaluation_time,
                    'metadata': result.metadata
                }
                
                # Add confusion matrix if present
                if result.confusion_matrix is not None:
                    result_dict['confusion_matrix'] = result.confusion_matrix.tolist()
                
                serializable_results.append(result_dict)
            
            # Save to JSON
            with open(output_file, 'w') as f:
                json.dump(serializable_results, f, indent=2)
            
            logger.info(f"Evaluation results saved to {output_path}")
            
        except Exception as e:
            logger.error(f"Failed to save evaluation results: {e}")
    
    async def compare_models(
        self,
        model_results: List[EvaluationResult],
        comparison_metrics: List[str] = None
    ) -> Dict[str, Any]:
        """Compare multiple model evaluation results."""
        if not model_results:
            return {}
        
        if not comparison_metrics:
            comparison_metrics = ['accuracy', 'f1', 'precision', 'recall']
        
        comparison = {
            'models': [],
            'metrics_comparison': {},
            'best_model': {},
            'summary': {}
        }
        
        try:
            # Collect model information
            for result in model_results:
                comparison['models'].append({
                    'model_type': result.model_type,
                    'model_path': result.model_path,
                    'dataset': result.dataset_name,
                    'metrics': result.metrics,
                    'evaluation_time': result.evaluation_time
                })
            
            # Compare metrics
            for metric in comparison_metrics:
                metric_values = []
                for result in model_results:
                    if metric in result.metrics:
                        metric_values.append({
                            'model': f"{result.model_type}_{result.dataset_name}",
                            'value': result.metrics[metric]
                        })
                
                if metric_values:
                    comparison['metrics_comparison'][metric] = {
                        'values': metric_values,
                        'best': max(metric_values, key=lambda x: x['value']),
                        'worst': min(metric_values, key=lambda x: x['value']),
                        'mean': np.mean([v['value'] for v in metric_values]),
                        'std': np.std([v['value'] for v in metric_values])
                    }
            
            # Find best overall model (based on first metric)
            if comparison_metrics and comparison['metrics_comparison']:
                primary_metric = comparison_metrics[0]
                if primary_metric in comparison['metrics_comparison']:
                    best_info = comparison['metrics_comparison'][primary_metric]['best']
                    comparison['best_model'] = best_info
            
            logger.info(f"Model comparison completed for {len(model_results)} models")
            return comparison
            
        except Exception as e:
            logger.error(f"Failed to compare models: {e}")
            return comparison
