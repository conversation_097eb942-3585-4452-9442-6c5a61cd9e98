"""
Model evaluation module for the ASI Learning Engine.

Provides comprehensive model evaluation capabilities including:
- Multi-modal model evaluation (NLP, Vision, RL, Abstraction)
- Standard and custom metrics computation
- Benchmark dataset evaluation
- Model comparison and analysis
- Performance profiling and optimization
"""

from .evaluator import ModelEvaluator
from .metrics import (
    ClassificationMetrics,
    RegressionMetrics,
    VisionMetrics,
    NLPMetrics,
    RLMetrics,
    AbstractionMetrics
)
from .benchmarks import BenchmarkRunner
from .profiler import ModelProfiler

__all__ = [
    "ModelEvaluator",
    "ClassificationMetrics",
    "RegressionMetrics", 
    "VisionMetrics",
    "NLPMetrics",
    "RLMetrics",
    "AbstractionMetrics",
    "BenchmarkRunner",
    "ModelProfiler"
]
