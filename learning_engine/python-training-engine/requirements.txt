# Core ML/DL frameworks
torch>=2.0.0
torchvision>=0.15.0
torchaudio>=2.0.0
transformers>=4.30.0
datasets>=2.12.0
accelerate>=0.20.0

# Computer Vision
opencv-python>=4.7.0
ultralytics>=8.0.0
albumentations>=1.3.0
Pillow>=9.5.0

# Reinforcement Learning
stable-baselines3>=2.0.0
gymnasium>=0.28.0
tensorboard>=2.13.0

# Data Processing
numpy>=1.24.0
pandas>=2.0.0
scikit-learn>=1.2.0
scipy>=1.10.0

# Distributed Training
deepspeed>=0.9.0
fairscale>=0.4.13

# Model Optimization
onnx>=1.14.0
onnxruntime>=1.15.0
tensorrt>=8.6.0

# Monitoring & Logging
mlflow>=2.4.0
wandb>=0.15.0
tensorboard>=2.13.0
prometheus-client>=0.16.0

# Communication
grpcio>=1.54.0
grpcio-tools>=1.54.0
kafka-python>=2.0.2
redis>=4.5.0

# Configuration
pyyaml>=6.0
hydra-core>=1.3.0
omegaconf>=2.3.0

# Utilities
tqdm>=4.65.0
rich>=13.3.0
click>=8.1.0
python-dotenv>=1.0.0

# Testing
pytest>=7.3.0
pytest-cov>=4.1.0
pytest-mock>=3.10.0

# Development
black>=23.3.0
flake8>=6.0.0
mypy>=1.3.0
pre-commit>=3.3.0

# API & Web
fastapi>=0.95.0
uvicorn>=0.22.0
pydantic>=1.10.0

# Database
sqlalchemy>=2.0.0
alembic>=1.11.0

# Async
asyncio>=3.4.3
aiofiles>=23.1.0
aiohttp>=3.8.0

# Image Processing
imageio>=2.28.0
matplotlib>=3.7.0
seaborn>=0.12.0

# NLP Specific
spacy>=3.5.0
nltk>=3.8.0
sentencepiece>=0.1.99

# Audio Processing
librosa>=0.10.0
soundfile>=0.12.0

# Graph Processing
networkx>=3.1.0
torch-geometric>=2.3.0

# Optimization
optuna>=3.2.0
hyperopt>=0.2.7

# Security
cryptography>=40.0.0
pyjwt>=2.7.0

# Deployment
docker>=6.1.0
kubernetes>=26.1.0
