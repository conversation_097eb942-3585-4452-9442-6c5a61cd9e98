"""
Tests for the Training Orchestrator.

Tests the core training orchestration functionality including job management,
hook system, and integration with different trainers.
"""

import pytest
import asyncio
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock

import sys
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from asi_learning.training.orchestrator import TrainingOrchestrator
from asi_learning.utils.config import Config


class TestTrainingOrchestrator:
    """Test suite for TrainingOrchestrator."""
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for tests."""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def config_path(self, temp_dir):
        """Create test configuration file."""
        config_content = """
orchestrator:
  max_concurrent_jobs: 2
  job_timeout: 3600
  checkpoint_dir: {}/checkpoints
  model_registry_dir: {}/models
  log_level: DEBUG

nlp:
  model_name: bert-base-uncased
  training:
    batch_size: 16
    epochs: 2
    learning_rate: 0.001

vision:
  model_name: resnet18
  training:
    batch_size: 32
    epochs: 2
    learning_rate: 0.001

monitoring:
  enabled: true
  report_interval: 10

kafka:
  bootstrap_servers: localhost:9092
""".format(temp_dir, temp_dir)
        
        config_path = Path(temp_dir) / "test_config.yaml"
        with open(config_path, 'w') as f:
            f.write(config_content)
        
        return str(config_path)
    
    @pytest.fixture
    def orchestrator(self, config_path):
        """Create TrainingOrchestrator instance."""
        return TrainingOrchestrator(config_path)
    
    def test_orchestrator_initialization(self, orchestrator):
        """Test orchestrator initialization."""
        assert orchestrator is not None
        assert orchestrator.config is not None
        assert orchestrator.job_manager is not None
        assert orchestrator.hook_manager is not None
        assert len(orchestrator.trainers) > 0
    
    def test_submit_training_job(self, orchestrator):
        """Test submitting a training job."""
        job_config = {
            "model_type": "nlp",
            "epochs": 1,
            "learning_rate": 0.001,
            "batch_size": 16,
            "data": [{"text": "test", "label": 1}],
            "model_save_path": "/tmp/test_model"
        }
        
        job_id = orchestrator.submit_training_job(job_config)
        
        assert job_id is not None
        assert isinstance(job_id, str)
        assert len(job_id) > 0
        
        # Check job is in the queue
        job_status = orchestrator.get_job_status(job_id)
        assert job_status is not None
        assert job_status["status"] in ["pending", "running"]
    
    def test_get_job_status(self, orchestrator):
        """Test getting job status."""
        # Test non-existent job
        status = orchestrator.get_job_status("non-existent-job")
        assert status is None
        
        # Test existing job
        job_config = {
            "model_type": "nlp",
            "epochs": 1,
            "data": [{"text": "test", "label": 1}]
        }
        job_id = orchestrator.submit_training_job(job_config)
        
        status = orchestrator.get_job_status(job_id)
        assert status is not None
        assert "status" in status
        assert "job_id" in status
        assert status["job_id"] == job_id
    
    def test_cancel_job(self, orchestrator):
        """Test cancelling a training job."""
        job_config = {
            "model_type": "nlp",
            "epochs": 10,  # Long running job
            "data": [{"text": "test", "label": 1}]
        }
        job_id = orchestrator.submit_training_job(job_config)
        
        # Cancel the job
        success = orchestrator.cancel_job(job_id)
        assert success is True
        
        # Check job status
        status = orchestrator.get_job_status(job_id)
        assert status["status"] == "cancelled"
    
    def test_list_jobs(self, orchestrator):
        """Test listing all jobs."""
        # Initially no jobs
        jobs = orchestrator.list_jobs()
        initial_count = len(jobs)
        
        # Submit some jobs
        for i in range(3):
            job_config = {
                "model_type": "nlp",
                "epochs": 1,
                "data": [{"text": f"test {i}", "label": i % 2}]
            }
            orchestrator.submit_training_job(job_config)
        
        # Check job list
        jobs = orchestrator.list_jobs()
        assert len(jobs) == initial_count + 3
        
        # Check job structure
        for job in jobs[-3:]:  # Last 3 jobs
            assert "job_id" in job
            assert "status" in job
            assert "model_type" in job
            assert "created_at" in job
    
    @pytest.mark.asyncio
    async def test_hook_system(self, orchestrator):
        """Test the hook system."""
        # Mock hooks
        pre_hook = Mock()
        post_hook = Mock()
        
        # Register hooks
        orchestrator.hook_manager.register_hook("pre_training", pre_hook)
        orchestrator.hook_manager.register_hook("post_training", post_hook)
        
        # Submit job
        job_config = {
            "model_type": "nlp",
            "epochs": 1,
            "data": [{"text": "test", "label": 1}]
        }
        job_id = orchestrator.submit_training_job(job_config)
        
        # Wait a bit for job to process
        await asyncio.sleep(2)
        
        # Check hooks were called
        assert pre_hook.called
        assert post_hook.called
    
    def test_concurrent_job_limit(self, orchestrator):
        """Test concurrent job limit enforcement."""
        # Submit more jobs than the limit
        job_ids = []
        for i in range(5):  # More than max_concurrent_jobs (2)
            job_config = {
                "model_type": "nlp",
                "epochs": 1,
                "data": [{"text": f"test {i}", "label": i % 2}]
            }
            job_id = orchestrator.submit_training_job(job_config)
            job_ids.append(job_id)
        
        # Check that some jobs are pending
        running_count = 0
        pending_count = 0
        
        for job_id in job_ids:
            status = orchestrator.get_job_status(job_id)
            if status["status"] == "running":
                running_count += 1
            elif status["status"] == "pending":
                pending_count += 1
        
        # Should not exceed max concurrent jobs
        assert running_count <= orchestrator.config.orchestrator.max_concurrent_jobs
        assert pending_count > 0
    
    def test_invalid_model_type(self, orchestrator):
        """Test handling of invalid model type."""
        job_config = {
            "model_type": "invalid_type",
            "epochs": 1,
            "data": [{"text": "test", "label": 1}]
        }
        
        with pytest.raises(ValueError):
            orchestrator.submit_training_job(job_config)
    
    def test_missing_required_config(self, orchestrator):
        """Test handling of missing required configuration."""
        job_config = {
            "model_type": "nlp",
            # Missing required fields like data
        }
        
        with pytest.raises(ValueError):
            orchestrator.submit_training_job(job_config)
    
    @pytest.mark.asyncio
    async def test_job_timeout(self, orchestrator):
        """Test job timeout handling."""
        # Set a very short timeout for testing
        orchestrator.config.orchestrator.job_timeout = 1  # 1 second
        
        job_config = {
            "model_type": "nlp",
            "epochs": 100,  # This should timeout
            "data": [{"text": "test", "label": 1}]
        }
        job_id = orchestrator.submit_training_job(job_config)
        
        # Wait for timeout
        await asyncio.sleep(3)
        
        status = orchestrator.get_job_status(job_id)
        assert status["status"] == "failed"
        assert "timeout" in status.get("error_message", "").lower()
    
    def test_metrics_collection(self, orchestrator):
        """Test metrics collection during training."""
        job_config = {
            "model_type": "nlp",
            "epochs": 1,
            "data": [{"text": "test", "label": 1}]
        }
        job_id = orchestrator.submit_training_job(job_config)
        
        # Wait for job to complete
        import time
        time.sleep(5)
        
        status = orchestrator.get_job_status(job_id)
        
        # Check if metrics are collected
        if status["status"] == "completed":
            assert "metrics" in status
            metrics = status["metrics"]
            assert isinstance(metrics, dict)
            # Should have at least loss metric
            assert "loss" in metrics or "final_loss" in metrics
    
    def test_model_saving(self, orchestrator, temp_dir):
        """Test model saving functionality."""
        model_path = Path(temp_dir) / "test_model"
        
        job_config = {
            "model_type": "nlp",
            "epochs": 1,
            "data": [{"text": "test", "label": 1}],
            "model_save_path": str(model_path)
        }
        job_id = orchestrator.submit_training_job(job_config)
        
        # Wait for job to complete
        import time
        time.sleep(5)
        
        status = orchestrator.get_job_status(job_id)
        
        if status["status"] == "completed":
            # Check if model was saved
            assert model_path.exists() or any(model_path.parent.glob("*model*"))
    
    def test_cleanup_completed_jobs(self, orchestrator):
        """Test cleanup of completed jobs."""
        # Submit and complete some jobs
        job_ids = []
        for i in range(3):
            job_config = {
                "model_type": "nlp",
                "epochs": 1,
                "data": [{"text": f"test {i}", "label": i % 2}]
            }
            job_id = orchestrator.submit_training_job(job_config)
            job_ids.append(job_id)
        
        # Wait for jobs to complete
        import time
        time.sleep(10)
        
        # Trigger cleanup
        orchestrator.cleanup_completed_jobs(max_age_hours=0)  # Clean all
        
        # Check that completed jobs are cleaned up
        for job_id in job_ids:
            status = orchestrator.get_job_status(job_id)
            # Job should either be None (cleaned up) or still running/pending
            if status is not None:
                assert status["status"] in ["running", "pending", "failed"]
    
    def test_orchestrator_shutdown(self, orchestrator):
        """Test graceful shutdown of orchestrator."""
        # Submit a job
        job_config = {
            "model_type": "nlp",
            "epochs": 1,
            "data": [{"text": "test", "label": 1}]
        }
        orchestrator.submit_training_job(job_config)
        
        # Shutdown orchestrator
        orchestrator.shutdown()
        
        # Should not be able to submit new jobs
        with pytest.raises(RuntimeError):
            orchestrator.submit_training_job(job_config)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
