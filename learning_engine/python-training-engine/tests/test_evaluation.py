"""
Tests for the Evaluation module.

Tests model evaluation functionality including metrics computation,
benchmark testing, and model comparison.
"""

import pytest
import numpy as np
import torch
import torch.nn as nn
from torch.utils.data import DataLoader, TensorDataset
import tempfile
import shutil
from pathlib import Path

import sys
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from asi_learning.evaluation.evaluator import ModelEvaluator, EvaluationResult
from asi_learning.evaluation.metrics import (
    ClassificationMetrics, RegressionMetrics, VisionMetrics, NLPMetrics
)
from asi_learning.utils.config import EvaluationConfig


class SimpleClassificationModel(nn.Module):
    """Simple model for testing."""
    
    def __init__(self, input_size=10, num_classes=2):
        super().__init__()
        self.fc = nn.Linear(input_size, num_classes)
    
    def forward(self, x):
        return self.fc(x)


class TestModelEvaluator:
    """Test suite for ModelEvaluator."""
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for tests."""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def evaluation_config(self):
        """Create evaluation configuration."""
        return EvaluationConfig(
            metrics=["accuracy", "precision", "recall", "f1"],
            test_batch_size=32,
            save_predictions=True,
            compute_confusion_matrix=True
        )
    
    @pytest.fixture
    def evaluator(self, evaluation_config):
        """Create ModelEvaluator instance."""
        return ModelEvaluator(evaluation_config)
    
    @pytest.fixture
    def classification_model(self):
        """Create simple classification model."""
        model = SimpleClassificationModel(input_size=10, num_classes=2)
        model.eval()
        return model
    
    @pytest.fixture
    def classification_dataloader(self):
        """Create classification test dataloader."""
        # Generate synthetic data
        X = torch.randn(100, 10)
        y = torch.randint(0, 2, (100,))
        
        dataset = TensorDataset(X, y)
        return DataLoader(dataset, batch_size=16, shuffle=False)
    
    @pytest.mark.asyncio
    async def test_evaluate_classification_model(
        self, evaluator, classification_model, classification_dataloader
    ):
        """Test classification model evaluation."""
        result = await evaluator.evaluate_model(
            model=classification_model,
            dataloader=classification_dataloader,
            model_type="classification",
            task_type="classification",
            model_path="test_model",
            dataset_name="test_dataset"
        )
        
        assert isinstance(result, EvaluationResult)
        assert result.model_type == "classification"
        assert result.dataset_name == "test_dataset"
        assert result.evaluation_time > 0
        
        # Check metrics
        assert "accuracy" in result.metrics
        assert "precision" in result.metrics
        assert "recall" in result.metrics
        assert "f1" in result.metrics
        
        # Check metric values are reasonable
        assert 0 <= result.metrics["accuracy"] <= 1
        assert 0 <= result.metrics["precision"] <= 1
        assert 0 <= result.metrics["recall"] <= 1
        assert 0 <= result.metrics["f1"] <= 1
        
        # Check predictions are saved
        assert result.predictions is not None
        assert len(result.predictions) == 100  # Total samples
        
        # Check confusion matrix
        assert result.confusion_matrix is not None
        assert result.confusion_matrix.shape == (2, 2)  # 2 classes
    
    @pytest.mark.asyncio
    async def test_evaluate_regression_model(self, evaluator):
        """Test regression model evaluation."""
        # Create simple regression model
        class SimpleRegressionModel(nn.Module):
            def __init__(self):
                super().__init__()
                self.fc = nn.Linear(5, 1)
            
            def forward(self, x):
                return self.fc(x)
        
        model = SimpleRegressionModel()
        model.eval()
        
        # Create regression dataloader
        X = torch.randn(50, 5)
        y = torch.randn(50, 1)
        dataset = TensorDataset(X, y)
        dataloader = DataLoader(dataset, batch_size=10, shuffle=False)
        
        result = await evaluator.evaluate_model(
            model=model,
            dataloader=dataloader,
            model_type="regression",
            task_type="regression",
            dataset_name="regression_test"
        )
        
        assert result.model_type == "regression"
        assert "mse" in result.metrics
        assert "mae" in result.metrics
        assert "r2" in result.metrics
        
        # Check metric values
        assert result.metrics["mse"] >= 0
        assert result.metrics["mae"] >= 0
    
    @pytest.mark.asyncio
    async def test_batch_evaluation(
        self, evaluator, classification_model, classification_dataloader
    ):
        """Test batch evaluation functionality."""
        # Test with small batch size
        evaluator.config.test_batch_size = 8
        
        result = await evaluator.evaluate_model(
            model=classification_model,
            dataloader=classification_dataloader,
            model_type="classification",
            task_type="classification"
        )
        
        assert result.success
        assert len(result.predictions) == 100  # All samples evaluated
    
    @pytest.mark.asyncio
    async def test_model_comparison(self, evaluator, classification_dataloader):
        """Test model comparison functionality."""
        # Create two different models
        model1 = SimpleClassificationModel(input_size=10, num_classes=2)
        model2 = SimpleClassificationModel(input_size=10, num_classes=2)
        
        # Evaluate both models
        result1 = await evaluator.evaluate_model(
            model=model1,
            dataloader=classification_dataloader,
            model_type="model1",
            task_type="classification"
        )
        
        result2 = await evaluator.evaluate_model(
            model=model2,
            dataloader=classification_dataloader,
            model_type="model2",
            task_type="classification"
        )
        
        # Compare models
        comparison = await evaluator.compare_models(
            [result1, result2],
            comparison_metrics=["accuracy", "f1"]
        )
        
        assert "models" in comparison
        assert "metrics_comparison" in comparison
        assert "best_model" in comparison
        
        assert len(comparison["models"]) == 2
        assert "accuracy" in comparison["metrics_comparison"]
        assert "f1" in comparison["metrics_comparison"]
    
    def test_save_and_load_results(self, evaluator, temp_dir):
        """Test saving and loading evaluation results."""
        # Create dummy results
        result1 = EvaluationResult(
            model_type="test_model",
            model_path="test_path",
            dataset_name="test_dataset",
            metrics={"accuracy": 0.85, "f1": 0.82},
            evaluation_time=1.5
        )
        
        result2 = EvaluationResult(
            model_type="test_model2",
            model_path="test_path2",
            dataset_name="test_dataset",
            metrics={"accuracy": 0.90, "f1": 0.88},
            evaluation_time=1.2
        )
        
        # Save results
        output_path = Path(temp_dir) / "evaluation_results.json"
        evaluator.save_results([result1, result2], str(output_path))
        
        # Check file was created
        assert output_path.exists()
        
        # Load and verify content
        import json
        with open(output_path, 'r') as f:
            loaded_results = json.load(f)
        
        assert len(loaded_results) == 2
        assert loaded_results[0]["model_type"] == "test_model"
        assert loaded_results[1]["metrics"]["accuracy"] == 0.90


class TestMetrics:
    """Test suite for metrics computation."""
    
    def test_classification_metrics(self):
        """Test classification metrics computation."""
        metrics = ClassificationMetrics()
        
        # Perfect predictions
        predictions = [0, 1, 0, 1, 0, 1]
        targets = [0, 1, 0, 1, 0, 1]
        
        result = metrics.compute_metrics(predictions, targets)
        
        assert result["accuracy"] == 1.0
        assert result["precision"] == 1.0
        assert result["recall"] == 1.0
        assert result["f1"] == 1.0
    
    def test_classification_metrics_with_errors(self):
        """Test classification metrics with prediction errors."""
        metrics = ClassificationMetrics()
        
        predictions = [0, 1, 0, 1, 0, 0]  # Last prediction is wrong
        targets = [0, 1, 0, 1, 0, 1]
        
        result = metrics.compute_metrics(predictions, targets)
        
        assert result["accuracy"] == 5/6  # 5 correct out of 6
        assert 0 < result["precision"] < 1
        assert 0 < result["recall"] < 1
        assert 0 < result["f1"] < 1
    
    def test_regression_metrics(self):
        """Test regression metrics computation."""
        metrics = RegressionMetrics()
        
        # Perfect predictions
        predictions = [1.0, 2.0, 3.0, 4.0, 5.0]
        targets = [1.0, 2.0, 3.0, 4.0, 5.0]
        
        result = metrics.compute_metrics(predictions, targets)
        
        assert result["mse"] == 0.0
        assert result["mae"] == 0.0
        assert result["r2"] == 1.0
    
    def test_regression_metrics_with_errors(self):
        """Test regression metrics with prediction errors."""
        metrics = RegressionMetrics()
        
        predictions = [1.1, 2.1, 3.1, 4.1, 5.1]  # Small errors
        targets = [1.0, 2.0, 3.0, 4.0, 5.0]
        
        result = metrics.compute_metrics(predictions, targets)
        
        assert result["mse"] > 0
        assert result["mae"] > 0
        assert result["r2"] < 1.0
        assert result["rmse"] == np.sqrt(result["mse"])
    
    def test_vision_metrics(self):
        """Test vision-specific metrics."""
        metrics = VisionMetrics()
        
        # Multi-class classification
        predictions = [0, 1, 2, 0, 1, 2]
        targets = [0, 1, 2, 0, 1, 2]
        
        # Mock outputs for top-k accuracy
        outputs = [
            [0.8, 0.1, 0.1],  # Class 0
            [0.1, 0.8, 0.1],  # Class 1
            [0.1, 0.1, 0.8],  # Class 2
            [0.7, 0.2, 0.1],  # Class 0
            [0.2, 0.7, 0.1],  # Class 1
            [0.1, 0.2, 0.7],  # Class 2
        ]
        
        result = metrics.compute_metrics(predictions, targets, outputs)
        
        assert result["accuracy"] == 1.0
        assert "top_1_accuracy" in result
        assert "top_3_accuracy" in result
        assert result["top_1_accuracy"] == 1.0
        assert result["top_3_accuracy"] == 1.0
    
    def test_nlp_metrics_classification(self):
        """Test NLP classification metrics."""
        metrics = NLPMetrics()
        
        predictions = [0, 1, 0, 1]  # Binary classification
        targets = [0, 1, 0, 1]
        
        result = metrics.compute_metrics(predictions, targets)
        
        assert result["accuracy"] == 1.0
        assert "balanced_accuracy" in result
        assert "specificity" in result
        assert "sensitivity" in result
    
    def test_nlp_metrics_generation(self):
        """Test NLP text generation metrics."""
        metrics = NLPMetrics()
        
        predictions = ["hello world", "goodbye world"]
        targets = ["hello world", "goodbye world"]
        
        result = metrics.compute_metrics(predictions, targets)
        
        assert "bleu" in result
        assert "exact_match" in result
        assert result["exact_match"] == 1.0  # Perfect match
        assert result["bleu"] > 0
    
    def test_empty_predictions(self):
        """Test handling of empty predictions."""
        metrics = ClassificationMetrics()
        
        result = metrics.compute_metrics([], [])
        
        # Should return empty dict or handle gracefully
        assert isinstance(result, dict)
    
    def test_mismatched_lengths(self):
        """Test handling of mismatched prediction/target lengths."""
        metrics = ClassificationMetrics()
        
        predictions = [0, 1, 0]
        targets = [0, 1]  # Different length
        
        # Should handle gracefully or raise appropriate error
        try:
            result = metrics.compute_metrics(predictions, targets)
            # If it doesn't raise an error, check it handles it reasonably
            assert isinstance(result, dict)
        except (ValueError, IndexError):
            # Expected behavior for mismatched lengths
            pass


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
