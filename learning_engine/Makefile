# ASI Learning Engine Module Makefile
# ===================================

.PHONY: help build start stop restart logs clean test proto deps check-deps
.DEFAULT_GOAL := help

# Default target
help:
	@echo "ASI Learning Engine Module"
	@echo "=========================="
	@echo ""
	@echo "Available targets:"
	@echo "  build          - Build all services"
	@echo "  start          - Start all services"
	@echo "  stop           - Stop all services"
	@echo "  restart        - Restart all services"
	@echo "  logs           - Show logs for all services"
	@echo "  clean          - Clean up containers and volumes"
	@echo "  test           - Run all tests"
	@echo "  proto          - Generate protobuf code"
	@echo "  deps           - Install dependencies"
	@echo "  check-deps     - Check if dependencies are installed"
	@echo ""
	@echo "Training targets:"
	@echo "  train-all      - Train all models"
	@echo "  train-nlp      - Train NLP models"
	@echo "  train-vision   - Train vision models"
	@echo "  train-rl       - Train RL agents"
	@echo ""
	@echo "Individual service targets:"
	@echo "  start-training - Start Python training engine"
	@echo "  start-inference- Start C++ inference engine"
	@echo "  start-grpc     - Start gRPC model server"
	@echo "  start-monitoring - Start MLflow + TensorBoard"

# Check dependencies
check-deps:
	@echo "Checking dependencies..."
	@command -v python3 >/dev/null 2>&1 || { echo "Python3 is required but not installed."; exit 1; }
	@command -v pip3 >/dev/null 2>&1 || { echo "pip3 is required but not installed."; exit 1; }
	@command -v cmake >/dev/null 2>&1 || { echo "CMake is required but not installed."; exit 1; }
	@command -v g++ >/dev/null 2>&1 || { echo "g++ is required but not installed."; exit 1; }
	@command -v docker >/dev/null 2>&1 || { echo "Docker is required but not installed."; exit 1; }
	@command -v docker-compose >/dev/null 2>&1 || { echo "Docker Compose is required but not installed."; exit 1; }
	@echo "All dependencies are installed."

# Install dependencies
deps: check-deps
	@echo "Installing Python dependencies..."
	cd python-training-engine && pip3 install -r requirements.txt
	@echo "Installing C++ dependencies..."
	cd cpp-inference-engine && mkdir -p build && cd build && cmake .. && make deps
	@echo "Dependencies installed successfully."

# Generate protobuf code
proto:
	@echo "Generating protobuf code..."
	cd grpc-model-server/proto && python3 -m grpc_tools.protoc --python_out=../src --grpc_python_out=../src --proto_path=. *.proto
	cd shared-schemas/protobuf && python3 -m grpc_tools.protoc --python_out=../../python-training-engine/src --grpc_python_out=../../python-training-engine/src --proto_path=. *.proto
	@echo "Protobuf code generated successfully."

# Build all services
build: proto
	@echo "Building all services..."
	cd python-training-engine && docker build -t asi-learning-training .
	cd cpp-inference-engine && docker build -t asi-learning-inference .
	cd grpc-model-server && docker build -t asi-learning-grpc .
	@echo "All services built successfully."

# Start all services
start:
	@echo "Starting all services..."
	cd docker && docker-compose up -d
	@echo "All services started. Check status with 'make logs'"

# Stop all services
stop:
	@echo "Stopping all services..."
	cd docker && docker-compose down
	@echo "All services stopped."

# Restart all services
restart: stop start

# Show logs
logs:
	cd docker && docker-compose logs -f

# Clean up
clean:
	@echo "Cleaning up containers and volumes..."
	cd docker && docker-compose down -v --remove-orphans
	docker system prune -f
	@echo "Cleanup completed."

# Run all tests
test:
	@echo "Running all tests..."
	make test-python
	make test-cpp
	make test-integration
	@echo "All tests completed."

# Python tests
test-python:
	@echo "Running Python tests..."
	cd python-training-engine && python3 -m pytest tests/ -v --cov=src/asi_learning --cov-report=html

# C++ tests
test-cpp:
	@echo "Running C++ tests..."
	cd cpp-inference-engine/build && make test

# Integration tests
test-integration:
	@echo "Running integration tests..."
	cd tests && python3 integration_test.py

# Training targets
train-all:
	@echo "Training all models..."
	make train-nlp
	make train-vision
	make train-rl

train-nlp:
	@echo "Training NLP models..."
	cd python-training-engine && python3 -m asi_learning.training.train_nlp --config configs/nlp_config.yaml

train-vision:
	@echo "Training vision models..."
	cd python-training-engine && python3 -m asi_learning.training.train_vision --config configs/vision_config.yaml

train-rl:
	@echo "Training RL agents..."
	cd python-training-engine && python3 -m asi_learning.training.train_rl --config configs/rl_config.yaml

# Individual service targets
start-training:
	@echo "Starting Python training engine..."
	cd python-training-engine && python3 -m asi_learning.training.orchestrator

start-inference:
	@echo "Starting C++ inference engine..."
	cd cpp-inference-engine/build && ./asi_inference_server

start-grpc:
	@echo "Starting gRPC model server..."
	cd grpc-model-server && python3 -m src.model_server

start-monitoring:
	@echo "Starting monitoring services..."
	cd docker && docker-compose up -d mlflow tensorboard grafana

# Model validation
validate-models:
	@echo "Validating models..."
	cd python-training-engine && python3 -m asi_learning.evaluation.validate_all

# Performance benchmarks
benchmark:
	@echo "Running performance benchmarks..."
	cd tests && python3 benchmark_test.py

# Development targets
dev-python:
	@echo "Starting Python development environment..."
	cd python-training-engine && python3 -m asi_learning.training.orchestrator --debug

dev-cpp:
	@echo "Starting C++ development environment..."
	cd cpp-inference-engine && mkdir -p build && cd build && cmake -DCMAKE_BUILD_TYPE=Debug .. && make && ./asi_inference_server --debug

# Health checks
health:
	@echo "Checking service health..."
	@curl -f http://localhost:8090/health || echo "Training engine unhealthy"
	@curl -f http://localhost:8091/health || echo "Inference engine unhealthy"
	@curl -f http://localhost:8092/health || echo "gRPC server unhealthy"

# Metrics
metrics:
	@echo "Fetching metrics..."
	@curl -s http://localhost:8090/metrics | head -20
	@echo "..."
	@echo "Full metrics available at http://localhost:3000"

# Model deployment
deploy-models:
	@echo "Deploying models to production..."
	cd scripts && ./deploy_models.sh

# Backup models
backup-models:
	@echo "Backing up models..."
	cd scripts && ./backup_models.sh

# Load test
load-test:
	@echo "Running load tests..."
	cd tests && python3 load_test.py
