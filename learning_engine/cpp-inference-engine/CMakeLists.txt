cmake_minimum_required(VERSION 3.16)
project(ASI_Inference_Engine VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set build type
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Compiler flags
set(CMAKE_CXX_FLAGS_DEBUG "-g -O0 -Wall -Wextra")
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -DNDEBUG -march=native")

# Find required packages
find_package(PkgConfig REQUIRED)
find_package(Threads REQUIRED)

# Find OpenCV
find_package(OpenCV REQUIRED)
if(OpenCV_FOUND)
    message(STATUS "Found OpenCV: ${OpenCV_VERSION}")
    include_directories(${OpenCV_INCLUDE_DIRS})
endif()

# Find PyTorch C++
find_package(Torch REQUIRED)
if(Torch_FOUND)
    message(STATUS "Found PyTorch: ${Torch_VERSION}")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} ${TORCH_CXX_FLAGS}")
endif()

# Find gRPC
find_package(PkgConfig REQUIRED)
pkg_check_modules(GRPC REQUIRED grpc++)
pkg_check_modules(PROTOBUF REQUIRED protobuf)

# Find TensorRT (optional)
find_path(TENSORRT_INCLUDE_DIR NvInfer.h
    HINTS ${TENSORRT_ROOT} ${CUDA_TOOLKIT_ROOT_DIR}
    PATH_SUFFIXES include)
find_library(TENSORRT_LIBRARY nvinfer
    HINTS ${TENSORRT_ROOT} ${TENSORRT_BUILD} ${CUDA_TOOLKIT_ROOT_DIR}
    PATH_SUFFIXES lib lib64 lib/x64)

if(TENSORRT_INCLUDE_DIR AND TENSORRT_LIBRARY)
    set(TENSORRT_FOUND TRUE)
    message(STATUS "Found TensorRT: ${TENSORRT_LIBRARY}")
    add_definitions(-DUSE_TENSORRT)
else()
    set(TENSORRT_FOUND FALSE)
    message(WARNING "TensorRT not found. Building without TensorRT support.")
endif()

# Find ONNX Runtime (optional)
find_path(ONNXRUNTIME_INCLUDE_DIR onnxruntime_cxx_api.h
    HINTS ${ONNXRUNTIME_ROOT}
    PATH_SUFFIXES include)
find_library(ONNXRUNTIME_LIBRARY onnxruntime
    HINTS ${ONNXRUNTIME_ROOT}
    PATH_SUFFIXES lib lib64)

if(ONNXRUNTIME_INCLUDE_DIR AND ONNXRUNTIME_LIBRARY)
    set(ONNXRUNTIME_FOUND TRUE)
    message(STATUS "Found ONNX Runtime: ${ONNXRUNTIME_LIBRARY}")
    add_definitions(-DUSE_ONNXRUNTIME)
else()
    set(ONNXRUNTIME_FOUND FALSE)
    message(WARNING "ONNX Runtime not found. Building without ONNX Runtime support.")
endif()

# Include directories
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}/src
    ${GRPC_INCLUDE_DIRS}
    ${PROTOBUF_INCLUDE_DIRS}
)

if(TENSORRT_FOUND)
    include_directories(${TENSORRT_INCLUDE_DIR})
endif()

if(ONNXRUNTIME_FOUND)
    include_directories(${ONNXRUNTIME_INCLUDE_DIR})
endif()

# Source files
set(SOURCES
    src/main.cpp
    src/inference_engine.cpp
    src/model_loader.cpp
    src/preprocessing.cpp
    src/postprocessing.cpp
    src/grpc_server.cpp
    src/metrics_collector.cpp
    src/logger.cpp
    src/config.cpp
)

# Header files
set(HEADERS
    include/inference_engine.h
    include/model_loader.h
    include/preprocessing.h
    include/postprocessing.h
    include/grpc_server.h
    include/metrics_collector.h
    include/logger.h
    include/config.h
    include/common.h
)

# Optional TensorRT sources
if(TENSORRT_FOUND)
    list(APPEND SOURCES src/tensorrt_engine.cpp)
    list(APPEND HEADERS include/tensorrt_engine.h)
endif()

# Optional ONNX Runtime sources
if(ONNXRUNTIME_FOUND)
    list(APPEND SOURCES src/onnx_engine.cpp)
    list(APPEND HEADERS include/onnx_engine.h)
endif()

# Create executable
add_executable(asi_inference_server ${SOURCES} ${HEADERS})

# Link libraries
target_link_libraries(asi_inference_server
    ${TORCH_LIBRARIES}
    ${OpenCV_LIBS}
    ${GRPC_LIBRARIES}
    ${PROTOBUF_LIBRARIES}
    Threads::Threads
)

if(TENSORRT_FOUND)
    target_link_libraries(asi_inference_server ${TENSORRT_LIBRARY})
endif()

if(ONNXRUNTIME_FOUND)
    target_link_libraries(asi_inference_server ${ONNXRUNTIME_LIBRARY})
endif()

# Compiler-specific options
if(CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
    target_compile_options(asi_inference_server PRIVATE -Wall -Wextra -Wpedantic)
elseif(CMAKE_CXX_COMPILER_ID STREQUAL "Clang")
    target_compile_options(asi_inference_server PRIVATE -Wall -Wextra -Wpedantic)
elseif(CMAKE_CXX_COMPILER_ID STREQUAL "MSVC")
    target_compile_options(asi_inference_server PRIVATE /W4)
endif()

# Installation
install(TARGETS asi_inference_server
    RUNTIME DESTINATION bin
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)

install(FILES ${HEADERS}
    DESTINATION include/asi_inference
)

# Testing
enable_testing()

# Unit tests
add_executable(test_inference_engine
    tests/test_inference_engine.cpp
    tests/test_model_loader.cpp
    tests/test_preprocessing.cpp
    tests/test_postprocessing.cpp
    ${SOURCES}
)

# Remove main.cpp from test sources
list(REMOVE_ITEM SOURCES src/main.cpp)
target_sources(test_inference_engine PRIVATE ${SOURCES})

target_link_libraries(test_inference_engine
    ${TORCH_LIBRARIES}
    ${OpenCV_LIBS}
    ${GRPC_LIBRARIES}
    ${PROTOBUF_LIBRARIES}
    Threads::Threads
)

if(TENSORRT_FOUND)
    target_link_libraries(test_inference_engine ${TENSORRT_LIBRARY})
endif()

if(ONNXRUNTIME_FOUND)
    target_link_libraries(test_inference_engine ${ONNXRUNTIME_LIBRARY})
endif()

add_test(NAME InferenceEngineTests COMMAND test_inference_engine)

# Benchmarks
add_executable(benchmark_inference
    benchmarks/benchmark_inference.cpp
    ${SOURCES}
)

list(REMOVE_ITEM SOURCES src/main.cpp)
target_sources(benchmark_inference PRIVATE ${SOURCES})

target_link_libraries(benchmark_inference
    ${TORCH_LIBRARIES}
    ${OpenCV_LIBS}
    ${GRPC_LIBRARIES}
    ${PROTOBUF_LIBRARIES}
    Threads::Threads
)

if(TENSORRT_FOUND)
    target_link_libraries(benchmark_inference ${TENSORRT_LIBRARY})
endif()

if(ONNXRUNTIME_FOUND)
    target_link_libraries(benchmark_inference ${ONNXRUNTIME_LIBRARY})
endif()

# Custom targets
add_custom_target(deps
    COMMAND echo "Installing dependencies..."
    COMMAND echo "Please install: PyTorch C++, OpenCV, gRPC, Protocol Buffers"
    COMMAND echo "Optional: TensorRT, ONNX Runtime"
)

add_custom_target(format
    COMMAND find ${CMAKE_CURRENT_SOURCE_DIR}/src ${CMAKE_CURRENT_SOURCE_DIR}/include -name "*.cpp" -o -name "*.h" | xargs clang-format -i
    COMMENT "Formatting source code"
)

add_custom_target(lint
    COMMAND find ${CMAKE_CURRENT_SOURCE_DIR}/src ${CMAKE_CURRENT_SOURCE_DIR}/include -name "*.cpp" -o -name "*.h" | xargs clang-tidy
    COMMENT "Running static analysis"
)

# Print configuration summary
message(STATUS "")
message(STATUS "ASI Inference Engine Configuration Summary:")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  PyTorch: ${Torch_VERSION}")
message(STATUS "  OpenCV: ${OpenCV_VERSION}")
message(STATUS "  TensorRT: ${TENSORRT_FOUND}")
message(STATUS "  ONNX Runtime: ${ONNXRUNTIME_FOUND}")
message(STATUS "")
