/**
 * @file inference_engine.h
 * @brief High-performance inference engine for the ASI Learning Engine
 * 
 * Provides optimized model inference with support for PyTorch, TensorRT, and ONNX Runtime.
 * Designed for edge deployment with minimal latency and memory footprint.
 */

#pragma once

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <future>
#include <chrono>

#include <torch/torch.h>
#include <opencv2/opencv.hpp>

#ifdef USE_TENSORRT
#include <NvInfer.h>
#include <NvInferRuntime.h>
#endif

#ifdef USE_ONNXRUNTIME
#include <onnxruntime_cxx_api.h>
#endif

#include "common.h"
#include "model_loader.h"
#include "preprocessing.h"
#include "postprocessing.h"
#include "metrics_collector.h"
#include "logger.h"

namespace asi {
namespace inference {

/**
 * @brief Inference backend types
 */
enum class BackendType {
    PYTORCH,
    TENSORRT,
    ONNX_RUNTIME
};

/**
 * @brief Model types supported by the inference engine
 */
enum class ModelType {
    NLP_TRANSFORMER,
    VISION_CNN,
    RL_AGENT,
    ABSTR<PERSON><PERSON>ON,
    MULTIMODAL
};

/**
 * @brief Inference configuration
 */
struct InferenceConfig {
    BackendType backend = BackendType::PYTORCH;
    ModelType model_type = ModelType::VISION_CNN;
    std::string model_path;
    std::string device = "cuda";
    int batch_size = 1;
    int max_sequence_length = 512;
    int image_size = 224;
    float confidence_threshold = 0.5f;
    bool use_fp16 = false;
    bool use_dynamic_batching = false;
    int max_batch_delay_ms = 10;
    
    // Performance tuning
    int num_threads = 4;
    bool enable_profiling = false;
    bool enable_optimization = true;
};

/**
 * @brief Inference input data
 */
struct InferenceInput {
    std::string input_id;
    DataType data_type;
    std::vector<uint8_t> data;
    std::unordered_map<std::string, std::string> metadata;
    std::chrono::high_resolution_clock::time_point timestamp;
};

/**
 * @brief Inference output data
 */
struct InferenceOutput {
    std::string input_id;
    std::vector<float> predictions;
    std::vector<std::string> class_names;
    float confidence;
    std::unordered_map<std::string, float> additional_scores;
    std::chrono::high_resolution_clock::time_point timestamp;
    std::chrono::duration<double, std::milli> latency;
};

/**
 * @brief Batch inference request
 */
struct BatchInferenceRequest {
    std::vector<InferenceInput> inputs;
    std::string request_id;
    std::chrono::high_resolution_clock::time_point timestamp;
};

/**
 * @brief Batch inference response
 */
struct BatchInferenceResponse {
    std::vector<InferenceOutput> outputs;
    std::string request_id;
    std::chrono::duration<double, std::milli> total_latency;
    std::chrono::duration<double, std::milli> avg_latency;
    size_t successful_inferences;
    size_t failed_inferences;
};

/**
 * @brief High-performance inference engine
 * 
 * Features:
 * - Multi-backend support (PyTorch, TensorRT, ONNX Runtime)
 * - Dynamic batching for throughput optimization
 * - Memory pool management
 * - Asynchronous inference
 * - Performance monitoring
 */
class InferenceEngine {
public:
    /**
     * @brief Constructor
     * @param config Inference configuration
     */
    explicit InferenceEngine(const InferenceConfig& config);
    
    /**
     * @brief Destructor
     */
    ~InferenceEngine();
    
    /**
     * @brief Initialize the inference engine
     * @return true if successful, false otherwise
     */
    bool initialize();
    
    /**
     * @brief Shutdown the inference engine
     */
    void shutdown();
    
    /**
     * @brief Load a model
     * @param model_path Path to the model file
     * @param model_type Type of the model
     * @return true if successful, false otherwise
     */
    bool loadModel(const std::string& model_path, ModelType model_type);
    
    /**
     * @brief Unload the current model
     */
    void unloadModel();
    
    /**
     * @brief Perform synchronous inference
     * @param input Input data
     * @return Inference output
     */
    InferenceOutput infer(const InferenceInput& input);
    
    /**
     * @brief Perform asynchronous inference
     * @param input Input data
     * @return Future containing inference output
     */
    std::future<InferenceOutput> inferAsync(const InferenceInput& input);
    
    /**
     * @brief Perform batch inference
     * @param request Batch inference request
     * @return Batch inference response
     */
    BatchInferenceResponse inferBatch(const BatchInferenceRequest& request);
    
    /**
     * @brief Perform asynchronous batch inference
     * @param request Batch inference request
     * @return Future containing batch inference response
     */
    std::future<BatchInferenceResponse> inferBatchAsync(const BatchInferenceRequest& request);
    
    /**
     * @brief Get engine statistics
     * @return Statistics map
     */
    std::unordered_map<std::string, double> getStatistics() const;
    
    /**
     * @brief Get engine health status
     * @return true if healthy, false otherwise
     */
    bool isHealthy() const;
    
    /**
     * @brief Warm up the engine with dummy data
     * @param num_warmup_runs Number of warmup runs
     */
    void warmUp(int num_warmup_runs = 10);
    
    /**
     * @brief Enable/disable profiling
     * @param enable Whether to enable profiling
     */
    void setProfilingEnabled(bool enable);
    
    /**
     * @brief Get profiling results
     * @return Profiling data
     */
    std::unordered_map<std::string, double> getProfilingResults() const;

private:
    /**
     * @brief Internal inference implementation
     * @param input Input data
     * @return Inference output
     */
    InferenceOutput inferInternal(const InferenceInput& input);
    
    /**
     * @brief Preprocess input data
     * @param input Raw input data
     * @return Preprocessed tensor
     */
    torch::Tensor preprocessInput(const InferenceInput& input);
    
    /**
     * @brief Postprocess model output
     * @param output Raw model output
     * @param input_id Input identifier
     * @return Processed inference output
     */
    InferenceOutput postprocessOutput(const torch::Tensor& output, const std::string& input_id);
    
    /**
     * @brief Run PyTorch inference
     * @param input Preprocessed input tensor
     * @return Model output tensor
     */
    torch::Tensor runPyTorchInference(const torch::Tensor& input);
    
#ifdef USE_TENSORRT
    /**
     * @brief Run TensorRT inference
     * @param input Preprocessed input tensor
     * @return Model output tensor
     */
    torch::Tensor runTensorRTInference(const torch::Tensor& input);
#endif

#ifdef USE_ONNXRUNTIME
    /**
     * @brief Run ONNX Runtime inference
     * @param input Preprocessed input tensor
     * @return Model output tensor
     */
    torch::Tensor runONNXInference(const torch::Tensor& input);
#endif
    
    /**
     * @brief Update performance metrics
     * @param latency Inference latency
     * @param success Whether inference was successful
     */
    void updateMetrics(const std::chrono::duration<double, std::milli>& latency, bool success);
    
    /**
     * @brief Check if model is loaded
     * @return true if model is loaded, false otherwise
     */
    bool isModelLoaded() const;

private:
    InferenceConfig config_;
    std::unique_ptr<ModelLoader> model_loader_;
    std::unique_ptr<Preprocessor> preprocessor_;
    std::unique_ptr<Postprocessor> postprocessor_;
    std::unique_ptr<MetricsCollector> metrics_collector_;
    std::unique_ptr<Logger> logger_;
    
    // PyTorch components
    torch::jit::script::Module pytorch_model_;
    torch::Device device_;
    
#ifdef USE_TENSORRT
    // TensorRT components
    std::unique_ptr<nvinfer1::IRuntime> tensorrt_runtime_;
    std::unique_ptr<nvinfer1::ICudaEngine> tensorrt_engine_;
    std::unique_ptr<nvinfer1::IExecutionContext> tensorrt_context_;
#endif

#ifdef USE_ONNXRUNTIME
    // ONNX Runtime components
    std::unique_ptr<Ort::Env> onnx_env_;
    std::unique_ptr<Ort::Session> onnx_session_;
    std::unique_ptr<Ort::SessionOptions> onnx_session_options_;
#endif
    
    // State management
    bool initialized_;
    bool model_loaded_;
    bool profiling_enabled_;
    
    // Performance tracking
    mutable std::mutex metrics_mutex_;
    size_t total_inferences_;
    size_t successful_inferences_;
    size_t failed_inferences_;
    std::chrono::duration<double, std::milli> total_latency_;
    std::chrono::duration<double, std::milli> min_latency_;
    std::chrono::duration<double, std::milli> max_latency_;
    
    // Memory management
    std::vector<torch::Tensor> tensor_pool_;
    mutable std::mutex tensor_pool_mutex_;
    
    // Thread safety
    mutable std::mutex inference_mutex_;
};

/**
 * @brief Factory function to create inference engine
 * @param config Inference configuration
 * @return Unique pointer to inference engine
 */
std::unique_ptr<InferenceEngine> createInferenceEngine(const InferenceConfig& config);

} // namespace inference
} // namespace asi
