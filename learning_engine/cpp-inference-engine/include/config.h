/**
 * Configuration management for the ASI Learning Engine C++ Inference Server
 * 
 * Provides configuration loading, validation, and management for the inference engine.
 */

#pragma once

#include <string>
#include <vector>
#include <memory>
#include <map>

namespace asi_learning {

/**
 * Model configuration structure
 */
struct ModelConfig {
    std::string name;
    std::string path;
    std::string type;  // "pytorch", "onnx", "tensorrt"
    int input_size = 0;
    int output_size = 0;
    std::vector<int> input_shape;
    std::vector<int> output_shape;
    std::map<std::string, std::string> metadata;
    
    ModelConfig() = default;
    ModelConfig(const std::string& n, const std::string& p, const std::string& t)
        : name(n), path(p), type(t) {}
};

/**
 * Server configuration structure
 */
struct ServerConfig {
    int grpc_port = 50060;
    int http_port = 8091;
    int max_concurrent_requests = 100;
    int request_timeout_ms = 30000;
    bool enable_metrics = true;
    bool enable_health_check = true;
    std::string log_level = "INFO";
    std::string log_file = "logs/inference_server.log";
};

/**
 * Performance configuration structure
 */
struct PerformanceConfig {
    int num_threads = 4;
    int batch_size = 1;
    int max_batch_delay_ms = 10;
    bool enable_dynamic_batching = false;
    bool enable_fp16 = false;
    bool enable_optimization = true;
    bool enable_profiling = false;
    int warmup_iterations = 10;
};

/**
 * Main configuration class
 */
class Config {
public:
    // Server configuration
    ServerConfig server;
    
    // Performance configuration
    PerformanceConfig performance;
    
    // Device configuration
    std::string device = "cpu";  // "cpu", "cuda", "auto"
    
    // Model configurations
    std::vector<ModelConfig> models;
    
    // Paths
    std::string model_registry_path = "./models";
    std::string checkpoint_path = "./checkpoints";
    std::string log_path = "./logs";
    
    // Metrics configuration
    int metrics_port = 8090;
    std::string metrics_endpoint = "/metrics";
    bool prometheus_enabled = true;
    
    /**
     * Load configuration from YAML file
     */
    static std::shared_ptr<Config> load(const std::string& config_path);
    
    /**
     * Load configuration from environment variables
     */
    void load_from_env();
    
    /**
     * Validate configuration
     */
    bool validate() const;
    
    /**
     * Save configuration to file
     */
    bool save(const std::string& config_path) const;
    
    /**
     * Get model configuration by name
     */
    const ModelConfig* get_model_config(const std::string& model_name) const;
    
    /**
     * Add model configuration
     */
    void add_model_config(const ModelConfig& model_config);
    
    /**
     * Remove model configuration
     */
    bool remove_model_config(const std::string& model_name);
    
    /**
     * Print configuration summary
     */
    void print_summary() const;

private:
    /**
     * Apply environment variable overrides
     */
    void apply_env_overrides();
    
    /**
     * Set default values
     */
    void set_defaults();
    
    /**
     * Validate paths
     */
    bool validate_paths() const;
    
    /**
     * Validate model configurations
     */
    bool validate_models() const;
    
    /**
     * Create directories if they don't exist
     */
    void create_directories() const;
};

/**
 * Configuration builder for programmatic configuration
 */
class ConfigBuilder {
public:
    ConfigBuilder();
    
    // Server configuration
    ConfigBuilder& set_grpc_port(int port);
    ConfigBuilder& set_http_port(int port);
    ConfigBuilder& set_max_concurrent_requests(int max_requests);
    ConfigBuilder& set_request_timeout(int timeout_ms);
    ConfigBuilder& enable_metrics(bool enable = true);
    ConfigBuilder& enable_health_check(bool enable = true);
    ConfigBuilder& set_log_level(const std::string& level);
    ConfigBuilder& set_log_file(const std::string& file);
    
    // Performance configuration
    ConfigBuilder& set_num_threads(int threads);
    ConfigBuilder& set_batch_size(int batch_size);
    ConfigBuilder& set_max_batch_delay(int delay_ms);
    ConfigBuilder& enable_dynamic_batching(bool enable = true);
    ConfigBuilder& enable_fp16(bool enable = true);
    ConfigBuilder& enable_optimization(bool enable = true);
    ConfigBuilder& enable_profiling(bool enable = true);
    ConfigBuilder& set_warmup_iterations(int iterations);
    
    // Device configuration
    ConfigBuilder& set_device(const std::string& device);
    
    // Model configuration
    ConfigBuilder& add_model(const std::string& name, const std::string& path, const std::string& type);
    ConfigBuilder& add_model(const ModelConfig& model_config);
    
    // Paths
    ConfigBuilder& set_model_registry_path(const std::string& path);
    ConfigBuilder& set_checkpoint_path(const std::string& path);
    ConfigBuilder& set_log_path(const std::string& path);
    
    // Metrics configuration
    ConfigBuilder& set_metrics_port(int port);
    ConfigBuilder& set_metrics_endpoint(const std::string& endpoint);
    ConfigBuilder& enable_prometheus(bool enable = true);
    
    // Build configuration
    std::shared_ptr<Config> build();

private:
    std::shared_ptr<Config> config_;
};

/**
 * Environment variable mappings
 */
struct EnvVarMapping {
    static const std::map<std::string, std::string> mappings;
};

/**
 * Configuration validation utilities
 */
namespace config_utils {
    bool is_valid_port(int port);
    bool is_valid_device(const std::string& device);
    bool is_valid_log_level(const std::string& level);
    bool is_valid_model_type(const std::string& type);
    bool file_exists(const std::string& path);
    bool directory_exists(const std::string& path);
    bool create_directory(const std::string& path);
}

} // namespace asi_learning
