/**
 * Model Loader for the ASI Learning Engine C++ Inference Server
 * 
 * Provides comprehensive model loading capabilities for PyTorch, ONNX, and TensorRT models
 * with automatic optimization and format conversion.
 */

#pragma once

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>

#include <torch/torch.h>

#ifdef USE_TENSORRT
#include <NvInfer.h>
#include <NvInferRuntime.h>
#include <cuda_runtime.h>

// TensorRT Logger
class TensorRTLogger : public nvinfer1::ILogger {
public:
    void log(Severity severity, const char* msg) noexcept override {
        // Log based on severity
        switch (severity) {
            case Severity::kINTERNAL_ERROR:
            case Severity::kERROR:
                std::cerr << "[TensorRT ERROR] " << msg << std::endl;
                break;
            case Severity::kWARNING:
                std::cout << "[TensorRT WARNING] " << msg << std::endl;
                break;
            case Severity::kINFO:
                std::cout << "[TensorRT INFO] " << msg << std::endl;
                break;
            case Severity::kVERBOSE:
                // Suppress verbose messages
                break;
        }
    }
};
#endif

#ifdef USE_ONNXRUNTIME
#include <onnxruntime_cxx_api.h>
#endif

namespace asi {
namespace inference {

/**
 * @brief Inference backend types
 */
enum class BackendType {
    PYTORCH,
    TENSORRT,
    ONNX_RUNTIME
};

/**
 * @brief Model information structure
 */
struct ModelInfo {
    std::string path;
    BackendType backend;
    std::string device;
    bool loaded = false;
    size_t memory_usage_mb = 0;
    std::string model_type;
    std::vector<std::vector<int64_t>> input_shapes;
    std::vector<std::vector<int64_t>> output_shapes;
};

/**
 * @brief Model Loader class for loading and managing different model formats
 */
class ModelLoader {
public:
    /**
     * @brief Constructor
     */
    ModelLoader();
    
    /**
     * @brief Destructor
     */
    ~ModelLoader();
    
    /**
     * @brief Load a model from file
     * @param model_path Path to the model file
     * @param model_type Type of the model (nlp, vision, rl, etc.)
     * @param device Device to load the model on (cpu, cuda, etc.)
     * @return true if successful, false otherwise
     */
    bool loadModel(const std::string& model_path, const std::string& model_type, const std::string& device);
    
    /**
     * @brief Load PyTorch model
     * @param model_path Path to the PyTorch model file (.pt, .pth)
     * @param device Device to load the model on
     * @return true if successful, false otherwise
     */
    bool loadPyTorchModel(const std::string& model_path, const std::string& device);
    
#ifdef USE_ONNXRUNTIME
    /**
     * @brief Load ONNX model
     * @param model_path Path to the ONNX model file (.onnx)
     * @param device Device to load the model on
     * @return true if successful, false otherwise
     */
    bool loadONNXModel(const std::string& model_path, const std::string& device);
#endif
    
#ifdef USE_TENSORRT
    /**
     * @brief Load TensorRT model
     * @param model_path Path to the TensorRT engine file (.trt, .engine)
     * @param device Device to load the model on
     * @return true if successful, false otherwise
     */
    bool loadTensorRTModel(const std::string& model_path, const std::string& device);
#endif
    
    /**
     * @brief Run inference on the loaded model
     * @param input Input tensor
     * @return Output tensor
     */
    torch::Tensor runInference(const torch::Tensor& input);
    
    /**
     * @brief Run PyTorch inference
     * @param input Input tensor
     * @return Output tensor
     */
    torch::Tensor runPyTorchInference(const torch::Tensor& input);
    
#ifdef USE_ONNXRUNTIME
    /**
     * @brief Run ONNX Runtime inference
     * @param input Input tensor
     * @return Output tensor
     */
    torch::Tensor runONNXInference(const torch::Tensor& input);
#endif
    
#ifdef USE_TENSORRT
    /**
     * @brief Run TensorRT inference
     * @param input Input tensor
     * @return Output tensor
     */
    torch::Tensor runTensorRTInference(const torch::Tensor& input);
#endif
    
    /**
     * @brief Get model information
     * @return ModelInfo structure
     */
    ModelInfo getModelInfo() const;
    
    /**
     * @brief Check if a model is loaded
     * @return true if model is loaded, false otherwise
     */
    bool isLoaded() const;
    
    /**
     * @brief Unload the current model
     */
    void unloadModel();
    
    /**
     * @brief Get supported model formats
     * @return Vector of supported file extensions
     */
    static std::vector<std::string> getSupportedFormats() {
        std::vector<std::string> formats = {".pt", ".pth"};
#ifdef USE_ONNXRUNTIME
        formats.push_back(".onnx");
#endif
#ifdef USE_TENSORRT
        formats.push_back(".trt");
        formats.push_back(".engine");
#endif
        return formats;
    }

private:
    // Model information
    ModelInfo model_info_;
    
    // PyTorch components
    torch::jit::script::Module pytorch_model_;
    
#ifdef USE_ONNXRUNTIME
    // ONNX Runtime components
    Ort::Env onnx_env_{ORT_LOGGING_LEVEL_WARNING, "ASI_Inference"};
    std::unique_ptr<Ort::Session> onnx_session_;
    Ort::AllocatorWithDefaultOptions allocator_;
    std::vector<std::string> input_names_;
    std::vector<std::string> output_names_;
    std::vector<std::vector<int64_t>> input_shapes_;
#endif
    
#ifdef USE_TENSORRT
    // TensorRT components
    TensorRTLogger tensorrt_logger_;
    std::unique_ptr<nvinfer1::IRuntime> tensorrt_runtime_;
    std::unique_ptr<nvinfer1::ICudaEngine> tensorrt_engine_;
    std::unique_ptr<nvinfer1::IExecutionContext> tensorrt_context_;
#endif
    
    /**
     * @brief Validate model file
     * @param model_path Path to the model file
     * @return true if valid, false otherwise
     */
    bool validateModelFile(const std::string& model_path);
    
    /**
     * @brief Get model format from file extension
     * @param model_path Path to the model file
     * @return Backend type
     */
    BackendType getBackendFromPath(const std::string& model_path);
    
    /**
     * @brief Optimize model for inference
     * @param backend Backend type
     * @return true if successful, false otherwise
     */
    bool optimizeModel(BackendType backend);
    
    /**
     * @brief Calculate model memory usage
     * @return Memory usage in MB
     */
    size_t calculateMemoryUsage();
};

} // namespace inference
} // namespace asi
