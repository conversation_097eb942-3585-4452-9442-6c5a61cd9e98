/**
 * Logging system for the ASI Learning Engine C++ Inference Server
 * 
 * Provides structured logging with multiple output targets, log levels,
 * and performance-optimized logging for high-throughput inference.
 */

#pragma once

#include <string>
#include <memory>
#include <fstream>
#include <mutex>
#include <thread>
#include <queue>
#include <condition_variable>
#include <chrono>
#include <sstream>

namespace asi_learning {

/**
 * Log levels
 */
enum class LogLevel {
    DEBUG = 0,
    INFO = 1,
    WARNING = 2,
    ERROR = 3,
    CRITICAL = 4
};

/**
 * Log entry structure
 */
struct LogEntry {
    LogLevel level;
    std::string message;
    std::string logger_name;
    std::string file;
    int line;
    std::string function;
    std::chrono::system_clock::time_point timestamp;
    std::thread::id thread_id;
    
    LogEntry() = default;
    LogEntry(LogLevel lvl, const std::string& msg, const std::string& name)
        : level(lvl), message(msg), logger_name(name), line(0),
          timestamp(std::chrono::system_clock::now()),
          thread_id(std::this_thread::get_id()) {}
};

/**
 * Log formatter interface
 */
class LogFormatter {
public:
    virtual ~LogFormatter() = default;
    virtual std::string format(const LogEntry& entry) = 0;
};

/**
 * Simple text formatter
 */
class SimpleFormatter : public LogFormatter {
public:
    std::string format(const LogEntry& entry) override;
};

/**
 * JSON formatter for structured logging
 */
class JsonFormatter : public LogFormatter {
public:
    std::string format(const LogEntry& entry) override;
};

/**
 * Log handler interface
 */
class LogHandler {
public:
    virtual ~LogHandler() = default;
    virtual void handle(const LogEntry& entry) = 0;
    virtual void flush() = 0;
    
    void set_formatter(std::unique_ptr<LogFormatter> formatter) {
        formatter_ = std::move(formatter);
    }
    
    void set_level(LogLevel level) {
        level_ = level;
    }
    
    LogLevel get_level() const {
        return level_;
    }

protected:
    std::unique_ptr<LogFormatter> formatter_;
    LogLevel level_ = LogLevel::INFO;
};

/**
 * Console log handler
 */
class ConsoleHandler : public LogHandler {
public:
    ConsoleHandler();
    void handle(const LogEntry& entry) override;
    void flush() override;

private:
    std::mutex mutex_;
};

/**
 * File log handler with rotation
 */
class FileHandler : public LogHandler {
public:
    FileHandler(const std::string& filename, size_t max_size = 100 * 1024 * 1024, int max_files = 5);
    ~FileHandler();
    
    void handle(const LogEntry& entry) override;
    void flush() override;

private:
    void rotate_if_needed();
    void rotate_files();
    
    std::string filename_;
    std::ofstream file_;
    size_t max_size_;
    int max_files_;
    size_t current_size_;
    std::mutex mutex_;
};

/**
 * Asynchronous logger for high-performance logging
 */
class AsyncLogger {
public:
    AsyncLogger(const std::string& name);
    ~AsyncLogger();
    
    // Disable copy and move
    AsyncLogger(const AsyncLogger&) = delete;
    AsyncLogger& operator=(const AsyncLogger&) = delete;
    AsyncLogger(AsyncLogger&&) = delete;
    AsyncLogger& operator=(AsyncLogger&&) = delete;
    
    void add_handler(std::unique_ptr<LogHandler> handler);
    void set_level(LogLevel level);
    LogLevel get_level() const;
    
    void log(LogLevel level, const std::string& message, 
             const std::string& file = "", int line = 0, const std::string& function = "");
    
    void debug(const std::string& message, const std::string& file = "", int line = 0, const std::string& function = "");
    void info(const std::string& message, const std::string& file = "", int line = 0, const std::string& function = "");
    void warning(const std::string& message, const std::string& file = "", int line = 0, const std::string& function = "");
    void error(const std::string& message, const std::string& file = "", int line = 0, const std::string& function = "");
    void critical(const std::string& message, const std::string& file = "", int line = 0, const std::string& function = "");
    
    void flush();
    void shutdown();

private:
    void worker_thread();
    
    std::string name_;
    LogLevel level_;
    std::vector<std::unique_ptr<LogHandler>> handlers_;
    
    // Async processing
    std::queue<LogEntry> log_queue_;
    std::mutex queue_mutex_;
    std::condition_variable queue_cv_;
    std::thread worker_thread_;
    bool shutdown_requested_;
    
    // Statistics
    std::atomic<uint64_t> total_logs_;
    std::atomic<uint64_t> dropped_logs_;
    static constexpr size_t MAX_QUEUE_SIZE = 10000;
};

/**
 * Global logger manager
 */
class Logger {
public:
    static void initialize(const std::string& level = "INFO", const std::string& log_file = "");
    static void shutdown();
    
    static void set_level(LogLevel level);
    static void set_level(const std::string& level);
    
    static void debug(const std::string& message, const std::string& file = "", int line = 0, const std::string& function = "");
    static void info(const std::string& message, const std::string& file = "", int line = 0, const std::string& function = "");
    static void warning(const std::string& message, const std::string& file = "", int line = 0, const std::string& function = "");
    static void error(const std::string& message, const std::string& file = "", int line = 0, const std::string& function = "");
    static void critical(const std::string& message, const std::string& file = "", int line = 0, const std::string& function = "");
    
    static std::shared_ptr<AsyncLogger> get_logger(const std::string& name);
    static void flush();
    
    // Utility functions
    static LogLevel string_to_level(const std::string& level);
    static std::string level_to_string(LogLevel level);

private:
    static std::shared_ptr<AsyncLogger> default_logger_;
    static std::unordered_map<std::string, std::shared_ptr<AsyncLogger>> loggers_;
    static std::mutex loggers_mutex_;
    static bool initialized_;
};

/**
 * Performance logger for inference metrics
 */
class PerformanceLogger {
public:
    PerformanceLogger(const std::string& operation_name);
    ~PerformanceLogger();
    
    void add_metric(const std::string& name, double value);
    void set_success(bool success);

private:
    std::string operation_name_;
    std::chrono::high_resolution_clock::time_point start_time_;
    std::unordered_map<std::string, double> metrics_;
    bool success_;
};

/**
 * Scoped timer for automatic performance logging
 */
class ScopedTimer {
public:
    ScopedTimer(const std::string& name, LogLevel level = LogLevel::DEBUG);
    ~ScopedTimer();

private:
    std::string name_;
    LogLevel level_;
    std::chrono::high_resolution_clock::time_point start_time_;
};

} // namespace asi_learning

// Convenience macros for logging with file/line information
#define ASI_LOG_DEBUG(msg) asi_learning::Logger::debug(msg, __FILE__, __LINE__, __FUNCTION__)
#define ASI_LOG_INFO(msg) asi_learning::Logger::info(msg, __FILE__, __LINE__, __FUNCTION__)
#define ASI_LOG_WARNING(msg) asi_learning::Logger::warning(msg, __FILE__, __LINE__, __FUNCTION__)
#define ASI_LOG_ERROR(msg) asi_learning::Logger::error(msg, __FILE__, __LINE__, __FUNCTION__)
#define ASI_LOG_CRITICAL(msg) asi_learning::Logger::critical(msg, __FILE__, __LINE__, __FUNCTION__)

// Scoped timer macro
#define ASI_SCOPED_TIMER(name) asi_learning::ScopedTimer _timer(name)
#define ASI_SCOPED_TIMER_LEVEL(name, level) asi_learning::ScopedTimer _timer(name, level)
