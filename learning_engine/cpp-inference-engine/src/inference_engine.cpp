/**
 * Core inference engine implementation for the ASI Learning Engine
 * 
 * Provides high-performance model inference with TensorRT/ONNX optimization,
 * multi-modal support, and edge deployment capabilities.
 */

#include "inference_engine.h"
#include "logger.h"
#include "preprocessing.h"
#include "postprocessing.h"

#include <chrono>
#include <algorithm>
#include <fstream>

namespace asi_learning {

InferenceEngine::InferenceEngine(std::shared_ptr<Config> config)
    : config_(config)
    , initialized_(false)
    , device_type_(DeviceType::CPU)
    , model_loader_(std::make_unique<ModelLoader>())
    , preprocessor_(std::make_unique<Preprocessor>())
    , postprocessor_(std::make_unique<Postprocessor>()) {
    
    // Determine device type
    std::string device = config_->device;
    std::transform(device.begin(), device.end(), device.begin(), ::tolower);
    
    if (device == "cuda" || device == "gpu") {
        device_type_ = DeviceType::CUDA;
    } else if (device == "cpu") {
        device_type_ = DeviceType::CPU;
    } else {
        Logger::warning("Unknown device type: " + device + ", defaulting to CPU");
        device_type_ = DeviceType::CPU;
    }
}

InferenceEngine::~InferenceEngine() {
    shutdown();
}

bool InferenceEngine::initialize() {
    try {
        Logger::info("Initializing inference engine...");

        // Initialize device
        if (!initialize_device()) {
            Logger::error("Failed to initialize device");
            return false;
        }

        // Load models
        if (!load_models()) {
            Logger::error("Failed to load models");
            return false;
        }

        // Initialize preprocessor and postprocessor
        preprocessor_->initialize(config_);
        postprocessor_->initialize(config_);

        // Warm up models
        if (!warmup_models()) {
            Logger::warning("Model warmup failed, but continuing...");
        }

        initialized_ = true;
        Logger::info("Inference engine initialized successfully");
        return true;

    } catch (const std::exception& e) {
        Logger::error("Failed to initialize inference engine: " + std::string(e.what()));
        return false;
    }
}

bool InferenceEngine::initialize_device() {
    try {
        if (device_type_ == DeviceType::CUDA) {
            // Initialize CUDA
            #ifdef USE_CUDA
            int device_count;
            cudaGetDeviceCount(&device_count);
            
            if (device_count == 0) {
                Logger::warning("No CUDA devices found, falling back to CPU");
                device_type_ = DeviceType::CPU;
                return true;
            }
            
            cudaSetDevice(0);  // Use first GPU
            Logger::info("CUDA device initialized");
            #else
            Logger::warning("CUDA support not compiled, falling back to CPU");
            device_type_ = DeviceType::CPU;
            #endif
        }

        Logger::info("Device initialized: " + device_type_to_string(device_type_));
        return true;

    } catch (const std::exception& e) {
        Logger::error("Failed to initialize device: " + std::string(e.what()));
        return false;
    }
}

bool InferenceEngine::load_models() {
    try {
        Logger::info("Loading models...");

        // Load each configured model
        for (const auto& model_config : config_->models) {
            std::string model_name = model_config.name;
            std::string model_path = model_config.path;
            std::string model_type = model_config.type;

            Logger::info("Loading model: " + model_name + " from " + model_path);

            auto model = model_loader_->load_model(model_path, model_type, device_type_);
            if (!model) {
                Logger::error("Failed to load model: " + model_name);
                return false;
            }

            models_[model_name] = std::move(model);
            Logger::info("Model loaded successfully: " + model_name);
        }

        Logger::info("All models loaded successfully");
        return true;

    } catch (const std::exception& e) {
        Logger::error("Failed to load models: " + std::string(e.what()));
        return false;
    }
}

bool InferenceEngine::warmup_models() {
    try {
        Logger::info("Warming up models...");

        for (const auto& [model_name, model] : models_) {
            Logger::debug("Warming up model: " + model_name);

            // Create dummy input based on model type
            auto dummy_input = create_dummy_input(model_name);
            if (!dummy_input.empty()) {
                auto start_time = std::chrono::high_resolution_clock::now();
                
                auto result = model->infer(dummy_input);
                
                auto end_time = std::chrono::high_resolution_clock::now();
                auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
                    end_time - start_time).count();

                Logger::debug("Model " + model_name + " warmup completed in " + 
                             std::to_string(duration) + "ms");
            }
        }

        Logger::info("Model warmup completed");
        return true;

    } catch (const std::exception& e) {
        Logger::warning("Model warmup failed: " + std::string(e.what()));
        return false;
    }
}

InferenceResult InferenceEngine::infer(const InferenceRequest& request) {
    InferenceResult result;
    result.success = false;

    if (!initialized_) {
        result.error_message = "Inference engine not initialized";
        return result;
    }

    try {
        auto start_time = std::chrono::high_resolution_clock::now();

        // Find the model
        auto model_it = models_.find(request.model_name);
        if (model_it == models_.end()) {
            result.error_message = "Model not found: " + request.model_name;
            return result;
        }

        auto& model = model_it->second;

        // Preprocess input
        auto preprocessed_input = preprocessor_->preprocess(request.input_data, request.model_name);
        if (preprocessed_input.empty()) {
            result.error_message = "Preprocessing failed";
            return result;
        }

        // Run inference
        auto raw_output = model->infer(preprocessed_input);
        if (raw_output.empty()) {
            result.error_message = "Inference failed";
            return result;
        }

        // Postprocess output
        result.output_data = postprocessor_->postprocess(raw_output, request.model_name);

        auto end_time = std::chrono::high_resolution_clock::now();
        result.inference_time_ms = std::chrono::duration_cast<std::chrono::milliseconds>(
            end_time - start_time).count();

        result.success = true;
        result.model_name = request.model_name;

        // Record metrics
        record_inference_metrics(request.model_name, result.inference_time_ms, true);

        Logger::debug("Inference completed for model " + request.model_name + 
                     " in " + std::to_string(result.inference_time_ms) + "ms");

    } catch (const std::exception& e) {
        result.error_message = "Inference error: " + std::string(e.what());
        record_inference_metrics(request.model_name, 0, false);
        Logger::error(result.error_message);
    }

    return result;
}

std::vector<InferenceResult> InferenceEngine::batch_infer(
    const std::vector<InferenceRequest>& requests) {
    
    std::vector<InferenceResult> results;
    results.reserve(requests.size());

    if (!initialized_) {
        InferenceResult error_result;
        error_result.success = false;
        error_result.error_message = "Inference engine not initialized";
        
        for (size_t i = 0; i < requests.size(); ++i) {
            results.push_back(error_result);
        }
        return results;
    }

    try {
        // Group requests by model for efficient batch processing
        std::map<std::string, std::vector<size_t>> model_groups;
        for (size_t i = 0; i < requests.size(); ++i) {
            model_groups[requests[i].model_name].push_back(i);
        }

        // Initialize results
        results.resize(requests.size());

        // Process each model group
        for (const auto& [model_name, indices] : model_groups) {
            auto model_it = models_.find(model_name);
            if (model_it == models_.end()) {
                for (size_t idx : indices) {
                    results[idx].success = false;
                    results[idx].error_message = "Model not found: " + model_name;
                }
                continue;
            }

            // Batch process for this model
            std::vector<std::vector<float>> batch_inputs;
            for (size_t idx : indices) {
                auto preprocessed = preprocessor_->preprocess(
                    requests[idx].input_data, model_name);
                batch_inputs.push_back(preprocessed);
            }

            auto start_time = std::chrono::high_resolution_clock::now();
            auto batch_outputs = model_it->second->batch_infer(batch_inputs);
            auto end_time = std::chrono::high_resolution_clock::now();

            auto inference_time = std::chrono::duration_cast<std::chrono::milliseconds>(
                end_time - start_time).count();

            // Process outputs
            for (size_t i = 0; i < indices.size(); ++i) {
                size_t idx = indices[i];
                if (i < batch_outputs.size()) {
                    results[idx].output_data = postprocessor_->postprocess(
                        batch_outputs[i], model_name);
                    results[idx].success = true;
                    results[idx].model_name = model_name;
                    results[idx].inference_time_ms = inference_time / indices.size();
                } else {
                    results[idx].success = false;
                    results[idx].error_message = "Batch inference failed";
                }
            }

            record_inference_metrics(model_name, inference_time, true);
        }

    } catch (const std::exception& e) {
        Logger::error("Batch inference error: " + std::string(e.what()));
        for (auto& result : results) {
            if (!result.success) {
                result.error_message = "Batch inference error: " + std::string(e.what());
            }
        }
    }

    return results;
}

bool InferenceEngine::health_check() {
    if (!initialized_) {
        return false;
    }

    try {
        // Check if all models are responsive
        for (const auto& [model_name, model] : models_) {
            if (!model->health_check()) {
                Logger::warning("Health check failed for model: " + model_name);
                return false;
            }
        }
        return true;

    } catch (const std::exception& e) {
        Logger::error("Health check error: " + std::string(e.what()));
        return false;
    }
}

void InferenceEngine::shutdown() {
    if (!initialized_) {
        return;
    }

    Logger::info("Shutting down inference engine...");

    // Clear models
    models_.clear();

    // Reset components
    model_loader_.reset();
    preprocessor_.reset();
    postprocessor_.reset();

    initialized_ = false;
    Logger::info("Inference engine shutdown completed");
}

std::vector<float> InferenceEngine::create_dummy_input(const std::string& model_name) {
    // Create dummy input based on model configuration
    auto model_config = get_model_config(model_name);
    if (!model_config) {
        return {};
    }

    std::vector<float> dummy_input(model_config->input_size, 0.5f);
    return dummy_input;
}

void InferenceEngine::record_inference_metrics(
    const std::string& model_name, 
    int64_t inference_time_ms, 
    bool success) {
    
    // This would integrate with the metrics collector
    // For now, just log the metrics
    Logger::debug("Inference metrics - Model: " + model_name + 
                 ", Time: " + std::to_string(inference_time_ms) + "ms" +
                 ", Success: " + (success ? "true" : "false"));
}

std::string InferenceEngine::device_type_to_string(DeviceType device_type) {
    switch (device_type) {
        case DeviceType::CPU: return "CPU";
        case DeviceType::CUDA: return "CUDA";
        default: return "Unknown";
    }
}

const ModelConfig* InferenceEngine::get_model_config(const std::string& model_name) {
    for (const auto& model_config : config_->models) {
        if (model_config.name == model_name) {
            return &model_config;
        }
    }
    return nullptr;
}

} // namespace asi_learning
