/**
 * Model Loader implementation for the ASI Learning Engine C++ Inference Server
 * 
 * Provides comprehensive model loading capabilities for PyTorch, ONNX, and TensorRT models
 * with automatic optimization and format conversion.
 */

#include "model_loader.h"
#include "logger.h"
#include <fstream>
#include <filesystem>
#include <algorithm>

namespace asi {
namespace inference {

ModelLoader::ModelLoader() {
    Logger::info("ModelLoader initialized");
}

ModelLoader::~ModelLoader() {
    Logger::info("ModelLoader destroyed");
}

bool ModelLoader::loadPyTorchModel(const std::string& model_path, const std::string& device) {
    try {
        Logger::info("Loading PyTorch model from: " + model_path);
        
        // Check if file exists
        if (!std::filesystem::exists(model_path)) {
            Logger::error("Model file does not exist: " + model_path);
            return false;
        }
        
        // Determine device
        torch::Device torch_device(device);
        
        // Load the model
        pytorch_model_ = torch::jit::load(model_path, torch_device);
        pytorch_model_.eval();
        
        // Store model info
        model_info_.path = model_path;
        model_info_.backend = BackendType::PYTORCH;
        model_info_.device = device;
        model_info_.loaded = true;
        
        Logger::info("PyTorch model loaded successfully");
        return true;
        
    } catch (const std::exception& e) {
        Logger::error("Failed to load PyTorch model: " + std::string(e.what()));
        return false;
    }
}

#ifdef USE_ONNXRUNTIME
bool ModelLoader::loadONNXModel(const std::string& model_path, const std::string& device) {
    try {
        Logger::info("Loading ONNX model from: " + model_path);
        
        // Check if file exists
        if (!std::filesystem::exists(model_path)) {
            Logger::error("Model file does not exist: " + model_path);
            return false;
        }
        
        // Create ONNX Runtime session options
        Ort::SessionOptions session_options;
        session_options.SetIntraOpNumThreads(4);
        session_options.SetGraphOptimizationLevel(GraphOptimizationLevel::ORT_ENABLE_ALL);
        
        // Configure providers based on device
        if (device == "cuda" || device.find("cuda") != std::string::npos) {
            OrtCUDAProviderOptions cuda_options{};
            cuda_options.device_id = 0;
            session_options.AppendExecutionProvider_CUDA(cuda_options);
        }
        
        // Create session
        onnx_session_ = std::make_unique<Ort::Session>(onnx_env_, model_path.c_str(), session_options);
        
        // Get input/output info
        size_t num_input_nodes = onnx_session_->GetInputCount();
        size_t num_output_nodes = onnx_session_->GetOutputCount();
        
        // Store input names and shapes
        for (size_t i = 0; i < num_input_nodes; i++) {
            auto input_name = onnx_session_->GetInputNameAllocated(i, allocator_);
            input_names_.push_back(input_name.get());
            
            auto input_shape = onnx_session_->GetInputTypeInfo(i).GetTensorTypeAndShapeInfo().GetShape();
            input_shapes_.push_back(input_shape);
        }
        
        // Store output names
        for (size_t i = 0; i < num_output_nodes; i++) {
            auto output_name = onnx_session_->GetOutputNameAllocated(i, allocator_);
            output_names_.push_back(output_name.get());
        }
        
        // Store model info
        model_info_.path = model_path;
        model_info_.backend = BackendType::ONNX_RUNTIME;
        model_info_.device = device;
        model_info_.loaded = true;
        
        Logger::info("ONNX model loaded successfully");
        Logger::info("Input nodes: " + std::to_string(num_input_nodes));
        Logger::info("Output nodes: " + std::to_string(num_output_nodes));
        
        return true;
        
    } catch (const std::exception& e) {
        Logger::error("Failed to load ONNX model: " + std::string(e.what()));
        return false;
    }
}
#endif

#ifdef USE_TENSORRT
bool ModelLoader::loadTensorRTModel(const std::string& model_path, const std::string& device) {
    try {
        Logger::info("Loading TensorRT model from: " + model_path);
        
        // Check if file exists
        if (!std::filesystem::exists(model_path)) {
            Logger::error("Model file does not exist: " + model_path);
            return false;
        }
        
        // Read engine file
        std::ifstream file(model_path, std::ios::binary);
        if (!file.good()) {
            Logger::error("Failed to open TensorRT engine file");
            return false;
        }
        
        file.seekg(0, std::ios::end);
        size_t size = file.tellg();
        file.seekg(0, std::ios::beg);
        
        std::vector<char> engine_data(size);
        file.read(engine_data.data(), size);
        file.close();
        
        // Create TensorRT runtime
        tensorrt_runtime_ = std::unique_ptr<nvinfer1::IRuntime>(nvinfer1::createInferRuntime(tensorrt_logger_));
        if (!tensorrt_runtime_) {
            Logger::error("Failed to create TensorRT runtime");
            return false;
        }
        
        // Deserialize engine
        tensorrt_engine_ = std::unique_ptr<nvinfer1::ICudaEngine>(
            tensorrt_runtime_->deserializeCudaEngine(engine_data.data(), size)
        );
        if (!tensorrt_engine_) {
            Logger::error("Failed to deserialize TensorRT engine");
            return false;
        }
        
        // Create execution context
        tensorrt_context_ = std::unique_ptr<nvinfer1::IExecutionContext>(
            tensorrt_engine_->createExecutionContext()
        );
        if (!tensorrt_context_) {
            Logger::error("Failed to create TensorRT execution context");
            return false;
        }
        
        // Store model info
        model_info_.path = model_path;
        model_info_.backend = BackendType::TENSORRT;
        model_info_.device = device;
        model_info_.loaded = true;
        
        Logger::info("TensorRT model loaded successfully");
        Logger::info("Engine bindings: " + std::to_string(tensorrt_engine_->getNbBindings()));
        
        return true;
        
    } catch (const std::exception& e) {
        Logger::error("Failed to load TensorRT model: " + std::string(e.what()));
        return false;
    }
}
#endif

bool ModelLoader::loadModel(const std::string& model_path, const std::string& model_type, const std::string& device) {
    // Determine backend from file extension
    std::string extension = std::filesystem::path(model_path).extension().string();
    std::transform(extension.begin(), extension.end(), extension.begin(), ::tolower);
    
    if (extension == ".pt" || extension == ".pth") {
        return loadPyTorchModel(model_path, device);
    }
#ifdef USE_ONNXRUNTIME
    else if (extension == ".onnx") {
        return loadONNXModel(model_path, device);
    }
#endif
#ifdef USE_TENSORRT
    else if (extension == ".trt" || extension == ".engine") {
        return loadTensorRTModel(model_path, device);
    }
#endif
    else {
        Logger::error("Unsupported model format: " + extension);
        return false;
    }
}

torch::Tensor ModelLoader::runInference(const torch::Tensor& input) {
    if (!model_info_.loaded) {
        throw std::runtime_error("No model loaded");
    }
    
    switch (model_info_.backend) {
        case BackendType::PYTORCH:
            return runPyTorchInference(input);
#ifdef USE_ONNXRUNTIME
        case BackendType::ONNX_RUNTIME:
            return runONNXInference(input);
#endif
#ifdef USE_TENSORRT
        case BackendType::TENSORRT:
            return runTensorRTInference(input);
#endif
        default:
            throw std::runtime_error("Unsupported backend type");
    }
}

torch::Tensor ModelLoader::runPyTorchInference(const torch::Tensor& input) {
    try {
        std::vector<torch::jit::IValue> inputs;
        inputs.push_back(input);
        
        at::Tensor output = pytorch_model_.forward(inputs).toTensor();
        return output;
        
    } catch (const std::exception& e) {
        throw std::runtime_error("PyTorch inference failed: " + std::string(e.what()));
    }
}

#ifdef USE_ONNXRUNTIME
torch::Tensor ModelLoader::runONNXInference(const torch::Tensor& input) {
    try {
        // Convert PyTorch tensor to ONNX format
        auto input_tensor_cpu = input.to(torch::kCPU);
        auto input_data = input_tensor_cpu.data_ptr<float>();
        auto input_shape = input_tensor_cpu.sizes().vec();
        
        // Create ONNX tensor
        std::vector<int64_t> onnx_shape(input_shape.begin(), input_shape.end());
        auto memory_info = Ort::MemoryInfo::CreateCpu(OrtArenaAllocator, OrtMemTypeDefault);
        Ort::Value input_tensor = Ort::Value::CreateTensor<float>(
            memory_info, input_data, input_tensor_cpu.numel(), onnx_shape.data(), onnx_shape.size()
        );
        
        // Run inference
        std::vector<const char*> input_names_cstr;
        for (const auto& name : input_names_) {
            input_names_cstr.push_back(name.c_str());
        }
        
        std::vector<const char*> output_names_cstr;
        for (const auto& name : output_names_) {
            output_names_cstr.push_back(name.c_str());
        }
        
        auto output_tensors = onnx_session_->Run(
            Ort::RunOptions{nullptr},
            input_names_cstr.data(), &input_tensor, 1,
            output_names_cstr.data(), output_names_cstr.size()
        );
        
        // Convert back to PyTorch tensor
        auto output_data = output_tensors[0].GetTensorMutableData<float>();
        auto output_shape = output_tensors[0].GetTensorTypeAndShapeInfo().GetShape();
        
        std::vector<int64_t> torch_shape(output_shape.begin(), output_shape.end());
        auto options = torch::TensorOptions().dtype(torch::kFloat32);
        
        return torch::from_blob(output_data, torch_shape, options).clone();
        
    } catch (const std::exception& e) {
        throw std::runtime_error("ONNX inference failed: " + std::string(e.what()));
    }
}
#endif

#ifdef USE_TENSORRT
torch::Tensor ModelLoader::runTensorRTInference(const torch::Tensor& input) {
    try {
        // Get input/output binding indices
        int input_index = tensorrt_engine_->getBindingIndex("input");
        int output_index = tensorrt_engine_->getBindingIndex("output");
        
        if (input_index == -1 || output_index == -1) {
            throw std::runtime_error("Failed to get binding indices");
        }
        
        // Get input/output dimensions
        auto input_dims = tensorrt_engine_->getBindingDimensions(input_index);
        auto output_dims = tensorrt_engine_->getBindingDimensions(output_index);
        
        // Allocate GPU memory
        void* gpu_input;
        void* gpu_output;
        
        size_t input_size = input.numel() * sizeof(float);
        size_t output_size = 1;
        for (int i = 0; i < output_dims.nbDims; ++i) {
            output_size *= output_dims.d[i];
        }
        output_size *= sizeof(float);
        
        cudaMalloc(&gpu_input, input_size);
        cudaMalloc(&gpu_output, output_size);
        
        // Copy input to GPU
        cudaMemcpy(gpu_input, input.data_ptr(), input_size, cudaMemcpyHostToDevice);
        
        // Set up bindings
        void* bindings[] = {gpu_input, gpu_output};
        
        // Run inference
        bool success = tensorrt_context_->executeV2(bindings);
        if (!success) {
            cudaFree(gpu_input);
            cudaFree(gpu_output);
            throw std::runtime_error("TensorRT inference execution failed");
        }
        
        // Copy output back to CPU
        std::vector<float> output_data(output_size / sizeof(float));
        cudaMemcpy(output_data.data(), gpu_output, output_size, cudaMemcpyDeviceToHost);
        
        // Clean up GPU memory
        cudaFree(gpu_input);
        cudaFree(gpu_output);
        
        // Convert to PyTorch tensor
        std::vector<int64_t> torch_shape;
        for (int i = 0; i < output_dims.nbDims; ++i) {
            torch_shape.push_back(output_dims.d[i]);
        }
        
        auto options = torch::TensorOptions().dtype(torch::kFloat32);
        return torch::from_blob(output_data.data(), torch_shape, options).clone();
        
    } catch (const std::exception& e) {
        throw std::runtime_error("TensorRT inference failed: " + std::string(e.what()));
    }
}
#endif

ModelInfo ModelLoader::getModelInfo() const {
    return model_info_;
}

bool ModelLoader::isLoaded() const {
    return model_info_.loaded;
}

void ModelLoader::unloadModel() {
    model_info_.loaded = false;
    model_info_.path.clear();
    
    // Clean up PyTorch model
    pytorch_model_ = torch::jit::script::Module();
    
#ifdef USE_ONNXRUNTIME
    // Clean up ONNX session
    onnx_session_.reset();
    input_names_.clear();
    output_names_.clear();
    input_shapes_.clear();
#endif

#ifdef USE_TENSORRT
    // Clean up TensorRT components
    tensorrt_context_.reset();
    tensorrt_engine_.reset();
    tensorrt_runtime_.reset();
#endif
    
    Logger::info("Model unloaded");
}

} // namespace inference
} // namespace asi
