/**
 * Main entry point for the ASI Learning Engine C++ Inference Server
 * 
 * High-performance inference server with TensorRT/ONNX optimization
 * for edge deployment and real-time model serving.
 */

#include <iostream>
#include <memory>
#include <string>
#include <signal.h>
#include <thread>
#include <chrono>

#include "inference_engine.h"
#include "grpc_server.h"
#include "config.h"
#include "logger.h"
#include "metrics_collector.h"

using namespace asi_learning;

// Global variables for graceful shutdown
std::unique_ptr<InferenceEngine> g_inference_engine;
std::unique_ptr<GrpcServer> g_grpc_server;
std::unique_ptr<MetricsCollector> g_metrics_collector;
bool g_shutdown_requested = false;

void signal_handler(int signal) {
    Logger::info("Received signal " + std::to_string(signal) + ", initiating graceful shutdown...");
    g_shutdown_requested = true;
}

void setup_signal_handlers() {
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
}

int main(int argc, char* argv[]) {
    try {
        // Initialize logger
        Logger::initialize("INFO", "logs/inference_engine.log");
        Logger::info("Starting ASI Learning Engine Inference Server");

        // Setup signal handlers
        setup_signal_handlers();

        // Load configuration
        std::string config_path = "configs/inference_config.yaml";
        if (argc > 1) {
            config_path = argv[1];
        }

        auto config = Config::load(config_path);
        Logger::info("Configuration loaded from: " + config_path);

        // Initialize metrics collector
        g_metrics_collector = std::make_unique<MetricsCollector>(config);
        g_metrics_collector->start();
        Logger::info("Metrics collector started");

        // Initialize inference engine
        g_inference_engine = std::make_unique<InferenceEngine>(config);
        if (!g_inference_engine->initialize()) {
            Logger::error("Failed to initialize inference engine");
            return 1;
        }
        Logger::info("Inference engine initialized");

        // Initialize gRPC server
        g_grpc_server = std::make_unique<GrpcServer>(config, g_inference_engine.get());
        if (!g_grpc_server->start()) {
            Logger::error("Failed to start gRPC server");
            return 1;
        }
        Logger::info("gRPC server started on port " + std::to_string(config->grpc_port));

        // Main loop
        Logger::info("Inference server is running. Press Ctrl+C to stop.");
        
        while (!g_shutdown_requested) {
            std::this_thread::sleep_for(std::chrono::seconds(1));
            
            // Health check
            if (!g_inference_engine->health_check()) {
                Logger::warning("Inference engine health check failed");
            }
            
            // Report metrics
            g_metrics_collector->record_metric("server.uptime", 
                std::chrono::duration_cast<std::chrono::seconds>(
                    std::chrono::steady_clock::now().time_since_epoch()
                ).count());
        }

        // Graceful shutdown
        Logger::info("Shutting down inference server...");

        if (g_grpc_server) {
            g_grpc_server->stop();
            Logger::info("gRPC server stopped");
        }

        if (g_inference_engine) {
            g_inference_engine->shutdown();
            Logger::info("Inference engine shutdown");
        }

        if (g_metrics_collector) {
            g_metrics_collector->stop();
            Logger::info("Metrics collector stopped");
        }

        Logger::info("Inference server shutdown completed");
        return 0;

    } catch (const std::exception& e) {
        Logger::error("Fatal error: " + std::string(e.what()));
        return 1;
    } catch (...) {
        Logger::error("Unknown fatal error occurred");
        return 1;
    }
}

/**
 * Print usage information
 */
void print_usage(const char* program_name) {
    std::cout << "Usage: " << program_name << " [config_file]" << std::endl;
    std::cout << "  config_file: Path to configuration file (default: configs/inference_config.yaml)" << std::endl;
    std::cout << std::endl;
    std::cout << "Environment Variables:" << std::endl;
    std::cout << "  ASI_LOG_LEVEL: Set log level (DEBUG, INFO, WARNING, ERROR)" << std::endl;
    std::cout << "  ASI_GRPC_PORT: Override gRPC server port" << std::endl;
    std::cout << "  ASI_MODEL_PATH: Override model path" << std::endl;
    std::cout << "  ASI_DEVICE: Override inference device (cpu, cuda)" << std::endl;
}
