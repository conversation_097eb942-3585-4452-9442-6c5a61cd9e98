# ASI Learning Engine Orchestrator Configuration
# ===============================================

# Orchestrator settings
orchestrator:
  max_concurrent_jobs: 4
  job_timeout: 86400  # 24 hours in seconds
  checkpoint_dir: "checkpoints"
  model_registry_dir: "models"
  temp_dir: "/tmp/asi_learning"

# Kafka configuration for data streaming
kafka:
  bootstrap_servers: "localhost:9092"
  training_topics:
    - "asi-learning-engine"
    - "asi-nlp-data"
    - "asi-vision-data"
    - "asi-rl-data"
    - "asi-abstraction-data"
  consumer_group: "asi-learning-engine"
  auto_offset_reset: "latest"
  enable_auto_commit: false
  max_poll_records: 500
  session_timeout_ms: 30000

# Data integration service configuration
data_integration:
  endpoint: "localhost:50055"
  timeout: 30
  max_retries: 3
  retry_delay: 1.0

# Monitoring and metrics configuration
monitoring:
  enabled: true
  prometheus_port: 8090
  report_interval: 30
  mlflow_tracking_uri: "http://localhost:5000"
  tensorboard_log_dir: "logs/tensorboard"

# NLP training configuration
nlp:
  model_name: "bert-base-uncased"
  max_sequence_length: 512
  num_labels: 2
  dropout_rate: 0.1
  warmup_steps: 1000
  weight_decay: 0.01
  training:
    batch_size: 32
    epochs: 10
    learning_rate: 0.00002
    optimizer: "adamw"
    scheduler: "linear"
    gradient_clip_norm: 1.0
    mixed_precision: true
    checkpoint_frequency: 5
    validation_frequency: 1
    early_stopping_patience: 3

# Vision training configuration
vision:
  model_name: "resnet50"
  image_size: 224
  num_classes: 1000
  augmentation_strength: 0.5
  pretrained: true
  freeze_backbone: false
  training:
    batch_size: 64
    epochs: 50
    learning_rate: 0.001
    optimizer: "adam"
    scheduler: "cosine"
    gradient_clip_norm: 1.0
    mixed_precision: true
    checkpoint_frequency: 10
    validation_frequency: 5
    early_stopping_patience: 10

# Reinforcement Learning configuration
rl:
  algorithm: "PPO"
  environment: "CartPole-v1"
  total_timesteps: 1000000
  learning_rate: 0.0003
  n_steps: 2048
  batch_size: 64
  n_epochs: 10
  gamma: 0.99
  gae_lambda: 0.95

# Abstraction learning configuration
abstraction:
  model_type: "transformer"
  hidden_size: 512
  num_layers: 6
  num_heads: 8
  dropout_rate: 0.1
  max_context_length: 1024
  training:
    batch_size: 16
    epochs: 20
    learning_rate: 0.0001
    optimizer: "adam"
    scheduler: "cosine"
    gradient_clip_norm: 1.0
    mixed_precision: true
    checkpoint_frequency: 5
    validation_frequency: 2
    early_stopping_patience: 5

# Model evaluation configuration
evaluation:
  metrics:
    - "accuracy"
    - "precision"
    - "recall"
    - "f1"
  test_batch_size: 64
  save_predictions: true
  compute_confusion_matrix: true
  benchmark_datasets: []

# Environment-specific overrides
development:
  orchestrator:
    max_concurrent_jobs: 2
  monitoring:
    report_interval: 10
  nlp:
    training:
      epochs: 3
      batch_size: 16
  vision:
    training:
      epochs: 5
      batch_size: 32
  abstraction:
    training:
      epochs: 5
      batch_size: 8

production:
  orchestrator:
    max_concurrent_jobs: 8
  monitoring:
    report_interval: 60
  kafka:
    bootstrap_servers: "kafka-1:9092,kafka-2:9092,kafka-3:9092"
  data_integration:
    endpoint: "data-integration-service:50055"
  monitoring:
    mlflow_tracking_uri: "http://mlflow-server:5000"

# Hardware configuration
hardware:
  use_gpu: true
  gpu_memory_fraction: 0.8
  mixed_precision: true
  distributed_training: false
  num_workers: 4

# Security configuration
security:
  enable_tls: false
  cert_file: ""
  key_file: ""
  ca_file: ""
  verify_ssl: true

# Logging configuration
logging:
  level: "INFO"
  format: "structured"
  output:
    - "console"
    - "file"
  file_path: "logs/orchestrator.log"
  max_file_size: "100MB"
  backup_count: 5

# Resource limits
resources:
  memory_limit: "16GB"
  cpu_limit: 8
  gpu_limit: 1
  disk_space_limit: "100GB"

# Feature flags
features:
  enable_auto_scaling: true
  enable_model_compression: true
  enable_quantization: false
  enable_pruning: false
  enable_knowledge_distillation: false
  enable_federated_learning: false

# Model registry configuration
model_registry:
  backend: "mlflow"
  staging_dir: "models/staging"
  production_dir: "models/production"
  versioning: true
  auto_promotion: false
  retention_policy:
    max_versions: 10
    max_age_days: 90

# Data pipeline configuration
data_pipeline:
  preprocessing:
    normalize: true
    augment: true
    cache: true
  validation:
    split_ratio: 0.2
    stratify: true
    shuffle: true
  batch_processing:
    prefetch_factor: 2
    num_workers: 4
    pin_memory: true
