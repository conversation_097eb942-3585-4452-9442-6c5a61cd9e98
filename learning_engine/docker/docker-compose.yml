version: '3.8'

services:
  # Python Training Engine
  training-engine:
    build:
      context: ../python-training-engine
      dockerfile: Dockerfile
    container_name: asi-learning-training
    hostname: training-engine
    restart: unless-stopped
    environment:
      - CUDA_VISIBLE_DEVICES=0
      - PYTHONPATH=/app/src
      - ASI_KAFKA_BOOTSTRAP_SERVERS=kafka:9092
      - ASI_DATA_INTEGRATION_ENDPOINT=data-integration:50055
      - ASI_MONITORING_ENABLED=true
      - ASI_MONITORING_PROMETHEUS_PORT=8090
      - ASI_MLFLOW_TRACKING_URI=http://mlflow:5000
      - ASI_MAX_CONCURRENT_JOBS=2
    ports:
      - "8090:8090"  # Prometheus metrics
      - "8093:8093"  # Health check
    volumes:
      - ../configs:/app/configs:ro
      - ../models:/app/models
      - ../checkpoints:/app/checkpoints
      - ../logs:/app/logs
      - training-data:/app/data
    networks:
      - asi-learning
    depends_on:
      - kafka
      - mlflow
      - redis
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8093/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # C++ Inference Engine
  inference-engine:
    build:
      context: ../cpp-inference-engine
      dockerfile: Dockerfile
    container_name: asi-learning-inference
    hostname: inference-engine
    restart: unless-stopped
    environment:
      - CUDA_VISIBLE_DEVICES=0
      - ASI_INFERENCE_DEVICE=cuda
      - ASI_INFERENCE_BATCH_SIZE=1
      - ASI_MODEL_REGISTRY_URL=http://mlflow:5000
      - ASI_GRPC_PORT=50060
    ports:
      - "8091:8091"  # Prometheus metrics
      - "8094:8094"  # Health check
      - "50060:50060"  # gRPC server
    volumes:
      - ../models:/app/models:ro
      - ../configs:/app/configs:ro
      - ../logs:/app/logs
    networks:
      - asi-learning
    depends_on:
      - training-engine
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8094/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # gRPC Model Server
  grpc-server:
    build:
      context: ../grpc-model-server
      dockerfile: Dockerfile
    container_name: asi-learning-grpc
    hostname: grpc-server
    restart: unless-stopped
    environment:
      - ASI_GRPC_PORT=50061
      - ASI_TRAINING_ENGINE_ENDPOINT=training-engine:8093
      - ASI_INFERENCE_ENGINE_ENDPOINT=inference-engine:50060
      - ASI_MODEL_REGISTRY_URL=http://mlflow:5000
    ports:
      - "8092:8092"  # Prometheus metrics
      - "8095:8095"  # Health check
      - "50061:50061"  # gRPC server
    volumes:
      - ../configs:/app/configs:ro
      - ../logs:/app/logs
    networks:
      - asi-learning
    depends_on:
      - training-engine
      - inference-engine
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8095/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MLflow Tracking Server
  mlflow:
    image: python:3.9-slim
    container_name: asi-learning-mlflow
    hostname: mlflow
    restart: unless-stopped
    command: >
      bash -c "
        pip install mlflow[extras] psycopg2-binary &&
        mlflow server 
          --backend-store-uri ****************************************/mlflow
          --default-artifact-root /mlflow/artifacts
          --host 0.0.0.0
          --port 5000
      "
    environment:
      - MLFLOW_TRACKING_URI=http://0.0.0.0:5000
    ports:
      - "5000:5000"
    volumes:
      - mlflow-artifacts:/mlflow/artifacts
    networks:
      - asi-learning
    depends_on:
      - postgres
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # TensorBoard
  tensorboard:
    image: tensorflow/tensorflow:latest
    container_name: asi-learning-tensorboard
    hostname: tensorboard
    restart: unless-stopped
    command: tensorboard --logdir=/logs --host=0.0.0.0 --port=6006
    ports:
      - "6006:6006"
    volumes:
      - ../logs/tensorboard:/logs
    networks:
      - asi-learning
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6006"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL for MLflow
  postgres:
    image: postgres:13
    container_name: asi-learning-postgres
    hostname: postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=mlflow
      - POSTGRES_USER=mlflow
      - POSTGRES_PASSWORD=mlflow
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - asi-learning
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U mlflow"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for caching
  redis:
    image: redis:7-alpine
    container_name: asi-learning-redis
    hostname: redis
    restart: unless-stopped
    command: redis-server --appendonly yes
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - asi-learning
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Kafka for streaming data
  kafka:
    image: confluentinc/cp-kafka:latest
    container_name: asi-learning-kafka
    hostname: kafka
    restart: unless-stopped
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: true
    ports:
      - "9092:9092"
    volumes:
      - kafka-data:/var/lib/kafka/data
    networks:
      - asi-learning
    depends_on:
      - zookeeper
    healthcheck:
      test: ["CMD", "kafka-broker-api-versions", "--bootstrap-server", "localhost:9092"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Zookeeper for Kafka
  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    container_name: asi-learning-zookeeper
    hostname: zookeeper
    restart: unless-stopped
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    volumes:
      - zookeeper-data:/var/lib/zookeeper/data
      - zookeeper-logs:/var/lib/zookeeper/log
    networks:
      - asi-learning
    healthcheck:
      test: ["CMD", "echo", "ruok", "|", "nc", "localhost", "2181"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus for metrics collection
  prometheus:
    image: prom/prometheus:latest
    container_name: asi-learning-prometheus
    hostname: prometheus
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    networks:
      - asi-learning
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9090/-/healthy"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    container_name: asi-learning-grafana
    hostname: grafana
    restart: unless-stopped
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    ports:
      - "3000:3000"
    volumes:
      - grafana-data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - asi-learning
    depends_on:
      - prometheus
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  training-data:
    driver: local
  mlflow-artifacts:
    driver: local
  postgres-data:
    driver: local
  redis-data:
    driver: local
  kafka-data:
    driver: local
  zookeeper-data:
    driver: local
  zookeeper-logs:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local

networks:
  asi-learning:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
