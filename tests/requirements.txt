# ASI System Testing Dependencies

# Core testing framework
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0
pytest-timeout>=2.1.0
pytest-xdist>=3.3.0
pytest-html>=3.2.0
pytest-json-report>=1.5.0

# Performance testing
pytest-benchmark>=4.0.0
pytest-profiling>=1.7.0
memory-profiler>=0.60.0

# HTTP testing
requests>=2.31.0
httpx>=0.24.0
aiohttp>=3.8.0

# gRPC testing
grpcio>=1.56.0
grpcio-tools>=1.56.0
grpcio-testing>=1.56.0

# Message queue testing
kafka-python>=2.0.2
redis>=4.6.0
pika>=1.3.0

# Database testing
sqlalchemy>=2.0.0
psycopg2-binary>=2.9.0
pymongo>=4.4.0
neo4j>=5.10.0

# Container testing
docker>=6.1.0
testcontainers>=3.7.0

# Data validation and mocking
pydantic>=2.0.0
factory-boy>=3.3.0
faker>=19.0.0
responses>=0.23.0
httpretty>=1.1.0

# Monitoring and observability testing
prometheus-client>=0.17.0
opentelemetry-api>=1.18.0
opentelemetry-sdk>=1.18.0
opentelemetry-instrumentation>=0.39b0

# Security testing
cryptography>=41.0.0
pyjwt>=2.8.0
passlib>=1.7.0

# ML/AI testing
numpy>=1.24.0
pandas>=2.0.0
scikit-learn>=1.3.0
torch>=2.0.0
transformers>=4.30.0

# Utilities
pyyaml>=6.0
python-dotenv>=1.0.0
click>=8.1.0
rich>=13.4.0
tabulate>=0.9.0

# Development tools
black>=23.0.0
isort>=5.12.0
flake8>=6.0.0
mypy>=1.4.0
pre-commit>=3.3.0

# Documentation testing
sphinx>=7.0.0
sphinx-rtd-theme>=1.3.0
myst-parser>=2.0.0
