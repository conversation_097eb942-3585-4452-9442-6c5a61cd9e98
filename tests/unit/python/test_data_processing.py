#!/usr/bin/env python3
"""
Unit Tests for Python Data Processing Components

Comprehensive unit tests for data processing, validation, 
and transformation functions across ASI modules.
"""

import pytest
import json
import time
import uuid
from unittest.mock import Mock, patch, MagicMock
from typing import Dict, Any, List
from dataclasses import dataclass
import numpy as np
import pandas as pd

# Test data structures
@dataclass
class TestDataPoint:
    """Test data point structure."""
    id: str
    timestamp: float
    value: float
    metadata: Dict[str, Any]

class DataProcessor:
    """Example data processor for testing."""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.processed_count = 0
    
    def validate_data(self, data: Dict[str, Any]) -> bool:
        """Validate input data structure."""
        required_fields = ['id', 'timestamp', 'value']
        return all(field in data for field in required_fields)
    
    def transform_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Transform data according to business rules."""
        if not self.validate_data(data):
            raise ValueError("Invalid data structure")
        
        transformed = data.copy()
        
        # Apply transformations
        transformed['normalized_value'] = self._normalize_value(data['value'])
        transformed['processed_timestamp'] = time.time()
        transformed['processor_id'] = self.config.get('processor_id', 'default')
        
        self.processed_count += 1
        return transformed
    
    def _normalize_value(self, value: float) -> float:
        """Normalize value to 0-1 range."""
        min_val = self.config.get('min_value', 0.0)
        max_val = self.config.get('max_value', 100.0)
        
        if max_val == min_val:
            return 0.0
        
        return (value - min_val) / (max_val - min_val)
    
    def batch_process(self, data_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Process a batch of data points."""
        results = []
        for data in data_list:
            try:
                result = self.transform_data(data)
                results.append(result)
            except ValueError as e:
                # Log error and continue with next item
                print(f"Error processing data {data.get('id', 'unknown')}: {e}")
                continue
        return results

class TestDataProcessor:
    """Unit tests for DataProcessor class."""
    
    @pytest.fixture
    def processor(self):
        """Create a DataProcessor instance for testing."""
        config = {
            'processor_id': 'test_processor',
            'min_value': 0.0,
            'max_value': 100.0
        }
        return DataProcessor(config)
    
    @pytest.fixture
    def valid_data(self):
        """Create valid test data."""
        return {
            'id': str(uuid.uuid4()),
            'timestamp': time.time(),
            'value': 50.0,
            'metadata': {'source': 'test'}
        }
    
    @pytest.fixture
    def invalid_data(self):
        """Create invalid test data."""
        return {
            'timestamp': time.time(),
            'metadata': {'source': 'test'}
            # Missing 'id' and 'value' fields
        }
    
    def test_processor_initialization(self):
        """Test processor initialization with config."""
        config = {'processor_id': 'test', 'min_value': 10.0}
        processor = DataProcessor(config)
        
        assert processor.config == config
        assert processor.processed_count == 0
    
    def test_processor_initialization_no_config(self):
        """Test processor initialization without config."""
        processor = DataProcessor()
        
        assert processor.config == {}
        assert processor.processed_count == 0
    
    def test_validate_data_valid(self, processor, valid_data):
        """Test data validation with valid data."""
        assert processor.validate_data(valid_data) is True
    
    def test_validate_data_invalid(self, processor, invalid_data):
        """Test data validation with invalid data."""
        assert processor.validate_data(invalid_data) is False
    
    def test_validate_data_missing_fields(self, processor):
        """Test data validation with various missing fields."""
        test_cases = [
            {'timestamp': time.time(), 'value': 50.0},  # Missing 'id'
            {'id': 'test', 'value': 50.0},              # Missing 'timestamp'
            {'id': 'test', 'timestamp': time.time()},   # Missing 'value'
            {}                                          # Missing all fields
        ]
        
        for data in test_cases:
            assert processor.validate_data(data) is False
    
    def test_normalize_value_normal_range(self, processor):
        """Test value normalization within normal range."""
        # Test with default config (0-100 range)
        assert processor._normalize_value(0.0) == 0.0
        assert processor._normalize_value(50.0) == 0.5
        assert processor._normalize_value(100.0) == 1.0
    
    def test_normalize_value_outside_range(self, processor):
        """Test value normalization outside normal range."""
        # Values outside range should still be normalized
        assert processor._normalize_value(-50.0) == -0.5
        assert processor._normalize_value(150.0) == 1.5
    
    def test_normalize_value_zero_range(self):
        """Test value normalization with zero range."""
        config = {'min_value': 50.0, 'max_value': 50.0}
        processor = DataProcessor(config)
        
        assert processor._normalize_value(50.0) == 0.0
        assert processor._normalize_value(100.0) == 0.0
    
    def test_transform_data_valid(self, processor, valid_data):
        """Test data transformation with valid data."""
        result = processor.transform_data(valid_data)
        
        # Check original fields are preserved
        assert result['id'] == valid_data['id']
        assert result['timestamp'] == valid_data['timestamp']
        assert result['value'] == valid_data['value']
        assert result['metadata'] == valid_data['metadata']
        
        # Check new fields are added
        assert 'normalized_value' in result
        assert 'processed_timestamp' in result
        assert 'processor_id' in result
        
        # Check values
        assert result['normalized_value'] == 0.5  # 50.0 normalized to 0-100 range
        assert result['processor_id'] == 'test_processor'
        assert isinstance(result['processed_timestamp'], float)
        
        # Check processed count is incremented
        assert processor.processed_count == 1
    
    def test_transform_data_invalid(self, processor, invalid_data):
        """Test data transformation with invalid data."""
        with pytest.raises(ValueError, match="Invalid data structure"):
            processor.transform_data(invalid_data)
        
        # Check processed count is not incremented
        assert processor.processed_count == 0
    
    def test_batch_process_all_valid(self, processor):
        """Test batch processing with all valid data."""
        data_list = [
            {'id': f'test_{i}', 'timestamp': time.time(), 'value': float(i * 10)}
            for i in range(5)
        ]
        
        results = processor.batch_process(data_list)
        
        assert len(results) == 5
        assert processor.processed_count == 5
        
        for i, result in enumerate(results):
            assert result['id'] == f'test_{i}'
            assert result['normalized_value'] == i * 0.1  # 0, 10, 20, 30, 40 normalized
    
    def test_batch_process_mixed_valid_invalid(self, processor):
        """Test batch processing with mixed valid and invalid data."""
        data_list = [
            {'id': 'test_1', 'timestamp': time.time(), 'value': 10.0},  # Valid
            {'timestamp': time.time(), 'value': 20.0},                  # Invalid (missing id)
            {'id': 'test_3', 'timestamp': time.time(), 'value': 30.0},  # Valid
            {'id': 'test_4', 'value': 40.0},                           # Invalid (missing timestamp)
        ]
        
        results = processor.batch_process(data_list)
        
        # Only valid items should be processed
        assert len(results) == 2
        assert processor.processed_count == 2
        
        assert results[0]['id'] == 'test_1'
        assert results[1]['id'] == 'test_3'
    
    def test_batch_process_empty_list(self, processor):
        """Test batch processing with empty list."""
        results = processor.batch_process([])
        
        assert results == []
        assert processor.processed_count == 0
    
    @pytest.mark.performance
    def test_batch_process_performance(self, processor):
        """Test batch processing performance with large dataset."""
        # Generate large dataset
        data_list = [
            {'id': f'test_{i}', 'timestamp': time.time(), 'value': float(i)}
            for i in range(1000)
        ]
        
        start_time = time.time()
        results = processor.batch_process(data_list)
        end_time = time.time()
        
        processing_time = end_time - start_time
        
        assert len(results) == 1000
        assert processor.processed_count == 1000
        assert processing_time < 1.0  # Should process 1000 items in less than 1 second
    
    def test_processor_state_isolation(self):
        """Test that processor instances maintain separate state."""
        processor1 = DataProcessor({'processor_id': 'proc1'})
        processor2 = DataProcessor({'processor_id': 'proc2'})
        
        data = {'id': 'test', 'timestamp': time.time(), 'value': 50.0}
        
        result1 = processor1.transform_data(data)
        result2 = processor2.transform_data(data)
        
        assert processor1.processed_count == 1
        assert processor2.processed_count == 1
        assert result1['processor_id'] == 'proc1'
        assert result2['processor_id'] == 'proc2'

# Integration with pytest-benchmark for performance testing
class TestDataProcessorPerformance:
    """Performance tests for DataProcessor."""
    
    @pytest.fixture
    def processor(self):
        return DataProcessor()
    
    @pytest.fixture
    def sample_data(self):
        return {'id': 'test', 'timestamp': time.time(), 'value': 50.0}
    
    def test_transform_data_benchmark(self, benchmark, processor, sample_data):
        """Benchmark single data transformation."""
        result = benchmark(processor.transform_data, sample_data)
        assert 'normalized_value' in result
    
    def test_batch_process_benchmark(self, benchmark, processor):
        """Benchmark batch processing."""
        data_list = [
            {'id': f'test_{i}', 'timestamp': time.time(), 'value': float(i)}
            for i in range(100)
        ]
        
        results = benchmark(processor.batch_process, data_list)
        assert len(results) == 100
