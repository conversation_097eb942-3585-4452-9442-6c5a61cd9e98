package config

import (
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gopkg.in/yaml.v3"
)

// Test every line of config.go with 100% coverage

func TestConfig_Struct_Creation(t *testing.T) {
	// Test Config struct instantiation - covers lines 13-18
	cfg := &Config{
		GRPCPort:    50051,
		MetricsPort: 8080,
		Kafka:       KafkaConfig{},
		Logging:     LoggingConfig{},
	}
	
	assert.Equal(t, 50051, cfg.GRPCPort)
	assert.Equal(t, 8080, cfg.MetricsPort)
	assert.NotNil(t, cfg.Kafka)
	assert.NotNil(t, cfg.Logging)
}

func TestKafkaConfig_Struct_Creation(t *testing.T) {
	// Test KafkaConfig struct instantiation - covers lines 21-43
	kafkaConfig := KafkaConfig{
		Brokers:              []string{"localhost:9092"},
		ClientID:             "test-client",
		MaxMessageBytes:      1000000,
		RequiredAcks:         1,
		Timeout:              30 * time.Second,
		CompressionType:      "snappy",
		FlushFrequency:       100 * time.Millisecond,
		FlushMessages:        100,
		FlushBytes:           16384,
		RetryMax:             3,
		RetryBackoff:         100 * time.Millisecond,
		EnableIdempotence:    true,
		MaxInFlightRequests:  5,
		BatchSize:            16384,
		LingerMs:             5 * time.Millisecond,
		SecurityProtocol:     "PLAINTEXT",
		SASLMechanism:        "",
		SASLUsername:         "",
		SASLPassword:         "",
		TLSEnabled:           false,
		TLSSkipVerify:        false,
	}
	
	assert.Equal(t, []string{"localhost:9092"}, kafkaConfig.Brokers)
	assert.Equal(t, "test-client", kafkaConfig.ClientID)
	assert.Equal(t, 1000000, kafkaConfig.MaxMessageBytes)
	assert.Equal(t, 1, kafkaConfig.RequiredAcks)
	assert.Equal(t, 30*time.Second, kafkaConfig.Timeout)
	assert.Equal(t, "snappy", kafkaConfig.CompressionType)
	assert.Equal(t, 100*time.Millisecond, kafkaConfig.FlushFrequency)
	assert.Equal(t, 100, kafkaConfig.FlushMessages)
	assert.Equal(t, 16384, kafkaConfig.FlushBytes)
	assert.Equal(t, 3, kafkaConfig.RetryMax)
	assert.Equal(t, 100*time.Millisecond, kafkaConfig.RetryBackoff)
	assert.True(t, kafkaConfig.EnableIdempotence)
	assert.Equal(t, 5, kafkaConfig.MaxInFlightRequests)
	assert.Equal(t, 16384, kafkaConfig.BatchSize)
	assert.Equal(t, 5*time.Millisecond, kafkaConfig.LingerMs)
	assert.Equal(t, "PLAINTEXT", kafkaConfig.SecurityProtocol)
	assert.Equal(t, "", kafkaConfig.SASLMechanism)
	assert.Equal(t, "", kafkaConfig.SASLUsername)
	assert.Equal(t, "", kafkaConfig.SASLPassword)
	assert.False(t, kafkaConfig.TLSEnabled)
	assert.False(t, kafkaConfig.TLSSkipVerify)
}

func TestLoggingConfig_Struct_Creation(t *testing.T) {
	// Test LoggingConfig struct instantiation - covers lines 46-49
	loggingConfig := LoggingConfig{
		Level:  "info",
		Format: "json",
	}
	
	assert.Equal(t, "info", loggingConfig.Level)
	assert.Equal(t, "json", loggingConfig.Format)
}

func TestLoad_WithDefaultValues(t *testing.T) {
	// Clear all environment variables to test defaults
	clearAllEnvVars()
	
	// Test Load function with default values - covers lines 52-93
	cfg, err := Load()
	
	require.NoError(t, err)
	require.NotNil(t, cfg)
	
	// Test default values - covers lines 54-55
	assert.Equal(t, 50051, cfg.GRPCPort)
	assert.Equal(t, 8080, cfg.MetricsPort)
	
	// Test Kafka default values - covers lines 56-78
	assert.Equal(t, []string{"localhost:9092"}, cfg.Kafka.Brokers)
	assert.Equal(t, "asi-ingestion-producer", cfg.Kafka.ClientID)
	assert.Equal(t, 1000000, cfg.Kafka.MaxMessageBytes)
	assert.Equal(t, 1, cfg.Kafka.RequiredAcks)
	assert.Equal(t, 30*time.Second, cfg.Kafka.Timeout)
	assert.Equal(t, "snappy", cfg.Kafka.CompressionType)
	assert.Equal(t, 100*time.Millisecond, cfg.Kafka.FlushFrequency)
	assert.Equal(t, 100, cfg.Kafka.FlushMessages)
	assert.Equal(t, 16384, cfg.Kafka.FlushBytes)
	assert.Equal(t, 3, cfg.Kafka.RetryMax)
	assert.Equal(t, 100*time.Millisecond, cfg.Kafka.RetryBackoff)
	assert.True(t, cfg.Kafka.EnableIdempotence)
	assert.Equal(t, 5, cfg.Kafka.MaxInFlightRequests)
	assert.Equal(t, 16384, cfg.Kafka.BatchSize)
	assert.Equal(t, 5*time.Millisecond, cfg.Kafka.LingerMs)
	assert.Equal(t, "PLAINTEXT", cfg.Kafka.SecurityProtocol)
	assert.Equal(t, "", cfg.Kafka.SASLMechanism)
	assert.Equal(t, "", cfg.Kafka.SASLUsername)
	assert.Equal(t, "", cfg.Kafka.SASLPassword)
	assert.False(t, cfg.Kafka.TLSEnabled)
	assert.False(t, cfg.Kafka.TLSSkipVerify)
	
	// Test Logging default values - covers lines 79-82
	assert.Equal(t, "info", cfg.Logging.Level)
	assert.Equal(t, "json", cfg.Logging.Format)
}

func TestLoad_WithEnvironmentVariables(t *testing.T) {
	// Clear environment first
	clearAllEnvVars()
	
	// Set environment variables to test env loading
	envVars := map[string]string{
		"GRPC_PORT":                    "50052",
		"METRICS_PORT":                 "8081",
		"KAFKA_BROKERS":                "broker1:9092,broker2:9092",
		"KAFKA_CLIENT_ID":              "test-client-id",
		"KAFKA_MAX_MESSAGE_BYTES":      "2000000",
		"KAFKA_REQUIRED_ACKS":          "2",
		"KAFKA_TIMEOUT":                "45s",
		"KAFKA_COMPRESSION":            "gzip",
		"KAFKA_FLUSH_FREQUENCY":        "200ms",
		"KAFKA_FLUSH_MESSAGES":         "200",
		"KAFKA_FLUSH_BYTES":            "32768",
		"KAFKA_RETRY_MAX":              "5",
		"KAFKA_RETRY_BACKOFF":          "200ms",
		"KAFKA_ENABLE_IDEMPOTENCE":     "false",
		"KAFKA_MAX_IN_FLIGHT":          "10",
		"KAFKA_BATCH_SIZE":             "32768",
		"KAFKA_LINGER_MS":              "10ms",
		"KAFKA_SECURITY_PROTOCOL":      "SASL_SSL",
		"KAFKA_SASL_MECHANISM":         "PLAIN",
		"KAFKA_SASL_USERNAME":          "testuser",
		"KAFKA_SASL_PASSWORD":          "testpass",
		"KAFKA_TLS_ENABLED":            "true",
		"KAFKA_TLS_SKIP_VERIFY":        "true",
		"LOG_LEVEL":                    "debug",
		"LOG_FORMAT":                   "text",
	}
	
	for key, value := range envVars {
		os.Setenv(key, value)
	}
	defer clearAllEnvVars()
	
	cfg, err := Load()
	require.NoError(t, err)
	require.NotNil(t, cfg)
	
	// Verify all environment variables are loaded correctly
	assert.Equal(t, 50052, cfg.GRPCPort)
	assert.Equal(t, 8081, cfg.MetricsPort)
	assert.Equal(t, []string{"broker1:9092", "broker2:9092"}, cfg.Kafka.Brokers)
	assert.Equal(t, "test-client-id", cfg.Kafka.ClientID)
	assert.Equal(t, 2000000, cfg.Kafka.MaxMessageBytes)
	assert.Equal(t, 2, cfg.Kafka.RequiredAcks)
	assert.Equal(t, 45*time.Second, cfg.Kafka.Timeout)
	assert.Equal(t, "gzip", cfg.Kafka.CompressionType)
	assert.Equal(t, 200*time.Millisecond, cfg.Kafka.FlushFrequency)
	assert.Equal(t, 200, cfg.Kafka.FlushMessages)
	assert.Equal(t, 32768, cfg.Kafka.FlushBytes)
	assert.Equal(t, 5, cfg.Kafka.RetryMax)
	assert.Equal(t, 200*time.Millisecond, cfg.Kafka.RetryBackoff)
	assert.False(t, cfg.Kafka.EnableIdempotence)
	assert.Equal(t, 10, cfg.Kafka.MaxInFlightRequests)
	assert.Equal(t, 32768, cfg.Kafka.BatchSize)
	assert.Equal(t, 10*time.Millisecond, cfg.Kafka.LingerMs)
	assert.Equal(t, "SASL_SSL", cfg.Kafka.SecurityProtocol)
	assert.Equal(t, "PLAIN", cfg.Kafka.SASLMechanism)
	assert.Equal(t, "testuser", cfg.Kafka.SASLUsername)
	assert.Equal(t, "testpass", cfg.Kafka.SASLPassword)
	assert.True(t, cfg.Kafka.TLSEnabled)
	assert.True(t, cfg.Kafka.TLSSkipVerify)
	assert.Equal(t, "debug", cfg.Logging.Level)
	assert.Equal(t, "text", cfg.Logging.Format)
}

func TestLoad_WithConfigFile(t *testing.T) {
	// Clear environment variables
	clearAllEnvVars()
	
	// Create temporary config file
	configData := `
grpc_port: 50053
metrics_port: 8082
kafka:
  brokers: ["file-broker:9092"]
  client_id: "file-client"
  max_message_bytes: 3000000
  required_acks: 3
  timeout: 60s
  compression_type: "lz4"
  flush_frequency: 300ms
  flush_messages: 300
  flush_bytes: 65536
  retry_max: 7
  retry_backoff: 300ms
  enable_idempotence: false
  max_in_flight_requests: 15
  batch_size: 65536
  linger_ms: 15ms
  security_protocol: "SSL"
  sasl_mechanism: "SCRAM-SHA-256"
  sasl_username: "fileuser"
  sasl_password: "filepass"
  tls_enabled: true
  tls_skip_verify: false
logging:
  level: "warn"
  format: "json"
`
	
	tmpFile, err := os.CreateTemp("", "config-*.yaml")
	require.NoError(t, err)
	defer os.Remove(tmpFile.Name())
	
	_, err = tmpFile.WriteString(configData)
	require.NoError(t, err)
	tmpFile.Close()
	
	// Set CONFIG_FILE environment variable - covers lines 86-90
	os.Setenv("CONFIG_FILE", tmpFile.Name())
	defer os.Unsetenv("CONFIG_FILE")
	
	cfg, err := Load()
	require.NoError(t, err)
	require.NotNil(t, cfg)
	
	// Verify config file values are loaded
	assert.Equal(t, 50053, cfg.GRPCPort)
	assert.Equal(t, 8082, cfg.MetricsPort)
	assert.Equal(t, []string{"file-broker:9092"}, cfg.Kafka.Brokers)
	assert.Equal(t, "file-client", cfg.Kafka.ClientID)
	assert.Equal(t, 3000000, cfg.Kafka.MaxMessageBytes)
	assert.Equal(t, 3, cfg.Kafka.RequiredAcks)
	assert.Equal(t, 60*time.Second, cfg.Kafka.Timeout)
	assert.Equal(t, "lz4", cfg.Kafka.CompressionType)
	assert.Equal(t, 300*time.Millisecond, cfg.Kafka.FlushFrequency)
	assert.Equal(t, 300, cfg.Kafka.FlushMessages)
	assert.Equal(t, 65536, cfg.Kafka.FlushBytes)
	assert.Equal(t, 7, cfg.Kafka.RetryMax)
	assert.Equal(t, 300*time.Millisecond, cfg.Kafka.RetryBackoff)
	assert.False(t, cfg.Kafka.EnableIdempotence)
	assert.Equal(t, 15, cfg.Kafka.MaxInFlightRequests)
	assert.Equal(t, 65536, cfg.Kafka.BatchSize)
	assert.Equal(t, 15*time.Millisecond, cfg.Kafka.LingerMs)
	assert.Equal(t, "SSL", cfg.Kafka.SecurityProtocol)
	assert.Equal(t, "SCRAM-SHA-256", cfg.Kafka.SASLMechanism)
	assert.Equal(t, "fileuser", cfg.Kafka.SASLUsername)
	assert.Equal(t, "filepass", cfg.Kafka.SASLPassword)
	assert.True(t, cfg.Kafka.TLSEnabled)
	assert.False(t, cfg.Kafka.TLSSkipVerify)
	assert.Equal(t, "warn", cfg.Logging.Level)
	assert.Equal(t, "json", cfg.Logging.Format)
}

func TestLoad_WithInvalidConfigFile(t *testing.T) {
	// Clear environment variables
	clearAllEnvVars()
	
	// Set CONFIG_FILE to non-existent file - covers lines 86-90 error path
	os.Setenv("CONFIG_FILE", "/non/existent/file.yaml")
	defer os.Unsetenv("CONFIG_FILE")
	
	cfg, err := Load()
	assert.Error(t, err)
	assert.Nil(t, cfg)
}

func TestLoadFromFile_Success(t *testing.T) {
	// Test loadFromFile function - covers lines 95-101
	configData := `
grpc_port: 50054
metrics_port: 8083
`
	
	tmpFile, err := os.CreateTemp("", "config-*.yaml")
	require.NoError(t, err)
	defer os.Remove(tmpFile.Name())
	
	_, err = tmpFile.WriteString(configData)
	require.NoError(t, err)
	tmpFile.Close()
	
	cfg := &Config{}
	err = loadFromFile(cfg, tmpFile.Name())
	require.NoError(t, err)
	
	assert.Equal(t, 50054, cfg.GRPCPort)
	assert.Equal(t, 8083, cfg.MetricsPort)
}

func TestLoadFromFile_FileNotFound(t *testing.T) {
	// Test loadFromFile with non-existent file - covers lines 96-98 error path
	cfg := &Config{}
	err := loadFromFile(cfg, "/non/existent/file.yaml")
	assert.Error(t, err)
}

func TestLoadFromFile_InvalidYAML(t *testing.T) {
	// Test loadFromFile with invalid YAML - covers lines 100 error path
	invalidYAML := `
grpc_port: 50054
metrics_port: invalid_yaml_content: [
`
	
	tmpFile, err := os.CreateTemp("", "config-*.yaml")
	require.NoError(t, err)
	defer os.Remove(tmpFile.Name())
	
	_, err = tmpFile.WriteString(invalidYAML)
	require.NoError(t, err)
	tmpFile.Close()
	
	cfg := &Config{}
	err = loadFromFile(cfg, tmpFile.Name())
	assert.Error(t, err)
}

// Test utility functions - covers lines 103-142

func TestGetEnvString_WithValue(t *testing.T) {
	// Test getEnvString with existing environment variable - covers lines 104-105
	os.Setenv("TEST_STRING", "test_value")
	defer os.Unsetenv("TEST_STRING")

	result := getEnvString("TEST_STRING", "default_value")
	assert.Equal(t, "test_value", result)
}

func TestGetEnvString_WithDefault(t *testing.T) {
	// Test getEnvString with default value - covers lines 107
	os.Unsetenv("TEST_STRING_NOT_SET")

	result := getEnvString("TEST_STRING_NOT_SET", "default_value")
	assert.Equal(t, "default_value", result)
}

func TestGetEnvString_WithEmptyValue(t *testing.T) {
	// Test getEnvString with empty environment variable - covers lines 107
	os.Setenv("TEST_STRING_EMPTY", "")
	defer os.Unsetenv("TEST_STRING_EMPTY")

	result := getEnvString("TEST_STRING_EMPTY", "default_value")
	assert.Equal(t, "default_value", result)
}

func TestGetEnvInt_WithValidValue(t *testing.T) {
	// Test getEnvInt with valid integer - covers lines 111-114
	os.Setenv("TEST_INT", "12345")
	defer os.Unsetenv("TEST_INT")

	result := getEnvInt("TEST_INT", 999)
	assert.Equal(t, 12345, result)
}

func TestGetEnvInt_WithInvalidValue(t *testing.T) {
	// Test getEnvInt with invalid integer - covers lines 116
	os.Setenv("TEST_INT_INVALID", "not_a_number")
	defer os.Unsetenv("TEST_INT_INVALID")

	result := getEnvInt("TEST_INT_INVALID", 999)
	assert.Equal(t, 999, result)
}

func TestGetEnvInt_WithDefault(t *testing.T) {
	// Test getEnvInt with default value - covers lines 116
	os.Unsetenv("TEST_INT_NOT_SET")

	result := getEnvInt("TEST_INT_NOT_SET", 999)
	assert.Equal(t, 999, result)
}

func TestGetEnvBool_WithTrueValue(t *testing.T) {
	// Test getEnvBool with true value - covers lines 120-122
	testCases := []string{"true", "True", "TRUE", "1", "t", "T"}

	for _, testCase := range testCases {
		os.Setenv("TEST_BOOL", testCase)
		result := getEnvBool("TEST_BOOL", false)
		assert.True(t, result, "Failed for value: %s", testCase)
	}
	os.Unsetenv("TEST_BOOL")
}

func TestGetEnvBool_WithFalseValue(t *testing.T) {
	// Test getEnvBool with false value - covers lines 120-122
	testCases := []string{"false", "False", "FALSE", "0", "f", "F"}

	for _, testCase := range testCases {
		os.Setenv("TEST_BOOL", testCase)
		result := getEnvBool("TEST_BOOL", true)
		assert.False(t, result, "Failed for value: %s", testCase)
	}
	os.Unsetenv("TEST_BOOL")
}

func TestGetEnvBool_WithInvalidValue(t *testing.T) {
	// Test getEnvBool with invalid boolean - covers lines 124
	os.Setenv("TEST_BOOL_INVALID", "not_a_boolean")
	defer os.Unsetenv("TEST_BOOL_INVALID")

	result := getEnvBool("TEST_BOOL_INVALID", true)
	assert.True(t, result)
}

func TestGetEnvBool_WithDefault(t *testing.T) {
	// Test getEnvBool with default value - covers lines 124
	os.Unsetenv("TEST_BOOL_NOT_SET")

	result := getEnvBool("TEST_BOOL_NOT_SET", true)
	assert.True(t, result)
}

func TestGetEnvDuration_WithValidValue(t *testing.T) {
	// Test getEnvDuration with valid duration - covers lines 129-131
	os.Setenv("TEST_DURATION", "5m30s")
	defer os.Unsetenv("TEST_DURATION")

	result := getEnvDuration("TEST_DURATION", 1*time.Minute)
	assert.Equal(t, 5*time.Minute+30*time.Second, result)
}

func TestGetEnvDuration_WithInvalidValue(t *testing.T) {
	// Test getEnvDuration with invalid duration - covers lines 133
	os.Setenv("TEST_DURATION_INVALID", "not_a_duration")
	defer os.Unsetenv("TEST_DURATION_INVALID")

	result := getEnvDuration("TEST_DURATION_INVALID", 1*time.Minute)
	assert.Equal(t, 1*time.Minute, result)
}

func TestGetEnvDuration_WithDefault(t *testing.T) {
	// Test getEnvDuration with default value - covers lines 133
	os.Unsetenv("TEST_DURATION_NOT_SET")

	result := getEnvDuration("TEST_DURATION_NOT_SET", 1*time.Minute)
	assert.Equal(t, 1*time.Minute, result)
}

func TestGetEnvStringSlice_WithValue(t *testing.T) {
	// Test getEnvStringSlice with comma-separated values - covers lines 138-139
	os.Setenv("TEST_STRING_SLICE", "value1,value2,value3")
	defer os.Unsetenv("TEST_STRING_SLICE")

	result := getEnvStringSlice("TEST_STRING_SLICE", []string{"default"})
	assert.Equal(t, []string{"value1", "value2", "value3"}, result)
}

func TestGetEnvStringSlice_WithSingleValue(t *testing.T) {
	// Test getEnvStringSlice with single value - covers lines 138-139
	os.Setenv("TEST_STRING_SLICE_SINGLE", "single_value")
	defer os.Unsetenv("TEST_STRING_SLICE_SINGLE")

	result := getEnvStringSlice("TEST_STRING_SLICE_SINGLE", []string{"default"})
	assert.Equal(t, []string{"single_value"}, result)
}

func TestGetEnvStringSlice_WithDefault(t *testing.T) {
	// Test getEnvStringSlice with default value - covers lines 141
	os.Unsetenv("TEST_STRING_SLICE_NOT_SET")

	result := getEnvStringSlice("TEST_STRING_SLICE_NOT_SET", []string{"default1", "default2"})
	assert.Equal(t, []string{"default1", "default2"}, result)
}

func TestGetEnvStringSlice_WithEmptyValue(t *testing.T) {
	// Test getEnvStringSlice with empty environment variable - covers lines 141
	os.Setenv("TEST_STRING_SLICE_EMPTY", "")
	defer os.Unsetenv("TEST_STRING_SLICE_EMPTY")

	result := getEnvStringSlice("TEST_STRING_SLICE_EMPTY", []string{"default"})
	assert.Equal(t, []string{"default"}, result)
}

// Helper function to clear all environment variables
func clearAllEnvVars() {
	envVars := []string{
		"GRPC_PORT", "METRICS_PORT", "KAFKA_BROKERS", "KAFKA_CLIENT_ID",
		"KAFKA_MAX_MESSAGE_BYTES", "KAFKA_REQUIRED_ACKS", "KAFKA_TIMEOUT",
		"KAFKA_COMPRESSION", "KAFKA_FLUSH_FREQUENCY", "KAFKA_FLUSH_MESSAGES",
		"KAFKA_FLUSH_BYTES", "KAFKA_RETRY_MAX", "KAFKA_RETRY_BACKOFF",
		"KAFKA_ENABLE_IDEMPOTENCE", "KAFKA_MAX_IN_FLIGHT", "KAFKA_BATCH_SIZE",
		"KAFKA_LINGER_MS", "KAFKA_SECURITY_PROTOCOL", "KAFKA_SASL_MECHANISM",
		"KAFKA_SASL_USERNAME", "KAFKA_SASL_PASSWORD", "KAFKA_TLS_ENABLED",
		"KAFKA_TLS_SKIP_VERIFY", "LOG_LEVEL", "LOG_FORMAT", "CONFIG_FILE",
	}

	for _, envVar := range envVars {
		os.Unsetenv(envVar)
	}
}
