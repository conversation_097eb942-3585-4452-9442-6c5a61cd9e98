package kafka

import (
	"context"
	"crypto/tls"
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/Shopify/sarama"
	"github.com/Shopify/sarama/mocks"
	"github.com/asi-system/ingestion/go-kafka-producer/internal/config"
	"github.com/asi-system/ingestion/go-kafka-producer/internal/metrics"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// Test every line of producer.go with 100% coverage

func TestMessage_Struct_Creation(t *testing.T) {
	// Test Message struct instantiation - covers lines 27-33
	timestamp := time.Now()
	headers := map[string]string{"key1": "value1", "key2": "value2"}
	
	msg := &Message{
		Topic:     "test-topic",
		Key:       "test-key",
		Value:     []byte("test-value"),
		Headers:   headers,
		Timestamp: timestamp,
	}
	
	assert.Equal(t, "test-topic", msg.Topic)
	assert.Equal(t, "test-key", msg.Key)
	assert.Equal(t, []byte("test-value"), msg.Value)
	assert.Equal(t, headers, msg.Headers)
	assert.Equal(t, timestamp, msg.Timestamp)
}

func TestProducer_Struct_Creation(t *testing.T) {
	// Test Producer struct instantiation - covers lines 17-24
	logger := logrus.New()
	metricsCollector := metrics.NewCollector()
	mockProducer := mocks.NewAsyncProducer(t, nil)
	ctx, cancel := context.WithCancel(context.Background())
	
	producer := &Producer{
		producer: mockProducer,
		logger:   logger,
		metrics:  metricsCollector,
		wg:       sync.WaitGroup{},
		ctx:      ctx,
		cancel:   cancel,
	}
	
	assert.NotNil(t, producer.producer)
	assert.NotNil(t, producer.logger)
	assert.NotNil(t, producer.metrics)
	assert.NotNil(t, producer.ctx)
	assert.NotNil(t, producer.cancel)
	
	cancel()
	mockProducer.Close()
}

func TestNewProducer_Success_BasicConfig(t *testing.T) {
	// Test NewProducer with basic configuration - covers lines 36-94
	logger := logrus.New()
	logger.SetLevel(logrus.FatalLevel) // Suppress logs during testing
	metricsCollector := metrics.NewCollector()
	
	cfg := config.KafkaConfig{
		Brokers:              []string{"localhost:9092"},
		ClientID:             "test-client",
		MaxMessageBytes:      1000000,
		RequiredAcks:         1,
		Timeout:              30 * time.Second,
		CompressionType:      "snappy",
		FlushFrequency:       100 * time.Millisecond,
		FlushMessages:        100,
		FlushBytes:           16384,
		RetryMax:             3,
		RetryBackoff:         100 * time.Millisecond,
		EnableIdempotence:    true,
		MaxInFlightRequests:  5,
		SecurityProtocol:     "PLAINTEXT",
		SASLMechanism:        "",
		SASLUsername:         "",
		SASLPassword:         "",
		TLSEnabled:           false,
		TLSSkipVerify:        false,
	}
	
	// Mock sarama.NewAsyncProducer to avoid actual Kafka connection
	originalNewAsyncProducer := sarama.NewAsyncProducer
	defer func() { sarama.NewAsyncProducer = originalNewAsyncProducer }()
	
	mockProducer := mocks.NewAsyncProducer(t, nil)
	sarama.NewAsyncProducer = func(addrs []string, conf *sarama.Config) (sarama.AsyncProducer, error) {
		// Verify configuration is set correctly - covers lines 37-54
		assert.Equal(t, sarama.RequiredAcks(cfg.RequiredAcks), conf.Producer.RequiredAcks)
		assert.Equal(t, cfg.Timeout, conf.Producer.Timeout)
		assert.Equal(t, sarama.CompressionSnappy, conf.Producer.Compression)
		assert.Equal(t, cfg.FlushFrequency, conf.Producer.Flush.Frequency)
		assert.Equal(t, cfg.FlushMessages, conf.Producer.Flush.Messages)
		assert.Equal(t, cfg.FlushBytes, conf.Producer.Flush.Bytes)
		assert.Equal(t, cfg.RetryMax, conf.Producer.Retry.Max)
		assert.Equal(t, cfg.RetryBackoff, conf.Producer.Retry.Backoff)
		assert.Equal(t, cfg.EnableIdempotence, conf.Producer.Idempotent)
		assert.Equal(t, cfg.MaxInFlightRequests, conf.Net.MaxOpenRequests)
		assert.Equal(t, cfg.MaxMessageBytes, conf.Producer.MaxMessageBytes)
		assert.Equal(t, cfg.ClientID, conf.ClientID)
		assert.Equal(t, sarama.V2_8_0_0, conf.Version)
		
		return mockProducer, nil
	}
	
	producer, err := NewProducer(cfg, logger, metricsCollector)
	
	require.NoError(t, err)
	require.NotNil(t, producer)
	assert.Equal(t, mockProducer, producer.producer)
	assert.Equal(t, logger, producer.logger)
	assert.Equal(t, metricsCollector, producer.metrics)
	assert.NotNil(t, producer.ctx)
	assert.NotNil(t, producer.cancel)
	
	// Clean up
	producer.Close()
}

func TestNewProducer_WithTLSEnabled(t *testing.T) {
	// Test NewProducer with TLS enabled - covers lines 57-62
	logger := logrus.New()
	logger.SetLevel(logrus.FatalLevel)
	metricsCollector := metrics.NewCollector()
	
	cfg := config.KafkaConfig{
		Brokers:       []string{"localhost:9092"},
		ClientID:      "test-client",
		TLSEnabled:    true,
		TLSSkipVerify: true,
	}
	
	originalNewAsyncProducer := sarama.NewAsyncProducer
	defer func() { sarama.NewAsyncProducer = originalNewAsyncProducer }()
	
	mockProducer := mocks.NewAsyncProducer(t, nil)
	sarama.NewAsyncProducer = func(addrs []string, conf *sarama.Config) (sarama.AsyncProducer, error) {
		// Verify TLS configuration - covers lines 57-62
		assert.True(t, conf.Net.TLS.Enable)
		assert.NotNil(t, conf.Net.TLS.Config)
		assert.True(t, conf.Net.TLS.Config.InsecureSkipVerify)
		
		return mockProducer, nil
	}
	
	producer, err := NewProducer(cfg, logger, metricsCollector)
	require.NoError(t, err)
	producer.Close()
}

func TestNewProducer_WithSASLEnabled(t *testing.T) {
	// Test NewProducer with SASL enabled - covers lines 64-69
	logger := logrus.New()
	logger.SetLevel(logrus.FatalLevel)
	metricsCollector := metrics.NewCollector()
	
	cfg := config.KafkaConfig{
		Brokers:       []string{"localhost:9092"},
		ClientID:      "test-client",
		SASLMechanism: "PLAIN",
		SASLUsername:  "testuser",
		SASLPassword:  "testpass",
	}
	
	originalNewAsyncProducer := sarama.NewAsyncProducer
	defer func() { sarama.NewAsyncProducer = originalNewAsyncProducer }()
	
	mockProducer := mocks.NewAsyncProducer(t, nil)
	sarama.NewAsyncProducer = func(addrs []string, conf *sarama.Config) (sarama.AsyncProducer, error) {
		// Verify SASL configuration - covers lines 64-69
		assert.True(t, conf.Net.SASL.Enable)
		assert.Equal(t, sarama.SASLMechanism("PLAIN"), conf.Net.SASL.Mechanism)
		assert.Equal(t, "testuser", conf.Net.SASL.User)
		assert.Equal(t, "testpass", conf.Net.SASL.Password)
		
		return mockProducer, nil
	}
	
	producer, err := NewProducer(cfg, logger, metricsCollector)
	require.NoError(t, err)
	producer.Close()
}

func TestNewProducer_SaramaError(t *testing.T) {
	// Test NewProducer when sarama.NewAsyncProducer returns error - covers lines 72-75
	logger := logrus.New()
	logger.SetLevel(logrus.FatalLevel)
	metricsCollector := metrics.NewCollector()
	
	cfg := config.KafkaConfig{
		Brokers:  []string{"localhost:9092"},
		ClientID: "test-client",
	}
	
	originalNewAsyncProducer := sarama.NewAsyncProducer
	defer func() { sarama.NewAsyncProducer = originalNewAsyncProducer }()
	
	expectedError := fmt.Errorf("connection failed")
	sarama.NewAsyncProducer = func(addrs []string, conf *sarama.Config) (sarama.AsyncProducer, error) {
		return nil, expectedError
	}
	
	producer, err := NewProducer(cfg, logger, metricsCollector)
	
	assert.Error(t, err)
	assert.Nil(t, producer)
	assert.Contains(t, err.Error(), "failed to create Kafka producer")
	assert.Contains(t, err.Error(), expectedError.Error())
}

func TestSendMessage_Success(t *testing.T) {
	// Test SendMessage success path - covers lines 97-129
	logger := logrus.New()
	logger.SetLevel(logrus.FatalLevel)
	metricsCollector := metrics.NewCollector()
	
	mockProducer := mocks.NewAsyncProducer(t, nil)
	ctx, cancel := context.WithCancel(context.Background())
	
	producer := &Producer{
		producer: mockProducer,
		logger:   logger,
		metrics:  metricsCollector,
		ctx:      ctx,
		cancel:   cancel,
	}
	
	// Test message with headers - covers lines 98-104
	msg := &Message{
		Topic:     "test-topic",
		Key:       "test-key",
		Value:     []byte("test-value"),
		Headers:   map[string]string{"header1": "value1", "header2": "value2"},
		Timestamp: time.Now(),
	}
	
	// Mock expects the message
	mockProducer.ExpectInputAndSucceed()
	
	err := producer.SendMessage(msg)
	
	assert.NoError(t, err)
	
	// Verify the message was sent correctly
	select {
	case kafkaMsg := <-mockProducer.Successes():
		assert.Equal(t, msg.Topic, kafkaMsg.Topic)
		assert.Equal(t, msg.Key, string(kafkaMsg.Key.(sarama.StringEncoder)))
		assert.Equal(t, msg.Value, []byte(kafkaMsg.Value.(sarama.ByteEncoder)))
		assert.Len(t, kafkaMsg.Headers, 2)
		
		// Verify headers conversion - covers lines 98-104
		headerMap := make(map[string]string)
		for _, header := range kafkaMsg.Headers {
			headerMap[string(header.Key)] = string(header.Value)
		}
		assert.Equal(t, msg.Headers, headerMap)
		
	case <-time.After(1 * time.Second):
		t.Fatal("Expected message not received")
	}
	
	cancel()
	mockProducer.Close()
}

func TestSendMessage_WithoutHeaders(t *testing.T) {
	// Test SendMessage without headers - covers lines 98-104 with nil headers
	logger := logrus.New()
	logger.SetLevel(logrus.FatalLevel)
	metricsCollector := metrics.NewCollector()
	
	mockProducer := mocks.NewAsyncProducer(t, nil)
	ctx, cancel := context.WithCancel(context.Background())
	
	producer := &Producer{
		producer: mockProducer,
		logger:   logger,
		metrics:  metricsCollector,
		ctx:      ctx,
		cancel:   cancel,
	}
	
	msg := &Message{
		Topic:     "test-topic",
		Key:       "test-key",
		Value:     []byte("test-value"),
		Headers:   nil, // No headers
		Timestamp: time.Now(),
	}
	
	mockProducer.ExpectInputAndSucceed()
	
	err := producer.SendMessage(msg)
	assert.NoError(t, err)
	
	cancel()
	mockProducer.Close()
}

func TestGetMetrics_Success(t *testing.T) {
	// Test GetMetrics function - covers lines 160-167
	logger := logrus.New()
	logger.SetLevel(logrus.FatalLevel)
	metricsCollector := metrics.NewCollector()

	mockProducer := mocks.NewAsyncProducer(t, nil)
	ctx, cancel := context.WithCancel(context.Background())

	producer := &Producer{
		producer: mockProducer,
		logger:   logger,
		metrics:  metricsCollector,
		ctx:      ctx,
		cancel:   cancel,
	}

	// Get metrics
	metrics := producer.GetMetrics()

	// Verify all expected metrics are present
	assert.Contains(t, metrics, "messages_sent")
	assert.Contains(t, metrics, "messages_failed")
	assert.Contains(t, metrics, "messages_dropped")
	assert.Contains(t, metrics, "queue_depth")

	// Verify types
	assert.IsType(t, uint64(0), metrics["messages_sent"])
	assert.IsType(t, uint64(0), metrics["messages_failed"])
	assert.IsType(t, uint64(0), metrics["messages_dropped"])
	assert.IsType(t, 0, metrics["queue_depth"])

	cancel()
	mockProducer.Close()
}

func TestClose_Success(t *testing.T) {
	// Test Close function success path - covers lines 170-186
	logger := logrus.New()
	logger.SetLevel(logrus.FatalLevel)
	metricsCollector := metrics.NewCollector()

	mockProducer := mocks.NewAsyncProducer(t, nil)
	ctx, cancel := context.WithCancel(context.Background())

	producer := &Producer{
		producer: mockProducer,
		logger:   logger,
		metrics:  metricsCollector,
		ctx:      ctx,
		cancel:   cancel,
		wg:       sync.WaitGroup{},
	}

	// Start background goroutines to test wg.Wait()
	producer.wg.Add(2)
	go func() {
		defer producer.wg.Done()
		<-producer.ctx.Done()
	}()
	go func() {
		defer producer.wg.Done()
		<-producer.ctx.Done()
	}()

	err := producer.Close()

	assert.NoError(t, err)

	// Verify context was cancelled
	select {
	case <-producer.ctx.Done():
		// Expected
	default:
		t.Fatal("Context should be cancelled")
	}
}

func TestClose_ProducerError(t *testing.T) {
	// Test Close function when producer.Close() returns error - covers lines 176-179
	logger := logrus.New()
	logger.SetLevel(logrus.FatalLevel)
	metricsCollector := metrics.NewCollector()

	// Create a mock that will return error on close
	mockProducer := &mockProducerWithCloseError{}
	ctx, cancel := context.WithCancel(context.Background())

	producer := &Producer{
		producer: mockProducer,
		logger:   logger,
		metrics:  metricsCollector,
		ctx:      ctx,
		cancel:   cancel,
		wg:       sync.WaitGroup{},
	}

	err := producer.Close()

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "close error")
}

func TestHandleSuccesses(t *testing.T) {
	// Test handleSuccesses function - covers lines 189-206
	logger := logrus.New()
	logger.SetLevel(logrus.FatalLevel)
	metricsCollector := metrics.NewCollector()

	mockProducer := mocks.NewAsyncProducer(t, nil)
	ctx, cancel := context.WithCancel(context.Background())

	producer := &Producer{
		producer: mockProducer,
		logger:   logger,
		metrics:  metricsCollector,
		ctx:      ctx,
		cancel:   cancel,
		wg:       sync.WaitGroup{},
	}

	// Start handleSuccesses in background
	producer.wg.Add(1)
	go producer.handleSuccesses()

	// Send a success message
	mockProducer.ExpectInputAndSucceed()

	msg := &sarama.ProducerMessage{
		Topic: "test-topic",
		Key:   sarama.StringEncoder("test-key"),
		Value: sarama.ByteEncoder("test-value"),
	}

	mockProducer.Input() <- msg

	// Wait a bit for processing
	time.Sleep(10 * time.Millisecond)

	// Cancel context to stop goroutine
	cancel()
	producer.wg.Wait()

	mockProducer.Close()
}

func TestHandleErrors(t *testing.T) {
	// Test handleErrors function - covers lines 209-225
	logger := logrus.New()
	logger.SetLevel(logrus.FatalLevel)
	metricsCollector := metrics.NewCollector()

	mockProducer := mocks.NewAsyncProducer(t, nil)
	ctx, cancel := context.WithCancel(context.Background())

	producer := &Producer{
		producer: mockProducer,
		logger:   logger,
		metrics:  metricsCollector,
		ctx:      ctx,
		cancel:   cancel,
		wg:       sync.WaitGroup{},
	}

	// Start handleErrors in background
	producer.wg.Add(1)
	go producer.handleErrors()

	// Send an error
	mockProducer.ExpectInputAndFail(fmt.Errorf("test error"))

	msg := &sarama.ProducerMessage{
		Topic: "test-topic",
		Key:   sarama.StringEncoder("test-key"),
		Value: sarama.ByteEncoder("test-value"),
	}

	mockProducer.Input() <- msg

	// Wait a bit for processing
	time.Sleep(10 * time.Millisecond)

	// Cancel context to stop goroutine
	cancel()
	producer.wg.Wait()

	mockProducer.Close()
}

// Mock producer that returns error on close
type mockProducerWithCloseError struct {
	sarama.AsyncProducer
}

func (m *mockProducerWithCloseError) Close() error {
	return fmt.Errorf("close error")
}

func (m *mockProducerWithCloseError) Input() chan<- *sarama.ProducerMessage {
	return make(chan *sarama.ProducerMessage)
}

func (m *mockProducerWithCloseError) Successes() <-chan *sarama.ProducerMessage {
	return make(chan *sarama.ProducerMessage)
}

func (m *mockProducerWithCloseError) Errors() <-chan *sarama.ProducerError {
	return make(chan *sarama.ProducerError)
}

func (m *mockProducerWithCloseError) AsyncClose() {
	// No-op
}

// Test compression type mapping - covers lines 228-242
func TestGetCompressionType_Snappy(t *testing.T) {
	compression := getCompressionType("snappy")
	assert.Equal(t, sarama.CompressionSnappy, compression)
}

func TestGetCompressionType_Gzip(t *testing.T) {
	compression := getCompressionType("gzip")
	assert.Equal(t, sarama.CompressionGZIP, compression)
}

func TestGetCompressionType_Lz4(t *testing.T) {
	compression := getCompressionType("lz4")
	assert.Equal(t, sarama.CompressionLZ4, compression)
}

func TestGetCompressionType_Zstd(t *testing.T) {
	compression := getCompressionType("zstd")
	assert.Equal(t, sarama.CompressionZSTD, compression)
}

func TestGetCompressionType_None(t *testing.T) {
	compression := getCompressionType("none")
	assert.Equal(t, sarama.CompressionNone, compression)
}

func TestGetCompressionType_Unknown(t *testing.T) {
	compression := getCompressionType("unknown")
	assert.Equal(t, sarama.CompressionNone, compression)
}

func TestGetCompressionType_Empty(t *testing.T) {
	compression := getCompressionType("")
	assert.Equal(t, sarama.CompressionNone, compression)
}

// Test edge cases and error conditions

func TestSendMessage_NilMessage(t *testing.T) {
	// Test SendMessage with nil message
	logger := logrus.New()
	logger.SetLevel(logrus.FatalLevel)
	metricsCollector := metrics.NewCollector()

	mockProducer := mocks.NewAsyncProducer(t, nil)
	ctx, cancel := context.WithCancel(context.Background())

	producer := &Producer{
		producer: mockProducer,
		logger:   logger,
		metrics:  metricsCollector,
		ctx:      ctx,
		cancel:   cancel,
	}

	err := producer.SendMessage(nil)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "message cannot be nil")

	cancel()
	mockProducer.Close()
}

func TestSendMessage_EmptyTopic(t *testing.T) {
	// Test SendMessage with empty topic
	logger := logrus.New()
	logger.SetLevel(logrus.FatalLevel)
	metricsCollector := metrics.NewCollector()

	mockProducer := mocks.NewAsyncProducer(t, nil)
	ctx, cancel := context.WithCancel(context.Background())

	producer := &Producer{
		producer: mockProducer,
		logger:   logger,
		metrics:  metricsCollector,
		ctx:      ctx,
		cancel:   cancel,
	}

	msg := &Message{
		Topic: "", // Empty topic
		Key:   "test-key",
		Value: []byte("test-value"),
	}

	err := producer.SendMessage(msg)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "topic cannot be empty")

	cancel()
	mockProducer.Close()
}

func TestSendMessage_NilValue(t *testing.T) {
	// Test SendMessage with nil value
	logger := logrus.New()
	logger.SetLevel(logrus.FatalLevel)
	metricsCollector := metrics.NewCollector()

	mockProducer := mocks.NewAsyncProducer(t, nil)
	ctx, cancel := context.WithCancel(context.Background())

	producer := &Producer{
		producer: mockProducer,
		logger:   logger,
		metrics:  metricsCollector,
		ctx:      ctx,
		cancel:   cancel,
	}

	msg := &Message{
		Topic: "test-topic",
		Key:   "test-key",
		Value: nil, // Nil value
	}

	mockProducer.ExpectInputAndSucceed()

	err := producer.SendMessage(msg)

	assert.NoError(t, err) // Nil value should be allowed

	cancel()
	mockProducer.Close()
}
