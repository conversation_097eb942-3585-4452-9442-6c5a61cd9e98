package main

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// Message represents a data message in the ASI system
type Message struct {
	ID        string                 `json:"id"`
	Timestamp int64                  `json:"timestamp"`
	Source    string                 `json:"source"`
	DataType  string                 `json:"data_type"`
	Payload   map[string]interface{} `json:"payload"`
}

// Producer interface for dependency injection and mocking
type Producer interface {
	SendMessage(ctx context.Context, topic string, message *Message) error
	SendBatch(ctx context.Context, topic string, messages []*Message) error
	Close() error
	GetMetrics() ProducerMetrics
}

// ProducerMetrics holds producer performance metrics
type ProducerMetrics struct {
	MessagesSent     int64         `json:"messages_sent"`
	BytesSent        int64         `json:"bytes_sent"`
	ErrorCount       int64         `json:"error_count"`
	AverageLatency   time.Duration `json:"average_latency"`
	LastMessageTime  time.Time     `json:"last_message_time"`
	ConnectionStatus string        `json:"connection_status"`
}

// KafkaProducer implements the Producer interface
type KafkaProducer struct {
	brokers       []string
	config        ProducerConfig
	metrics       ProducerMetrics
	metricsMutex  sync.RWMutex
	isConnected   bool
	connectionMux sync.RWMutex
}

// ProducerConfig holds producer configuration
type ProducerConfig struct {
	Brokers           []string      `json:"brokers"`
	RetryAttempts     int           `json:"retry_attempts"`
	RetryDelay        time.Duration `json:"retry_delay"`
	BatchSize         int           `json:"batch_size"`
	FlushTimeout      time.Duration `json:"flush_timeout"`
	CompressionType   string        `json:"compression_type"`
	EnableIdempotence bool          `json:"enable_idempotence"`
}

// NewKafkaProducer creates a new Kafka producer instance
func NewKafkaProducer(config ProducerConfig) *KafkaProducer {
	return &KafkaProducer{
		brokers: config.Brokers,
		config:  config,
		metrics: ProducerMetrics{
			ConnectionStatus: "disconnected",
		},
		isConnected: false,
	}
}

// Connect establishes connection to Kafka brokers
func (p *KafkaProducer) Connect() error {
	p.connectionMux.Lock()
	defer p.connectionMux.Unlock()

	// Simulate connection logic
	if len(p.brokers) == 0 {
		return fmt.Errorf("no brokers configured")
	}

	p.isConnected = true
	p.updateConnectionStatus("connected")
	return nil
}

// SendMessage sends a single message to the specified topic
func (p *KafkaProducer) SendMessage(ctx context.Context, topic string, message *Message) error {
	if !p.isConnected {
		return fmt.Errorf("producer not connected")
	}

	if message == nil {
		return fmt.Errorf("message cannot be nil")
	}

	if topic == "" {
		return fmt.Errorf("topic cannot be empty")
	}

	start := time.Now()

	// Simulate message validation
	if err := p.validateMessage(message); err != nil {
		p.incrementErrorCount()
		return fmt.Errorf("message validation failed: %w", err)
	}

	// Simulate message serialization
	data, err := json.Marshal(message)
	if err != nil {
		p.incrementErrorCount()
		return fmt.Errorf("message serialization failed: %w", err)
	}

	// Simulate sending with retry logic
	err = p.sendWithRetry(ctx, topic, data)
	if err != nil {
		p.incrementErrorCount()
		return err
	}

	// Update metrics
	latency := time.Since(start)
	p.updateMetrics(1, int64(len(data)), latency)

	return nil
}

// SendBatch sends multiple messages in a batch
func (p *KafkaProducer) SendBatch(ctx context.Context, topic string, messages []*Message) error {
	if !p.isConnected {
		return fmt.Errorf("producer not connected")
	}

	if len(messages) == 0 {
		return fmt.Errorf("message batch cannot be empty")
	}

	start := time.Now()
	totalBytes := int64(0)
	successCount := int64(0)

	for _, message := range messages {
		if err := p.validateMessage(message); err != nil {
			p.incrementErrorCount()
			continue
		}

		data, err := json.Marshal(message)
		if err != nil {
			p.incrementErrorCount()
			continue
		}

		if err := p.sendWithRetry(ctx, topic, data); err != nil {
			p.incrementErrorCount()
			continue
		}

		totalBytes += int64(len(data))
		successCount++
	}

	latency := time.Since(start)
	p.updateMetrics(successCount, totalBytes, latency)

	if successCount == 0 {
		return fmt.Errorf("failed to send any messages in batch")
	}

	return nil
}

// Close closes the producer and releases resources
func (p *KafkaProducer) Close() error {
	p.connectionMux.Lock()
	defer p.connectionMux.Unlock()

	p.isConnected = false
	p.updateConnectionStatus("disconnected")
	return nil
}

// GetMetrics returns current producer metrics
func (p *KafkaProducer) GetMetrics() ProducerMetrics {
	p.metricsMutex.RLock()
	defer p.metricsMutex.RUnlock()
	return p.metrics
}

// Helper methods

func (p *KafkaProducer) validateMessage(message *Message) error {
	if message.ID == "" {
		return fmt.Errorf("message ID cannot be empty")
	}
	if message.Timestamp <= 0 {
		return fmt.Errorf("message timestamp must be positive")
	}
	if message.Source == "" {
		return fmt.Errorf("message source cannot be empty")
	}
	return nil
}

func (p *KafkaProducer) sendWithRetry(ctx context.Context, topic string, data []byte) error {
	for attempt := 0; attempt < p.config.RetryAttempts; attempt++ {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		// Simulate sending (would be actual Kafka send in real implementation)
		if err := p.simulateSend(topic, data); err != nil {
			if attempt == p.config.RetryAttempts-1 {
				return fmt.Errorf("failed to send after %d attempts: %w", p.config.RetryAttempts, err)
			}
			time.Sleep(p.config.RetryDelay)
			continue
		}
		return nil
	}
	return fmt.Errorf("unexpected retry loop exit")
}

func (p *KafkaProducer) simulateSend(topic string, data []byte) error {
	// Simulate network latency
	time.Sleep(1 * time.Millisecond)
	return nil
}

func (p *KafkaProducer) updateMetrics(messageCount, byteCount int64, latency time.Duration) {
	p.metricsMutex.Lock()
	defer p.metricsMutex.Unlock()

	p.metrics.MessagesSent += messageCount
	p.metrics.BytesSent += byteCount
	p.metrics.LastMessageTime = time.Now()

	// Update average latency (simple moving average)
	if p.metrics.MessagesSent == messageCount {
		p.metrics.AverageLatency = latency
	} else {
		p.metrics.AverageLatency = (p.metrics.AverageLatency + latency) / 2
	}
}

func (p *KafkaProducer) incrementErrorCount() {
	p.metricsMutex.Lock()
	defer p.metricsMutex.Unlock()
	p.metrics.ErrorCount++
}

func (p *KafkaProducer) updateConnectionStatus(status string) {
	p.metricsMutex.Lock()
	defer p.metricsMutex.Unlock()
	p.metrics.ConnectionStatus = status
}

// Mock implementation for testing
type MockProducer struct {
	mock.Mock
}

func (m *MockProducer) SendMessage(ctx context.Context, topic string, message *Message) error {
	args := m.Called(ctx, topic, message)
	return args.Error(0)
}

func (m *MockProducer) SendBatch(ctx context.Context, topic string, messages []*Message) error {
	args := m.Called(ctx, topic, messages)
	return args.Error(0)
}

func (m *MockProducer) Close() error {
	args := m.Called()
	return args.Error(0)
}

func (m *MockProducer) GetMetrics() ProducerMetrics {
	args := m.Called()
	return args.Get(0).(ProducerMetrics)
}

// Test Suite
type ProducerTestSuite struct {
	suite.Suite
	producer *KafkaProducer
	config   ProducerConfig
}

func (suite *ProducerTestSuite) SetupTest() {
	suite.config = ProducerConfig{
		Brokers:           []string{"localhost:9092"},
		RetryAttempts:     3,
		RetryDelay:        100 * time.Millisecond,
		BatchSize:         100,
		FlushTimeout:      5 * time.Second,
		CompressionType:   "gzip",
		EnableIdempotence: true,
	}
	suite.producer = NewKafkaProducer(suite.config)
}

func (suite *ProducerTestSuite) TearDownTest() {
	if suite.producer != nil {
		suite.producer.Close()
	}
}

func (suite *ProducerTestSuite) TestNewKafkaProducer() {
	producer := NewKafkaProducer(suite.config)
	
	assert.NotNil(suite.T(), producer)
	assert.Equal(suite.T(), suite.config.Brokers, producer.brokers)
	assert.Equal(suite.T(), suite.config, producer.config)
	assert.False(suite.T(), producer.isConnected)
	assert.Equal(suite.T(), "disconnected", producer.metrics.ConnectionStatus)
}

func (suite *ProducerTestSuite) TestConnect() {
	err := suite.producer.Connect()
	
	assert.NoError(suite.T(), err)
	assert.True(suite.T(), suite.producer.isConnected)
	assert.Equal(suite.T(), "connected", suite.producer.GetMetrics().ConnectionStatus)
}

func (suite *ProducerTestSuite) TestConnectNoBrokers() {
	producer := NewKafkaProducer(ProducerConfig{Brokers: []string{}})
	
	err := producer.Connect()
	
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "no brokers configured")
	assert.False(suite.T(), producer.isConnected)
}

func (suite *ProducerTestSuite) TestSendMessage() {
	suite.producer.Connect()
	
	message := &Message{
		ID:        "test-123",
		Timestamp: time.Now().Unix(),
		Source:    "test-source",
		DataType:  "test-data",
		Payload:   map[string]interface{}{"key": "value"},
	}
	
	ctx := context.Background()
	err := suite.producer.SendMessage(ctx, "test-topic", message)
	
	assert.NoError(suite.T(), err)
	
	metrics := suite.producer.GetMetrics()
	assert.Equal(suite.T(), int64(1), metrics.MessagesSent)
	assert.Greater(suite.T(), metrics.BytesSent, int64(0))
	assert.Equal(suite.T(), int64(0), metrics.ErrorCount)
}

func (suite *ProducerTestSuite) TestSendMessageNotConnected() {
	message := &Message{
		ID:        "test-123",
		Timestamp: time.Now().Unix(),
		Source:    "test-source",
		DataType:  "test-data",
		Payload:   map[string]interface{}{"key": "value"},
	}
	
	ctx := context.Background()
	err := suite.producer.SendMessage(ctx, "test-topic", message)
	
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "producer not connected")
}

func (suite *ProducerTestSuite) TestSendMessageNilMessage() {
	suite.producer.Connect()
	
	ctx := context.Background()
	err := suite.producer.SendMessage(ctx, "test-topic", nil)
	
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "message cannot be nil")
}

func (suite *ProducerTestSuite) TestSendMessageEmptyTopic() {
	suite.producer.Connect()
	
	message := &Message{
		ID:        "test-123",
		Timestamp: time.Now().Unix(),
		Source:    "test-source",
		DataType:  "test-data",
		Payload:   map[string]interface{}{"key": "value"},
	}
	
	ctx := context.Background()
	err := suite.producer.SendMessage(ctx, "", message)
	
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "topic cannot be empty")
}

func (suite *ProducerTestSuite) TestSendMessageInvalidMessage() {
	suite.producer.Connect()
	
	message := &Message{
		// Missing required fields
		Payload: map[string]interface{}{"key": "value"},
	}
	
	ctx := context.Background()
	err := suite.producer.SendMessage(ctx, "test-topic", message)
	
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "message validation failed")
	
	metrics := suite.producer.GetMetrics()
	assert.Equal(suite.T(), int64(1), metrics.ErrorCount)
}

func (suite *ProducerTestSuite) TestSendBatch() {
	suite.producer.Connect()
	
	messages := []*Message{
		{
			ID:        "test-1",
			Timestamp: time.Now().Unix(),
			Source:    "test-source",
			DataType:  "test-data",
			Payload:   map[string]interface{}{"key": "value1"},
		},
		{
			ID:        "test-2",
			Timestamp: time.Now().Unix(),
			Source:    "test-source",
			DataType:  "test-data",
			Payload:   map[string]interface{}{"key": "value2"},
		},
	}
	
	ctx := context.Background()
	err := suite.producer.SendBatch(ctx, "test-topic", messages)
	
	assert.NoError(suite.T(), err)
	
	metrics := suite.producer.GetMetrics()
	assert.Equal(suite.T(), int64(2), metrics.MessagesSent)
	assert.Greater(suite.T(), metrics.BytesSent, int64(0))
}

func (suite *ProducerTestSuite) TestSendBatchEmpty() {
	suite.producer.Connect()
	
	ctx := context.Background()
	err := suite.producer.SendBatch(ctx, "test-topic", []*Message{})
	
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "message batch cannot be empty")
}

func (suite *ProducerTestSuite) TestConcurrentSending() {
	suite.producer.Connect()
	
	const numGoroutines = 10
	const messagesPerGoroutine = 10
	
	var wg sync.WaitGroup
	wg.Add(numGoroutines)
	
	for i := 0; i < numGoroutines; i++ {
		go func(routineID int) {
			defer wg.Done()
			
			for j := 0; j < messagesPerGoroutine; j++ {
				message := &Message{
					ID:        fmt.Sprintf("test-%d-%d", routineID, j),
					Timestamp: time.Now().Unix(),
					Source:    "test-source",
					DataType:  "test-data",
					Payload:   map[string]interface{}{"routine": routineID, "message": j},
				}
				
				ctx := context.Background()
				err := suite.producer.SendMessage(ctx, "test-topic", message)
				assert.NoError(suite.T(), err)
			}
		}(i)
	}
	
	wg.Wait()
	
	metrics := suite.producer.GetMetrics()
	assert.Equal(suite.T(), int64(numGoroutines*messagesPerGoroutine), metrics.MessagesSent)
}

// Benchmark tests
func BenchmarkSendMessage(b *testing.B) {
	config := ProducerConfig{
		Brokers:       []string{"localhost:9092"},
		RetryAttempts: 1,
		RetryDelay:    10 * time.Millisecond,
	}
	producer := NewKafkaProducer(config)
	producer.Connect()
	defer producer.Close()

	message := &Message{
		ID:        "benchmark-test",
		Timestamp: time.Now().Unix(),
		Source:    "benchmark",
		DataType:  "test-data",
		Payload:   map[string]interface{}{"key": "value"},
	}

	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		producer.SendMessage(ctx, "benchmark-topic", message)
	}
}

func BenchmarkSendBatch(b *testing.B) {
	config := ProducerConfig{
		Brokers:       []string{"localhost:9092"},
		RetryAttempts: 1,
		RetryDelay:    10 * time.Millisecond,
	}
	producer := NewKafkaProducer(config)
	producer.Connect()
	defer producer.Close()

	messages := make([]*Message, 10)
	for i := range messages {
		messages[i] = &Message{
			ID:        fmt.Sprintf("benchmark-test-%d", i),
			Timestamp: time.Now().Unix(),
			Source:    "benchmark",
			DataType:  "test-data",
			Payload:   map[string]interface{}{"key": fmt.Sprintf("value-%d", i)},
		}
	}

	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		producer.SendBatch(ctx, "benchmark-topic", messages)
	}
}

// Additional performance benchmarks
func BenchmarkMessageSerialization(b *testing.B) {
	message := &Message{
		ID:        "serialization-test",
		Timestamp: time.Now().Unix(),
		Source:    "benchmark",
		DataType:  "test-data",
		Payload: map[string]interface{}{
			"key1": "value1",
			"key2": 12345,
			"key3": []string{"a", "b", "c"},
			"key4": map[string]interface{}{"nested": "value"},
		},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := json.Marshal(message)
		if err != nil {
			b.Fatal(err)
		}
	}
}

func BenchmarkConcurrentSending(b *testing.B) {
	config := ProducerConfig{
		Brokers:       []string{"localhost:9092"},
		RetryAttempts: 1,
		RetryDelay:    10 * time.Millisecond,
	}
	producer := NewKafkaProducer(config)
	producer.Connect()
	defer producer.Close()

	message := &Message{
		ID:        "concurrent-test",
		Timestamp: time.Now().Unix(),
		Source:    "benchmark",
		DataType:  "test-data",
		Payload:   map[string]interface{}{"key": "value"},
	}

	ctx := context.Background()

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			producer.SendMessage(ctx, "benchmark-topic", message)
		}
	})
}

func BenchmarkMemoryAllocation(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		message := &Message{
			ID:        fmt.Sprintf("memory-test-%d", i),
			Timestamp: time.Now().Unix(),
			Source:    "benchmark",
			DataType:  "test-data",
			Payload:   make(map[string]interface{}),
		}

		// Add some data to the payload
		for j := 0; j < 10; j++ {
			message.Payload[fmt.Sprintf("key_%d", j)] = fmt.Sprintf("value_%d", j)
		}
	}
}

// Test runner
func TestProducerTestSuite(t *testing.T) {
	suite.Run(t, new(ProducerTestSuite))
}
