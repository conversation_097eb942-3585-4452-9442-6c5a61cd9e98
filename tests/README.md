# ASI System Advanced Testing Framework

## 🎯 Overview

This comprehensive testing framework provides enterprise-grade testing capabilities for the ASI (Artificial Super Intelligence) system. It implements Test-Driven Development (TDD) principles across multiple programming languages and testing levels.

## 🏗️ Testing Architecture

### Testing Pyramid
```
                    E2E Tests
                 ┌─────────────────┐
                 │  Complete       │
                 │  Workflows      │
                 └─────────────────┘
              ┌─────────────────────────┐
              │   Integration Tests     │
              │   Cross-Module APIs     │
              └─────────────────────────┘
         ┌─────────────────────────────────────┐
         │           Unit Tests                │
         │  Go • Rust • Python • C++          │
         └─────────────────────────────────────┘
```

### Test Categories

#### 🔬 Unit Tests
- **Python**: `pytest` with coverage, mocking, and fixtures
- **Go**: `go test` with race detection and benchmarks
- **Rust**: `cargo test` with property-based testing
- **C++**: Google Test (gtest) with Google Mock (gmock)

#### 🔗 Integration Tests
- Cross-module API testing
- Kafka/gRPC/REST communication validation
- Database integration testing
- External service mocking

#### 🌐 End-to-End Tests
- Complete workflow validation
- Real data flow testing
- Performance under load
- User scenario simulation

#### ⚡ Performance Tests
- Throughput benchmarking
- Latency measurement
- Memory usage profiling
- Concurrent load testing

## 📁 Directory Structure

```
tests/
├── conftest.py                 # Global pytest configuration
├── requirements.txt            # Testing dependencies
├── README.md                   # This file
│
├── unit/                       # Unit tests by language
│   ├── python/
│   │   ├── test_data_processing.py
│   │   └── test_*.py
│   ├── go/
│   │   ├── producer_test.go
│   │   └── *_test.go
│   ├── rust/
│   │   ├── consumer_test.rs
│   │   └── lib.rs
│   └── cpp/
│       ├── inference_engine_test.cpp
│       ├── CMakeLists.txt
│       └── *.cpp
│
├── integration/                # Integration tests
│   ├── test_cross_module_communication.py
│   ├── test_kafka_integration.py
│   ├── test_grpc_services.py
│   └── test_database_integration.py
│
├── e2e/                        # End-to-end tests
│   ├── test_complete_asi_workflows.py
│   ├── test_user_scenarios.py
│   └── test_system_resilience.py
│
├── performance/                # Performance benchmarks
│   ├── benchmark_suite.py
│   ├── load_tests.py
│   └── memory_profiling.py
│
└── data/                       # Test data and scenarios
    ├── scenarios/
    ├── fixtures/
    └── mock_data/
```

## 🚀 Quick Start

### Prerequisites
```bash
# Install dependencies
make check-deps
make setup-test-env
```

### Running Tests

#### Unit Tests
```bash
# All unit tests
make test-unit-all

# Language-specific
make test-python
make test-go
make test-rust
make test-cpp
```

#### Integration Tests
```bash
# All integration tests
make test-integration

# Module-specific
make test-integration-ingestion
make test-integration-learning
make test-integration-decision
```

#### End-to-End Tests
```bash
# Complete E2E suite
make test-e2e-full

# Specific workflows
make test-e2e-ingestion-to-learning
make test-e2e-learning-to-decision
```

#### Performance Benchmarks
```bash
# All benchmarks
make benchmark-go
make benchmark-rust
make benchmark-python

# Specific metrics
make benchmark-throughput
make benchmark-latency
make benchmark-memory
```

## 📊 Test Configuration

### Pytest Configuration (`pytest.ini`)
```ini
[tool:pytest]
testpaths = tests
addopts = 
    --strict-markers
    --verbose
    --cov=.
    --cov-report=html
    --cov-fail-under=80
    --durations=10
markers =
    unit: Unit tests
    integration: Integration tests
    e2e: End-to-end tests
    performance: Performance tests
    slow: Slow running tests
```

### Test Markers
- `@pytest.mark.unit` - Unit tests
- `@pytest.mark.integration` - Integration tests
- `@pytest.mark.e2e` - End-to-end tests
- `@pytest.mark.performance` - Performance tests
- `@pytest.mark.slow` - Long-running tests

## 🔧 Test Utilities

### Global Fixtures (`conftest.py`)
- `test_config` - Global test configuration
- `test_data_factory` - Test data generation
- `kafka_test_client` - Kafka testing client
- `redis_test_client` - Redis testing client
- `grpc_test_channels` - gRPC testing channels

### Test Data Factory
```python
def test_example(test_data_factory):
    # Generate test data
    ingestion_data = test_data_factory.create_ingestion_data()
    learning_data = test_data_factory.create_learning_data()
    decision_data = test_data_factory.create_decision_data()
```

## 📈 Coverage Requirements

### Minimum Coverage Targets
- **Unit Tests**: 80% line coverage
- **Integration Tests**: 70% API coverage
- **E2E Tests**: 90% critical path coverage

### Coverage Reports
```bash
# Generate coverage reports
make coverage-report
make coverage-html

# View reports
open htmlcov/index.html  # Python
open tests/unit/go/coverage.html  # Go
open tests/unit/rust/coverage/index.html  # Rust
```

## 🚨 CI/CD Integration

### GitHub Actions Workflow
- Automated testing on push/PR
- Multi-language test execution
- Coverage reporting
- Security scanning
- Performance regression detection

### Local CI Simulation
```bash
# Run complete CI suite locally
make ci-test-local
```

## 🔍 Test Categories by Module

### Ingestion Module
- **Unit**: Data validation, serialization, rate limiting
- **Integration**: Kafka producer/consumer, gRPC APIs
- **E2E**: Complete data ingestion workflows
- **Performance**: Throughput, latency, memory usage

### Learning Engine
- **Unit**: Model training, inference, feature extraction
- **Integration**: Model serving APIs, data pipelines
- **E2E**: Training to deployment workflows
- **Performance**: Inference speed, memory efficiency

### Decision Engine
- **Unit**: Rule evaluation, decision logic, constraints
- **Integration**: Real-time decision APIs
- **E2E**: Decision to action workflows
- **Performance**: Decision latency, concurrent decisions

### Security Module
- **Unit**: Encryption, authentication, authorization
- **Integration**: Security policy enforcement
- **E2E**: Threat detection and response
- **Performance**: Security overhead, audit logging

## 🛠️ Development Workflow

### Test-Driven Development (TDD)
1. **Red**: Write failing test
2. **Green**: Implement minimal code to pass
3. **Refactor**: Improve code while keeping tests green

### Adding New Tests
```bash
# Create test file
touch tests/unit/python/test_new_feature.py

# Write test
def test_new_feature():
    # Arrange
    # Act
    # Assert
    pass

# Run test
pytest tests/unit/python/test_new_feature.py -v
```

### Test Naming Conventions
- Test files: `test_*.py`, `*_test.go`, `*_test.rs`, `*_test.cpp`
- Test functions: `test_*` (Python), `Test*` (Go), `test_*` (Rust), `TEST*` (C++)
- Test classes: `Test*` (all languages)

## 📋 Best Practices

### Unit Tests
- ✅ Test one thing at a time
- ✅ Use descriptive test names
- ✅ Follow AAA pattern (Arrange, Act, Assert)
- ✅ Mock external dependencies
- ✅ Test edge cases and error conditions

### Integration Tests
- ✅ Test real component interactions
- ✅ Use test containers for external services
- ✅ Clean up resources after tests
- ✅ Test both happy path and error scenarios

### E2E Tests
- ✅ Test complete user workflows
- ✅ Use realistic test data
- ✅ Validate end-to-end functionality
- ✅ Include performance assertions

### Performance Tests
- ✅ Establish baseline metrics
- ✅ Test under realistic load
- ✅ Monitor resource usage
- ✅ Set performance thresholds

## 🔧 Troubleshooting

### Common Issues

#### Test Environment Setup
```bash
# Reset test environment
make clean-test-env
make setup-test-env
```

#### Dependency Issues
```bash
# Check all dependencies
make check-deps

# Update dependencies
pip install -r tests/requirements.txt --upgrade
```

#### Service Connectivity
```bash
# Check service health
curl http://localhost:8080/health
curl http://localhost:8081/health
```

### Debug Mode
```bash
# Run tests with debug output
pytest -v -s --log-cli-level=DEBUG

# Run specific test with debugging
pytest tests/unit/python/test_data_processing.py::test_specific_function -v -s
```

## 📚 Additional Resources

- [pytest Documentation](https://docs.pytest.org/)
- [Go Testing Package](https://golang.org/pkg/testing/)
- [Rust Testing Guide](https://doc.rust-lang.org/book/ch11-00-testing.html)
- [Google Test Documentation](https://google.github.io/googletest/)
- [ASI System Architecture](../README.md)

## 🤝 Contributing

1. Write tests for new features
2. Ensure all tests pass
3. Maintain coverage requirements
4. Update documentation
5. Follow coding standards

---

*This testing framework ensures the ASI system maintains the highest quality standards through comprehensive automated testing.*
