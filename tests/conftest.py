#!/usr/bin/env python3
"""
ASI System Global Test Configuration

Pytest configuration for the entire ASI system with fixtures, 
test utilities, and shared test infrastructure.
"""

import os
import sys
import asyncio
import pytest
import docker
import time
import uuid
import json
import logging
from typing import Dict, Any, List, Optional, Generator
from pathlib import Path
from dataclasses import dataclass
from contextlib import asynccontextmanager

# Add project root to Python path
PROJECT_ROOT = Path(__file__).parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

# Configure logging for tests
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class TestConfig:
    """Global test configuration."""
    # Service endpoints
    kafka_brokers: List[str] = None
    redis_host: str = "localhost"
    redis_port: int = 6379
    
    # gRPC endpoints
    grpc_producer_port: int = 50051
    grpc_consumer_port: int = 50054
    grpc_learning_port: int = 50055
    grpc_decision_port: int = 50056
    
    # HTTP endpoints
    http_producer_port: int = 8080
    http_consumer_port: int = 8081
    http_learning_port: int = 8082
    http_decision_port: int = 8083
    
    # Test data
    test_timeout: int = 30
    test_data_dir: Path = PROJECT_ROOT / "tests" / "data"
    
    def __post_init__(self):
        if self.kafka_brokers is None:
            self.kafka_brokers = ["localhost:9092"]
        
        # Ensure test data directory exists
        self.test_data_dir.mkdir(parents=True, exist_ok=True)

@pytest.fixture(scope="session")
def test_config() -> TestConfig:
    """Global test configuration fixture."""
    return TestConfig()

@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture(scope="session")
def docker_client():
    """Docker client for container management in tests."""
    try:
        client = docker.from_env()
        yield client
    except Exception as e:
        logger.warning(f"Docker not available: {e}")
        yield None

@pytest.fixture(scope="function")
def test_id() -> str:
    """Generate unique test ID for each test."""
    return str(uuid.uuid4())

@pytest.fixture(scope="function")
def test_data_factory(test_config: TestConfig, test_id: str):
    """Factory for generating test data."""
    
    class TestDataFactory:
        def __init__(self, config: TestConfig, test_id: str):
            self.config = config
            self.test_id = test_id
            self.timestamp = time.time()
        
        def create_ingestion_data(self, **kwargs) -> Dict[str, Any]:
            """Create test data for ingestion pipeline."""
            default_data = {
                "test_id": self.test_id,
                "timestamp": self.timestamp,
                "source": "test_suite",
                "data_type": "structured",
                "payload": {
                    "user_id": 12345,
                    "action": "test_action",
                    "metadata": {
                        "test_run": True,
                        "environment": "test"
                    }
                }
            }
            default_data.update(kwargs)
            return default_data
        
        def create_learning_data(self, **kwargs) -> Dict[str, Any]:
            """Create test data for learning engine."""
            default_data = {
                "test_id": self.test_id,
                "timestamp": self.timestamp,
                "model_type": "test_model",
                "training_data": [1, 2, 3, 4, 5],
                "labels": [0, 1, 0, 1, 0],
                "hyperparameters": {
                    "learning_rate": 0.001,
                    "batch_size": 32,
                    "epochs": 10
                }
            }
            default_data.update(kwargs)
            return default_data
        
        def create_decision_data(self, **kwargs) -> Dict[str, Any]:
            """Create test data for decision engine."""
            default_data = {
                "test_id": self.test_id,
                "timestamp": self.timestamp,
                "context": {
                    "user_id": 12345,
                    "session_id": "test_session",
                    "environment": "test"
                },
                "features": {
                    "feature_1": 0.5,
                    "feature_2": 0.8,
                    "feature_3": 0.2
                },
                "constraints": {
                    "max_latency_ms": 100,
                    "confidence_threshold": 0.7
                }
            }
            default_data.update(kwargs)
            return default_data
        
        def save_test_data(self, data: Dict[str, Any], filename: str) -> Path:
            """Save test data to file."""
            filepath = self.config.test_data_dir / f"{self.test_id}_{filename}"
            with open(filepath, 'w') as f:
                json.dump(data, f, indent=2)
            return filepath
    
    return TestDataFactory(test_config, test_id)

@pytest.fixture(scope="function")
async def kafka_test_client(test_config: TestConfig):
    """Kafka client for testing."""
    try:
        from kafka import KafkaProducer, KafkaConsumer
        
        producer = KafkaProducer(
            bootstrap_servers=test_config.kafka_brokers,
            value_serializer=lambda x: json.dumps(x).encode('utf-8'),
            key_serializer=lambda x: x.encode('utf-8') if x else None
        )
        
        yield producer
        
        producer.close()
    except ImportError:
        logger.warning("Kafka client not available")
        yield None

@pytest.fixture(scope="function")
async def redis_test_client(test_config: TestConfig):
    """Redis client for testing."""
    try:
        import redis
        
        client = redis.Redis(
            host=test_config.redis_host,
            port=test_config.redis_port,
            db=15,  # Use test database
            decode_responses=True
        )
        
        # Clear test database
        client.flushdb()
        
        yield client
        
        # Clean up
        client.flushdb()
        client.close()
    except ImportError:
        logger.warning("Redis client not available")
        yield None

@pytest.fixture(scope="function")
def grpc_test_channels(test_config: TestConfig):
    """gRPC channels for testing."""
    try:
        import grpc
        
        channels = {
            'producer': grpc.insecure_channel(f'localhost:{test_config.grpc_producer_port}'),
            'consumer': grpc.insecure_channel(f'localhost:{test_config.grpc_consumer_port}'),
            'learning': grpc.insecure_channel(f'localhost:{test_config.grpc_learning_port}'),
            'decision': grpc.insecure_channel(f'localhost:{test_config.grpc_decision_port}'),
        }
        
        yield channels
        
        # Close channels
        for channel in channels.values():
            channel.close()
    except ImportError:
        logger.warning("gRPC not available")
        yield {}

@pytest.fixture(scope="function")
def http_test_client():
    """HTTP client for testing."""
    try:
        import requests
        
        session = requests.Session()
        session.timeout = 10
        
        yield session
        
        session.close()
    except ImportError:
        logger.warning("Requests not available")
        yield None

# Test markers
pytest.mark.unit = pytest.mark.unit
pytest.mark.integration = pytest.mark.integration
pytest.mark.e2e = pytest.mark.e2e
pytest.mark.performance = pytest.mark.performance
pytest.mark.slow = pytest.mark.slow

# Pytest configuration
def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line("markers", "unit: Unit tests")
    config.addinivalue_line("markers", "integration: Integration tests")
    config.addinivalue_line("markers", "e2e: End-to-end tests")
    config.addinivalue_line("markers", "performance: Performance tests")
    config.addinivalue_line("markers", "slow: Slow running tests")

def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers based on file paths."""
    for item in items:
        # Add markers based on file path
        if "unit" in str(item.fspath):
            item.add_marker(pytest.mark.unit)
        elif "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        elif "e2e" in str(item.fspath):
            item.add_marker(pytest.mark.e2e)
        elif "performance" in str(item.fspath):
            item.add_marker(pytest.mark.performance)
