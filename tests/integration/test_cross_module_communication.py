#!/usr/bin/env python3
"""
Cross-Module Integration Tests for ASI System

Comprehensive integration tests for communication between ASI modules
including Kafka, gRPC, REST APIs, and data flow validation.
"""

import asyncio
import json
import time
import uuid
import pytest
import grpc
import requests
from typing import Dict, Any, List, Optional
from unittest.mock import Mock, patch, AsyncMock
from dataclasses import dataclass, asdict
from contextlib import asynccontextmanager

# Test dependencies
import kafka
from kafka import KafkaProducer, KafkaConsumer
import redis
import websockets

@dataclass
class TestMessage:
    """Standard test message format across modules."""
    id: str
    timestamp: float
    source_module: str
    target_module: str
    message_type: str
    payload: Dict[str, Any]
    correlation_id: str = None
    
    def __post_init__(self):
        if self.correlation_id is None:
            self.correlation_id = str(uuid.uuid4())

@dataclass
class ModuleEndpoint:
    """Module endpoint configuration."""
    name: str
    grpc_port: int
    http_port: int
    health_endpoint: str
    metrics_endpoint: str
    kafka_topics: List[str]

class TestEnvironment:
    """Test environment configuration and utilities."""
    
    def __init__(self):
        self.modules = {
            'ingestion': ModuleEndpoint(
                name='ingestion',
                grpc_port=50051,
                http_port=8080,
                health_endpoint='/health',
                metrics_endpoint='/metrics',
                kafka_topics=['asi-ingestion', 'asi-web-scraping']
            ),
            'learning': ModuleEndpoint(
                name='learning',
                grpc_port=50055,
                http_port=8082,
                health_endpoint='/health',
                metrics_endpoint='/metrics',
                kafka_topics=['asi-learning-input', 'asi-learning-output']
            ),
            'decision': ModuleEndpoint(
                name='decision',
                grpc_port=50056,
                http_port=8083,
                health_endpoint='/health',
                metrics_endpoint='/metrics',
                kafka_topics=['asi-decision-input', 'asi-decision-output']
            ),
            'security': ModuleEndpoint(
                name='security',
                grpc_port=50057,
                http_port=8084,
                health_endpoint='/health',
                metrics_endpoint='/metrics',
                kafka_topics=['asi-security-events', 'asi-audit-logs']
            )
        }
        
        self.kafka_config = {
            'bootstrap_servers': ['localhost:9092'],
            'timeout': 30
        }
        
        self.redis_config = {
            'host': 'localhost',
            'port': 6379,
            'db': 15  # Test database
        }

class IntegrationTestBase:
    """Base class for integration tests with common utilities."""
    
    def __init__(self):
        self.env = TestEnvironment()
        self.test_id = str(uuid.uuid4())
        self.kafka_producer = None
        self.kafka_consumers = {}
        self.redis_client = None
        self.grpc_channels = {}
        
    async def setup(self):
        """Set up test environment."""
        print(f"Setting up integration test environment (ID: {self.test_id})")
        
        # Initialize Kafka producer
        self.kafka_producer = KafkaProducer(
            bootstrap_servers=self.env.kafka_config['bootstrap_servers'],
            value_serializer=lambda x: json.dumps(x).encode('utf-8'),
            key_serializer=lambda x: x.encode('utf-8') if x else None
        )
        
        # Initialize Redis client
        try:
            import redis
            self.redis_client = redis.Redis(**self.env.redis_config)
            self.redis_client.flushdb()  # Clear test database
        except ImportError:
            print("Redis not available for testing")
        
        # Initialize gRPC channels
        for module_name, module in self.env.modules.items():
            try:
                channel = grpc.insecure_channel(f'localhost:{module.grpc_port}')
                self.grpc_channels[module_name] = channel
            except Exception as e:
                print(f"Failed to create gRPC channel for {module_name}: {e}")
        
        print("Integration test environment setup completed")
    
    async def teardown(self):
        """Clean up test environment."""
        print("Cleaning up integration test environment...")
        
        if self.kafka_producer:
            self.kafka_producer.close()
        
        for consumer in self.kafka_consumers.values():
            consumer.close()
        
        if self.redis_client:
            self.redis_client.flushdb()
            self.redis_client.close()
        
        for channel in self.grpc_channels.values():
            channel.close()
        
        print("Integration test environment cleaned up")
    
    async def wait_for_module_health(self, module_name: str, timeout: int = 30) -> bool:
        """Wait for a module to become healthy."""
        module = self.env.modules[module_name]
        url = f"http://localhost:{module.http_port}{module.health_endpoint}"
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                response = requests.get(url, timeout=5)
                if response.status_code == 200:
                    return True
            except requests.RequestException:
                pass
            
            await asyncio.sleep(1)
        
        return False
    
    async def send_kafka_message(self, topic: str, message: TestMessage) -> bool:
        """Send a message to Kafka topic."""
        try:
            future = self.kafka_producer.send(
                topic, 
                asdict(message), 
                key=message.correlation_id
            )
            future.get(timeout=10)
            return True
        except Exception as e:
            print(f"Failed to send Kafka message: {e}")
            return False
    
    async def consume_kafka_messages(self, topic: str, timeout: int = 10) -> List[TestMessage]:
        """Consume messages from Kafka topic."""
        if topic not in self.kafka_consumers:
            self.kafka_consumers[topic] = KafkaConsumer(
                topic,
                bootstrap_servers=self.env.kafka_config['bootstrap_servers'],
                value_deserializer=lambda x: json.loads(x.decode('utf-8')),
                key_deserializer=lambda x: x.decode('utf-8') if x else None,
                group_id=f'test-group-{self.test_id}',
                auto_offset_reset='latest'
            )
        
        consumer = self.kafka_consumers[topic]
        messages = []
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            msg_pack = consumer.poll(timeout_ms=1000)
            for tp, msgs in msg_pack.items():
                for msg in msgs:
                    try:
                        test_msg = TestMessage(**msg.value)
                        messages.append(test_msg)
                    except Exception as e:
                        print(f"Failed to parse message: {e}")
            
            if messages:
                break
        
        return messages
    
    async def make_http_request(self, module_name: str, endpoint: str, 
                               method: str = 'GET', data: Dict = None) -> Optional[requests.Response]:
        """Make HTTP request to module."""
        module = self.env.modules[module_name]
        url = f"http://localhost:{module.http_port}{endpoint}"
        
        try:
            if method.upper() == 'GET':
                return requests.get(url, timeout=10)
            elif method.upper() == 'POST':
                return requests.post(url, json=data, timeout=10)
            elif method.upper() == 'PUT':
                return requests.put(url, json=data, timeout=10)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
        except requests.RequestException as e:
            print(f"HTTP request failed: {e}")
            return None

class TestIngestionToLearningFlow(IntegrationTestBase):
    """Test data flow from Ingestion to Learning Engine."""
    
    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_data_ingestion_to_learning_pipeline(self):
        """Test complete data flow from ingestion to learning."""
        await self.setup()
        
        try:
            # 1. Verify modules are healthy
            assert await self.wait_for_module_health('ingestion'), "Ingestion module not healthy"
            assert await self.wait_for_module_health('learning'), "Learning module not healthy"
            
            # 2. Send data to ingestion
            ingestion_message = TestMessage(
                id=f"test-{self.test_id}",
                timestamp=time.time(),
                source_module="test_suite",
                target_module="ingestion",
                message_type="raw_data",
                payload={
                    "data_type": "sensor_reading",
                    "sensor_id": "temp_001",
                    "value": 23.5,
                    "unit": "celsius",
                    "location": "server_room_a"
                }
            )
            
            success = await self.send_kafka_message('asi-ingestion', ingestion_message)
            assert success, "Failed to send message to ingestion"
            
            # 3. Wait for processed data to appear in learning input topic
            learning_messages = await self.consume_kafka_messages('asi-learning-input', timeout=15)
            assert len(learning_messages) > 0, "No messages received in learning input topic"
            
            # 4. Verify message transformation
            learning_msg = learning_messages[0]
            assert learning_msg.correlation_id == ingestion_message.correlation_id
            assert learning_msg.source_module == "ingestion"
            assert learning_msg.target_module == "learning"
            assert "processed_data" in learning_msg.payload
            
            # 5. Verify learning module processes the data
            learning_output = await self.consume_kafka_messages('asi-learning-output', timeout=15)
            assert len(learning_output) > 0, "No output from learning module"
            
            output_msg = learning_output[0]
            assert output_msg.correlation_id == ingestion_message.correlation_id
            assert "model_prediction" in output_msg.payload or "training_update" in output_msg.payload
            
        finally:
            await self.teardown()
    
    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_ingestion_learning_error_handling(self):
        """Test error handling in ingestion to learning flow."""
        await self.setup()
        
        try:
            # Send malformed data
            malformed_message = TestMessage(
                id=f"malformed-{self.test_id}",
                timestamp=time.time(),
                source_module="test_suite",
                target_module="ingestion",
                message_type="raw_data",
                payload={
                    "invalid_field": "this should cause an error",
                    "missing_required_fields": True
                }
            )
            
            await self.send_kafka_message('asi-ingestion', malformed_message)
            
            # Check for error messages or dead letter queue
            await asyncio.sleep(5)  # Allow processing time
            
            # Verify error handling (implementation specific)
            # This could check error logs, dead letter queues, or error metrics
            
        finally:
            await self.teardown()

class TestLearningToDecisionFlow(IntegrationTestBase):
    """Test data flow from Learning Engine to Decision Engine."""
    
    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_learning_to_decision_pipeline(self):
        """Test model predictions flowing to decision engine."""
        await self.setup()
        
        try:
            # 1. Verify modules are healthy
            assert await self.wait_for_module_health('learning'), "Learning module not healthy"
            assert await self.wait_for_module_health('decision'), "Decision module not healthy"
            
            # 2. Send model prediction from learning
            prediction_message = TestMessage(
                id=f"prediction-{self.test_id}",
                timestamp=time.time(),
                source_module="learning",
                target_module="decision",
                message_type="model_prediction",
                payload={
                    "model_id": "temperature_predictor_v1",
                    "prediction": {
                        "temperature": 25.3,
                        "confidence": 0.87,
                        "prediction_horizon": "1_hour"
                    },
                    "features_used": ["current_temp", "humidity", "time_of_day"],
                    "model_version": "1.2.3"
                }
            )
            
            success = await self.send_kafka_message('asi-learning-output', prediction_message)
            assert success, "Failed to send prediction message"
            
            # 3. Wait for decision engine to process
            decision_messages = await self.consume_kafka_messages('asi-decision-output', timeout=15)
            assert len(decision_messages) > 0, "No decisions received from decision engine"
            
            # 4. Verify decision output
            decision_msg = decision_messages[0]
            assert decision_msg.correlation_id == prediction_message.correlation_id
            assert decision_msg.source_module == "decision"
            assert "decision" in decision_msg.payload
            assert "action_plan" in decision_msg.payload
            
        finally:
            await self.teardown()

class TestSecurityIntegration(IntegrationTestBase):
    """Test security module integration with other components."""
    
    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_security_monitoring_integration(self):
        """Test security module monitoring other modules."""
        await self.setup()
        
        try:
            # 1. Verify security module is healthy
            assert await self.wait_for_module_health('security'), "Security module not healthy"
            
            # 2. Send suspicious activity message
            suspicious_message = TestMessage(
                id=f"suspicious-{self.test_id}",
                timestamp=time.time(),
                source_module="test_suite",
                target_module="security",
                message_type="security_event",
                payload={
                    "event_type": "anomalous_data_pattern",
                    "severity": "medium",
                    "source_ip": "*************",
                    "affected_module": "learning",
                    "details": {
                        "pattern": "unusual_model_behavior",
                        "deviation_score": 0.85
                    }
                }
            )
            
            success = await self.send_kafka_message('asi-security-events', suspicious_message)
            assert success, "Failed to send security event"
            
            # 3. Wait for security response
            audit_messages = await self.consume_kafka_messages('asi-audit-logs', timeout=15)
            assert len(audit_messages) > 0, "No audit logs generated"
            
            # 4. Verify audit log
            audit_msg = audit_messages[0]
            assert audit_msg.correlation_id == suspicious_message.correlation_id
            assert "security_analysis" in audit_msg.payload
            assert "risk_assessment" in audit_msg.payload
            
        finally:
            await self.teardown()

class TestGRPCCommunication(IntegrationTestBase):
    """Test gRPC communication between modules."""
    
    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_grpc_health_checks(self):
        """Test gRPC health check endpoints."""
        await self.setup()
        
        try:
            for module_name, channel in self.grpc_channels.items():
                # Test gRPC health check (would use actual gRPC health service)
                # This is a placeholder for actual gRPC health check implementation
                print(f"Testing gRPC health for {module_name}")
                
                # In real implementation, would use:
                # import grpc_health.v1.health_pb2 as health_pb2
                # import grpc_health.v1.health_pb2_grpc as health_pb2_grpc
                # 
                # stub = health_pb2_grpc.HealthStub(channel)
                # request = health_pb2.HealthCheckRequest(service="")
                # response = stub.Check(request)
                # assert response.status == health_pb2.HealthCheckResponse.SERVING
                
        finally:
            await self.teardown()

class TestWebSocketCommunication(IntegrationTestBase):
    """Test WebSocket communication for real-time updates."""
    
    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_websocket_real_time_updates(self):
        """Test real-time updates via WebSocket."""
        await self.setup()
        
        try:
            # Test WebSocket connection to UI module
            # This would connect to actual WebSocket endpoint
            websocket_url = "ws://localhost:8085/ws/real-time-updates"
            
            # Placeholder for WebSocket testing
            # In real implementation:
            # async with websockets.connect(websocket_url) as websocket:
            #     # Send test message
            #     test_data = {"type": "test", "data": "hello"}
            #     await websocket.send(json.dumps(test_data))
            #     
            #     # Receive response
            #     response = await websocket.recv()
            #     response_data = json.loads(response)
            #     assert response_data["status"] == "ok"
            
            print("WebSocket test placeholder - would test real-time communication")
            
        finally:
            await self.teardown()

# Performance integration tests
class TestPerformanceIntegration(IntegrationTestBase):
    """Test performance characteristics of module integration."""
    
    @pytest.mark.asyncio
    @pytest.mark.integration
    @pytest.mark.performance
    async def test_end_to_end_latency(self):
        """Test end-to-end latency across modules."""
        await self.setup()
        
        try:
            start_time = time.time()
            
            # Send message through complete pipeline
            test_message = TestMessage(
                id=f"latency-test-{self.test_id}",
                timestamp=start_time,
                source_module="test_suite",
                target_module="ingestion",
                message_type="latency_test",
                payload={"test_data": "latency_measurement"}
            )
            
            await self.send_kafka_message('asi-ingestion', test_message)
            
            # Wait for message to complete full pipeline
            final_messages = await self.consume_kafka_messages('asi-decision-output', timeout=30)
            
            if final_messages:
                end_time = time.time()
                total_latency = end_time - start_time
                
                print(f"End-to-end latency: {total_latency:.3f} seconds")
                assert total_latency < 5.0, f"Latency too high: {total_latency:.3f}s"
            else:
                pytest.fail("Message did not complete pipeline within timeout")
                
        finally:
            await self.teardown()
    
    @pytest.mark.asyncio
    @pytest.mark.integration
    @pytest.mark.performance
    async def test_throughput_under_load(self):
        """Test system throughput under load."""
        await self.setup()
        
        try:
            num_messages = 100
            start_time = time.time()
            
            # Send multiple messages concurrently
            tasks = []
            for i in range(num_messages):
                message = TestMessage(
                    id=f"throughput-test-{self.test_id}-{i}",
                    timestamp=time.time(),
                    source_module="test_suite",
                    target_module="ingestion",
                    message_type="throughput_test",
                    payload={"message_index": i}
                )
                
                task = self.send_kafka_message('asi-ingestion', message)
                tasks.append(task)
            
            # Wait for all messages to be sent
            results = await asyncio.gather(*tasks)
            send_time = time.time() - start_time
            
            successful_sends = sum(results)
            throughput = successful_sends / send_time
            
            print(f"Throughput: {throughput:.2f} messages/second")
            assert throughput > 10, f"Throughput too low: {throughput:.2f} msg/s"
            
        finally:
            await self.teardown()

# Test runner configuration
if __name__ == "__main__":
    pytest.main([
        __file__,
        "-v",
        "--tb=short",
        "-m", "integration"
    ])
