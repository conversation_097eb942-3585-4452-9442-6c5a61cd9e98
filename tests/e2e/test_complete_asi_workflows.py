#!/usr/bin/env python3
"""
End-to-End Tests for Complete ASI System Workflows

Comprehensive E2E tests that validate complete data flows through
the entire ASI system from ingestion to final decision and action.
"""

import asyncio
import json
import time
import uuid
import pytest
import logging
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
import numpy as np
import pandas as pd

# Test infrastructure
from tests.conftest import TestConfig, TestDataFactory
from tests.integration.test_cross_module_communication import IntegrationTestBase, TestMessage

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class E2ETestScenario:
    """End-to-end test scenario definition."""
    name: str
    description: str
    input_data: Dict[str, Any]
    expected_outputs: Dict[str, Any]
    max_duration_seconds: int
    validation_rules: List[str]

@dataclass
class WorkflowStep:
    """Individual step in an E2E workflow."""
    step_name: str
    module: str
    input_topic: str
    output_topic: str
    expected_processing_time_ms: int
    validation_function: Optional[callable] = None

class E2ETestOrchestrator(IntegrationTestBase):
    """Orchestrator for end-to-end test execution."""
    
    def __init__(self):
        super().__init__()
        self.test_scenarios = []
        self.workflow_results = {}
        self.performance_metrics = {}
        
    def add_scenario(self, scenario: E2ETestScenario):
        """Add a test scenario to the orchestrator."""
        self.test_scenarios.append(scenario)
    
    async def execute_workflow(self, workflow_steps: List[WorkflowStep], 
                              initial_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a complete workflow through multiple modules."""
        workflow_id = str(uuid.uuid4())
        start_time = time.time()
        
        logger.info(f"Starting workflow execution: {workflow_id}")
        
        results = {
            'workflow_id': workflow_id,
            'start_time': start_time,
            'steps': [],
            'final_output': None,
            'total_duration': 0,
            'success': False
        }
        
        current_data = initial_data
        
        try:
            for i, step in enumerate(workflow_steps):
                step_start = time.time()
                
                logger.info(f"Executing step {i+1}/{len(workflow_steps)}: {step.step_name}")
                
                # Create message for this step
                message = TestMessage(
                    id=f"{workflow_id}-step-{i}",
                    timestamp=time.time(),
                    source_module="e2e_test",
                    target_module=step.module,
                    message_type="workflow_data",
                    payload=current_data,
                    correlation_id=workflow_id
                )
                
                # Send message to input topic
                success = await self.send_kafka_message(step.input_topic, message)
                if not success:
                    raise Exception(f"Failed to send message to {step.input_topic}")
                
                # Wait for output
                output_messages = await self.consume_kafka_messages(
                    step.output_topic, 
                    timeout=step.expected_processing_time_ms // 1000 + 10
                )
                
                if not output_messages:
                    raise Exception(f"No output received from {step.output_topic}")
                
                # Find our message in the output
                step_output = None
                for msg in output_messages:
                    if msg.correlation_id == workflow_id:
                        step_output = msg
                        break
                
                if not step_output:
                    raise Exception(f"Output message not found for workflow {workflow_id}")
                
                # Validate step output if validation function provided
                if step.validation_function:
                    validation_result = step.validation_function(step_output.payload)
                    if not validation_result:
                        raise Exception(f"Step validation failed for {step.step_name}")
                
                step_duration = time.time() - step_start
                
                step_result = {
                    'step_name': step.step_name,
                    'module': step.module,
                    'duration_ms': step_duration * 1000,
                    'input_size_bytes': len(json.dumps(current_data)),
                    'output_size_bytes': len(json.dumps(step_output.payload)),
                    'success': True
                }
                
                results['steps'].append(step_result)
                current_data = step_output.payload
                
                logger.info(f"Step {step.step_name} completed in {step_duration:.3f}s")
            
            results['final_output'] = current_data
            results['total_duration'] = time.time() - start_time
            results['success'] = True
            
            logger.info(f"Workflow {workflow_id} completed successfully in {results['total_duration']:.3f}s")
            
        except Exception as e:
            results['error'] = str(e)
            results['total_duration'] = time.time() - start_time
            logger.error(f"Workflow {workflow_id} failed: {e}")
        
        return results

class TestCompleteASIWorkflows(E2ETestOrchestrator):
    """Complete ASI system workflow tests."""
    
    @pytest.mark.asyncio
    @pytest.mark.e2e
    async def test_sensor_data_to_action_workflow(self):
        """Test complete workflow from sensor data ingestion to automated action."""
        await self.setup()
        
        try:
            # Define the complete workflow
            workflow_steps = [
                WorkflowStep(
                    step_name="data_ingestion",
                    module="ingestion",
                    input_topic="asi-ingestion",
                    output_topic="asi-learning-input",
                    expected_processing_time_ms=1000,
                    validation_function=self._validate_ingestion_output
                ),
                WorkflowStep(
                    step_name="learning_inference",
                    module="learning",
                    input_topic="asi-learning-input",
                    output_topic="asi-learning-output",
                    expected_processing_time_ms=2000,
                    validation_function=self._validate_learning_output
                ),
                WorkflowStep(
                    step_name="decision_making",
                    module="decision",
                    input_topic="asi-learning-output",
                    output_topic="asi-decision-output",
                    expected_processing_time_ms=500,
                    validation_function=self._validate_decision_output
                ),
                WorkflowStep(
                    step_name="action_execution",
                    module="runtime",
                    input_topic="asi-decision-output",
                    output_topic="asi-action-results",
                    expected_processing_time_ms=1000,
                    validation_function=self._validate_action_output
                )
            ]
            
            # Initial sensor data
            sensor_data = {
                "sensor_readings": [
                    {"sensor_id": "temp_001", "value": 28.5, "unit": "celsius"},
                    {"sensor_id": "humidity_001", "value": 65.2, "unit": "percent"},
                    {"sensor_id": "pressure_001", "value": 1013.25, "unit": "hPa"}
                ],
                "timestamp": time.time(),
                "location": "server_room_a",
                "data_quality": "high"
            }
            
            # Execute the workflow
            results = await self.execute_workflow(workflow_steps, sensor_data)
            
            # Validate overall workflow success
            assert results['success'], f"Workflow failed: {results.get('error', 'Unknown error')}"
            assert results['total_duration'] < 10.0, f"Workflow too slow: {results['total_duration']:.3f}s"
            
            # Validate each step
            assert len(results['steps']) == len(workflow_steps), "Not all steps completed"
            
            for step_result in results['steps']:
                assert step_result['success'], f"Step {step_result['step_name']} failed"
                assert step_result['duration_ms'] > 0, "Step duration should be positive"
            
            # Validate final output contains action plan
            final_output = results['final_output']
            assert 'action_plan' in final_output, "Final output missing action plan"
            assert 'execution_status' in final_output, "Final output missing execution status"
            
            logger.info("Sensor data to action workflow test passed")
            
        finally:
            await self.teardown()
    
    @pytest.mark.asyncio
    @pytest.mark.e2e
    async def test_anomaly_detection_and_response_workflow(self):
        """Test anomaly detection and automated response workflow."""
        await self.setup()
        
        try:
            # Create anomalous data
            anomalous_data = {
                "metrics": [
                    {"name": "cpu_usage", "value": 95.8, "threshold": 80.0},
                    {"name": "memory_usage", "value": 89.2, "threshold": 85.0},
                    {"name": "disk_io", "value": 1250.5, "threshold": 1000.0}
                ],
                "timestamp": time.time(),
                "source": "monitoring_system",
                "severity": "high"
            }
            
            workflow_steps = [
                WorkflowStep(
                    step_name="anomaly_ingestion",
                    module="ingestion",
                    input_topic="asi-ingestion",
                    output_topic="asi-security-events",
                    expected_processing_time_ms=500,
                    validation_function=self._validate_anomaly_ingestion
                ),
                WorkflowStep(
                    step_name="security_analysis",
                    module="security",
                    input_topic="asi-security-events",
                    output_topic="asi-security-response",
                    expected_processing_time_ms=1500,
                    validation_function=self._validate_security_analysis
                ),
                WorkflowStep(
                    step_name="response_decision",
                    module="decision",
                    input_topic="asi-security-response",
                    output_topic="asi-decision-output",
                    expected_processing_time_ms=800,
                    validation_function=self._validate_response_decision
                )
            ]
            
            results = await self.execute_workflow(workflow_steps, anomalous_data)
            
            assert results['success'], f"Anomaly workflow failed: {results.get('error')}"
            
            # Validate security response
            final_output = results['final_output']
            assert 'threat_level' in final_output, "Missing threat level assessment"
            assert 'mitigation_actions' in final_output, "Missing mitigation actions"
            assert final_output['threat_level'] in ['low', 'medium', 'high', 'critical']
            
            logger.info("Anomaly detection and response workflow test passed")
            
        finally:
            await self.teardown()
    
    @pytest.mark.asyncio
    @pytest.mark.e2e
    async def test_learning_model_update_workflow(self):
        """Test model training and deployment workflow."""
        await self.setup()
        
        try:
            # Training data
            training_data = {
                "dataset": {
                    "features": np.random.rand(1000, 10).tolist(),
                    "labels": np.random.randint(0, 3, 1000).tolist()
                },
                "model_config": {
                    "model_type": "neural_network",
                    "layers": [10, 20, 10, 3],
                    "learning_rate": 0.001,
                    "epochs": 50
                },
                "validation_split": 0.2,
                "timestamp": time.time()
            }
            
            workflow_steps = [
                WorkflowStep(
                    step_name="data_preprocessing",
                    module="learning",
                    input_topic="asi-learning-input",
                    output_topic="asi-learning-preprocessed",
                    expected_processing_time_ms=2000,
                    validation_function=self._validate_preprocessing
                ),
                WorkflowStep(
                    step_name="model_training",
                    module="learning",
                    input_topic="asi-learning-preprocessed",
                    output_topic="asi-learning-trained",
                    expected_processing_time_ms=10000,  # Training takes longer
                    validation_function=self._validate_training
                ),
                WorkflowStep(
                    step_name="model_validation",
                    module="learning",
                    input_topic="asi-learning-trained",
                    output_topic="asi-learning-validated",
                    expected_processing_time_ms=3000,
                    validation_function=self._validate_model_validation
                ),
                WorkflowStep(
                    step_name="model_deployment",
                    module="runtime",
                    input_topic="asi-learning-validated",
                    output_topic="asi-model-deployed",
                    expected_processing_time_ms=2000,
                    validation_function=self._validate_deployment
                )
            ]
            
            results = await self.execute_workflow(workflow_steps, training_data)
            
            assert results['success'], f"Model update workflow failed: {results.get('error')}"
            
            # Validate model deployment
            final_output = results['final_output']
            assert 'model_id' in final_output, "Missing model ID"
            assert 'deployment_status' in final_output, "Missing deployment status"
            assert 'model_metrics' in final_output, "Missing model metrics"
            assert final_output['deployment_status'] == 'deployed'
            
            logger.info("Learning model update workflow test passed")
            
        finally:
            await self.teardown()
    
    @pytest.mark.asyncio
    @pytest.mark.e2e
    @pytest.mark.performance
    async def test_high_throughput_workflow(self):
        """Test system performance under high throughput."""
        await self.setup()
        
        try:
            num_concurrent_workflows = 10
            messages_per_workflow = 5
            
            # Create multiple concurrent workflows
            workflow_tasks = []
            
            for i in range(num_concurrent_workflows):
                workflow_steps = [
                    WorkflowStep(
                        step_name="fast_ingestion",
                        module="ingestion",
                        input_topic="asi-ingestion",
                        output_topic="asi-learning-input",
                        expected_processing_time_ms=500
                    ),
                    WorkflowStep(
                        step_name="fast_processing",
                        module="learning",
                        input_topic="asi-learning-input",
                        output_topic="asi-learning-output",
                        expected_processing_time_ms=1000
                    )
                ]
                
                for j in range(messages_per_workflow):
                    test_data = {
                        "workflow_id": i,
                        "message_id": j,
                        "data": f"test_data_{i}_{j}",
                        "timestamp": time.time()
                    }
                    
                    task = self.execute_workflow(workflow_steps, test_data)
                    workflow_tasks.append(task)
            
            # Execute all workflows concurrently
            start_time = time.time()
            results = await asyncio.gather(*workflow_tasks, return_exceptions=True)
            total_time = time.time() - start_time
            
            # Analyze results
            successful_workflows = [r for r in results if isinstance(r, dict) and r.get('success')]
            failed_workflows = [r for r in results if not (isinstance(r, dict) and r.get('success'))]
            
            success_rate = len(successful_workflows) / len(results)
            throughput = len(successful_workflows) / total_time
            
            logger.info(f"High throughput test results:")
            logger.info(f"  Total workflows: {len(results)}")
            logger.info(f"  Successful: {len(successful_workflows)}")
            logger.info(f"  Failed: {len(failed_workflows)}")
            logger.info(f"  Success rate: {success_rate:.2%}")
            logger.info(f"  Throughput: {throughput:.2f} workflows/second")
            logger.info(f"  Total time: {total_time:.3f} seconds")
            
            # Assertions
            assert success_rate >= 0.9, f"Success rate too low: {success_rate:.2%}"
            assert throughput >= 1.0, f"Throughput too low: {throughput:.2f} workflows/s"
            
        finally:
            await self.teardown()
    
    # Validation functions
    def _validate_ingestion_output(self, payload: Dict[str, Any]) -> bool:
        """Validate ingestion module output."""
        required_fields = ['processed_data', 'data_quality_score', 'processing_timestamp']
        return all(field in payload for field in required_fields)
    
    def _validate_learning_output(self, payload: Dict[str, Any]) -> bool:
        """Validate learning module output."""
        required_fields = ['predictions', 'confidence_scores', 'model_version']
        return all(field in payload for field in required_fields)
    
    def _validate_decision_output(self, payload: Dict[str, Any]) -> bool:
        """Validate decision module output."""
        required_fields = ['decision', 'reasoning', 'confidence']
        return all(field in payload for field in required_fields)
    
    def _validate_action_output(self, payload: Dict[str, Any]) -> bool:
        """Validate action execution output."""
        required_fields = ['action_plan', 'execution_status', 'results']
        return all(field in payload for field in required_fields)
    
    def _validate_anomaly_ingestion(self, payload: Dict[str, Any]) -> bool:
        """Validate anomaly ingestion output."""
        return 'anomaly_score' in payload and 'alert_level' in payload
    
    def _validate_security_analysis(self, payload: Dict[str, Any]) -> bool:
        """Validate security analysis output."""
        return 'threat_assessment' in payload and 'recommended_actions' in payload
    
    def _validate_response_decision(self, payload: Dict[str, Any]) -> bool:
        """Validate response decision output."""
        return 'response_plan' in payload and 'priority' in payload
    
    def _validate_preprocessing(self, payload: Dict[str, Any]) -> bool:
        """Validate data preprocessing output."""
        return 'preprocessed_features' in payload and 'feature_stats' in payload
    
    def _validate_training(self, payload: Dict[str, Any]) -> bool:
        """Validate model training output."""
        return 'trained_model' in payload and 'training_metrics' in payload
    
    def _validate_model_validation(self, payload: Dict[str, Any]) -> bool:
        """Validate model validation output."""
        return 'validation_metrics' in payload and 'model_approved' in payload
    
    def _validate_deployment(self, payload: Dict[str, Any]) -> bool:
        """Validate model deployment output."""
        return 'deployment_id' in payload and 'deployment_status' in payload

class TestDataDrivenWorkflows(E2ETestOrchestrator):
    """Data-driven E2E tests using real-world scenarios."""
    
    @pytest.mark.asyncio
    @pytest.mark.e2e
    @pytest.mark.parametrize("scenario_file", [
        "tests/data/scenarios/iot_sensor_scenario.json",
        "tests/data/scenarios/financial_fraud_scenario.json",
        "tests/data/scenarios/predictive_maintenance_scenario.json"
    ])
    async def test_scenario_based_workflows(self, scenario_file: str):
        """Test workflows based on predefined scenarios."""
        await self.setup()
        
        try:
            # Load scenario from file
            scenario_path = Path(scenario_file)
            if not scenario_path.exists():
                pytest.skip(f"Scenario file not found: {scenario_file}")
            
            with open(scenario_path, 'r') as f:
                scenario_data = json.load(f)
            
            scenario = E2ETestScenario(**scenario_data)
            
            # Execute scenario workflow
            # Implementation would depend on scenario structure
            logger.info(f"Executing scenario: {scenario.name}")
            
            # This is a placeholder for scenario execution
            # Real implementation would parse scenario and execute workflow
            
        finally:
            await self.teardown()

# Performance benchmarking
class TestE2EPerformanceBenchmarks(E2ETestOrchestrator):
    """Performance benchmarks for E2E workflows."""
    
    @pytest.mark.asyncio
    @pytest.mark.e2e
    @pytest.mark.performance
    async def test_latency_benchmarks(self):
        """Benchmark end-to-end latency for different workflow types."""
        await self.setup()
        
        try:
            benchmark_results = {}
            
            # Test different workflow types
            workflow_types = [
                ("simple_prediction", 1),
                ("complex_analysis", 3),
                ("batch_processing", 5)
            ]
            
            for workflow_type, num_steps in workflow_types:
                latencies = []
                
                for _ in range(10):  # Run each test 10 times
                    start_time = time.time()
                    
                    # Simulate workflow execution
                    await asyncio.sleep(0.1 * num_steps)  # Simulate processing
                    
                    latency = time.time() - start_time
                    latencies.append(latency)
                
                benchmark_results[workflow_type] = {
                    'mean_latency': np.mean(latencies),
                    'p95_latency': np.percentile(latencies, 95),
                    'p99_latency': np.percentile(latencies, 99),
                    'min_latency': np.min(latencies),
                    'max_latency': np.max(latencies)
                }
            
            # Log benchmark results
            for workflow_type, metrics in benchmark_results.items():
                logger.info(f"Benchmark results for {workflow_type}:")
                for metric, value in metrics.items():
                    logger.info(f"  {metric}: {value:.3f}s")
            
            # Validate performance requirements
            assert benchmark_results['simple_prediction']['p95_latency'] < 1.0
            assert benchmark_results['complex_analysis']['p95_latency'] < 5.0
            assert benchmark_results['batch_processing']['p95_latency'] < 10.0
            
        finally:
            await self.teardown()

# Test runner
if __name__ == "__main__":
    pytest.main([
        __file__,
        "-v",
        "--tb=short",
        "-m", "e2e"
    ])
