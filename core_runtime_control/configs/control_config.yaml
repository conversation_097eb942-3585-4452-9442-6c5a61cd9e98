# ASI Core Runtime & Real-Time Control Configuration
# =================================================

# General system configuration
system:
  name: "ASI Core Runtime Control"
  version: "1.0.0"
  environment: "production"  # development, testing, production
  log_level: "info"          # trace, debug, info, warn, error
  enable_metrics: true
  enable_tracing: true

# Real-time control configuration
control:
  # Control loop timing
  frequency: 1000.0           # Control frequency in Hz (1kHz)
  period_us: 1000            # Control period in microseconds
  max_jitter_us: 10          # Maximum allowed jitter in microseconds
  deadline_miss_threshold: 5  # Maximum consecutive deadline misses
  
  # Real-time scheduling
  enable_realtime: true
  rt_priority: 80            # Real-time priority (1-99)
  cpu_affinity: "0x0F"       # CPU affinity mask (hex) - cores 0-3
  memory_lock: true          # Lock memory to prevent page faults
  
  # Control algorithm parameters
  controller_type: "pid"     # pid, mpc, adaptive, neural
  pid:
    kp: 1.0                 # Proportional gain
    ki: 0.1                 # Integral gain
    kd: 0.01                # Derivative gain
    output_min: -100.0      # Minimum output
    output_max: 100.0       # Maximum output
    integral_windup_limit: 50.0
  
  # State machine configuration
  state_machine:
    initial_state: "idle"
    enable_auto_transitions: true
    fault_recovery_enabled: true
    emergency_stop_timeout_ms: 100

# Safety system configuration
safety:
  # Safety monitoring
  frequency: 10000.0         # Safety check frequency in Hz (10kHz)
  enable_hardware_watchdog: true
  enable_software_watchdog: true
  
  # Watchdog configuration
  watchdog:
    timeout_ms: 100          # Watchdog timeout in milliseconds
    enable_external_watchdog: true
    external_watchdog_pin: 25  # GPIO pin for external watchdog
    kick_frequency_hz: 100   # Watchdog kick frequency
  
  # Fault detection
  fault_detection:
    enable_sensor_monitoring: true
    enable_actuator_monitoring: true
    enable_communication_monitoring: true
    sensor_timeout_ms: 50
    actuator_timeout_ms: 100
    communication_timeout_ms: 200
    max_sensor_deviation: 10.0
    max_actuator_error: 5.0
  
  # Emergency stop
  emergency_stop:
    enable_hardware_estop: true
    enable_software_estop: true
    estop_gpio_pin: 18       # GPIO pin for hardware E-stop
    estop_response_time_us: 100  # Maximum response time
    auto_recovery_enabled: false
    recovery_delay_ms: 5000
  
  # Monitoring and alerting
  monitoring:
    safety_check_frequency_hz: 10000.0
    fault_history_size: 1000
    enable_predictive_monitoring: true
    alert_thresholds:
      critical_fault_count: 10
      warning_fault_count: 50
      fault_rate_per_minute: 100.0

# Hardware configuration
hardware:
  # Platform configuration
  platform: "generic"       # generic, raspberry_pi, nvidia_jetson, custom
  architecture: "arm64"     # x86_64, arm64, armv7
  
  # GPIO configuration
  gpio:
    enable: true
    chip: "/dev/gpiochip0"
    pins:
      emergency_stop: 18
      status_led: 24
      watchdog_kick: 25
      fault_indicator: 23
  
  # Serial interfaces
  serial:
    enable: true
    ports:
      - device: "/dev/ttyUSB0"
        baudrate: 115200
        protocol: "modbus_rtu"
      - device: "/dev/ttyACM0"
        baudrate: 9600
        protocol: "custom"
  
  # CAN bus configuration
  can:
    enable: true
    interfaces:
      - name: "can0"
        bitrate: 500000
        protocol: "canopen"
      - name: "can1"
        bitrate: 1000000
        protocol: "custom"
  
  # I2C configuration
  i2c:
    enable: true
    buses:
      - bus: 1
        frequency: 400000
        devices:
          - address: 0x48
            type: "imu"
          - address: 0x68
            type: "rtc"
  
  # SPI configuration
  spi:
    enable: true
    buses:
      - bus: 0
        frequency: 1000000
        devices:
          - cs: 0
            type: "encoder"
          - cs: 1
            type: "adc"

# Communication configuration
communication:
  # gRPC configuration
  grpc:
    enable: true
    server:
      address: "0.0.0.0"
      port: 50090
      max_connections: 100
      keepalive_time_ms: 30000
      keepalive_timeout_ms: 5000
    
    # Client connections to other ASI modules
    clients:
      decision_engine:
        address: "localhost:50070"
        timeout_ms: 1000
        retry_attempts: 3
        enable_compression: true
      learning_engine:
        address: "localhost:50060"
        timeout_ms: 2000
        retry_attempts: 2
        enable_compression: true
      self_improvement:
        address: "localhost:50080"
        timeout_ms: 1500
        retry_attempts: 2
        enable_compression: true
  
  # Message queuing
  messaging:
    enable: true
    broker_type: "redis"     # redis, kafka, zeromq
    redis:
      url: "redis://localhost:6379"
      db: 0
      pool_size: 10
    
    # Topics for real-time communication
    topics:
      sensor_data: "asi.sensors"
      control_commands: "asi.control"
      safety_alerts: "asi.safety"
      system_status: "asi.status"
  
  # Shared memory for ultra-low latency
  shared_memory:
    enable: true
    segments:
      - name: "sensor_data"
        size: 4096
        permissions: 0o666
      - name: "control_output"
        size: 2048
        permissions: 0o666

# Inference acceleration configuration
inference:
  # C++ inference engine
  cpp_engine:
    enable: true
    library_path: "./cpp-inference-accelerator/build/libasi-inference-accelerator.so"
    execution_mode: "auto"   # cpu, cuda, opencl, tensorrt, auto
    precision_mode: "fp16"   # fp32, fp16, int8, mixed
    max_batch_size: 1
    max_workspace_size: 1073741824  # 1GB
    enable_optimization: true
    cache_directory: "./cache"
  
  # Real-time inference settings
  realtime:
    enable_realtime_mode: true
    max_latency_us: 100      # 100μs maximum inference latency
    target_latency_us: 50    # 50μs target latency
    enable_deadline_scheduling: true
    thread_priority: 85      # Higher than control loop
    cpu_affinity_mask: 0x10  # Dedicated CPU core
  
  # Model management
  models:
    model_directory: "./models"
    enable_hot_swapping: true
    preload_models: ["control_model", "safety_model"]
    default_models:
      control: "control_model_v1.onnx"
      safety: "safety_model_v1.onnx"
      prediction: "prediction_model_v1.onnx"

# Metrics and monitoring
metrics:
  # Prometheus metrics
  prometheus:
    enable: true
    port: 9091
    path: "/metrics"
    scrape_interval: 15
    
    # Custom metrics
    custom_metrics:
      - name: "control_loop_latency_us"
        type: "histogram"
        help: "Control loop execution latency in microseconds"
      - name: "safety_violations_total"
        type: "counter"
        help: "Total number of safety violations"
      - name: "inference_latency_us"
        type: "histogram"
        help: "Neural inference latency in microseconds"
  
  # Health checks
  health:
    enable: true
    port: 8091
    path: "/health"
    check_interval: 30
    timeout: 5
  
  # Performance profiling
  profiling:
    enable: false            # Enable only for debugging
    sample_rate: 0.01       # 1% sampling rate
    output_directory: "./profiles"

# Logging configuration
logging:
  # Log levels: trace, debug, info, warn, error
  level: "info"
  format: "json"            # json, text
  
  # Output destinations
  outputs:
    - type: "console"
      level: "info"
    - type: "file"
      level: "debug"
      path: "/var/log/asi-control/control.log"
      max_size: "100MB"
      max_files: 10
    - type: "syslog"
      level: "warn"
      facility: "daemon"
  
  # Structured logging
  structured:
    enable: true
    include_caller: true
    include_timestamp: true
    correlation_id: true

# Development and testing
development:
  # Mock hardware for testing
  enable_mock_hardware: false
  mock_sensor_data: true
  mock_actuator_responses: true
  
  # Simulation mode
  simulation:
    enable: false
    physics_engine: "bullet"
    real_time_factor: 1.0
    
  # Debug features
  debug:
    enable_debug_output: false
    save_debug_traces: false
    debug_output_directory: "./debug"
    
  # Testing configuration
  testing:
    enable_latency_tests: true
    enable_stress_tests: false
    test_duration_ms: 10000
    max_test_latency_us: 1000

# Security configuration
security:
  # Authentication and authorization
  auth:
    enable: false            # Disabled for embedded systems
    method: "none"          # none, jwt, mtls
  
  # Encryption
  encryption:
    enable_tls: false       # Enable for production networks
    cert_file: "/etc/ssl/certs/asi-control.crt"
    key_file: "/etc/ssl/private/asi-control.key"
  
  # Access control
  access_control:
    enable_rbac: false
    allowed_clients: []     # Empty = allow all
    blocked_clients: []
