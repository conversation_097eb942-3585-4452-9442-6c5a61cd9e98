[package]
name = "asi-control-kernel"
version = "1.0.0"
edition = "2021"
authors = ["ASI System Team <<EMAIL>>"]
description = "Real-time control kernel for ASI System robotics and embedded applications"
license = "MIT"
repository = "https://github.com/asi-system/core-runtime-control"
keywords = ["real-time", "control", "robotics", "embedded", "asi"]
categories = ["embedded", "science", "hardware-support"]

[dependencies]
# Async runtime with real-time features
tokio = { version = "1.35", features = ["full", "rt-multi-thread", "time"] }
tokio-util = "0.7"
futures = "0.3"

# Real-time and scheduling
rt-priority = "0.1"
libc = "0.2"
nix = "0.27"

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
serde_yaml = "0.9"
bincode = "1.3"

# gRPC and networking
tonic = "0.10"
prost = "0.12"
tower = "0.4"
hyper = "1.0"

# Logging and tracing
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["json", "env-filter", "time"] }
tracing-opentelemetry = "0.21"
opentelemetry = "0.21"
opentelemetry-jaeger = "0.20"

# Metrics and monitoring
prometheus = "0.13"
metrics = "0.22"
metrics-prometheus = "0.7"

# Configuration
config = "0.13"
clap = { version = "4.4", features = ["derive"] }

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Data structures for real-time
dashmap = "5.5"
crossbeam = "0.8"
parking_lot = "0.12"
lockfree = "0.5"

# Time and scheduling
chrono = { version = "0.4", features = ["serde"] }
instant = "0.1"

# Performance and memory
rayon = "1.8"
num_cpus = "1.16"
bytes = "1.5"
smallvec = "1.11"

# Hashing
ahash = "0.8"
fnv = "1.0"

# UUID generation
uuid = { version = "1.6", features = ["v4", "serde"] }

# Hardware interfaces
serialport = "4.2"
embedded-hal = "1.0"
nb = "1.1"

# Mathematical operations
nalgebra = "0.32"
ndarray = "0.15"

# Signal processing
rustfft = "6.1"

# Hardware abstraction
gpio = { version = "0.4", optional = true }
spidev = { version = "0.6", optional = true }
i2cdev = { version = "0.6", optional = true }

# CAN bus support
socketcan = { version = "3.1", optional = true }

# Real-time communication
shared_memory = "0.12"
mmap = "0.1"

# Safety and watchdog
watchdog = "0.1"

# Protocol implementations
modbus = { version = "1.1", optional = true }
opcua = { version = "0.12", optional = true }

# Database (optional for logging)
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "postgres", "chrono", "uuid"], optional = true }

# Redis (optional for caching)
redis = { version = "0.24", features = ["tokio-comp"], optional = true }

# Kafka (optional for telemetry)
rdkafka = { version = "0.36", features = ["cmake-build"], optional = true }

# FFI for C++ integration
cxx = "1.0"

[build-dependencies]
tonic-build = "0.10"
cxx-build = "1.0"

[features]
default = ["metrics", "tracing", "safety"]
hardware = ["gpio", "spidev", "i2cdev"]
protocols = ["socketcan", "modbus", "opcua"]
database = ["sqlx"]
cache = ["redis"]
telemetry = ["rdkafka"]
full = ["hardware", "protocols", "database", "cache", "telemetry"]
safety = ["watchdog"]

# Real-time optimizations
[profile.release]
lto = true
codegen-units = 1
panic = "abort"
strip = true
opt-level = 3

[profile.dev]
debug = true
opt-level = 1

# Real-time profile for maximum performance
[profile.realtime]
inherits = "release"
lto = "fat"
codegen-units = 1
panic = "abort"
strip = true
opt-level = 3
debug = false

[[bin]]
name = "control-kernel"
path = "src/main.rs"

[lib]
name = "asi_control_kernel"
path = "src/lib.rs"

# Benchmarks
[[bench]]
name = "control_loop_latency"
harness = false

[[bench]]
name = "inference_latency"
harness = false

[[bench]]
name = "communication_latency"
harness = false

# Examples
[[example]]
name = "basic_control_loop"
path = "examples/basic_control_loop.rs"

[[example]]
name = "robot_arm_control"
path = "examples/robot_arm_control.rs"

[[example]]
name = "drone_flight_control"
path = "examples/drone_flight_control.rs"

[[example]]
name = "safety_system_demo"
path = "examples/safety_system_demo.rs"
