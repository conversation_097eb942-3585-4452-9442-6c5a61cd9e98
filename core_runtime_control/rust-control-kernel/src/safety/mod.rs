/*!
 * Safety System Implementation
 * ============================
 * 
 * Multi-layer safety system providing watchdog monitoring, fault detection,
 * and emergency stop mechanisms for real-time control systems.
 */

use anyhow::Result;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::time::{interval, MissedTickBehavior};
use tracing::{info, error, warn, debug, instrument};
use parking_lot::RwLock;
use serde::{Deserialize, Serialize};

use crate::hardware::HardwareManager;
use crate::utils::{Config, MetricsCollector};

pub mod watchdog;
pub mod fault_detector;
pub mod emergency_stop;

use watchdog::{Watchdog, WatchdogConfig};
use fault_detector::{FaultDetector, FaultType, FaultSeverity};
use emergency_stop::{EmergencyStopController, EmergencyStopReason};

/// Safety system configuration
#[derive(Debug, Clone, Deserialize)]
pub struct SafetyConfig {
    pub watchdog: WatchdogConfig,
    pub fault_detection: FaultDetectionConfig,
    pub emergency_stop: EmergencyStopConfig,
    pub monitoring: MonitoringConfig,
}

#[derive(Debug, Clone, Deserialize)]
pub struct FaultDetectionConfig {
    pub enable_sensor_monitoring: bool,
    pub enable_actuator_monitoring: bool,
    pub enable_communication_monitoring: bool,
    pub sensor_timeout_ms: u64,
    pub actuator_timeout_ms: u64,
    pub communication_timeout_ms: u64,
    pub max_sensor_deviation: f64,
    pub max_actuator_error: f64,
}

#[derive(Debug, Clone, Deserialize)]
pub struct EmergencyStopConfig {
    pub enable_hardware_estop: bool,
    pub enable_software_estop: bool,
    pub estop_gpio_pin: Option<u32>,
    pub estop_response_time_us: u64,
    pub auto_recovery_enabled: bool,
    pub recovery_delay_ms: u64,
}

#[derive(Debug, Clone, Deserialize)]
pub struct MonitoringConfig {
    pub safety_check_frequency_hz: f64,
    pub fault_history_size: usize,
    pub enable_predictive_monitoring: bool,
    pub alert_thresholds: AlertThresholds,
}

#[derive(Debug, Clone, Deserialize)]
pub struct AlertThresholds {
    pub critical_fault_count: u32,
    pub warning_fault_count: u32,
    pub fault_rate_per_minute: f64,
}

/// Safety system state
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum SafetyState {
    Normal,
    Warning,
    Critical,
    Emergency,
    Fault,
}

/// Safety violation information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SafetyViolation {
    pub timestamp: Instant,
    pub violation_type: String,
    pub severity: FaultSeverity,
    pub description: String,
    pub source: String,
    pub recovery_action: Option<String>,
}

/// Safety system statistics
#[derive(Debug, Default, Clone, Serialize)]
pub struct SafetyStats {
    pub total_checks: u64,
    pub fault_count: u32,
    pub critical_faults: u32,
    pub emergency_stops: u32,
    pub false_alarms: u32,
    pub recovery_count: u32,
    pub uptime_seconds: u64,
    pub last_fault_time: Option<Instant>,
}

/// Main safety system
pub struct SafetySystem {
    config: SafetyConfig,
    hardware_manager: Arc<HardwareManager>,
    metrics: Arc<MetricsCollector>,
    
    // Safety components
    watchdog: Arc<RwLock<Watchdog>>,
    fault_detector: Arc<RwLock<FaultDetector>>,
    emergency_stop: Arc<RwLock<EmergencyStopController>>,
    
    // State management
    state: Arc<RwLock<SafetyState>>,
    violations: Arc<RwLock<Vec<SafetyViolation>>>,
    stats: Arc<RwLock<SafetyStats>>,
    
    // Control flags
    running: Arc<RwLock<bool>>,
    emergency_active: Arc<RwLock<bool>>,
    
    // Timing
    last_check: Arc<RwLock<Instant>>,
}

impl SafetySystem {
    /// Create a new safety system
    pub async fn new(
        config: &SafetyConfig,
        hardware_manager: Arc<HardwareManager>,
        metrics: Arc<MetricsCollector>,
    ) -> Result<Self> {
        info!("Initializing safety system");

        // Initialize watchdog
        let watchdog = Arc::new(RwLock::new(
            Watchdog::new(&config.watchdog)?
        ));

        // Initialize fault detector
        let fault_detector = Arc::new(RwLock::new(
            FaultDetector::new(&config.fault_detection, hardware_manager.clone())?
        ));

        // Initialize emergency stop controller
        let emergency_stop = Arc::new(RwLock::new(
            EmergencyStopController::new(&config.emergency_stop, hardware_manager.clone()).await?
        ));

        let system = Self {
            config: config.clone(),
            hardware_manager,
            metrics,
            watchdog,
            fault_detector,
            emergency_stop,
            state: Arc::new(RwLock::new(SafetyState::Normal)),
            violations: Arc::new(RwLock::new(Vec::new())),
            stats: Arc::new(RwLock::new(SafetyStats::default())),
            running: Arc::new(RwLock::new(false)),
            emergency_active: Arc::new(RwLock::new(false)),
            last_check: Arc::new(RwLock::new(Instant::now())),
        };

        info!("Safety system initialized");
        Ok(system)
    }

    /// Start the safety system
    #[instrument(skip(self))]
    pub async fn start(&self) -> Result<()> {
        info!("Starting safety system");
        
        *self.running.write() = true;
        *self.last_check.write() = Instant::now();

        // Start safety monitoring loop
        let monitoring_handle = self.start_monitoring_loop();
        
        // Start watchdog
        let watchdog_handle = tokio::spawn({
            let watchdog = self.watchdog.clone();
            async move {
                if let Err(e) = watchdog.write().start().await {
                    error!("Watchdog error: {}", e);
                }
            }
        });

        // Start emergency stop monitoring
        let estop_handle = tokio::spawn({
            let emergency_stop = self.emergency_stop.clone();
            async move {
                if let Err(e) = emergency_stop.write().start_monitoring().await {
                    error!("Emergency stop monitoring error: {}", e);
                }
            }
        });

        // Wait for any component to fail
        tokio::select! {
            result = monitoring_handle => {
                error!("Safety monitoring loop exited: {:?}", result);
                result?
            }
            result = watchdog_handle => {
                error!("Watchdog exited: {:?}", result);
                result?
            }
            result = estop_handle => {
                error!("Emergency stop monitoring exited: {:?}", result);
                result?
            }
        }

        Ok(())
    }

    /// Stop the safety system
    pub async fn stop(&self) -> Result<()> {
        info!("Stopping safety system");
        
        *self.running.write() = false;
        
        // Stop all components
        self.watchdog.write().stop().await?;
        self.emergency_stop.write().stop().await?;
        
        info!("Safety system stopped");
        Ok(())
    }

    /// Check safety conditions
    #[instrument(skip(self))]
    pub async fn check_safety(&self) -> Result<()> {
        let check_start = Instant::now();
        
        // Update statistics
        self.stats.write().total_checks += 1;
        *self.last_check.write() = check_start;

        // Check watchdog status
        if let Err(e) = self.watchdog.read().check_status() {
            self.handle_safety_violation(SafetyViolation {
                timestamp: check_start,
                violation_type: "watchdog_timeout".to_string(),
                severity: FaultSeverity::Critical,
                description: format!("Watchdog timeout: {}", e),
                source: "watchdog".to_string(),
                recovery_action: Some("system_restart".to_string()),
            }).await?;
        }

        // Run fault detection
        let faults = self.fault_detector.write().detect_faults().await?;
        for fault in faults {
            self.handle_fault(fault).await?;
        }

        // Check emergency stop status
        if self.emergency_stop.read().is_triggered().await? {
            if !*self.emergency_active.read() {
                self.handle_emergency_stop_triggered().await?;
            }
        }

        // Update state based on current conditions
        self.update_safety_state().await?;

        // Record timing metrics
        let check_duration = check_start.elapsed();
        self.metrics.record_histogram("safety_check_duration_us", check_duration.as_micros() as f64);

        Ok(())
    }

    /// Start safety monitoring loop
    async fn start_monitoring_loop(&self) -> Result<()> {
        let frequency = self.config.monitoring.safety_check_frequency_hz;
        let period = Duration::from_secs_f64(1.0 / frequency);
        let mut interval = interval(period);
        interval.set_missed_tick_behavior(MissedTickBehavior::Skip);

        info!("Starting safety monitoring loop at {}Hz", frequency);

        while *self.running.read() {
            if let Err(e) = self.check_safety().await {
                error!("Safety check failed: {}", e);
                
                // Critical safety check failure - trigger emergency stop
                self.emergency_stop("Safety check failure").await?;
            }

            interval.tick().await;
        }

        info!("Safety monitoring loop stopped");
        Ok(())
    }

    /// Handle a detected fault
    async fn handle_fault(&self, fault: FaultType) -> Result<()> {
        let violation = SafetyViolation {
            timestamp: Instant::now(),
            violation_type: format!("{:?}", fault),
            severity: fault.severity(),
            description: fault.description(),
            source: fault.source(),
            recovery_action: fault.recovery_action(),
        };

        self.handle_safety_violation(violation).await
    }

    /// Handle a safety violation
    async fn handle_safety_violation(&self, violation: SafetyViolation) -> Result<()> {
        warn!("Safety violation detected: {} - {}", violation.violation_type, violation.description);

        // Add to violation history
        {
            let mut violations = self.violations.write();
            violations.push(violation.clone());
            
            // Limit history size
            let max_size = self.config.monitoring.fault_history_size;
            if violations.len() > max_size {
                violations.drain(0..violations.len() - max_size);
            }
        }

        // Update statistics
        {
            let mut stats = self.stats.write();
            stats.fault_count += 1;
            stats.last_fault_time = Some(violation.timestamp);
            
            if violation.severity == FaultSeverity::Critical {
                stats.critical_faults += 1;
            }
        }

        // Take action based on severity
        match violation.severity {
            FaultSeverity::Info => {
                // Log only
                info!("Safety info: {}", violation.description);
            }
            FaultSeverity::Warning => {
                // Log and monitor
                warn!("Safety warning: {}", violation.description);
                self.metrics.increment_counter("safety_warnings_total");
            }
            FaultSeverity::Critical => {
                // Trigger emergency stop
                error!("Critical safety violation: {}", violation.description);
                self.emergency_stop(&violation.description).await?;
            }
        }

        // Update metrics
        self.metrics.increment_counter("safety_violations_total");
        
        Ok(())
    }

    /// Handle emergency stop triggered
    async fn handle_emergency_stop_triggered(&self) -> Result<()> {
        error!("Emergency stop triggered");
        
        *self.emergency_active.write() = true;
        self.stats.write().emergency_stops += 1;
        
        // Set state to emergency
        *self.state.write() = SafetyState::Emergency;
        
        // Notify hardware manager
        self.hardware_manager.emergency_stop().await?;
        
        // Update metrics
        self.metrics.increment_counter("emergency_stops_total");
        
        Ok(())
    }

    /// Trigger emergency stop
    pub async fn emergency_stop(&self, reason: &str) -> Result<()> {
        error!("Triggering emergency stop: {}", reason);
        
        // Activate emergency stop
        self.emergency_stop.write().trigger(EmergencyStopReason::Software(reason.to_string())).await?;
        
        // Handle the emergency stop
        self.handle_emergency_stop_triggered().await?;
        
        Ok(())
    }

    /// Update safety state based on current conditions
    async fn update_safety_state(&self) -> Result<()> {
        let current_state = *self.state.read();
        let new_state = self.calculate_safety_state().await;
        
        if new_state != current_state {
            info!("Safety state changed: {:?} -> {:?}", current_state, new_state);
            *self.state.write() = new_state;
            
            // Update metrics
            self.metrics.set_gauge("safety_state", new_state as u8 as f64);
        }
        
        Ok(())
    }

    /// Calculate current safety state
    async fn calculate_safety_state(&self) -> SafetyState {
        // Check for emergency conditions
        if *self.emergency_active.read() {
            return SafetyState::Emergency;
        }

        // Check for critical faults
        let stats = self.stats.read();
        let thresholds = &self.config.monitoring.alert_thresholds;
        
        if stats.critical_faults > thresholds.critical_fault_count {
            return SafetyState::Critical;
        }

        // Check for warnings
        if stats.fault_count > thresholds.warning_fault_count {
            return SafetyState::Warning;
        }

        // Check fault rate
        if let Some(last_fault) = stats.last_fault_time {
            let time_since_fault = last_fault.elapsed().as_secs_f64() / 60.0; // minutes
            let fault_rate = stats.fault_count as f64 / time_since_fault.max(1.0);
            
            if fault_rate > thresholds.fault_rate_per_minute {
                return SafetyState::Warning;
            }
        }

        SafetyState::Normal
    }

    /// Check if system has faults
    pub async fn has_faults(&self) -> bool {
        self.stats.read().fault_count > 0
    }

    /// Check if emergency stop can be cleared
    pub async fn can_clear_emergency(&self) -> Result<bool> {
        if !*self.emergency_active.read() {
            return Ok(true);
        }

        // Check if emergency stop is still triggered
        if self.emergency_stop.read().is_triggered().await? {
            return Ok(false);
        }

        // Check if all faults are cleared
        if self.fault_detector.read().has_active_faults().await {
            return Ok(false);
        }

        // Check if recovery delay has passed
        if self.config.emergency_stop.auto_recovery_enabled {
            let recovery_delay = Duration::from_millis(self.config.emergency_stop.recovery_delay_ms);
            if let Some(last_fault) = self.stats.read().last_fault_time {
                if last_fault.elapsed() < recovery_delay {
                    return Ok(false);
                }
            }
        }

        // Clear emergency stop
        *self.emergency_active.write() = false;
        *self.state.write() = SafetyState::Normal;
        self.stats.write().recovery_count += 1;
        
        info!("Emergency stop cleared - system recovered");
        Ok(true)
    }

    /// Get current safety state
    pub fn get_state(&self) -> SafetyState {
        *self.state.read()
    }

    /// Get safety statistics
    pub fn get_stats(&self) -> SafetyStats {
        self.stats.read().clone()
    }

    /// Get recent violations
    pub fn get_recent_violations(&self, count: usize) -> Vec<SafetyViolation> {
        let violations = self.violations.read();
        let start = violations.len().saturating_sub(count);
        violations[start..].to_vec()
    }
}
