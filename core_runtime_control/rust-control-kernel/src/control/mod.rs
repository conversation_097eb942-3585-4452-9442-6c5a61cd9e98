/*!
 * Real-Time Control Kernel
 * ========================
 * 
 * Core control loop implementation providing deterministic real-time control
 * with error safety, watchdog monitoring, and hardware integration.
 */

use anyhow::Result;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::time::{interval, MissedTickBehavior};
use tracing::{info, error, warn, debug, instrument};
use parking_lot::RwLock;

use crate::safety::SafetySystem;
use crate::scheduler::RealTimeScheduler;
use crate::hardware::HardwareManager;
use crate::communication::CommunicationManager;
use crate::utils::{Config, MetricsCollector};

pub mod controller;
pub mod pid;
pub mod state_machine;
pub mod trajectory;

use controller::Controller;
use state_machine::{ControlState, ControlStateMachine};

/// Control loop timing configuration
#[derive(Debug, Clone)]
pub struct ControlTiming {
    /// Main control loop frequency (Hz)
    pub control_frequency: f64,
    /// Safety monitoring frequency (Hz)
    pub safety_frequency: f64,
    /// Communication frequency (Hz)
    pub communication_frequency: f64,
    /// Maximum allowed jitter (microseconds)
    pub max_jitter_us: u64,
    /// Deadline miss threshold
    pub deadline_miss_threshold: u32,
}

impl Default for ControlTiming {
    fn default() -> Self {
        Self {
            control_frequency: 1000.0,  // 1kHz
            safety_frequency: 10000.0,  // 10kHz
            communication_frequency: 100.0,  // 100Hz
            max_jitter_us: 10,
            deadline_miss_threshold: 5,
        }
    }
}

/// Control kernel statistics
#[derive(Debug, Default, Clone)]
pub struct ControlStats {
    pub total_cycles: u64,
    pub deadline_misses: u32,
    pub max_latency_us: u64,
    pub avg_latency_us: f64,
    pub max_jitter_us: u64,
    pub safety_violations: u32,
    pub emergency_stops: u32,
    pub uptime_seconds: u64,
}

/// Main control kernel
pub struct ControlKernel {
    config: Config,
    hardware_manager: Arc<HardwareManager>,
    safety_system: Option<Arc<SafetySystem>>,
    scheduler: Arc<RealTimeScheduler>,
    communication_manager: Arc<CommunicationManager>,
    metrics: Arc<MetricsCollector>,
    
    // Control components
    controller: Arc<RwLock<Controller>>,
    state_machine: Arc<RwLock<ControlStateMachine>>,
    
    // Timing and statistics
    timing: ControlTiming,
    stats: Arc<RwLock<ControlStats>>,
    
    // Control flags
    running: Arc<RwLock<bool>>,
    emergency_stop: Arc<RwLock<bool>>,
    
    // Watchdog
    last_heartbeat: Arc<RwLock<Instant>>,
}

impl ControlKernel {
    /// Create a new control kernel
    pub async fn new(
        config: &Config,
        hardware_manager: Arc<HardwareManager>,
        safety_system: Option<Arc<SafetySystem>>,
        scheduler: Arc<RealTimeScheduler>,
        communication_manager: Arc<CommunicationManager>,
        metrics: Arc<MetricsCollector>,
    ) -> Result<Self> {
        info!("Initializing control kernel");

        // Load timing configuration
        let timing = ControlTiming {
            control_frequency: config.get("control.frequency").unwrap_or(1000.0),
            safety_frequency: config.get("safety.frequency").unwrap_or(10000.0),
            communication_frequency: config.get("communication.frequency").unwrap_or(100.0),
            max_jitter_us: config.get("control.max_jitter_us").unwrap_or(10),
            deadline_miss_threshold: config.get("control.deadline_miss_threshold").unwrap_or(5),
        };

        // Initialize controller
        let controller = Arc::new(RwLock::new(
            Controller::new(config, hardware_manager.clone()).await?
        ));

        // Initialize state machine
        let state_machine = Arc::new(RwLock::new(
            ControlStateMachine::new(config)?
        ));

        let kernel = Self {
            config: config.clone(),
            hardware_manager,
            safety_system,
            scheduler,
            communication_manager,
            metrics,
            controller,
            state_machine,
            timing,
            stats: Arc::new(RwLock::new(ControlStats::default())),
            running: Arc::new(RwLock::new(false)),
            emergency_stop: Arc::new(RwLock::new(false)),
            last_heartbeat: Arc::new(RwLock::new(Instant::now())),
        };

        info!("Control kernel initialized with {}Hz control frequency", timing.control_frequency);
        Ok(kernel)
    }

    /// Start the control kernel
    #[instrument(skip(self))]
    pub async fn start(&self) -> Result<()> {
        info!("Starting control kernel");
        
        *self.running.write() = true;
        *self.last_heartbeat.write() = Instant::now();

        // Start control loops concurrently
        let control_handle = self.start_control_loop();
        let safety_handle = self.start_safety_loop();
        let communication_handle = self.start_communication_loop();
        let watchdog_handle = self.start_watchdog_loop();

        // Wait for any loop to complete (which indicates an error)
        tokio::select! {
            result = control_handle => {
                error!("Control loop exited: {:?}", result);
                result?
            }
            result = safety_handle => {
                error!("Safety loop exited: {:?}", result);
                result?
            }
            result = communication_handle => {
                error!("Communication loop exited: {:?}", result);
                result?
            }
            result = watchdog_handle => {
                error!("Watchdog loop exited: {:?}", result);
                result?
            }
        }

        Ok(())
    }

    /// Stop the control kernel
    pub async fn stop(&self) -> Result<()> {
        info!("Stopping control kernel");
        
        *self.running.write() = false;
        
        // Trigger emergency stop to ensure safe shutdown
        self.trigger_emergency_stop("System shutdown").await?;
        
        // Wait for all loops to stop
        tokio::time::sleep(Duration::from_millis(100)).await;
        
        info!("Control kernel stopped");
        Ok(())
    }

    /// Main control loop - highest priority, deterministic timing
    async fn start_control_loop(&self) -> Result<()> {
        let period = Duration::from_secs_f64(1.0 / self.timing.control_frequency);
        let mut interval = interval(period);
        interval.set_missed_tick_behavior(MissedTickBehavior::Skip);

        info!("Starting control loop at {}Hz", self.timing.control_frequency);

        while *self.running.read() {
            let cycle_start = Instant::now();
            
            // Check for emergency stop
            if *self.emergency_stop.read() {
                self.handle_emergency_stop().await?;
                continue;
            }

            // Execute control cycle
            if let Err(e) = self.execute_control_cycle().await {
                error!("Control cycle error: {}", e);
                self.trigger_emergency_stop(&format!("Control cycle error: {}", e)).await?;
                continue;
            }

            // Update heartbeat
            *self.last_heartbeat.write() = Instant::now();

            // Update statistics
            self.update_control_stats(cycle_start).await;

            // Wait for next cycle
            interval.tick().await;
        }

        info!("Control loop stopped");
        Ok(())
    }

    /// Execute a single control cycle
    #[instrument(skip(self))]
    async fn execute_control_cycle(&self) -> Result<()> {
        // Read sensor data
        let sensor_data = self.hardware_manager.read_sensors().await?;

        // Update state machine
        let current_state = {
            let mut state_machine = self.state_machine.write();
            state_machine.update(&sensor_data)?;
            state_machine.current_state()
        };

        // Execute control based on current state
        match current_state {
            ControlState::Idle => {
                // Idle state - minimal processing
                self.controller.write().idle_control().await?;
            }
            ControlState::Active => {
                // Active control
                let control_output = self.controller.write()
                    .compute_control(&sensor_data).await?;
                
                // Apply control output to hardware
                self.hardware_manager.write_actuators(&control_output).await?;
            }
            ControlState::Emergency => {
                // Emergency state - safe shutdown
                self.controller.write().emergency_control().await?;
            }
            ControlState::Fault => {
                // Fault state - diagnostic mode
                self.controller.write().fault_control().await?;
            }
        }

        // Update metrics
        self.metrics.increment_counter("control_cycles_total");

        Ok(())
    }

    /// Safety monitoring loop - highest frequency for critical safety checks
    async fn start_safety_loop(&self) -> Result<()> {
        if self.safety_system.is_none() {
            warn!("Safety system disabled - skipping safety loop");
            return Ok(());
        }

        let period = Duration::from_secs_f64(1.0 / self.timing.safety_frequency);
        let mut interval = interval(period);
        interval.set_missed_tick_behavior(MissedTickBehavior::Skip);

        info!("Starting safety loop at {}Hz", self.timing.safety_frequency);

        while *self.running.read() {
            if let Some(safety_system) = &self.safety_system {
                // Check safety conditions
                if let Err(e) = safety_system.check_safety().await {
                    error!("Safety violation detected: {}", e);
                    self.trigger_emergency_stop(&format!("Safety violation: {}", e)).await?;
                }

                // Check for safety system faults
                if safety_system.has_faults().await {
                    warn!("Safety system faults detected");
                    self.stats.write().safety_violations += 1;
                }
            }

            interval.tick().await;
        }

        info!("Safety loop stopped");
        Ok(())
    }

    /// Communication loop - handles gRPC communication with Decision Engine
    async fn start_communication_loop(&self) -> Result<()> {
        let period = Duration::from_secs_f64(1.0 / self.timing.communication_frequency);
        let mut interval = interval(period);
        interval.set_missed_tick_behavior(MissedTickBehavior::Skip);

        info!("Starting communication loop at {}Hz", self.timing.communication_frequency);

        while *self.running.read() {
            // Process incoming commands from Decision Engine
            if let Err(e) = self.communication_manager.process_commands().await {
                warn!("Communication error: {}", e);
            }

            // Send status updates
            let status = self.get_system_status().await;
            if let Err(e) = self.communication_manager.send_status(&status).await {
                warn!("Failed to send status: {}", e);
            }

            interval.tick().await;
        }

        info!("Communication loop stopped");
        Ok(())
    }

    /// Watchdog loop - monitors system health and deadlines
    async fn start_watchdog_loop(&self) -> Result<()> {
        let mut interval = interval(Duration::from_millis(10)); // 100Hz watchdog
        
        info!("Starting watchdog loop");

        while *self.running.read() {
            let now = Instant::now();
            let last_heartbeat = *self.last_heartbeat.read();
            
            // Check for control loop timeout
            let heartbeat_age = now.duration_since(last_heartbeat);
            let max_heartbeat_age = Duration::from_secs_f64(2.0 / self.timing.control_frequency);
            
            if heartbeat_age > max_heartbeat_age {
                error!("Control loop timeout detected: {:?}", heartbeat_age);
                self.trigger_emergency_stop("Control loop timeout").await?;
            }

            // Check deadline misses
            let stats = self.stats.read();
            if stats.deadline_misses > self.timing.deadline_miss_threshold {
                warn!("Excessive deadline misses: {}", stats.deadline_misses);
            }

            interval.tick().await;
        }

        info!("Watchdog loop stopped");
        Ok(())
    }

    /// Trigger emergency stop
    async fn trigger_emergency_stop(&self, reason: &str) -> Result<()> {
        error!("EMERGENCY STOP TRIGGERED: {}", reason);
        
        *self.emergency_stop.write() = true;
        self.stats.write().emergency_stops += 1;
        
        // Immediately stop all actuators
        self.hardware_manager.emergency_stop().await?;
        
        // Notify safety system
        if let Some(safety_system) = &self.safety_system {
            safety_system.emergency_stop(reason).await?;
        }

        // Update metrics
        self.metrics.increment_counter("emergency_stops_total");
        
        Ok(())
    }

    /// Handle emergency stop state
    async fn handle_emergency_stop(&self) -> Result<()> {
        // Keep actuators in safe state
        self.hardware_manager.maintain_safe_state().await?;
        
        // Check if emergency stop can be cleared
        if let Some(safety_system) = &self.safety_system {
            if safety_system.can_clear_emergency().await? {
                info!("Emergency stop cleared");
                *self.emergency_stop.write() = false;
            }
        }
        
        Ok(())
    }

    /// Update control loop statistics
    async fn update_control_stats(&self, cycle_start: Instant) {
        let cycle_time = cycle_start.elapsed();
        let cycle_time_us = cycle_time.as_micros() as u64;
        
        let mut stats = self.stats.write();
        stats.total_cycles += 1;
        
        // Update latency statistics
        if cycle_time_us > stats.max_latency_us {
            stats.max_latency_us = cycle_time_us;
        }
        
        // Update average latency (exponential moving average)
        stats.avg_latency_us = 0.95 * stats.avg_latency_us + 0.05 * cycle_time_us as f64;
        
        // Check for deadline miss
        let deadline_us = (1_000_000.0 / self.timing.control_frequency) as u64;
        if cycle_time_us > deadline_us {
            stats.deadline_misses += 1;
            warn!("Control loop deadline miss: {}μs > {}μs", cycle_time_us, deadline_us);
        }
        
        // Update jitter
        let expected_period_us = (1_000_000.0 / self.timing.control_frequency) as u64;
        let jitter_us = if cycle_time_us > expected_period_us {
            cycle_time_us - expected_period_us
        } else {
            expected_period_us - cycle_time_us
        };
        
        if jitter_us > stats.max_jitter_us {
            stats.max_jitter_us = jitter_us;
        }
        
        // Update metrics
        self.metrics.record_histogram("control_loop_latency_us", cycle_time_us as f64);
        self.metrics.record_histogram("control_loop_jitter_us", jitter_us as f64);
    }

    /// Get current system status
    async fn get_system_status(&self) -> serde_json::Value {
        let stats = self.stats.read().clone();
        let state = self.state_machine.read().current_state();
        
        serde_json::json!({
            "state": format!("{:?}", state),
            "emergency_stop": *self.emergency_stop.read(),
            "stats": {
                "total_cycles": stats.total_cycles,
                "deadline_misses": stats.deadline_misses,
                "max_latency_us": stats.max_latency_us,
                "avg_latency_us": stats.avg_latency_us,
                "max_jitter_us": stats.max_jitter_us,
                "safety_violations": stats.safety_violations,
                "emergency_stops": stats.emergency_stops,
            },
            "timing": {
                "control_frequency": self.timing.control_frequency,
                "safety_frequency": self.timing.safety_frequency,
            }
        })
    }

    /// Get control statistics
    pub fn get_stats(&self) -> ControlStats {
        self.stats.read().clone()
    }

    /// Check if system is in emergency stop
    pub fn is_emergency_stop(&self) -> bool {
        *self.emergency_stop.read()
    }
}
