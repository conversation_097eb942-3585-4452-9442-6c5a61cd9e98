/*!
 * Real-Time Scheduler for ASI Control Kernel
 * =========================================
 * 
 * Provides deterministic real-time scheduling with deadline guarantees,
 * priority inheritance, and resource management for control loops.
 */

use anyhow::Result;
use std::collections::{BinaryHeap, HashMap, VecDeque};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::{RwLock, Semaphore};
use tokio::time::{interval, MissedTickBehavior};
use tracing::{debug, error, info, instrument, warn};
use serde::{Deserialize, Serialize};
use parking_lot::Mutex;

/// Task priority levels
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize)]
pub enum Priority {
    Critical = 0,    // Emergency stop, safety systems
    High = 1,        // Control loops, sensor processing
    Normal = 2,      // Communication, logging
    Low = 3,         // Background tasks, diagnostics
    Idle = 4,        // Cleanup, maintenance
}

/// Scheduling policy
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum SchedulingPolicy {
    /// Rate Monotonic Scheduling
    RateMonotonic,
    /// Earliest Deadline First
    EarliestDeadlineFirst,
    /// Fixed Priority Preemptive
    FixedPriorityPreemptive,
    /// Round Robin with priorities
    PriorityRoundRobin,
}

/// Task state
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum TaskState {
    Ready,
    Running,
    Blocked,
    Suspended,
    Terminated,
}

/// Real-time task definition
#[derive(Debug, Clone)]
pub struct RealTimeTask {
    pub id: u64,
    pub name: String,
    pub priority: Priority,
    pub period: Duration,
    pub deadline: Duration,
    pub worst_case_execution_time: Duration,
    pub state: TaskState,
    pub next_release: Instant,
    pub absolute_deadline: Instant,
    pub execution_count: u64,
    pub missed_deadlines: u64,
    pub total_execution_time: Duration,
    pub last_execution_time: Duration,
    pub cpu_affinity: Option<usize>,
}

impl RealTimeTask {
    pub fn new(
        id: u64,
        name: String,
        priority: Priority,
        period: Duration,
        deadline: Duration,
        wcet: Duration,
    ) -> Self {
        let now = Instant::now();
        Self {
            id,
            name,
            priority,
            period,
            deadline,
            worst_case_execution_time: wcet,
            state: TaskState::Ready,
            next_release: now,
            absolute_deadline: now + deadline,
            execution_count: 0,
            missed_deadlines: 0,
            total_execution_time: Duration::ZERO,
            last_execution_time: Duration::ZERO,
            cpu_affinity: None,
        }
    }

    pub fn update_release_time(&mut self) {
        self.next_release += self.period;
        self.absolute_deadline = self.next_release + self.deadline;
    }

    pub fn record_execution(&mut self, execution_time: Duration) {
        self.execution_count += 1;
        self.last_execution_time = execution_time;
        self.total_execution_time += execution_time;
    }

    pub fn record_missed_deadline(&mut self) {
        self.missed_deadlines += 1;
        warn!("Task {} missed deadline", self.name);
    }

    pub fn utilization(&self) -> f64 {
        if self.period.is_zero() {
            0.0
        } else {
            self.worst_case_execution_time.as_secs_f64() / self.period.as_secs_f64()
        }
    }
}

impl PartialEq for RealTimeTask {
    fn eq(&self, other: &Self) -> bool {
        self.id == other.id
    }
}

impl Eq for RealTimeTask {}

impl PartialOrd for RealTimeTask {
    fn partial_cmp(&self, other: &Self) -> Option<std::cmp::Ordering> {
        Some(self.cmp(other))
    }
}

impl Ord for RealTimeTask {
    fn cmp(&self, other: &Self) -> std::cmp::Ordering {
        // For EDF: earlier deadline has higher priority
        // For priority-based: higher priority value has higher priority
        match (self.priority, other.priority) {
            (p1, p2) if p1 != p2 => p1.cmp(&p2),
            _ => self.absolute_deadline.cmp(&other.absolute_deadline),
        }
    }
}

/// Scheduler configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SchedulerConfig {
    pub policy: SchedulingPolicy,
    pub time_slice: Duration,
    pub enable_priority_inheritance: bool,
    pub enable_deadline_monitoring: bool,
    pub max_tasks: usize,
    pub cpu_count: usize,
    pub enable_load_balancing: bool,
    pub context_switch_overhead: Duration,
}

impl Default for SchedulerConfig {
    fn default() -> Self {
        Self {
            policy: SchedulingPolicy::EarliestDeadlineFirst,
            time_slice: Duration::from_millis(1),
            enable_priority_inheritance: true,
            enable_deadline_monitoring: true,
            max_tasks: 1000,
            cpu_count: num_cpus::get(),
            enable_load_balancing: true,
            context_switch_overhead: Duration::from_micros(10),
        }
    }
}

/// Scheduling statistics
#[derive(Debug, Default, Clone)]
pub struct SchedulingStats {
    pub total_context_switches: u64,
    pub total_preemptions: u64,
    pub total_deadline_misses: u64,
    pub average_response_time: Duration,
    pub cpu_utilization: f64,
    pub scheduler_overhead: Duration,
    pub last_update: Option<Instant>,
}

/// Real-time scheduler
pub struct RealTimeScheduler {
    config: SchedulerConfig,
    tasks: Arc<RwLock<HashMap<u64, RealTimeTask>>>,
    ready_queue: Arc<Mutex<BinaryHeap<RealTimeTask>>>,
    running_tasks: Arc<RwLock<HashMap<usize, Option<u64>>>>, // CPU -> Task ID
    blocked_tasks: Arc<RwLock<VecDeque<u64>>>,
    task_counter: Arc<Mutex<u64>>,
    stats: Arc<RwLock<SchedulingStats>>,
    cpu_semaphores: Vec<Arc<Semaphore>>,
    scheduler_active: Arc<RwLock<bool>>,
}

impl RealTimeScheduler {
    /// Create a new real-time scheduler
    pub fn new(config: SchedulerConfig) -> Self {
        let cpu_count = config.cpu_count;
        let cpu_semaphores = (0..cpu_count)
            .map(|_| Arc::new(Semaphore::new(1)))
            .collect();

        let mut running_tasks = HashMap::new();
        for cpu in 0..cpu_count {
            running_tasks.insert(cpu, None);
        }

        Self {
            config,
            tasks: Arc::new(RwLock::new(HashMap::new())),
            ready_queue: Arc::new(Mutex::new(BinaryHeap::new())),
            running_tasks: Arc::new(RwLock::new(running_tasks)),
            blocked_tasks: Arc::new(RwLock::new(VecDeque::new())),
            task_counter: Arc::new(Mutex::new(0)),
            stats: Arc::new(RwLock::new(SchedulingStats::default())),
            cpu_semaphores,
            scheduler_active: Arc::new(RwLock::new(false)),
        }
    }

    /// Start the scheduler
    #[instrument(skip(self))]
    pub async fn start(&self) -> Result<()> {
        info!("Starting real-time scheduler");
        *self.scheduler_active.write().await = true;

        // Start scheduler loop
        let scheduler_handle = self.run_scheduler_loop();
        
        // Start deadline monitor if enabled
        let deadline_handle = if self.config.enable_deadline_monitoring {
            Some(self.run_deadline_monitor())
        } else {
            None
        };

        // Start statistics collector
        let stats_handle = self.run_statistics_collector();

        // Wait for all tasks
        tokio::select! {
            result = scheduler_handle => {
                error!("Scheduler loop terminated: {:?}", result);
            }
            result = async {
                if let Some(handle) = deadline_handle {
                    handle.await
                } else {
                    std::future::pending().await
                }
            } => {
                error!("Deadline monitor terminated: {:?}", result);
            }
            result = stats_handle => {
                error!("Statistics collector terminated: {:?}", result);
            }
        }

        Ok(())
    }

    /// Stop the scheduler
    pub async fn stop(&self) {
        info!("Stopping real-time scheduler");
        *self.scheduler_active.write().await = false;
    }

    /// Add a new task to the scheduler
    #[instrument(skip(self))]
    pub async fn add_task(&self, mut task: RealTimeTask) -> Result<u64> {
        let task_id = {
            let mut counter = self.task_counter.lock();
            *counter += 1;
            *counter
        };

        task.id = task_id;
        task.state = TaskState::Ready;

        // Check schedulability
        if !self.check_schedulability(&task).await? {
            return Err(anyhow::anyhow!("Task would make system unschedulable"));
        }

        // Add to tasks map
        self.tasks.write().await.insert(task_id, task.clone());

        // Add to ready queue
        self.ready_queue.lock().push(task);

        info!("Added task {} (ID: {}) to scheduler", task.name, task_id);
        Ok(task_id)
    }

    /// Remove a task from the scheduler
    pub async fn remove_task(&self, task_id: u64) -> Result<()> {
        // Remove from tasks map
        if let Some(mut task) = self.tasks.write().await.remove(&task_id) {
            task.state = TaskState::Terminated;
            info!("Removed task {} (ID: {}) from scheduler", task.name, task_id);
        }

        // Remove from ready queue (will be cleaned up in next scheduling cycle)
        Ok(())
    }

    /// Main scheduler loop
    async fn run_scheduler_loop(&self) -> Result<()> {
        let mut interval = interval(self.config.time_slice);
        interval.set_missed_tick_behavior(MissedTickBehavior::Skip);

        while *self.scheduler_active.read().await {
            let schedule_start = Instant::now();

            // Perform scheduling decision
            if let Err(e) = self.schedule().await {
                error!("Scheduling error: {}", e);
            }

            // Update scheduler overhead statistics
            let schedule_time = schedule_start.elapsed();
            self.stats.write().await.scheduler_overhead = schedule_time;

            interval.tick().await;
        }

        Ok(())
    }

    /// Perform scheduling decision
    #[instrument(skip(self))]
    async fn schedule(&self) -> Result<()> {
        match self.config.policy {
            SchedulingPolicy::EarliestDeadlineFirst => self.schedule_edf().await,
            SchedulingPolicy::RateMonotonic => self.schedule_rate_monotonic().await,
            SchedulingPolicy::FixedPriorityPreemptive => self.schedule_fixed_priority().await,
            SchedulingPolicy::PriorityRoundRobin => self.schedule_priority_round_robin().await,
        }
    }

    /// Earliest Deadline First scheduling
    async fn schedule_edf(&self) -> Result<()> {
        let now = Instant::now();
        let mut ready_queue = self.ready_queue.lock();
        let mut running_tasks = self.running_tasks.write().await;
        let mut tasks = self.tasks.write().await;

        // Release periodic tasks
        for task in tasks.values_mut() {
            if task.state == TaskState::Ready && now >= task.next_release {
                ready_queue.push(task.clone());
            }
        }

        // Schedule tasks on available CPUs
        for cpu in 0..self.config.cpu_count {
            if running_tasks[&cpu].is_none() {
                if let Some(mut task) = ready_queue.pop() {
                    // Check if deadline can be met
                    if now + task.worst_case_execution_time <= task.absolute_deadline {
                        task.state = TaskState::Running;
                        running_tasks.insert(cpu, Some(task.id));
                        tasks.insert(task.id, task);
                        
                        debug!("Scheduled task {} on CPU {}", task.name, cpu);
                    } else {
                        // Deadline cannot be met
                        task.record_missed_deadline();
                        task.update_release_time();
                        tasks.insert(task.id, task);
                    }
                }
            }
        }

        Ok(())
    }

    /// Rate Monotonic scheduling
    async fn schedule_rate_monotonic(&self) -> Result<()> {
        // Sort tasks by period (shorter period = higher priority)
        let mut ready_queue = self.ready_queue.lock();
        let mut tasks_vec: Vec<_> = ready_queue.drain().collect();
        
        tasks_vec.sort_by(|a, b| a.period.cmp(&b.period));
        
        for task in tasks_vec {
            ready_queue.push(task);
        }

        // Use fixed priority scheduling with period-based priorities
        drop(ready_queue);
        self.schedule_fixed_priority().await
    }

    /// Fixed Priority Preemptive scheduling
    async fn schedule_fixed_priority(&self) -> Result<()> {
        let mut ready_queue = self.ready_queue.lock();
        let mut running_tasks = self.running_tasks.write().await;
        let mut tasks = self.tasks.write().await;

        // Check for preemption opportunities
        for cpu in 0..self.config.cpu_count {
            if let Some(running_task_id) = running_tasks[&cpu] {
                if let Some(running_task) = tasks.get(&running_task_id) {
                    // Check if a higher priority task is ready
                    if let Some(highest_priority_task) = ready_queue.peek() {
                        if highest_priority_task.priority < running_task.priority {
                            // Preempt running task
                            let mut preempted_task = tasks.get_mut(&running_task_id).unwrap();
                            preempted_task.state = TaskState::Ready;
                            ready_queue.push(preempted_task.clone());
                            
                            // Schedule higher priority task
                            let mut new_task = ready_queue.pop().unwrap();
                            new_task.state = TaskState::Running;
                            running_tasks.insert(cpu, Some(new_task.id));
                            tasks.insert(new_task.id, new_task);
                            
                            self.stats.write().await.total_preemptions += 1;
                            debug!("Preempted task {} with task {}", 
                                   preempted_task.name, tasks[&new_task.id].name);
                        }
                    }
                }
            } else {
                // CPU is idle, schedule highest priority task
                if let Some(mut task) = ready_queue.pop() {
                    task.state = TaskState::Running;
                    running_tasks.insert(cpu, Some(task.id));
                    tasks.insert(task.id, task);
                }
            }
        }

        Ok(())
    }

    /// Priority Round Robin scheduling
    async fn schedule_priority_round_robin(&self) -> Result<()> {
        // Implement round-robin within each priority level
        // This is a simplified version
        self.schedule_fixed_priority().await
    }

    /// Check if adding a task would maintain schedulability
    async fn check_schedulability(&self, new_task: &RealTimeTask) -> Result<bool> {
        let tasks = self.tasks.read().await;
        
        // Calculate total utilization
        let mut total_utilization = new_task.utilization();
        for task in tasks.values() {
            total_utilization += task.utilization();
        }

        // Liu and Layland bound for EDF: U ≤ 1
        // For Rate Monotonic: U ≤ n(2^(1/n) - 1)
        let bound = match self.config.policy {
            SchedulingPolicy::EarliestDeadlineFirst => 1.0,
            SchedulingPolicy::RateMonotonic => {
                let n = tasks.len() + 1;
                n as f64 * (2.0_f64.powf(1.0 / n as f64) - 1.0)
            }
            _ => 0.69, // Conservative bound for other policies
        };

        Ok(total_utilization <= bound)
    }

    /// Run deadline monitoring
    async fn run_deadline_monitor(&self) -> Result<()> {
        let mut interval = interval(Duration::from_millis(1));
        interval.set_missed_tick_behavior(MissedTickBehavior::Skip);

        while *self.scheduler_active.read().await {
            let now = Instant::now();
            let mut tasks = self.tasks.write().await;
            let mut stats = self.stats.write().await;

            for task in tasks.values_mut() {
                if task.state == TaskState::Running && now > task.absolute_deadline {
                    task.record_missed_deadline();
                    stats.total_deadline_misses += 1;
                    warn!("Task {} missed deadline by {:?}", 
                          task.name, now.duration_since(task.absolute_deadline));
                }
            }

            interval.tick().await;
        }

        Ok(())
    }

    /// Run statistics collector
    async fn run_statistics_collector(&self) -> Result<()> {
        let mut interval = interval(Duration::from_secs(1));
        interval.set_missed_tick_behavior(MissedTickBehavior::Skip);

        while *self.scheduler_active.read().await {
            self.update_statistics().await;
            interval.tick().await;
        }

        Ok(())
    }

    /// Update scheduling statistics
    async fn update_statistics(&self) {
        let tasks = self.tasks.read().await;
        let mut stats = self.stats.write().await;

        // Calculate CPU utilization
        let mut total_utilization = 0.0;
        let mut total_response_time = Duration::ZERO;
        let mut task_count = 0;

        for task in tasks.values() {
            total_utilization += task.utilization();
            if task.execution_count > 0 {
                total_response_time += task.total_execution_time;
                task_count += task.execution_count;
            }
        }

        stats.cpu_utilization = total_utilization / self.config.cpu_count as f64;
        stats.average_response_time = if task_count > 0 {
            total_response_time / task_count as u32
        } else {
            Duration::ZERO
        };
        stats.last_update = Some(Instant::now());
    }

    /// Get current scheduling statistics
    pub async fn get_statistics(&self) -> SchedulingStats {
        self.stats.read().await.clone()
    }

    /// Get task information
    pub async fn get_task_info(&self, task_id: u64) -> Option<RealTimeTask> {
        self.tasks.read().await.get(&task_id).cloned()
    }

    /// List all tasks
    pub async fn list_tasks(&self) -> Vec<RealTimeTask> {
        self.tasks.read().await.values().cloned().collect()
    }
}
