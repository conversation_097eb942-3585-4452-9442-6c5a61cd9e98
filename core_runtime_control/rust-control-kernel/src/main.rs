/*!
 * ASI Core Runtime & Real-Time Control Kernel
 * ==========================================
 * 
 * Critical control kernel providing ultra-low latency real-time control
 * for robotics, drones, and embedded systems in the ASI ecosystem.
 * 
 * Features:
 * - Deterministic real-time control loops (<1ms latency)
 * - Memory-safe hardware interfaces
 * - Multi-layer safety systems with watchdogs
 * - Hardware-accelerated inference integration
 * - gRPC integration with Decision Engine
 */

use anyhow::Result;
use clap::Parser;
use std::sync::Arc;
use tokio::signal;
use tracing::{info, error, warn, debug};

// Internal modules
mod control;
mod safety;
mod scheduler;
mod hardware;
mod communication;
mod utils;

use control::ControlKernel;
use safety::SafetySystem;
use scheduler::RealTimeScheduler;
use hardware::HardwareManager;
use communication::CommunicationManager;
use utils::{Config, Logger, MetricsCollector};

/// Command line arguments
#[derive(Parser, Debug)]
#[command(author, version, about, long_about = None)]
struct Args {
    /// Configuration file path
    #[arg(short, long, default_value = "configs/control_config.yaml")]
    config: String,

    /// Log level
    #[arg(short, long, default_value = "info")]
    log_level: String,

    /// Enable real-time mode
    #[arg(short, long)]
    realtime: bool,

    /// Hardware configuration file
    #[arg(long, default_value = "configs/hardware_config.yaml")]
    hardware_config: String,

    /// Safety configuration file
    #[arg(long, default_value = "configs/safety_config.yaml")]
    safety_config: String,

    /// Disable safety systems (DANGEROUS - for testing only)
    #[arg(long)]
    disable_safety: bool,

    /// CPU affinity mask (hex)
    #[arg(long)]
    cpu_affinity: Option<String>,

    /// Real-time priority (1-99)
    #[arg(long, default_value = "80")]
    rt_priority: u8,
}

#[tokio::main(flavor = "multi_thread", worker_threads = 4)]
async fn main() -> Result<()> {
    let args = Args::parse();

    // Initialize logging
    Logger::init(&args.log_level)?;
    info!("Starting ASI Control Kernel v{}", env!("CARGO_PKG_VERSION"));

    // Load configuration
    let config = Config::load(&args.config)?;
    let hardware_config = Config::load(&args.hardware_config)?;
    let safety_config = Config::load(&args.safety_config)?;

    // Set real-time scheduling if enabled
    if args.realtime {
        setup_realtime_scheduling(args.rt_priority, args.cpu_affinity)?;
    }

    // Initialize metrics collection
    let metrics = Arc::new(MetricsCollector::new(&config)?);

    // Initialize hardware manager
    let hardware_manager = Arc::new(
        HardwareManager::new(&hardware_config, metrics.clone()).await?
    );

    // Initialize safety system
    let safety_system = if !args.disable_safety {
        Some(Arc::new(
            SafetySystem::new(&safety_config, hardware_manager.clone(), metrics.clone()).await?
        ))
    } else {
        warn!("Safety systems disabled - this should only be used for testing!");
        None
    };

    // Initialize real-time scheduler
    let scheduler = Arc::new(
        RealTimeScheduler::new(&config, metrics.clone())?
    );

    // Initialize communication manager
    let communication_manager = Arc::new(
        CommunicationManager::new(&config, metrics.clone()).await?
    );

    // Initialize control kernel
    let control_kernel = Arc::new(
        ControlKernel::new(
            &config,
            hardware_manager.clone(),
            safety_system.clone(),
            scheduler.clone(),
            communication_manager.clone(),
            metrics.clone(),
        ).await?
    );

    // Start all systems
    info!("Starting control kernel systems...");

    // Start metrics collection
    let metrics_handle = start_metrics_server(&config, metrics.clone()).await?;

    // Start hardware manager
    let hardware_handle = tokio::spawn({
        let hardware_manager = hardware_manager.clone();
        async move {
            if let Err(e) = hardware_manager.start().await {
                error!("Hardware manager error: {}", e);
            }
        }
    });

    // Start safety system
    let safety_handle = if let Some(safety_system) = safety_system.clone() {
        Some(tokio::spawn({
            let safety_system = safety_system.clone();
            async move {
                if let Err(e) = safety_system.start().await {
                    error!("Safety system error: {}", e);
                }
            }
        }))
    } else {
        None
    };

    // Start communication manager
    let communication_handle = tokio::spawn({
        let communication_manager = communication_manager.clone();
        async move {
            if let Err(e) = communication_manager.start().await {
                error!("Communication manager error: {}", e);
            }
        }
    });

    // Start control kernel (this is the main real-time loop)
    let control_handle = tokio::spawn({
        let control_kernel = control_kernel.clone();
        async move {
            if let Err(e) = control_kernel.start().await {
                error!("Control kernel error: {}", e);
            }
        }
    });

    info!("All systems started successfully");
    info!("Control kernel running in {} mode", 
          if args.realtime { "real-time" } else { "normal" });

    // Wait for shutdown signal or system failure
    tokio::select! {
        _ = signal::ctrl_c() => {
            info!("Received shutdown signal");
        }
        result = control_handle => {
            match result {
                Ok(_) => info!("Control kernel completed"),
                Err(e) => error!("Control kernel error: {}", e),
            }
        }
        result = hardware_handle => {
            match result {
                Ok(_) => info!("Hardware manager completed"),
                Err(e) => error!("Hardware manager error: {}", e),
            }
        }
        result = communication_handle => {
            match result {
                Ok(_) => info!("Communication manager completed"),
                Err(e) => error!("Communication manager error: {}", e),
            }
        }
        result = async {
            if let Some(handle) = safety_handle {
                handle.await
            } else {
                std::future::pending().await
            }
        } => {
            match result {
                Ok(_) => info!("Safety system completed"),
                Err(e) => error!("Safety system error: {}", e),
            }
        }
    }

    // Graceful shutdown
    info!("Initiating graceful shutdown...");

    // Stop control kernel first (most critical)
    control_kernel.stop().await?;

    // Stop safety system
    if let Some(safety_system) = safety_system {
        safety_system.stop().await?;
    }

    // Stop hardware manager
    hardware_manager.stop().await?;

    // Stop communication manager
    communication_manager.stop().await?;

    // Stop metrics server
    metrics_handle.abort();

    info!("ASI Control Kernel shutdown complete");
    Ok(())
}

/// Setup real-time scheduling for the process
fn setup_realtime_scheduling(priority: u8, cpu_affinity: Option<String>) -> Result<()> {
    use nix::sched::{sched_setscheduler, CpuSet, sched_setaffinity};
    use nix::sched::CloneFlags;
    use nix::unistd::Pid;
    use libc::{SCHED_FIFO, sched_param};

    info!("Setting up real-time scheduling with priority {}", priority);

    // Set real-time scheduling policy
    let param = sched_param {
        sched_priority: priority as i32,
    };

    unsafe {
        if libc::sched_setscheduler(0, SCHED_FIFO, &param) != 0 {
            return Err(anyhow::anyhow!("Failed to set real-time scheduling policy"));
        }
    }

    // Set CPU affinity if specified
    if let Some(affinity_str) = cpu_affinity {
        let affinity_mask = u64::from_str_radix(&affinity_str, 16)?;
        let mut cpu_set = CpuSet::new();
        
        for cpu in 0..64 {
            if (affinity_mask & (1 << cpu)) != 0 {
                cpu_set.set(cpu)?;
            }
        }

        sched_setaffinity(Pid::from_raw(0), &cpu_set)?;
        info!("Set CPU affinity to 0x{:x}", affinity_mask);
    }

    // Lock memory to prevent page faults
    unsafe {
        if libc::mlockall(libc::MCL_CURRENT | libc::MCL_FUTURE) != 0 {
            warn!("Failed to lock memory - may experience page faults");
        } else {
            info!("Memory locked to prevent page faults");
        }
    }

    info!("Real-time scheduling configured successfully");
    Ok(())
}

/// Start metrics server
async fn start_metrics_server(
    config: &Config,
    metrics: Arc<MetricsCollector>,
) -> Result<tokio::task::JoinHandle<()>> {
    let port = config.get("metrics.port").unwrap_or(9090u16);
    
    let handle = tokio::spawn(async move {
        use hyper::{Body, Request, Response, Server};
        use hyper::service::{make_service_fn, service_fn};
        use std::convert::Infallible;

        let make_svc = make_service_fn(move |_conn| {
            let metrics = metrics.clone();
            async move {
                Ok::<_, Infallible>(service_fn(move |req: Request<Body>| {
                    let metrics = metrics.clone();
                    async move {
                        match req.uri().path() {
                            "/metrics" => {
                                let metrics_text = metrics.export_prometheus();
                                Ok::<_, Infallible>(
                                    Response::builder()
                                        .header("content-type", "text/plain")
                                        .body(Body::from(metrics_text))
                                        .unwrap()
                                )
                            }
                            "/health" => {
                                Ok::<_, Infallible>(
                                    Response::builder()
                                        .body(Body::from("OK"))
                                        .unwrap()
                                )
                            }
                            _ => {
                                Ok::<_, Infallible>(
                                    Response::builder()
                                        .status(404)
                                        .body(Body::from("Not Found"))
                                        .unwrap()
                                )
                            }
                        }
                    }
                }))
            }
        });

        let addr = ([0, 0, 0, 0], port).into();
        let server = Server::bind(&addr).serve(make_svc);

        info!("Metrics server listening on http://0.0.0.0:{}", port);

        if let Err(e) = server.await {
            error!("Metrics server error: {}", e);
        }
    });

    Ok(handle)
}
