/**
 * @file inference_engine.cpp
 * @brief Implementation of hardware-accelerated neural inference engine
 */

#include "inference_engine.hpp"
#include "utils/logger.hpp"
#include "utils/timer.hpp"
#include <algorithm>
#include <thread>
#include <sstream>

#ifdef ENABLE_CUDA
#include <cuda_runtime.h>
#include <cudnn.h>
#endif

#ifdef ENABLE_TENSORRT
#include <NvInfer.h>
#include <NvInferRuntime.h>
#endif

namespace asi {
namespace inference {

InferenceEngine::InferenceEngine(const InferenceConfig& config)
    : config_(config)
    , initialized_(false)
    , profiling_enabled_(config.enable_profiling)
    , model_loader_(std::make_unique<ModelLoader>())
    , memory_manager_(std::make_unique<MemoryManager>())
    , profiler_(std::make_unique<Profiler>())
{
    LOG_INFO("Creating inference engine with execution mode: {}", 
             static_cast<int>(config.execution_mode));
}

InferenceEngine::~InferenceEngine() {
    shutdown();
}

bool InferenceEngine::initialize() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (initialized_) {
        LOG_WARN("Inference engine already initialized");
        return true;
    }
    
    LOG_INFO("Initializing inference engine");
    
    try {
        // Initialize execution backend
        if (!initialize_backend()) {
            LOG_ERROR("Failed to initialize execution backend");
            return false;
        }
        
        // Initialize memory manager
        if (!memory_manager_->initialize(config_)) {
            LOG_ERROR("Failed to initialize memory manager");
            return false;
        }
        
        // Initialize model loader
        if (!model_loader_->initialize(config_)) {
            LOG_ERROR("Failed to initialize model loader");
            return false;
        }
        
        // Set real-time scheduling if enabled
        if (config_.enable_realtime_mode) {
            setup_realtime_scheduling();
        }
        
        // Initialize profiler
        if (profiling_enabled_) {
            profiler_->initialize();
        }
        
        initialized_ = true;
        LOG_INFO("Inference engine initialized successfully");
        return true;
        
    } catch (const std::exception& e) {
        LOG_ERROR("Exception during initialization: {}", e.what());
        return false;
    }
}

bool InferenceEngine::initialize_backend() {
    switch (config_.execution_mode) {
        case ExecutionMode::CPU_ONLY:
            return initialize_cpu_backend();
            
        case ExecutionMode::CUDA:
            return initialize_cuda_backend();
            
        case ExecutionMode::OPENCL:
            return initialize_opencl_backend();
            
        case ExecutionMode::TENSORRT:
            return initialize_tensorrt_backend();
            
        case ExecutionMode::AUTO:
            return initialize_auto_backend();
            
        default:
            LOG_ERROR("Unknown execution mode");
            return false;
    }
}

bool InferenceEngine::initialize_cpu_backend() {
    LOG_INFO("Initializing CPU backend");
    
    // Set CPU thread count
    const size_t num_threads = std::thread::hardware_concurrency();
    LOG_INFO("Using {} CPU threads", num_threads);
    
    return true;
}

bool InferenceEngine::initialize_cuda_backend() {
    LOG_INFO("Initializing CUDA backend");
    
#ifdef ENABLE_CUDA
    // Check CUDA availability
    int device_count = 0;
    cudaError_t error = cudaGetDeviceCount(&device_count);
    
    if (error != cudaSuccess || device_count == 0) {
        LOG_ERROR("No CUDA devices available");
        return false;
    }
    
    // Select best device
    int best_device = 0;
    size_t max_memory = 0;
    
    for (int i = 0; i < device_count; ++i) {
        cudaDeviceProp prop;
        cudaGetDeviceProperties(&prop, i);
        
        if (prop.totalGlobalMem > max_memory) {
            max_memory = prop.totalGlobalMem;
            best_device = i;
        }
    }
    
    // Set device
    error = cudaSetDevice(best_device);
    if (error != cudaSuccess) {
        LOG_ERROR("Failed to set CUDA device: {}", cudaGetErrorString(error));
        return false;
    }
    
    // Initialize cuDNN
    cudnnHandle_t cudnn_handle;
    cudnnStatus_t cudnn_status = cudnnCreate(&cudnn_handle);
    if (cudnn_status != CUDNN_STATUS_SUCCESS) {
        LOG_ERROR("Failed to initialize cuDNN");
        return false;
    }
    
    cuda_device_ = best_device;
    cudnn_handle_ = cudnn_handle;
    
    LOG_INFO("CUDA backend initialized on device {}", best_device);
    return true;
#else
    LOG_ERROR("CUDA support not compiled");
    return false;
#endif
}

bool InferenceEngine::initialize_opencl_backend() {
    LOG_INFO("Initializing OpenCL backend");
    // OpenCL initialization would go here
    LOG_WARN("OpenCL backend not yet implemented");
    return false;
}

bool InferenceEngine::initialize_tensorrt_backend() {
    LOG_INFO("Initializing TensorRT backend");
    
#ifdef ENABLE_TENSORRT
    // Initialize TensorRT logger
    tensorrt_logger_ = std::make_unique<TensorRTLogger>();
    
    // Create TensorRT runtime
    tensorrt_runtime_ = std::unique_ptr<nvinfer1::IRuntime>(
        nvinfer1::createInferRuntime(*tensorrt_logger_)
    );
    
    if (!tensorrt_runtime_) {
        LOG_ERROR("Failed to create TensorRT runtime");
        return false;
    }
    
    LOG_INFO("TensorRT backend initialized");
    return true;
#else
    LOG_ERROR("TensorRT support not compiled");
    return false;
#endif
}

bool InferenceEngine::initialize_auto_backend() {
    LOG_INFO("Auto-selecting execution backend");
    
    // Try backends in order of preference
    if (initialize_tensorrt_backend()) {
        config_.execution_mode = ExecutionMode::TENSORRT;
        return true;
    }
    
    if (initialize_cuda_backend()) {
        config_.execution_mode = ExecutionMode::CUDA;
        return true;
    }
    
    if (initialize_cpu_backend()) {
        config_.execution_mode = ExecutionMode::CPU_ONLY;
        return true;
    }
    
    LOG_ERROR("No suitable backend available");
    return false;
}

void InferenceEngine::setup_realtime_scheduling() {
    LOG_INFO("Setting up real-time scheduling");
    
#ifdef __linux__
    // Set real-time priority
    struct sched_param param;
    param.sched_priority = config_.thread_priority;
    
    if (sched_setscheduler(0, SCHED_FIFO, &param) != 0) {
        LOG_WARN("Failed to set real-time priority: {}", strerror(errno));
    }
    
    // Set CPU affinity if specified
    if (config_.cpu_affinity_mask != 0) {
        cpu_set_t cpuset;
        CPU_ZERO(&cpuset);
        
        for (int i = 0; i < CPU_SETSIZE; ++i) {
            if (config_.cpu_affinity_mask & (1 << i)) {
                CPU_SET(i, &cpuset);
            }
        }
        
        if (sched_setaffinity(0, sizeof(cpuset), &cpuset) != 0) {
            LOG_WARN("Failed to set CPU affinity: {}", strerror(errno));
        }
    }
#endif
}

bool InferenceEngine::load_model(const std::string& model_path, const std::string& model_name) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!initialized_) {
        LOG_ERROR("Inference engine not initialized");
        return false;
    }
    
    LOG_INFO("Loading model: {} from {}", model_name, model_path);
    
    try {
        // Load model using model loader
        auto model = model_loader_->load_model(model_path, config_.execution_mode);
        if (!model) {
            LOG_ERROR("Failed to load model from {}", model_path);
            return false;
        }
        
        // Store model
        models_[model_name] = std::move(model);
        
        // Warm up model if in real-time mode
        if (config_.enable_realtime_mode) {
            warmup(model_name, 5);
        }
        
        LOG_INFO("Model {} loaded successfully", model_name);
        return true;
        
    } catch (const std::exception& e) {
        LOG_ERROR("Exception loading model {}: {}", model_name, e.what());
        return false;
    }
}

InferenceResult InferenceEngine::run_inference(
    const std::string& model_name,
    const std::vector<Tensor>& inputs
) {
    if (!initialized_) {
        return create_error_result("Inference engine not initialized");
    }
    
    auto it = models_.find(model_name);
    if (it == models_.end()) {
        return create_error_result("Model not found: " + model_name);
    }
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    try {
        // Validate inputs
        if (!validate_inputs(model_name, inputs)) {
            return create_error_result("Invalid inputs");
        }
        
        // Run inference
        auto outputs = execute_inference(it->second.get(), inputs);
        
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
            end_time - start_time
        ).count();
        
        // Check latency constraint
        if (config_.enable_realtime_mode && duration > config_.max_latency_us) {
            LOG_WARN("Inference latency {}μs exceeds maximum {}μs", 
                     duration, config_.max_latency_us);
        }
        
        // Update statistics
        update_statistics(model_name, duration, true);
        
        // Create result
        InferenceResult result;
        result.success = true;
        result.outputs = std::move(outputs);
        result.latency_us = duration;
        result.model_name = model_name;
        result.timestamp = start_time;
        
        return result;
        
    } catch (const std::exception& e) {
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
            end_time - start_time
        ).count();
        
        update_statistics(model_name, duration, false);
        
        LOG_ERROR("Inference failed for model {}: {}", model_name, e.what());
        return create_error_result("Inference failed: " + std::string(e.what()));
    }
}

std::vector<Tensor> InferenceEngine::execute_inference(
    Model* model,
    const std::vector<Tensor>& inputs
) {
    switch (config_.execution_mode) {
        case ExecutionMode::CPU_ONLY:
            return execute_cpu_inference(model, inputs);
            
        case ExecutionMode::CUDA:
            return execute_cuda_inference(model, inputs);
            
        case ExecutionMode::TENSORRT:
            return execute_tensorrt_inference(model, inputs);
            
        default:
            throw std::runtime_error("Unsupported execution mode");
    }
}

std::vector<Tensor> InferenceEngine::execute_cpu_inference(
    Model* model,
    const std::vector<Tensor>& inputs
) {
    // CPU inference implementation
    return model->forward_cpu(inputs);
}

std::vector<Tensor> InferenceEngine::execute_cuda_inference(
    Model* model,
    const std::vector<Tensor>& inputs
) {
#ifdef ENABLE_CUDA
    // Transfer inputs to GPU
    std::vector<Tensor> gpu_inputs;
    for (const auto& input : inputs) {
        gpu_inputs.push_back(memory_manager_->transfer_to_gpu(input));
    }
    
    // Run inference on GPU
    auto gpu_outputs = model->forward_cuda(gpu_inputs);
    
    // Transfer outputs back to CPU
    std::vector<Tensor> outputs;
    for (const auto& output : gpu_outputs) {
        outputs.push_back(memory_manager_->transfer_to_cpu(output));
    }
    
    return outputs;
#else
    throw std::runtime_error("CUDA support not available");
#endif
}

std::vector<Tensor> InferenceEngine::execute_tensorrt_inference(
    Model* model,
    const std::vector<Tensor>& inputs
) {
#ifdef ENABLE_TENSORRT
    return model->forward_tensorrt(inputs);
#else
    throw std::runtime_error("TensorRT support not available");
#endif
}

bool InferenceEngine::validate_inputs(
    const std::string& model_name,
    const std::vector<Tensor>& inputs
) {
    auto it = models_.find(model_name);
    if (it == models_.end()) {
        return false;
    }
    
    return it->second->validate_inputs(inputs);
}

void InferenceEngine::update_statistics(
    const std::string& model_name,
    uint64_t latency_us,
    bool success
) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    
    auto& stats = model_stats_[model_name];
    stats.total_inferences++;
    
    if (success) {
        stats.successful_inferences++;
        stats.total_latency_us += latency_us;
        stats.min_latency_us = std::min(stats.min_latency_us, latency_us);
        stats.max_latency_us = std::max(stats.max_latency_us, latency_us);
    } else {
        stats.failed_inferences++;
    }
    
    stats.last_inference_time = std::chrono::high_resolution_clock::now();
}

InferenceResult InferenceEngine::create_error_result(const std::string& error_message) {
    InferenceResult result;
    result.success = false;
    result.error_message = error_message;
    result.timestamp = std::chrono::high_resolution_clock::now();
    return result;
}

bool InferenceEngine::warmup(const std::string& model_name, size_t num_iterations) {
    LOG_INFO("Warming up model {} with {} iterations", model_name, num_iterations);
    
    auto it = models_.find(model_name);
    if (it == models_.end()) {
        LOG_ERROR("Model not found for warmup: {}", model_name);
        return false;
    }
    
    try {
        // Create dummy inputs
        auto dummy_inputs = it->second->create_dummy_inputs();
        
        // Run warmup iterations
        for (size_t i = 0; i < num_iterations; ++i) {
            auto result = run_inference(model_name, dummy_inputs);
            if (!result.success) {
                LOG_WARN("Warmup iteration {} failed: {}", i, result.error_message);
            }
        }
        
        LOG_INFO("Model {} warmup completed", model_name);
        return true;
        
    } catch (const std::exception& e) {
        LOG_ERROR("Warmup failed for model {}: {}", model_name, e.what());
        return false;
    }
}

std::string InferenceEngine::get_device_info() const {
    std::stringstream ss;
    
    ss << "Execution Mode: ";
    switch (config_.execution_mode) {
        case ExecutionMode::CPU_ONLY: ss << "CPU"; break;
        case ExecutionMode::CUDA: ss << "CUDA"; break;
        case ExecutionMode::OPENCL: ss << "OpenCL"; break;
        case ExecutionMode::TENSORRT: ss << "TensorRT"; break;
        case ExecutionMode::AUTO: ss << "Auto"; break;
    }
    
#ifdef ENABLE_CUDA
    if (config_.execution_mode == ExecutionMode::CUDA || 
        config_.execution_mode == ExecutionMode::TENSORRT) {
        cudaDeviceProp prop;
        cudaGetDeviceProperties(&prop, cuda_device_);
        ss << "\nGPU: " << prop.name;
        ss << "\nCompute Capability: " << prop.major << "." << prop.minor;
        ss << "\nMemory: " << prop.totalGlobalMem / (1024 * 1024) << " MB";
    }
#endif
    
    return ss.str();
}

void InferenceEngine::shutdown() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!initialized_) {
        return;
    }
    
    LOG_INFO("Shutting down inference engine");
    
    // Clear models
    models_.clear();
    
    // Cleanup backends
#ifdef ENABLE_CUDA
    if (cudnn_handle_) {
        cudnnDestroy(static_cast<cudnnHandle_t>(cudnn_handle_));
        cudnn_handle_ = nullptr;
    }
#endif
    
    // Reset state
    initialized_ = false;
    
    LOG_INFO("Inference engine shutdown complete");
}

} // namespace inference
} // namespace asi
