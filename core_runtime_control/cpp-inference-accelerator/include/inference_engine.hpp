/**
 * @file inference_engine.hpp
 * @brief Hardware-accelerated neural inference engine for real-time control
 * 
 * Provides ultra-low latency neural network inference with CUDA/OpenCL acceleration
 * for real-time robotics and embedded control applications.
 */

#pragma once

#include <memory>
#include <vector>
#include <string>
#include <chrono>
#include <future>
#include <unordered_map>
#include <atomic>
#include <mutex>

#include "tensor.hpp"
#include "model_loader.hpp"
#include "memory_manager.hpp"
#include "utils/types.hpp"
#include "utils/profiler.hpp"

namespace asi {
namespace inference {

/**
 * @brief Inference execution mode
 */
enum class ExecutionMode {
    CPU_ONLY,           ///< CPU-only execution
    CUDA,               ///< CUDA GPU acceleration
    OPENCL,             ///< OpenCL acceleration
    TENSORRT,           ///< TensorRT optimization
    AUTO                ///< Automatic selection
};

/**
 * @brief Inference precision mode
 */
enum class PrecisionMode {
    FP32,               ///< 32-bit floating point
    FP16,               ///< 16-bit floating point
    INT8,               ///< 8-bit integer quantization
    MIXED               ///< Mixed precision
};

/**
 * @brief Inference configuration
 */
struct InferenceConfig {
    ExecutionMode execution_mode = ExecutionMode::AUTO;
    PrecisionMode precision_mode = PrecisionMode::FP32;
    size_t max_batch_size = 1;
    size_t max_workspace_size = 1024 * 1024 * 1024; // 1GB
    bool enable_profiling = false;
    bool enable_optimization = true;
    bool enable_caching = true;
    std::string cache_directory = "./cache";
    
    // Real-time specific settings
    bool enable_realtime_mode = true;
    uint32_t max_latency_us = 1000;     // 1ms max latency
    uint32_t target_latency_us = 500;   // 500μs target latency
    bool enable_deadline_scheduling = true;
    int32_t thread_priority = 80;       // Real-time priority
    uint32_t cpu_affinity_mask = 0;     // CPU affinity (0 = auto)
};

/**
 * @brief Inference statistics
 */
struct InferenceStats {
    std::atomic<uint64_t> total_inferences{0};
    std::atomic<uint64_t> successful_inferences{0};
    std::atomic<uint64_t> failed_inferences{0};
    std::atomic<uint64_t> deadline_misses{0};
    
    std::atomic<uint64_t> min_latency_us{UINT64_MAX};
    std::atomic<uint64_t> max_latency_us{0};
    std::atomic<uint64_t> avg_latency_us{0};
    std::atomic<uint64_t> total_latency_us{0};
    
    std::atomic<uint64_t> memory_usage_bytes{0};
    std::atomic<uint64_t> gpu_memory_usage_bytes{0};
    
    std::chrono::steady_clock::time_point start_time;
    
    void reset() {
        total_inferences = 0;
        successful_inferences = 0;
        failed_inferences = 0;
        deadline_misses = 0;
        min_latency_us = UINT64_MAX;
        max_latency_us = 0;
        avg_latency_us = 0;
        total_latency_us = 0;
        memory_usage_bytes = 0;
        gpu_memory_usage_bytes = 0;
        start_time = std::chrono::steady_clock::now();
    }
};

/**
 * @brief Inference result
 */
struct InferenceResult {
    bool success = false;
    std::string error_message;
    uint64_t latency_us = 0;
    uint64_t memory_usage_bytes = 0;
    std::chrono::steady_clock::time_point timestamp;
    std::vector<Tensor> outputs;
    
    // Real-time specific metrics
    bool deadline_met = true;
    uint64_t queue_time_us = 0;
    uint64_t execution_time_us = 0;
    uint64_t postprocess_time_us = 0;
};

/**
 * @brief Hardware-accelerated inference engine
 */
class InferenceEngine {
public:
    /**
     * @brief Constructor
     * @param config Inference configuration
     */
    explicit InferenceEngine(const InferenceConfig& config = InferenceConfig{});
    
    /**
     * @brief Destructor
     */
    ~InferenceEngine();
    
    // Non-copyable, movable
    InferenceEngine(const InferenceEngine&) = delete;
    InferenceEngine& operator=(const InferenceEngine&) = delete;
    InferenceEngine(InferenceEngine&&) = default;
    InferenceEngine& operator=(InferenceEngine&&) = default;
    
    /**
     * @brief Initialize the inference engine
     * @return True if initialization successful
     */
    bool initialize();
    
    /**
     * @brief Shutdown the inference engine
     */
    void shutdown();
    
    /**
     * @brief Load a model from file
     * @param model_path Path to the model file
     * @param model_name Optional model name (defaults to filename)
     * @return True if model loaded successfully
     */
    bool load_model(const std::string& model_path, const std::string& model_name = "");
    
    /**
     * @brief Load a model from memory
     * @param model_data Model data buffer
     * @param model_size Size of model data
     * @param model_name Model name
     * @return True if model loaded successfully
     */
    bool load_model_from_memory(const void* model_data, size_t model_size, const std::string& model_name);
    
    /**
     * @brief Unload a model
     * @param model_name Model name to unload
     * @return True if model unloaded successfully
     */
    bool unload_model(const std::string& model_name);
    
    /**
     * @brief Run synchronous inference
     * @param model_name Model to use for inference
     * @param inputs Input tensors
     * @param deadline_us Optional deadline in microseconds
     * @return Inference result
     */
    InferenceResult infer(
        const std::string& model_name,
        const std::vector<Tensor>& inputs,
        uint64_t deadline_us = 0
    );
    
    /**
     * @brief Run asynchronous inference
     * @param model_name Model to use for inference
     * @param inputs Input tensors
     * @param deadline_us Optional deadline in microseconds
     * @return Future containing inference result
     */
    std::future<InferenceResult> infer_async(
        const std::string& model_name,
        const std::vector<Tensor>& inputs,
        uint64_t deadline_us = 0
    );
    
    /**
     * @brief Run batch inference
     * @param model_name Model to use for inference
     * @param batch_inputs Batch of input tensors
     * @param deadline_us Optional deadline in microseconds
     * @return Vector of inference results
     */
    std::vector<InferenceResult> infer_batch(
        const std::string& model_name,
        const std::vector<std::vector<Tensor>>& batch_inputs,
        uint64_t deadline_us = 0
    );
    
    /**
     * @brief Get model information
     * @param model_name Model name
     * @return Model information or nullptr if not found
     */
    std::shared_ptr<ModelInfo> get_model_info(const std::string& model_name) const;
    
    /**
     * @brief Get list of loaded models
     * @return Vector of model names
     */
    std::vector<std::string> get_loaded_models() const;
    
    /**
     * @brief Get inference statistics
     * @return Current statistics
     */
    InferenceStats get_stats() const;
    
    /**
     * @brief Reset statistics
     */
    void reset_stats();
    
    /**
     * @brief Check if engine is initialized
     * @return True if initialized
     */
    bool is_initialized() const;
    
    /**
     * @brief Get current configuration
     * @return Configuration
     */
    const InferenceConfig& get_config() const;
    
    /**
     * @brief Update configuration (some settings require restart)
     * @param config New configuration
     * @return True if update successful
     */
    bool update_config(const InferenceConfig& config);
    
    /**
     * @brief Get available execution modes
     * @return Vector of supported execution modes
     */
    std::vector<ExecutionMode> get_available_execution_modes() const;
    
    /**
     * @brief Get device information
     * @return Device information string
     */
    std::string get_device_info() const;
    
    /**
     * @brief Warm up the inference engine
     * @param model_name Model to warm up
     * @param num_iterations Number of warm-up iterations
     * @return True if warm-up successful
     */
    bool warmup(const std::string& model_name, size_t num_iterations = 10);
    
    /**
     * @brief Enable/disable profiling
     * @param enable Enable profiling
     */
    void set_profiling_enabled(bool enable);
    
    /**
     * @brief Get profiling results
     * @return Profiling data
     */
    std::string get_profiling_results() const;
    
    /**
     * @brief Set memory limit
     * @param limit_bytes Memory limit in bytes
     */
    void set_memory_limit(size_t limit_bytes);
    
    /**
     * @brief Get memory usage
     * @return Current memory usage in bytes
     */
    size_t get_memory_usage() const;
    
    /**
     * @brief Optimize model for target hardware
     * @param model_name Model to optimize
     * @param optimization_level Optimization level (0-3)
     * @return True if optimization successful
     */
    bool optimize_model(const std::string& model_name, int optimization_level = 2);

private:
    class Impl;
    std::unique_ptr<Impl> pimpl_;
};

/**
 * @brief Inference engine factory
 */
class InferenceEngineFactory {
public:
    /**
     * @brief Create inference engine instance
     * @param config Configuration
     * @return Unique pointer to inference engine
     */
    static std::unique_ptr<InferenceEngine> create(const InferenceConfig& config = InferenceConfig{});
    
    /**
     * @brief Get default configuration for target platform
     * @param execution_mode Target execution mode
     * @return Default configuration
     */
    static InferenceConfig get_default_config(ExecutionMode execution_mode = ExecutionMode::AUTO);
    
    /**
     * @brief Check if execution mode is supported
     * @param mode Execution mode to check
     * @return True if supported
     */
    static bool is_execution_mode_supported(ExecutionMode mode);
    
    /**
     * @brief Get recommended execution mode for current hardware
     * @return Recommended execution mode
     */
    static ExecutionMode get_recommended_execution_mode();
};

} // namespace inference
} // namespace asi
