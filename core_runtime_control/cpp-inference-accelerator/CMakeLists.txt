# ASI Core Runtime - C++ Inference Accelerator
# =============================================

cmake_minimum_required(VERSION 3.20)
project(asi-inference-accelerator VERSION 1.0.0 LANGUAGES CXX CUDA)

# Set C++ standard
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Set CUDA standard
set(CMAKE_CUDA_STANDARD 17)
set(CMAKE_CUDA_STANDARD_REQUIRED ON)

# Build type
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Compiler flags
set(CMAKE_CXX_FLAGS_DEBUG "-g -O0 -Wall -Wextra -DDEBUG")
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -DNDEBUG -march=native -mtune=native")
set(CMAKE_CXX_FLAGS_REALTIME "-O3 -DNDEBUG -DREALTIME -march=native -mtune=native -ffast-math")

# CUDA flags
set(CMAKE_CUDA_FLAGS_DEBUG "-g -G -O0 -DDEBUG")
set(CMAKE_CUDA_FLAGS_RELEASE "-O3 -DNDEBUG --use_fast_math")
set(CMAKE_CUDA_FLAGS_REALTIME "-O3 -DNDEBUG -DREALTIME --use_fast_math --ptxas-options=-O3")

# Find packages
find_package(CUDA REQUIRED)
find_package(OpenMP REQUIRED)
find_package(Threads REQUIRED)

# Find optional packages
find_package(PkgConfig QUIET)
find_package(OpenCL QUIET)
find_package(TensorRT QUIET)

# Find Intel MKL
find_package(MKL QUIET)
if(MKL_FOUND)
    add_definitions(-DUSE_MKL)
endif()

# Find cuDNN
find_path(CUDNN_INCLUDE_DIR cudnn.h
    HINTS ${CUDA_TOOLKIT_ROOT_DIR}
    PATH_SUFFIXES include)
find_library(CUDNN_LIBRARY cudnn
    HINTS ${CUDA_TOOLKIT_ROOT_DIR}
    PATH_SUFFIXES lib64 lib)

if(CUDNN_INCLUDE_DIR AND CUDNN_LIBRARY)
    set(CUDNN_FOUND TRUE)
    add_definitions(-DUSE_CUDNN)
endif()

# Find cuBLAS
find_library(CUBLAS_LIBRARY cublas
    HINTS ${CUDA_TOOLKIT_ROOT_DIR}
    PATH_SUFFIXES lib64 lib)

if(CUBLAS_LIBRARY)
    set(CUBLAS_FOUND TRUE)
    add_definitions(-DUSE_CUBLAS)
endif()

# Include directories
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CUDA_INCLUDE_DIRS}
)

if(CUDNN_FOUND)
    include_directories(${CUDNN_INCLUDE_DIR})
endif()

# Source files
set(SOURCES
    src/inference_engine.cpp
    src/model_loader.cpp
    src/memory_manager.cpp
    src/optimization/quantization.cpp
    src/optimization/pruning.cpp
    src/optimization/fusion.cpp
    src/acceleration/cuda_kernels.cu
    src/acceleration/tensor_ops.cu
    src/acceleration/memory_pool.cu
    src/utils/profiler.cpp
    src/utils/logger.cpp
    src/utils/config.cpp
)

# Header files
set(HEADERS
    include/inference_engine.hpp
    include/model_loader.hpp
    include/memory_manager.hpp
    include/tensor.hpp
    include/optimization/quantization.hpp
    include/optimization/pruning.hpp
    include/optimization/fusion.hpp
    include/acceleration/cuda_kernels.cuh
    include/acceleration/tensor_ops.cuh
    include/acceleration/memory_pool.cuh
    include/utils/profiler.hpp
    include/utils/logger.hpp
    include/utils/config.hpp
    include/utils/types.hpp
)

# Create shared library
add_library(${PROJECT_NAME} SHARED ${SOURCES} ${HEADERS})

# Create static library for embedded systems
add_library(${PROJECT_NAME}-static STATIC ${SOURCES} ${HEADERS})

# Set target properties
set_target_properties(${PROJECT_NAME} PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION 1
    CUDA_SEPARABLE_COMPILATION ON
    CUDA_RESOLVE_DEVICE_SYMBOLS ON
)

set_target_properties(${PROJECT_NAME}-static PROPERTIES
    OUTPUT_NAME ${PROJECT_NAME}
    CUDA_SEPARABLE_COMPILATION ON
    CUDA_RESOLVE_DEVICE_SYMBOLS ON
)

# Link libraries
target_link_libraries(${PROJECT_NAME}
    ${CUDA_LIBRARIES}
    ${CUDA_CUBLAS_LIBRARIES}
    ${CUDA_CURAND_LIBRARIES}
    ${CUDA_CUFFT_LIBRARIES}
    OpenMP::OpenMP_CXX
    Threads::Threads
)

target_link_libraries(${PROJECT_NAME}-static
    ${CUDA_LIBRARIES}
    ${CUDA_CUBLAS_LIBRARIES}
    ${CUDA_CURAND_LIBRARIES}
    ${CUDA_CUFFT_LIBRARIES}
    OpenMP::OpenMP_CXX
    Threads::Threads
)

# Link optional libraries
if(CUDNN_FOUND)
    target_link_libraries(${PROJECT_NAME} ${CUDNN_LIBRARY})
    target_link_libraries(${PROJECT_NAME}-static ${CUDNN_LIBRARY})
endif()

if(CUBLAS_FOUND)
    target_link_libraries(${PROJECT_NAME} ${CUBLAS_LIBRARY})
    target_link_libraries(${PROJECT_NAME}-static ${CUBLAS_LIBRARY})
endif()

if(MKL_FOUND)
    target_link_libraries(${PROJECT_NAME} ${MKL_LIBRARIES})
    target_link_libraries(${PROJECT_NAME}-static ${MKL_LIBRARIES})
endif()

if(OpenCL_FOUND)
    target_link_libraries(${PROJECT_NAME} ${OpenCL_LIBRARIES})
    target_link_libraries(${PROJECT_NAME}-static ${OpenCL_LIBRARIES})
    add_definitions(-DUSE_OPENCL)
endif()

if(TensorRT_FOUND)
    target_link_libraries(${PROJECT_NAME} ${TensorRT_LIBRARIES})
    target_link_libraries(${PROJECT_NAME}-static ${TensorRT_LIBRARIES})
    add_definitions(-DUSE_TENSORRT)
endif()

# Compiler-specific options
if(CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
    target_compile_options(${PROJECT_NAME} PRIVATE
        -fPIC
        -ffast-math
        -funroll-loops
        -fomit-frame-pointer
    )
elseif(CMAKE_CXX_COMPILER_ID STREQUAL "Clang")
    target_compile_options(${PROJECT_NAME} PRIVATE
        -fPIC
        -ffast-math
        -funroll-loops
        -fomit-frame-pointer
    )
elseif(CMAKE_CXX_COMPILER_ID STREQUAL "MSVC")
    target_compile_options(${PROJECT_NAME} PRIVATE
        /fp:fast
        /Ox
    )
endif()

# CUDA architecture
set(CUDA_ARCHITECTURES "60;61;70;75;80;86")
set_property(TARGET ${PROJECT_NAME} PROPERTY CUDA_ARCHITECTURES ${CUDA_ARCHITECTURES})
set_property(TARGET ${PROJECT_NAME}-static PROPERTY CUDA_ARCHITECTURES ${CUDA_ARCHITECTURES})

# Install targets
install(TARGETS ${PROJECT_NAME} ${PROJECT_NAME}-static
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
)

install(DIRECTORY include/
    DESTINATION include/${PROJECT_NAME}
    FILES_MATCHING PATTERN "*.hpp" PATTERN "*.cuh"
)

# Create pkg-config file
configure_file(
    ${CMAKE_CURRENT_SOURCE_DIR}/asi-inference-accelerator.pc.in
    ${CMAKE_CURRENT_BINARY_DIR}/asi-inference-accelerator.pc
    @ONLY
)

install(FILES ${CMAKE_CURRENT_BINARY_DIR}/asi-inference-accelerator.pc
    DESTINATION lib/pkgconfig
)

# Examples
option(BUILD_EXAMPLES "Build examples" ON)
if(BUILD_EXAMPLES)
    add_subdirectory(examples)
endif()

# Tests
option(BUILD_TESTS "Build tests" ON)
if(BUILD_TESTS)
    enable_testing()
    add_subdirectory(tests)
endif()

# Benchmarks
option(BUILD_BENCHMARKS "Build benchmarks" ON)
if(BUILD_BENCHMARKS)
    add_subdirectory(benchmarks)
endif()

# Documentation
find_package(Doxygen QUIET)
if(DOXYGEN_FOUND)
    configure_file(${CMAKE_CURRENT_SOURCE_DIR}/Doxyfile.in
        ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile @ONLY)
    
    add_custom_target(doc
        ${DOXYGEN_EXECUTABLE} ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile
        WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
        COMMENT "Generating API documentation with Doxygen"
        VERBATIM
    )
endif()

# Print configuration summary
message(STATUS "")
message(STATUS "ASI Inference Accelerator Configuration Summary:")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  CUDA standard: ${CMAKE_CUDA_STANDARD}")
message(STATUS "  CUDA architectures: ${CUDA_ARCHITECTURES}")
message(STATUS "  CUDA found: ${CUDA_FOUND}")
message(STATUS "  cuDNN found: ${CUDNN_FOUND}")
message(STATUS "  cuBLAS found: ${CUBLAS_FOUND}")
message(STATUS "  OpenCL found: ${OpenCL_FOUND}")
message(STATUS "  TensorRT found: ${TensorRT_FOUND}")
message(STATUS "  MKL found: ${MKL_FOUND}")
message(STATUS "  OpenMP found: ${OpenMP_FOUND}")
message(STATUS "  Build examples: ${BUILD_EXAMPLES}")
message(STATUS "  Build tests: ${BUILD_TESTS}")
message(STATUS "  Build benchmarks: ${BUILD_BENCHMARKS}")
message(STATUS "")
