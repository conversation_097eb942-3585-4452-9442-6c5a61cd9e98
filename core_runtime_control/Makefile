# ASI Core Runtime & Real-Time Control Makefile
# ==============================================

# Variables
SHELL := /bin/bash
.DEFAULT_GOAL := help

# Directories
RUST_DIR := rust-control-kernel
CPP_DIR := cpp-inference-accelerator
EMBEDDED_DIR := embedded-runtime
SAFETY_DIR := safety-systems
HARDWARE_DIR := hardware-interfaces
SCHEDULER_DIR := real-time-scheduler
CONFIGS_DIR := configs
TESTS_DIR := tests
BENCHMARKS_DIR := benchmarks

# Build configuration
BUILD_TYPE := release
RUST_TARGET := x86_64-unknown-linux-gnu
CPP_BUILD_DIR := $(CPP_DIR)/build
EMBEDDED_TARGET := thumbv7em-none-eabihf

# Cross-compilation targets
ARM64_TARGET := aarch64-unknown-linux-gnu
ARM_TARGET := armv7-unknown-linux-gnueabihf
RISCV_TARGET := riscv64gc-unknown-linux-gnu

# Real-time configuration
RT_PRIORITY := 80
CPU_AFFINITY := 0x0F
ENABLE_RT := true

# Colors for output
RED := \033[0;31m
GREEN := \033[0;32m
YELLOW := \033[0;33m
BLUE := \033[0;34m
PURPLE := \033[0;35m
CYAN := \033[0;36m
WHITE := \033[0;37m
RESET := \033[0m

# Help target
.PHONY: help
help: ## Show this help message
	@echo "$(CYAN)ASI Core Runtime & Real-Time Control - Available Commands$(RESET)"
	@echo "=========================================================="
	@awk 'BEGIN {FS = ":.*##"} /^[a-zA-Z_-]+:.*?##/ { printf "$(GREEN)%-25s$(RESET) %s\n", $$1, $$2 }' $(MAKEFILE_LIST)

# Dependency checks
.PHONY: check-deps
check-deps: ## Check if all dependencies are installed
	@echo "$(BLUE)Checking dependencies...$(RESET)"
	@command -v rustc >/dev/null 2>&1 || { echo "$(RED)Rust is required but not installed$(RESET)"; exit 1; }
	@command -v cargo >/dev/null 2>&1 || { echo "$(RED)Cargo is required but not installed$(RESET)"; exit 1; }
	@command -v cmake >/dev/null 2>&1 || { echo "$(RED)CMake is required but not installed$(RESET)"; exit 1; }
	@command -v g++ >/dev/null 2>&1 || { echo "$(RED)G++ is required but not installed$(RESET)"; exit 1; }
	@command -v nvcc >/dev/null 2>&1 || echo "$(YELLOW)NVCC not found - CUDA acceleration disabled$(RESET)"
	@echo "$(GREEN)Dependencies check completed$(RESET)"

# Hardware checks
.PHONY: check-hardware
check-hardware: ## Check hardware capabilities and real-time support
	@echo "$(BLUE)Checking hardware capabilities...$(RESET)"
	@echo "CPU cores: $$(nproc)"
	@echo "CPU architecture: $$(uname -m)"
	@echo "Kernel version: $$(uname -r)"
	@if [ -f /sys/kernel/realtime ]; then \
		echo "$(GREEN)Real-time kernel detected$(RESET)"; \
	else \
		echo "$(YELLOW)Standard kernel - real-time performance may be limited$(RESET)"; \
	fi
	@if command -v nvidia-smi >/dev/null 2>&1; then \
		echo "$(GREEN)NVIDIA GPU detected:$(RESET)"; \
		nvidia-smi --query-gpu=name,memory.total --format=csv,noheader,nounits; \
	else \
		echo "$(YELLOW)No NVIDIA GPU detected$(RESET)"; \
	fi

# Installation targets
.PHONY: install
install: check-deps install-rust install-cpp ## Install all dependencies
	@echo "$(GREEN)All dependencies installed successfully$(RESET)"

.PHONY: install-rust
install-rust: ## Install Rust dependencies and targets
	@echo "$(BLUE)Installing Rust dependencies...$(RESET)"
	cd $(RUST_DIR) && cargo fetch
	rustup target add $(ARM64_TARGET) $(ARM_TARGET) $(EMBEDDED_TARGET) $(RISCV_TARGET)
	@echo "$(GREEN)Rust dependencies installed$(RESET)"

.PHONY: install-cpp
install-cpp: ## Install C++ dependencies
	@echo "$(BLUE)Installing C++ dependencies...$(RESET)"
	sudo apt-get update
	sudo apt-get install -y \
		build-essential \
		cmake \
		ninja-build \
		libopencv-dev \
		libeigen3-dev \
		libboost-all-dev \
		libtbb-dev
	@echo "$(GREEN)C++ dependencies installed$(RESET)"

# Build targets
.PHONY: build
build: build-rust build-cpp ## Build all components
	@echo "$(GREEN)All components built successfully$(RESET)"

.PHONY: build-rust
build-rust: ## Build Rust control kernel
	@echo "$(BLUE)Building Rust control kernel...$(RESET)"
	cd $(RUST_DIR) && cargo build --profile $(BUILD_TYPE)
	@echo "$(GREEN)Rust control kernel built$(RESET)"

.PHONY: build-cpp
build-cpp: ## Build C++ inference accelerator
	@echo "$(BLUE)Building C++ inference accelerator...$(RESET)"
	mkdir -p $(CPP_BUILD_DIR)
	cd $(CPP_BUILD_DIR) && cmake -GNinja -DCMAKE_BUILD_TYPE=Release ..
	cd $(CPP_BUILD_DIR) && ninja
	@echo "$(GREEN)C++ inference accelerator built$(RESET)"

.PHONY: build-embedded
build-embedded: ## Build embedded runtime for microcontrollers
	@echo "$(BLUE)Building embedded runtime...$(RESET)"
	cd $(EMBEDDED_DIR) && cargo build --target $(EMBEDDED_TARGET) --profile $(BUILD_TYPE)
	@echo "$(GREEN)Embedded runtime built$(RESET)"

# Cross-compilation targets
.PHONY: build-arm64
build-arm64: ## Build for ARM64 (e.g., Raspberry Pi 4, NVIDIA Jetson)
	@echo "$(BLUE)Building for ARM64...$(RESET)"
	cd $(RUST_DIR) && cargo build --target $(ARM64_TARGET) --profile $(BUILD_TYPE)
	@echo "$(GREEN)ARM64 build completed$(RESET)"

.PHONY: build-arm
build-arm: ## Build for ARM (e.g., Raspberry Pi 3)
	@echo "$(BLUE)Building for ARM...$(RESET)"
	cd $(RUST_DIR) && cargo build --target $(ARM_TARGET) --profile $(BUILD_TYPE)
	@echo "$(GREEN)ARM build completed$(RESET)"

.PHONY: build-riscv
build-riscv: ## Build for RISC-V
	@echo "$(BLUE)Building for RISC-V...$(RESET)"
	cd $(RUST_DIR) && cargo build --target $(RISCV_TARGET) --profile $(BUILD_TYPE)
	@echo "$(GREEN)RISC-V build completed$(RESET)"

# Real-time optimized builds
.PHONY: build-realtime
build-realtime: ## Build with real-time optimizations
	@echo "$(BLUE)Building with real-time optimizations...$(RESET)"
	cd $(RUST_DIR) && cargo build --profile realtime
	cd $(CPP_BUILD_DIR) && cmake -DCMAKE_BUILD_TYPE=Realtime .. && ninja
	@echo "$(GREEN)Real-time optimized build completed$(RESET)"

# Testing targets
.PHONY: test
test: test-rust test-cpp test-latency ## Run all tests
	@echo "$(GREEN)All tests completed$(RESET)"

.PHONY: test-rust
test-rust: ## Run Rust unit tests
	@echo "$(BLUE)Running Rust unit tests...$(RESET)"
	cd $(RUST_DIR) && cargo test --profile $(BUILD_TYPE)
	@echo "$(GREEN)Rust tests completed$(RESET)"

.PHONY: test-cpp
test-cpp: ## Run C++ unit tests
	@echo "$(BLUE)Running C++ unit tests...$(RESET)"
	cd $(CPP_BUILD_DIR) && ninja test
	@echo "$(GREEN)C++ tests completed$(RESET)"

.PHONY: test-latency
test-latency: ## Run real-time latency tests
	@echo "$(BLUE)Running real-time latency tests...$(RESET)"
	cd $(RUST_DIR) && cargo test --profile $(BUILD_TYPE) latency_tests
	@echo "$(GREEN)Latency tests completed$(RESET)"

.PHONY: test-hardware
test-hardware: ## Run hardware-in-the-loop tests
	@echo "$(BLUE)Running hardware tests...$(RESET)"
	@if [ "$(ENABLE_RT)" = "true" ]; then \
		echo "$(YELLOW)Running with real-time priority $(RT_PRIORITY)$(RESET)"; \
		sudo chrt -f $(RT_PRIORITY) $(RUST_DIR)/target/$(BUILD_TYPE)/control-kernel --realtime --cpu-affinity $(CPU_AFFINITY); \
	else \
		$(RUST_DIR)/target/$(BUILD_TYPE)/control-kernel; \
	fi
	@echo "$(GREEN)Hardware tests completed$(RESET)"

.PHONY: test-safety
test-safety: ## Run safety system tests
	@echo "$(BLUE)Running safety system tests...$(RESET)"
	cd $(RUST_DIR) && cargo test --profile $(BUILD_TYPE) safety
	@echo "$(GREEN)Safety tests completed$(RESET)"

.PHONY: test-stress
test-stress: ## Run stress tests
	@echo "$(BLUE)Running stress tests...$(RESET)"
	cd $(RUST_DIR) && cargo test --profile $(BUILD_TYPE) --release stress_tests -- --ignored
	@echo "$(GREEN)Stress tests completed$(RESET)"

# Benchmarking targets
.PHONY: benchmark
benchmark: benchmark-latency benchmark-throughput ## Run all benchmarks
	@echo "$(GREEN)All benchmarks completed$(RESET)"

.PHONY: benchmark-latency
benchmark-latency: ## Run latency benchmarks
	@echo "$(BLUE)Running latency benchmarks...$(RESET)"
	cd $(RUST_DIR) && cargo bench --bench control_loop_latency
	cd $(RUST_DIR) && cargo bench --bench inference_latency
	@echo "$(GREEN)Latency benchmarks completed$(RESET)"

.PHONY: benchmark-throughput
benchmark-throughput: ## Run throughput benchmarks
	@echo "$(BLUE)Running throughput benchmarks...$(RESET)"
	cd $(RUST_DIR) && cargo bench --bench throughput
	@echo "$(GREEN)Throughput benchmarks completed$(RESET)"

.PHONY: benchmark-memory
benchmark-memory: ## Run memory usage benchmarks
	@echo "$(BLUE)Running memory benchmarks...$(RESET)"
	cd $(RUST_DIR) && cargo bench --bench memory_usage
	@echo "$(GREEN)Memory benchmarks completed$(RESET)"

# Runtime targets
.PHONY: start-realtime
start-realtime: build-realtime ## Start real-time control system
	@echo "$(BLUE)Starting real-time control system...$(RESET)"
	sudo chrt -f $(RT_PRIORITY) $(RUST_DIR)/target/realtime/control-kernel \
		--realtime \
		--cpu-affinity $(CPU_AFFINITY) \
		--rt-priority $(RT_PRIORITY) \
		--config $(CONFIGS_DIR)/control_config.yaml

.PHONY: start-dev
start-dev: build ## Start in development mode
	@echo "$(BLUE)Starting in development mode...$(RESET)"
	$(RUST_DIR)/target/$(BUILD_TYPE)/control-kernel \
		--config $(CONFIGS_DIR)/control_config.yaml \
		--log-level debug

.PHONY: start-simulation
start-simulation: build ## Start in simulation mode
	@echo "$(BLUE)Starting in simulation mode...$(RESET)"
	$(RUST_DIR)/target/$(BUILD_TYPE)/control-kernel \
		--config $(CONFIGS_DIR)/control_config.yaml \
		--disable-safety

# Monitoring and debugging
.PHONY: monitor
monitor: ## Monitor real-time performance
	@echo "$(BLUE)Monitoring real-time performance...$(RESET)"
	watch -n 1 'cat /proc/interrupts | head -20; echo; ps -eo pid,pri,ni,rtprio,comm | grep control-kernel'

.PHONY: trace
trace: ## Trace system calls and performance
	@echo "$(BLUE)Tracing system performance...$(RESET)"
	sudo perf record -g $(RUST_DIR)/target/$(BUILD_TYPE)/control-kernel --config $(CONFIGS_DIR)/control_config.yaml
	sudo perf report

.PHONY: profile
profile: ## Profile CPU usage
	@echo "$(BLUE)Profiling CPU usage...$(RESET)"
	cd $(RUST_DIR) && cargo flamegraph --bin control-kernel

# Deployment targets
.PHONY: package
package: build ## Package for deployment
	@echo "$(BLUE)Packaging for deployment...$(RESET)"
	mkdir -p dist/bin dist/lib dist/configs
	cp $(RUST_DIR)/target/$(BUILD_TYPE)/control-kernel dist/bin/
	cp $(CPP_BUILD_DIR)/libasi-inference-accelerator.so dist/lib/
	cp -r $(CONFIGS_DIR)/* dist/configs/
	tar -czf asi-core-runtime-control.tar.gz dist/
	@echo "$(GREEN)Package created: asi-core-runtime-control.tar.gz$(RESET)"

.PHONY: install-systemd
install-systemd: ## Install systemd service
	@echo "$(BLUE)Installing systemd service...$(RESET)"
	sudo cp scripts/asi-control.service /etc/systemd/system/
	sudo systemctl daemon-reload
	sudo systemctl enable asi-control
	@echo "$(GREEN)Systemd service installed$(RESET)"

# Cleanup targets
.PHONY: clean
clean: clean-rust clean-cpp ## Clean all build artifacts
	@echo "$(GREEN)All build artifacts cleaned$(RESET)"

.PHONY: clean-rust
clean-rust: ## Clean Rust build artifacts
	@echo "$(BLUE)Cleaning Rust artifacts...$(RESET)"
	cd $(RUST_DIR) && cargo clean

.PHONY: clean-cpp
clean-cpp: ## Clean C++ build artifacts
	@echo "$(BLUE)Cleaning C++ artifacts...$(RESET)"
	rm -rf $(CPP_BUILD_DIR)

.PHONY: clean-all
clean-all: clean ## Clean everything including dependencies
	@echo "$(BLUE)Cleaning all artifacts and dependencies...$(RESET)"
	cd $(RUST_DIR) && cargo clean --release
	rm -rf dist/ *.tar.gz

# Documentation targets
.PHONY: docs
docs: docs-rust docs-cpp ## Generate all documentation
	@echo "$(GREEN)Documentation generated$(RESET)"

.PHONY: docs-rust
docs-rust: ## Generate Rust documentation
	@echo "$(BLUE)Generating Rust documentation...$(RESET)"
	cd $(RUST_DIR) && cargo doc --no-deps --open

.PHONY: docs-cpp
docs-cpp: ## Generate C++ documentation
	@echo "$(BLUE)Generating C++ documentation...$(RESET)"
	cd $(CPP_DIR) && doxygen Doxyfile

# Linting and formatting
.PHONY: lint
lint: lint-rust lint-cpp ## Run all linters
	@echo "$(GREEN)All linting completed$(RESET)"

.PHONY: lint-rust
lint-rust: ## Lint Rust code
	@echo "$(BLUE)Linting Rust code...$(RESET)"
	cd $(RUST_DIR) && cargo clippy -- -D warnings

.PHONY: lint-cpp
lint-cpp: ## Lint C++ code
	@echo "$(BLUE)Linting C++ code...$(RESET)"
	find $(CPP_DIR) -name "*.cpp" -o -name "*.hpp" | xargs clang-tidy

.PHONY: format
format: format-rust format-cpp ## Format all code
	@echo "$(GREEN)All code formatted$(RESET)"

.PHONY: format-rust
format-rust: ## Format Rust code
	@echo "$(BLUE)Formatting Rust code...$(RESET)"
	cd $(RUST_DIR) && cargo fmt

.PHONY: format-cpp
format-cpp: ## Format C++ code
	@echo "$(BLUE)Formatting C++ code...$(RESET)"
	find $(CPP_DIR) -name "*.cpp" -o -name "*.hpp" | xargs clang-format -i

# Security and validation
.PHONY: security-audit
security-audit: ## Run security audit
	@echo "$(BLUE)Running security audit...$(RESET)"
	cd $(RUST_DIR) && cargo audit
	@echo "$(GREEN)Security audit completed$(RESET)"

.PHONY: validate-config
validate-config: ## Validate configuration files
	@echo "$(BLUE)Validating configuration files...$(RESET)"
	@for config in $(CONFIGS_DIR)/*.yaml; do \
		echo "Validating $$config"; \
		python3 -c "import yaml; yaml.safe_load(open('$$config'))" || exit 1; \
	done
	@echo "$(GREEN)Configuration validation completed$(RESET)"

# System information
.PHONY: info
info: ## Show system and build information
	@echo "$(CYAN)ASI Core Runtime & Real-Time Control - System Information$(RESET)"
	@echo "========================================================"
	@echo "Build type: $(BUILD_TYPE)"
	@echo "Rust target: $(RUST_TARGET)"
	@echo "Real-time enabled: $(ENABLE_RT)"
	@echo "RT priority: $(RT_PRIORITY)"
	@echo "CPU affinity: $(CPU_AFFINITY)"
	@echo ""
	@echo "System Information:"
	@echo "  OS: $$(uname -s)"
	@echo "  Kernel: $$(uname -r)"
	@echo "  Architecture: $$(uname -m)"
	@echo "  CPU cores: $$(nproc)"
	@echo "  Memory: $$(free -h | awk '/^Mem:/ {print $$2}')"
	@echo ""
	@echo "Rust Information:"
	@rustc --version
	@cargo --version
	@echo ""
	@echo "C++ Information:"
	@g++ --version | head -1
	@cmake --version | head -1
