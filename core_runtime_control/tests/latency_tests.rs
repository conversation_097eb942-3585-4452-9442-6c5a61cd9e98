/*!
 * Real-Time Latency Tests
 * =======================
 * 
 * Comprehensive test suite for validating real-time latency requirements
 * and deterministic behavior of the control kernel.
 */

use std::time::{Duration, Instant};
use std::sync::Arc;
use std::collections::VecDeque;
use tokio::time::{interval, MissedTickBehavior};
use parking_lot::RwLock;

use asi_control_kernel::control::ControlKernel;
use asi_control_kernel::safety::SafetySystem;
use asi_control_kernel::hardware::HardwareManager;
use asi_control_kernel::utils::{Config, MetricsCollector};

/// Latency measurement result
#[derive(Debug, Clone)]
pub struct LatencyMeasurement {
    pub timestamp: Instant,
    pub latency_us: u64,
    pub jitter_us: i64,
    pub deadline_met: bool,
    pub cycle_count: u64,
}

/// Latency test configuration
#[derive(Debug, <PERSON><PERSON>)]
pub struct LatencyTestConfig {
    pub test_duration_ms: u64,
    pub control_frequency_hz: f64,
    pub max_latency_us: u64,
    pub max_jitter_us: u64,
    pub deadline_miss_threshold: f64, // Percentage
    pub sample_size: usize,
}

impl Default for LatencyTestConfig {
    fn default() -> Self {
        Self {
            test_duration_ms: 10000,  // 10 seconds
            control_frequency_hz: 1000.0,  // 1kHz
            max_latency_us: 1000,     // 1ms
            max_jitter_us: 10,        // 10μs
            deadline_miss_threshold: 0.1,  // 0.1%
            sample_size: 10000,
        }
    }
}

/// Latency test results
#[derive(Debug)]
pub struct LatencyTestResults {
    pub total_samples: usize,
    pub min_latency_us: u64,
    pub max_latency_us: u64,
    pub avg_latency_us: f64,
    pub median_latency_us: u64,
    pub p95_latency_us: u64,
    pub p99_latency_us: u64,
    pub p999_latency_us: u64,
    pub max_jitter_us: u64,
    pub avg_jitter_us: f64,
    pub deadline_misses: usize,
    pub deadline_miss_rate: f64,
    pub test_passed: bool,
    pub measurements: Vec<LatencyMeasurement>,
}

/// Real-time latency tester
pub struct LatencyTester {
    config: LatencyTestConfig,
    measurements: Arc<RwLock<VecDeque<LatencyMeasurement>>>,
    cycle_count: Arc<RwLock<u64>>,
}

impl LatencyTester {
    /// Create a new latency tester
    pub fn new(config: LatencyTestConfig) -> Self {
        Self {
            config,
            measurements: Arc::new(RwLock::new(VecDeque::new())),
            cycle_count: Arc::new(RwLock::new(0)),
        }
    }

    /// Run control loop latency test
    pub async fn test_control_loop_latency(&self) -> LatencyTestResults {
        println!("Starting control loop latency test...");
        println!("Configuration: {:?}", self.config);

        let period = Duration::from_secs_f64(1.0 / self.config.control_frequency_hz);
        let expected_period_us = period.as_micros() as u64;
        let test_duration = Duration::from_millis(self.config.test_duration_ms);
        let test_start = Instant::now();

        let mut interval = interval(period);
        interval.set_missed_tick_behavior(MissedTickBehavior::Skip);

        let mut last_cycle_time = Instant::now();
        let mut cycle_count = 0u64;

        while test_start.elapsed() < test_duration {
            let cycle_start = Instant::now();
            
            // Simulate control loop work
            self.simulate_control_work().await;
            
            let cycle_end = Instant::now();
            let latency_us = cycle_end.duration_since(cycle_start).as_micros() as u64;
            
            // Calculate jitter
            let actual_period_us = cycle_start.duration_since(last_cycle_time).as_micros() as u64;
            let jitter_us = actual_period_us as i64 - expected_period_us as i64;
            
            // Check deadline
            let deadline_met = latency_us <= self.config.max_latency_us;
            
            // Record measurement
            let measurement = LatencyMeasurement {
                timestamp: cycle_start,
                latency_us,
                jitter_us,
                deadline_met,
                cycle_count,
            };
            
            {
                let mut measurements = self.measurements.write();
                measurements.push_back(measurement);
                
                // Limit buffer size
                if measurements.len() > self.config.sample_size {
                    measurements.pop_front();
                }
            }
            
            last_cycle_time = cycle_start;
            cycle_count += 1;
            
            interval.tick().await;
        }

        self.analyze_measurements()
    }

    /// Test inference latency with hardware acceleration
    pub async fn test_inference_latency(&self) -> LatencyTestResults {
        println!("Starting inference latency test...");
        
        // This would integrate with the C++ inference accelerator
        // For now, simulate inference workload
        
        let test_duration = Duration::from_millis(self.config.test_duration_ms);
        let test_start = Instant::now();
        let mut cycle_count = 0u64;

        while test_start.elapsed() < test_duration {
            let inference_start = Instant::now();
            
            // Simulate neural network inference
            self.simulate_inference_work().await;
            
            let inference_end = Instant::now();
            let latency_us = inference_end.duration_since(inference_start).as_micros() as u64;
            
            let deadline_met = latency_us <= 100; // 100μs deadline for inference
            
            let measurement = LatencyMeasurement {
                timestamp: inference_start,
                latency_us,
                jitter_us: 0, // Not applicable for inference
                deadline_met,
                cycle_count,
            };
            
            {
                let mut measurements = self.measurements.write();
                measurements.push_back(measurement);
                
                if measurements.len() > self.config.sample_size {
                    measurements.pop_front();
                }
            }
            
            cycle_count += 1;
            
            // Run inference at high frequency for stress testing
            tokio::time::sleep(Duration::from_micros(50)).await;
        }

        self.analyze_measurements()
    }

    /// Test communication latency with Decision Engine
    pub async fn test_communication_latency(&self) -> LatencyTestResults {
        println!("Starting communication latency test...");
        
        let test_duration = Duration::from_millis(self.config.test_duration_ms);
        let test_start = Instant::now();
        let mut cycle_count = 0u64;

        while test_start.elapsed() < test_duration {
            let comm_start = Instant::now();
            
            // Simulate gRPC communication
            self.simulate_communication_work().await;
            
            let comm_end = Instant::now();
            let latency_us = comm_end.duration_since(comm_start).as_micros() as u64;
            
            let deadline_met = latency_us <= 500; // 500μs deadline for communication
            
            let measurement = LatencyMeasurement {
                timestamp: comm_start,
                latency_us,
                jitter_us: 0,
                deadline_met,
                cycle_count,
            };
            
            {
                let mut measurements = self.measurements.write();
                measurements.push_back(measurement);
                
                if measurements.len() > self.config.sample_size {
                    measurements.pop_front();
                }
            }
            
            cycle_count += 1;
            
            // Test communication at 100Hz
            tokio::time::sleep(Duration::from_millis(10)).await;
        }

        self.analyze_measurements()
    }

    /// Test end-to-end latency from sensor input to actuator output
    pub async fn test_end_to_end_latency(&self) -> LatencyTestResults {
        println!("Starting end-to-end latency test...");
        
        let test_duration = Duration::from_millis(self.config.test_duration_ms);
        let test_start = Instant::now();
        let mut cycle_count = 0u64;

        while test_start.elapsed() < test_duration {
            let e2e_start = Instant::now();
            
            // Simulate complete control pipeline:
            // 1. Sensor reading
            self.simulate_sensor_reading().await;
            
            // 2. Inference
            self.simulate_inference_work().await;
            
            // 3. Control computation
            self.simulate_control_work().await;
            
            // 4. Actuator output
            self.simulate_actuator_output().await;
            
            let e2e_end = Instant::now();
            let latency_us = e2e_end.duration_since(e2e_start).as_micros() as u64;
            
            let deadline_met = latency_us <= self.config.max_latency_us;
            
            let measurement = LatencyMeasurement {
                timestamp: e2e_start,
                latency_us,
                jitter_us: 0,
                deadline_met,
                cycle_count,
            };
            
            {
                let mut measurements = self.measurements.write();
                measurements.push_back(measurement);
                
                if measurements.len() > self.config.sample_size {
                    measurements.pop_front();
                }
            }
            
            cycle_count += 1;
            
            // Run at control frequency
            let period = Duration::from_secs_f64(1.0 / self.config.control_frequency_hz);
            tokio::time::sleep(period).await;
        }

        self.analyze_measurements()
    }

    /// Analyze collected measurements and generate results
    fn analyze_measurements(&self) -> LatencyTestResults {
        let measurements = self.measurements.read();
        let measurements_vec: Vec<LatencyMeasurement> = measurements.iter().cloned().collect();
        
        if measurements_vec.is_empty() {
            return LatencyTestResults {
                total_samples: 0,
                min_latency_us: 0,
                max_latency_us: 0,
                avg_latency_us: 0.0,
                median_latency_us: 0,
                p95_latency_us: 0,
                p99_latency_us: 0,
                p999_latency_us: 0,
                max_jitter_us: 0,
                avg_jitter_us: 0.0,
                deadline_misses: 0,
                deadline_miss_rate: 0.0,
                test_passed: false,
                measurements: measurements_vec,
            };
        }

        // Sort latencies for percentile calculations
        let mut latencies: Vec<u64> = measurements_vec.iter().map(|m| m.latency_us).collect();
        latencies.sort_unstable();

        let total_samples = latencies.len();
        let min_latency_us = latencies[0];
        let max_latency_us = latencies[total_samples - 1];
        let avg_latency_us = latencies.iter().sum::<u64>() as f64 / total_samples as f64;
        
        let median_latency_us = latencies[total_samples / 2];
        let p95_latency_us = latencies[(total_samples as f64 * 0.95) as usize];
        let p99_latency_us = latencies[(total_samples as f64 * 0.99) as usize];
        let p999_latency_us = latencies[(total_samples as f64 * 0.999) as usize];

        // Calculate jitter statistics
        let jitters: Vec<u64> = measurements_vec.iter()
            .map(|m| m.jitter_us.abs() as u64)
            .collect();
        let max_jitter_us = jitters.iter().max().copied().unwrap_or(0);
        let avg_jitter_us = jitters.iter().sum::<u64>() as f64 / total_samples as f64;

        // Calculate deadline miss rate
        let deadline_misses = measurements_vec.iter()
            .filter(|m| !m.deadline_met)
            .count();
        let deadline_miss_rate = deadline_misses as f64 / total_samples as f64 * 100.0;

        // Determine if test passed
        let test_passed = 
            max_latency_us <= self.config.max_latency_us &&
            max_jitter_us <= self.config.max_jitter_us &&
            deadline_miss_rate <= self.config.deadline_miss_threshold;

        LatencyTestResults {
            total_samples,
            min_latency_us,
            max_latency_us,
            avg_latency_us,
            median_latency_us,
            p95_latency_us,
            p99_latency_us,
            p999_latency_us,
            max_jitter_us,
            avg_jitter_us,
            deadline_misses,
            deadline_miss_rate,
            test_passed,
            measurements: measurements_vec,
        }
    }

    /// Simulate control loop work
    async fn simulate_control_work(&self) {
        // Simulate PID controller computation
        let start = Instant::now();
        while start.elapsed() < Duration::from_micros(50) {
            // Busy wait to simulate computation
            std::hint::spin_loop();
        }
    }

    /// Simulate neural network inference
    async fn simulate_inference_work(&self) {
        // Simulate neural network forward pass
        let start = Instant::now();
        while start.elapsed() < Duration::from_micros(30) {
            std::hint::spin_loop();
        }
    }

    /// Simulate communication work
    async fn simulate_communication_work(&self) {
        // Simulate gRPC call overhead
        let start = Instant::now();
        while start.elapsed() < Duration::from_micros(20) {
            std::hint::spin_loop();
        }
    }

    /// Simulate sensor reading
    async fn simulate_sensor_reading(&self) {
        let start = Instant::now();
        while start.elapsed() < Duration::from_micros(10) {
            std::hint::spin_loop();
        }
    }

    /// Simulate actuator output
    async fn simulate_actuator_output(&self) {
        let start = Instant::now();
        while start.elapsed() < Duration::from_micros(15) {
            std::hint::spin_loop();
        }
    }

    /// Print test results
    pub fn print_results(&self, results: &LatencyTestResults) {
        println!("\n=== Latency Test Results ===");
        println!("Total samples: {}", results.total_samples);
        println!("Min latency: {}μs", results.min_latency_us);
        println!("Max latency: {}μs", results.max_latency_us);
        println!("Avg latency: {:.2}μs", results.avg_latency_us);
        println!("Median latency: {}μs", results.median_latency_us);
        println!("95th percentile: {}μs", results.p95_latency_us);
        println!("99th percentile: {}μs", results.p99_latency_us);
        println!("99.9th percentile: {}μs", results.p999_latency_us);
        println!("Max jitter: {}μs", results.max_jitter_us);
        println!("Avg jitter: {:.2}μs", results.avg_jitter_us);
        println!("Deadline misses: {} ({:.3}%)", results.deadline_misses, results.deadline_miss_rate);
        println!("Test result: {}", if results.test_passed { "PASSED" } else { "FAILED" });
        
        if !results.test_passed {
            println!("\nFailure reasons:");
            if results.max_latency_us > self.config.max_latency_us {
                println!("  - Max latency exceeded: {}μs > {}μs", 
                         results.max_latency_us, self.config.max_latency_us);
            }
            if results.max_jitter_us > self.config.max_jitter_us {
                println!("  - Max jitter exceeded: {}μs > {}μs", 
                         results.max_jitter_us, self.config.max_jitter_us);
            }
            if results.deadline_miss_rate > self.config.deadline_miss_threshold {
                println!("  - Deadline miss rate exceeded: {:.3}% > {:.3}%", 
                         results.deadline_miss_rate, self.config.deadline_miss_threshold);
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_control_loop_latency() {
        let config = LatencyTestConfig {
            test_duration_ms: 1000,  // 1 second for quick test
            control_frequency_hz: 1000.0,
            max_latency_us: 1000,
            max_jitter_us: 50,
            deadline_miss_threshold: 1.0,
            sample_size: 1000,
        };

        let tester = LatencyTester::new(config);
        let results = tester.test_control_loop_latency().await;
        
        tester.print_results(&results);
        
        // Assert basic requirements
        assert!(results.total_samples > 0);
        assert!(results.avg_latency_us > 0.0);
        assert!(results.deadline_miss_rate < 5.0); // Allow up to 5% misses in test environment
    }

    #[tokio::test]
    async fn test_inference_latency() {
        let config = LatencyTestConfig {
            test_duration_ms: 500,
            ..Default::default()
        };

        let tester = LatencyTester::new(config);
        let results = tester.test_inference_latency().await;
        
        tester.print_results(&results);
        
        assert!(results.total_samples > 0);
        assert!(results.max_latency_us < 200); // Should be under 200μs for simulated inference
    }

    #[tokio::test]
    async fn test_end_to_end_latency() {
        let config = LatencyTestConfig {
            test_duration_ms: 1000,
            max_latency_us: 2000, // More lenient for end-to-end
            ..Default::default()
        };

        let tester = LatencyTester::new(config);
        let results = tester.test_end_to_end_latency().await;
        
        tester.print_results(&results);
        
        assert!(results.total_samples > 0);
        // End-to-end should be sum of all components
        assert!(results.avg_latency_us > 100.0); // Should be > individual components
    }
}
