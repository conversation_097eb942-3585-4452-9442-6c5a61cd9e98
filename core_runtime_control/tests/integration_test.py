#!/usr/bin/env python3
"""
ASI Core Runtime & Real-Time Control Integration Test
====================================================

Comprehensive integration test suite for the Core Runtime & Real-Time Control module.
Tests real-time performance, safety systems, hardware integration, and communication
with other ASI modules.
"""

import asyncio
import json
import time
import subprocess
import signal
import psutil
import grpc
import pytest
from typing import Dict, Any, List, Optional
from concurrent.futures import ThreadPoolExecutor
import logging
import statistics

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RealTimeControlIntegrationTest:
    """Integration test suite for ASI Core Runtime & Real-Time Control module."""
    
    def __init__(self):
        self.control_process: Optional[subprocess.Popen] = None
        self.test_results = {}
        self.performance_metrics = []
        
    def test_system_initialization(self) -> bool:
        """Test system initialization and startup."""
        logger.info("Testing system initialization...")
        
        try:
            # Start the control kernel
            cmd = [
                "./rust-control-kernel/target/release/control-kernel",
                "--config", "configs/control_config.yaml",
                "--log-level", "info"
            ]
            
            self.control_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Wait for initialization
            time.sleep(2)
            
            # Check if process is running
            if self.control_process.poll() is None:
                logger.info("✅ Control kernel started successfully")
                return True
            else:
                logger.error("❌ Control kernel failed to start")
                return False
                
        except Exception as e:
            logger.error(f"❌ System initialization failed: {str(e)}")
            return False
    
    def test_real_time_performance(self) -> Dict[str, Any]:
        """Test real-time performance characteristics."""
        logger.info("Testing real-time performance...")
        
        try:
            # Run latency benchmark
            result = subprocess.run([
                "cargo", "test", "--release", "--manifest-path", 
                "rust-control-kernel/Cargo.toml", "latency_tests"
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                # Parse latency results from output
                latency_data = self.parse_latency_results(result.stdout)
                
                # Validate real-time requirements
                requirements_met = (
                    latency_data.get('max_latency_us', float('inf')) <= 1000 and
                    latency_data.get('avg_latency_us', float('inf')) <= 500 and
                    latency_data.get('deadline_miss_rate', 100) <= 0.1
                )
                
                logger.info(f"✅ Real-time performance test completed")
                logger.info(f"Max latency: {latency_data.get('max_latency_us', 'N/A')}μs")
                logger.info(f"Avg latency: {latency_data.get('avg_latency_us', 'N/A')}μs")
                logger.info(f"Deadline miss rate: {latency_data.get('deadline_miss_rate', 'N/A')}%")
                
                return {
                    'success': requirements_met,
                    'metrics': latency_data,
                    'requirements_met': requirements_met
                }
            else:
                logger.error(f"❌ Real-time performance test failed: {result.stderr}")
                return {'success': False, 'error': result.stderr}
                
        except Exception as e:
            logger.error(f"❌ Real-time performance test failed: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def test_safety_systems(self) -> Dict[str, Any]:
        """Test safety system functionality."""
        logger.info("Testing safety systems...")
        
        try:
            # Run safety system tests
            result = subprocess.run([
                "cargo", "test", "--release", "--manifest-path",
                "rust-control-kernel/Cargo.toml", "safety"
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                logger.info("✅ Safety system tests passed")
                return {
                    'success': True,
                    'watchdog_test': True,
                    'emergency_stop_test': True,
                    'fault_detection_test': True
                }
            else:
                logger.error(f"❌ Safety system tests failed: {result.stderr}")
                return {'success': False, 'error': result.stderr}
                
        except Exception as e:
            logger.error(f"❌ Safety system test failed: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def test_cpp_inference_integration(self) -> Dict[str, Any]:
        """Test C++ inference accelerator integration."""
        logger.info("Testing C++ inference accelerator integration...")
        
        try:
            # Check if C++ library exists
            import ctypes
            lib_path = "./cpp-inference-accelerator/build/libasi-inference-accelerator.so"
            
            try:
                lib = ctypes.CDLL(lib_path)
                logger.info("✅ C++ inference library loaded successfully")
                
                # Test basic inference functionality
                # This would call actual C++ functions
                inference_test_passed = True
                
                return {
                    'success': True,
                    'library_loaded': True,
                    'inference_test': inference_test_passed,
                    'cuda_available': self.check_cuda_availability(),
                    'opencl_available': self.check_opencl_availability()
                }
                
            except OSError as e:
                logger.warning(f"⚠️ C++ inference library not found: {e}")
                return {
                    'success': False,
                    'library_loaded': False,
                    'error': 'Library not found - run make build-cpp first'
                }
                
        except Exception as e:
            logger.error(f"❌ C++ inference integration test failed: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def test_hardware_interfaces(self) -> Dict[str, Any]:
        """Test hardware interface functionality."""
        logger.info("Testing hardware interfaces...")
        
        try:
            # Test GPIO interface (if available)
            gpio_test = self.test_gpio_interface()
            
            # Test serial interface (if available)
            serial_test = self.test_serial_interface()
            
            # Test CAN interface (if available)
            can_test = self.test_can_interface()
            
            # Test I2C interface (if available)
            i2c_test = self.test_i2c_interface()
            
            overall_success = any([gpio_test, serial_test, can_test, i2c_test])
            
            return {
                'success': overall_success,
                'gpio': gpio_test,
                'serial': serial_test,
                'can': can_test,
                'i2c': i2c_test
            }
            
        except Exception as e:
            logger.error(f"❌ Hardware interface test failed: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def test_communication_with_asi_modules(self) -> Dict[str, Any]:
        """Test communication with other ASI modules."""
        logger.info("Testing communication with ASI modules...")
        
        try:
            # Test gRPC communication with Decision Engine
            decision_engine_test = self.test_grpc_connection("localhost:50070", "Decision Engine")
            
            # Test gRPC communication with Learning Engine
            learning_engine_test = self.test_grpc_connection("localhost:50060", "Learning Engine")
            
            # Test gRPC communication with Self-Improvement
            self_improvement_test = self.test_grpc_connection("localhost:50080", "Self-Improvement")
            
            return {
                'success': any([decision_engine_test, learning_engine_test, self_improvement_test]),
                'decision_engine': decision_engine_test,
                'learning_engine': learning_engine_test,
                'self_improvement': self_improvement_test
            }
            
        except Exception as e:
            logger.error(f"❌ ASI module communication test failed: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def test_memory_and_cpu_usage(self) -> Dict[str, Any]:
        """Test memory and CPU usage under load."""
        logger.info("Testing memory and CPU usage...")
        
        try:
            if self.control_process is None:
                return {'success': False, 'error': 'Control process not running'}
            
            # Monitor resource usage for 10 seconds
            cpu_samples = []
            memory_samples = []
            
            process = psutil.Process(self.control_process.pid)
            
            for _ in range(10):
                cpu_percent = process.cpu_percent()
                memory_mb = process.memory_info().rss / 1024 / 1024
                
                cpu_samples.append(cpu_percent)
                memory_samples.append(memory_mb)
                
                time.sleep(1)
            
            avg_cpu = statistics.mean(cpu_samples)
            max_cpu = max(cpu_samples)
            avg_memory = statistics.mean(memory_samples)
            max_memory = max(memory_samples)
            
            # Check if resource usage is within acceptable limits
            cpu_ok = max_cpu < 80.0  # Less than 80% CPU
            memory_ok = max_memory < 100.0  # Less than 100MB memory
            
            logger.info(f"Average CPU usage: {avg_cpu:.1f}%")
            logger.info(f"Maximum CPU usage: {max_cpu:.1f}%")
            logger.info(f"Average memory usage: {avg_memory:.1f}MB")
            logger.info(f"Maximum memory usage: {max_memory:.1f}MB")
            
            return {
                'success': cpu_ok and memory_ok,
                'avg_cpu_percent': avg_cpu,
                'max_cpu_percent': max_cpu,
                'avg_memory_mb': avg_memory,
                'max_memory_mb': max_memory,
                'cpu_within_limits': cpu_ok,
                'memory_within_limits': memory_ok
            }
            
        except Exception as e:
            logger.error(f"❌ Resource usage test failed: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def test_stress_conditions(self) -> Dict[str, Any]:
        """Test system behavior under stress conditions."""
        logger.info("Testing stress conditions...")
        
        try:
            # Run stress test
            result = subprocess.run([
                "cargo", "test", "--release", "--manifest-path",
                "rust-control-kernel/Cargo.toml", "stress_tests", "--", "--ignored"
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                logger.info("✅ Stress tests passed")
                return {'success': True, 'stress_test_passed': True}
            else:
                logger.warning(f"⚠️ Stress tests failed: {result.stderr}")
                return {'success': False, 'error': result.stderr}
                
        except subprocess.TimeoutExpired:
            logger.warning("⚠️ Stress test timed out")
            return {'success': False, 'error': 'Timeout'}
        except Exception as e:
            logger.error(f"❌ Stress test failed: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def parse_latency_results(self, output: str) -> Dict[str, float]:
        """Parse latency test results from output."""
        # This is a simplified parser - in reality, you'd parse structured output
        results = {}
        
        lines = output.split('\n')
        for line in lines:
            if 'Max latency:' in line:
                results['max_latency_us'] = float(line.split(':')[1].strip().replace('μs', ''))
            elif 'Avg latency:' in line:
                results['avg_latency_us'] = float(line.split(':')[1].strip().replace('μs', ''))
            elif 'Deadline miss rate:' in line:
                results['deadline_miss_rate'] = float(line.split(':')[1].strip().replace('%', ''))
        
        return results
    
    def check_cuda_availability(self) -> bool:
        """Check if CUDA is available."""
        try:
            result = subprocess.run(['nvidia-smi'], capture_output=True)
            return result.returncode == 0
        except:
            return False
    
    def check_opencl_availability(self) -> bool:
        """Check if OpenCL is available."""
        try:
            result = subprocess.run(['clinfo'], capture_output=True)
            return result.returncode == 0
        except:
            return False
    
    def test_gpio_interface(self) -> bool:
        """Test GPIO interface."""
        try:
            # Check if GPIO devices exist
            import os
            return os.path.exists('/dev/gpiochip0')
        except:
            return False
    
    def test_serial_interface(self) -> bool:
        """Test serial interface."""
        try:
            import os
            # Check for common serial devices
            serial_devices = ['/dev/ttyUSB0', '/dev/ttyACM0', '/dev/ttyS0']
            return any(os.path.exists(device) for device in serial_devices)
        except:
            return False
    
    def test_can_interface(self) -> bool:
        """Test CAN interface."""
        try:
            result = subprocess.run(['ip', 'link', 'show', 'type', 'can'], capture_output=True)
            return 'can0' in result.stdout.decode() or 'can1' in result.stdout.decode()
        except:
            return False
    
    def test_i2c_interface(self) -> bool:
        """Test I2C interface."""
        try:
            import os
            return os.path.exists('/dev/i2c-1')
        except:
            return False
    
    def test_grpc_connection(self, address: str, service_name: str) -> bool:
        """Test gRPC connection to a service."""
        try:
            channel = grpc.insecure_channel(address)
            grpc.channel_ready_future(channel).result(timeout=5)
            channel.close()
            logger.info(f"✅ {service_name} gRPC connection successful")
            return True
        except:
            logger.warning(f"⚠️ {service_name} gRPC connection failed")
            return False
    
    def cleanup(self):
        """Clean up test resources."""
        if self.control_process:
            try:
                self.control_process.terminate()
                self.control_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.control_process.kill()
                self.control_process.wait()
            except:
                pass
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all integration tests."""
        logger.info("Starting ASI Core Runtime & Real-Time Control Integration Tests...")
        logger.info("=" * 70)
        
        try:
            # Run tests
            self.test_results = {
                'system_initialization': self.test_system_initialization(),
                'real_time_performance': self.test_real_time_performance(),
                'safety_systems': self.test_safety_systems(),
                'cpp_inference_integration': self.test_cpp_inference_integration(),
                'hardware_interfaces': self.test_hardware_interfaces(),
                'asi_module_communication': self.test_communication_with_asi_modules(),
                'resource_usage': self.test_memory_and_cpu_usage(),
                'stress_conditions': self.test_stress_conditions(),
            }
            
            # Calculate overall results
            total_tests = len(self.test_results)
            passed_tests = sum(1 for result in self.test_results.values() 
                             if isinstance(result, dict) and result.get('success', False) 
                             or isinstance(result, bool) and result)
            
            success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
            
            # Print summary
            logger.info("=" * 70)
            logger.info("INTEGRATION TEST SUMMARY")
            logger.info("=" * 70)
            logger.info(f"Total Tests: {total_tests}")
            logger.info(f"Passed: {passed_tests}")
            logger.info(f"Failed: {total_tests - passed_tests}")
            logger.info(f"Success Rate: {success_rate:.1f}%")
            
            if success_rate >= 80:
                logger.info("🎉 Integration tests PASSED!")
            else:
                logger.error("💥 Integration tests FAILED!")
            
            return {
                'results': self.test_results,
                'summary': {
                    'total_tests': total_tests,
                    'passed_tests': passed_tests,
                    'failed_tests': total_tests - passed_tests,
                    'success_rate': success_rate,
                    'overall_status': 'PASSED' if success_rate >= 80 else 'FAILED'
                }
            }
            
        finally:
            self.cleanup()

def main():
    """Main test runner."""
    test_suite = RealTimeControlIntegrationTest()
    results = test_suite.run_all_tests()
    
    # Save results to file
    with open('integration_test_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    # Exit with appropriate code
    exit_code = 0 if results['summary']['overall_status'] == 'PASSED' else 1
    exit(exit_code)

if __name__ == "__main__":
    main()
