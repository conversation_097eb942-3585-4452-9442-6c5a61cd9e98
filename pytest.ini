[tool:pytest]
# Pytest configuration for ASI System

# Test discovery
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Minimum version
minversion = 6.0

# Add options
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=.
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml:coverage.xml
    --cov-fail-under=80
    --durations=10
    --maxfail=5
    -ra

# Markers
markers =
    unit: Unit tests
    integration: Integration tests  
    e2e: End-to-end tests
    performance: Performance tests
    slow: Slow running tests (>5s)
    kafka: Tests requiring Kafka
    redis: Tests requiring Redis
    grpc: Tests requiring gRPC
    docker: Tests requiring Docker
    gpu: Tests requiring GPU
    
# Test timeout
timeout = 300

# Asyncio mode
asyncio_mode = auto

# Logging
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:tensorflow.*
    ignore::UserWarning:torch.*

# Coverage configuration
[coverage:run]
source = .
omit = 
    */tests/*
    */test_*
    */__pycache__/*
    */venv/*
    */env/*
    */.venv/*
    */node_modules/*
    */target/*
    */build/*
    */dist/*
    setup.py
    conftest.py

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

[coverage:html]
directory = htmlcov

[coverage:xml]
output = coverage.xml
