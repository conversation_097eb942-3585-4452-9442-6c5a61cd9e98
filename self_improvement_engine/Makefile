# ASI Self-Improvement Engine - Build and Deployment Automation
# =============================================================

.PHONY: help build test clean install start stop restart logs status deploy

# Default target
help:
	@echo "ASI Self-Improvement Engine - Available Commands:"
	@echo "  build          - Build all components (Lisp, Python, Julia)"
	@echo "  test           - Run all tests"
	@echo "  clean          - Clean build artifacts"
	@echo "  install        - Install dependencies"
	@echo "  start          - Start all services"
	@echo "  stop           - Stop all services"
	@echo "  restart        - Restart all services"
	@echo "  logs           - Show service logs"
	@echo "  status         - Show service status"
	@echo "  deploy         - Deploy to production"
	@echo "  check-deps     - Check system dependencies"
	@echo "  format         - Format code"
	@echo "  lint           - Run linting"
	@echo "  security       - Run security checks"
	@echo "  improve        - Run improvement cycle"
	@echo "  dashboard      - Start performance dashboard"

# Variables
LISP_DIR = lisp-symbolic-refactor
PYTHON_DIR = python-model-retraining
JULIA_DIR = julia-performance-analytics
GRPC_DIR = grpc-improvement-server
DOCKER_COMPOSE = docker/docker-compose.yml

# Check system dependencies
check-deps:
	@echo "Checking system dependencies..."
	@command -v python3 >/dev/null 2>&1 || { echo "Python 3 is required but not installed."; exit 1; }
	@command -v julia >/dev/null 2>&1 || { echo "Julia is required but not installed."; exit 1; }
	@command -v sbcl >/dev/null 2>&1 || { echo "SBCL (Steel Bank Common Lisp) is required but not installed."; exit 1; }
	@command -v docker >/dev/null 2>&1 || { echo "Docker is required but not installed."; exit 1; }
	@command -v docker-compose >/dev/null 2>&1 || { echo "Docker Compose is required but not installed."; exit 1; }
	@echo "All dependencies are available."

# Install dependencies
install: check-deps
	@echo "Installing dependencies..."
	
	# Python dependencies
	cd $(PYTHON_DIR) && python3 -m pip install -r requirements.txt
	
	# Julia dependencies
	cd $(JULIA_DIR) && julia -e 'using Pkg; Pkg.instantiate()'
	
	# Lisp dependencies (Quicklisp)
	@if [ ! -d "$(HOME)/quicklisp" ]; then \
		echo "Installing Quicklisp..."; \
		curl -O https://beta.quicklisp.org/quicklisp.lisp; \
		sbcl --load quicklisp.lisp --eval '(quicklisp-quickstart:install)' --quit; \
		rm quicklisp.lisp; \
	fi
	
	@echo "Dependencies installed successfully."

# Build all components
build: install
	@echo "Building all components..."
	
	# Build Python self-improvement engine
	@echo "Building Python self-improvement engine..."
	cd $(PYTHON_DIR) && python3 -m pip install -e .
	
	# Build Julia performance analytics
	@echo "Building Julia performance analytics..."
	cd $(JULIA_DIR) && julia -e 'using Pkg; Pkg.build()'
	
	# Compile Lisp symbolic refactoring
	@echo "Compiling Lisp symbolic refactoring..."
	cd $(LISP_DIR) && sbcl --eval '(load "src/load-system.lisp")' --eval '(compile-system)' --quit
	
	# Build gRPC server
	@echo "Building gRPC server..."
	cd $(GRPC_DIR) && python3 -m grpc_tools.protoc -I proto --python_out=src --grpc_python_out=src proto/*.proto
	
	@echo "Build completed successfully."

# Build Docker images
build-docker:
	@echo "Building Docker images..."
	docker-compose -f $(DOCKER_COMPOSE) build
	@echo "Docker images built successfully."

# Run tests
test:
	@echo "Running tests..."
	
	# Python tests
	cd $(PYTHON_DIR) && python3 -m pytest tests/ -v --cov=asi_self_improvement --cov-report=html
	
	# Julia tests
	cd $(JULIA_DIR) && julia -e 'using Pkg; Pkg.test()'
	
	# Lisp tests
	cd $(LISP_DIR) && sbcl --eval '(load "tests/run-tests.lisp")' --quit
	
	# Integration tests
	python3 tests/integration_test.py
	
	@echo "All tests completed."

# Run benchmarks
benchmark:
	@echo "Running performance benchmarks..."
	
	# Python benchmarks
	cd $(PYTHON_DIR) && python3 -m pytest tests/benchmarks/ -v
	
	# Julia benchmarks
	cd $(JULIA_DIR) && julia benchmarks/performance_benchmark.jl
	
	# Lisp benchmarks
	cd $(LISP_DIR) && sbcl --eval '(load "benchmarks/genetic-programming-benchmark.lisp")' --quit
	
	@echo "Benchmarks completed."

# Format code
format:
	@echo "Formatting code..."
	
	# Python formatting
	cd $(PYTHON_DIR) && black src/ tests/
	cd $(PYTHON_DIR) && isort src/ tests/
	
	# Julia formatting (if JuliaFormatter is available)
	@if command -v julia >/dev/null 2>&1; then \
		cd $(JULIA_DIR) && julia -e 'using JuliaFormatter; format("src/")' 2>/dev/null || echo "JuliaFormatter not available"; \
	fi
	
	# Lisp formatting (basic indentation)
	@find $(LISP_DIR) -name "*.lisp" -exec emacs --batch {} --eval '(indent-region (point-min) (point-max))' -f save-buffer \; 2>/dev/null || echo "Emacs not available for Lisp formatting"
	
	@echo "Code formatting completed."

# Run linting
lint:
	@echo "Running linting..."
	
	# Python linting
	cd $(PYTHON_DIR) && flake8 src/ tests/
	cd $(PYTHON_DIR) && mypy src/
	
	# Julia linting (basic syntax check)
	cd $(JULIA_DIR) && julia -e 'using Pkg; Pkg.test("PerformanceAnalytics"; test_args=["--check-syntax"])'
	
	@echo "Linting completed."

# Security checks
security:
	@echo "Running security checks..."
	
	# Python security
	cd $(PYTHON_DIR) && safety check
	cd $(PYTHON_DIR) && bandit -r src/
	
	# Check for secrets in code
	@command -v git-secrets >/dev/null 2>&1 && git secrets --scan || echo "git-secrets not available"
	
	@echo "Security checks completed."

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	
	# Python cleanup
	cd $(PYTHON_DIR) && find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	cd $(PYTHON_DIR) && find . -type f -name "*.pyc" -delete 2>/dev/null || true
	cd $(PYTHON_DIR) && rm -rf build/ dist/ *.egg-info/ .coverage htmlcov/
	
	# Julia cleanup
	cd $(JULIA_DIR) && rm -rf Manifest.toml .julia_cache/
	
	# Lisp cleanup
	cd $(LISP_DIR) && find . -name "*.fasl" -delete 2>/dev/null || true
	cd $(LISP_DIR) && find . -name "*.fas" -delete 2>/dev/null || true
	
	# Docker cleanup
	docker-compose -f $(DOCKER_COMPOSE) down --volumes --remove-orphans
	docker system prune -f
	
	@echo "Cleanup completed."

# Start services
start:
	@echo "Starting ASI Self-Improvement Engine services..."
	docker-compose -f $(DOCKER_COMPOSE) up -d
	@echo "Services started. Use 'make logs' to view logs."

# Stop services
stop:
	@echo "Stopping ASI Self-Improvement Engine services..."
	docker-compose -f $(DOCKER_COMPOSE) down
	@echo "Services stopped."

# Restart services
restart: stop start

# Show logs
logs:
	docker-compose -f $(DOCKER_COMPOSE) logs -f

# Show service status
status:
	@echo "ASI Self-Improvement Engine Service Status:"
	@echo "=========================================="
	docker-compose -f $(DOCKER_COMPOSE) ps
	@echo ""
	@echo "Health Checks:"
	@echo "-------------"
	@curl -s http://localhost:8080/health 2>/dev/null | jq . || echo "Self-Improvement Engine: Not responding"
	@curl -s http://localhost:8090/health 2>/dev/null || echo "Julia Analytics: Not responding"

# Development server
dev:
	@echo "Starting development environment..."
	
	# Start dependencies
	docker-compose -f $(DOCKER_COMPOSE) up -d redis postgres
	
	# Start Python self-improvement engine in development mode
	cd $(PYTHON_DIR) && python3 -m asi_self_improvement.main --config ../configs/self_improvement_config.yaml --debug &
	
	# Start Julia performance analytics
	cd $(JULIA_DIR) && julia -e 'include("src/PerformanceAnalytics.jl"); using .PerformanceAnalytics; start_monitoring()' &
	
	# Start Lisp symbolic refactoring server
	cd $(LISP_DIR) && sbcl --eval '(load "src/server.lisp")' --eval '(start-server)' &
	
	@echo "Development environment started."

# Run improvement cycle
improve:
	@echo "Running self-improvement cycle..."
	cd $(PYTHON_DIR) && python3 -c "
import asyncio
from asi_self_improvement.integration.asi_connector import ASIConnector
from asi_self_improvement.utils.config import Config

async def run_improvement():
    config = Config.load('../configs/self_improvement_config.yaml')
    connector = ASIConnector(config)
    await connector.initialize()
    
    # Collect performance data
    performance_data = await connector.collect_system_performance()
    print(f'Collected performance data: {len(performance_data.metrics)} metrics')
    
    # Identify improvement opportunities
    opportunities = await connector.identify_improvement_opportunities()
    print(f'Identified {len(opportunities)} improvement opportunities')
    
    # Create improvement plan
    if opportunities:
        plan = await connector.create_improvement_plan(opportunities[:3])
        print(f'Created improvement plan: {plan.plan_id}')
    
    await connector.cleanup()

asyncio.run(run_improvement())
"
	@echo "Improvement cycle completed."

# Start performance dashboard
dashboard:
	@echo "Starting performance dashboard..."
	cd $(JULIA_DIR) && julia -e '
	include("src/PerformanceAnalytics.jl")
	using .PerformanceAnalytics
	using HTTP
	
	monitor = PerformanceMonitor()
	start_monitoring(monitor)
	
	# Simple dashboard server
	function dashboard_handler(req)
		data = generate_dashboard_data(monitor)
		return HTTP.Response(200, JSON3.write(data))
	end
	
	HTTP.serve(dashboard_handler, "0.0.0.0", 8090)
	'

# Production deployment
deploy:
	@echo "Deploying to production..."
	
	# Build production images
	docker-compose -f $(DOCKER_COMPOSE) -f docker/docker-compose.prod.yml build
	
	# Deploy to Kubernetes
	kubectl apply -f k8s/
	
	# Wait for deployment
	kubectl rollout status deployment/self-improvement-engine
	
	@echo "Production deployment completed."

# Kubernetes operations
k8s-deploy:
	@echo "Deploying to Kubernetes..."
	kubectl apply -f k8s/namespace.yaml
	kubectl apply -f k8s/
	kubectl rollout status deployment/self-improvement-engine -n asi-system

k8s-status:
	kubectl get pods -n asi-system
	kubectl get services -n asi-system

k8s-logs:
	kubectl logs -f deployment/self-improvement-engine -n asi-system

k8s-clean:
	kubectl delete -f k8s/
	kubectl delete namespace asi-system

# Performance testing
perf-test:
	@echo "Running performance tests..."
	
	# Load testing with genetic algorithms
	cd $(PYTHON_DIR) && python3 tests/performance/genetic_algorithm_benchmark.py
	
	# RLHF performance testing
	cd $(PYTHON_DIR) && python3 tests/performance/rlhf_benchmark.py
	
	# Julia analytics performance
	cd $(JULIA_DIR) && julia benchmarks/analytics_benchmark.jl
	
	@echo "Performance testing completed."

# Database operations
db-migrate:
	@echo "Running database migrations..."
	cd $(PYTHON_DIR) && alembic upgrade head

db-seed:
	@echo "Seeding database with test data..."
	cd $(PYTHON_DIR) && python3 scripts/seed_database.py

db-backup:
	@echo "Creating database backup..."
	docker exec asi-postgres pg_dump -U asi_user asi_improvements > backups/improvements_$(shell date +%Y%m%d_%H%M%S).sql

# Monitoring setup
monitoring-setup:
	@echo "Setting up monitoring stack..."
	docker-compose -f docker/monitoring.yml up -d
	@echo "Monitoring available at:"
	@echo "  Prometheus: http://localhost:9090"
	@echo "  Grafana: http://localhost:3000"
	@echo "  Jaeger: http://localhost:16686"
	@echo "  Julia Dashboard: http://localhost:8090"

# Documentation
docs:
	@echo "Generating documentation..."
	cd $(PYTHON_DIR) && sphinx-build -b html docs/ docs/_build/html/
	cd $(JULIA_DIR) && julia -e 'using Documenter; makedocs()'
	@echo "Documentation generated."

# Interactive development
repl-python:
	cd $(PYTHON_DIR) && python3 -c "
import sys
sys.path.insert(0, 'src')
from asi_self_improvement import *
print('ASI Self-Improvement Engine Python REPL')
print('Available modules: genetic, rlhf, evolution, integration, utils')
"

repl-julia:
	cd $(JULIA_DIR) && julia -i -e 'include("src/PerformanceAnalytics.jl"); using .PerformanceAnalytics'

repl-lisp:
	cd $(LISP_DIR) && sbcl --eval '(load "src/load-system.lisp")' --eval '(in-package :asi-genetic-programming)'

# Release
release:
	@echo "Creating release..."
	
	# Tag version
	git tag -a v$(VERSION) -m "Release version $(VERSION)"
	
	# Build release artifacts
	make build
	make test
	
	# Create release package
	tar -czf asi-self-improvement-engine-$(VERSION).tar.gz \
		$(PYTHON_DIR)/dist/ \
		$(JULIA_DIR)/src/ \
		$(LISP_DIR)/src/ \
		configs/ \
		docker/ \
		k8s/ \
		README.md
	
	@echo "Release $(VERSION) created."

# Quick development setup
quick-start: install build start
	@echo "Quick start completed. Self-Improvement Engine is running!"
	@echo "Access points:"
	@echo "  gRPC API: localhost:50080"
	@echo "  Metrics: http://localhost:8080/metrics"
	@echo "  Julia Dashboard: http://localhost:8090"
	@echo "  Health: http://localhost:8080/health"

# Continuous improvement (runs improvement cycles continuously)
continuous-improve:
	@echo "Starting continuous improvement mode..."
	@while true; do \
		make improve; \
		echo "Waiting 1 hour before next improvement cycle..."; \
		sleep 3600; \
	done
