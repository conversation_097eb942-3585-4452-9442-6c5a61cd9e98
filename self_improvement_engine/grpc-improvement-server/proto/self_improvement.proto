syntax = "proto3";

package asi.self_improvement;

import "google/protobuf/timestamp.proto";
import "google/protobuf/any.proto";
import "google/protobuf/struct.proto";

option go_package = "github.com/asi-system/self-improvement/proto";
option java_package = "com.asi.self_improvement";
option java_outer_classname = "SelfImprovementProto";

// Self-Improvement Engine Service for ASI System
service SelfImprovementService {
  // Start improvement cycle
  rpc StartImprovementCycle(StartImprovementRequest) returns (StartImprovementResponse);
  
  // Stream improvement progress
  rpc StreamImprovementProgress(StreamProgressRequest) returns (stream StreamProgressResponse);
  
  // Submit performance feedback
  rpc SubmitFeedback(FeedbackRequest) returns (FeedbackResponse);
  
  // Get improvement suggestions
  rpc GetImprovementSuggestions(SuggestionsRequest) returns (SuggestionsResponse);
  
  // Apply improvements
  rpc ApplyImprovement(ApplyImprovementRequest) returns (ApplyImprovementResponse);
  
  // Rollback improvements
  rpc RollbackImprovement(RollbackRequest) returns (RollbackResponse);
  
  // Get improvement history
  rpc GetImprovementHistory(HistoryRequest) returns (HistoryResponse);
  
  // Genetic programming operations
  rpc EvolveCode(EvolveCodeRequest) returns (EvolveCodeResponse);
  rpc MutateAST(MutateASTRequest) returns (MutateASTResponse);
  
  // Model retraining operations
  rpc RetrainModel(RetrainModelRequest) returns (RetrainModelResponse);
  rpc OptimizeHyperparameters(OptimizeHyperparametersRequest) returns (OptimizeHyperparametersResponse);
  
  // Performance analytics
  rpc GetPerformanceMetrics(PerformanceMetricsRequest) returns (PerformanceMetricsResponse);
  rpc GeneratePerformanceReport(ReportRequest) returns (ReportResponse);
  
  // Health check
  rpc HealthCheck(HealthCheckRequest) returns (HealthCheckResponse);
}

// Improvement types
enum ImprovementType {
  IMPROVEMENT_TYPE_UNSPECIFIED = 0;
  IMPROVEMENT_TYPE_CODE_REFACTOR = 1;
  IMPROVEMENT_TYPE_MODEL_RETRAIN = 2;
  IMPROVEMENT_TYPE_HYPERPARAMETER_TUNE = 3;
  IMPROVEMENT_TYPE_RULE_OPTIMIZE = 4;
  IMPROVEMENT_TYPE_ARCHITECTURE_EVOLVE = 5;
  IMPROVEMENT_TYPE_PERFORMANCE_OPTIMIZE = 6;
}

// Improvement priority levels
enum ImprovementPriority {
  PRIORITY_UNSPECIFIED = 0;
  PRIORITY_LOW = 1;
  PRIORITY_NORMAL = 2;
  PRIORITY_HIGH = 3;
  PRIORITY_CRITICAL = 4;
}

// Improvement status
enum ImprovementStatus {
  STATUS_UNSPECIFIED = 0;
  STATUS_PENDING = 1;
  STATUS_ANALYZING = 2;
  STATUS_GENERATING = 3;
  STATUS_TESTING = 4;
  STATUS_VALIDATING = 5;
  STATUS_DEPLOYING = 6;
  STATUS_COMPLETED = 7;
  STATUS_FAILED = 8;
  STATUS_ROLLED_BACK = 9;
}

// Start improvement cycle request
message StartImprovementRequest {
  string cycle_id = 1;
  repeated ImprovementType improvement_types = 2;
  ImprovementPriority priority = 3;
  ImprovementConfig config = 4;
  map<string, string> metadata = 5;
  google.protobuf.Timestamp timestamp = 6;
}

// Improvement configuration
message ImprovementConfig {
  // Genetic programming settings
  GeneticProgrammingConfig genetic_config = 1;
  
  // RLHF settings
  RLHFConfig rlhf_config = 2;
  
  // Evolutionary strategies settings
  EvolutionaryConfig evolutionary_config = 3;
  
  // Safety settings
  SafetyConfig safety_config = 4;
  
  // Performance targets
  PerformanceTargets targets = 5;
}

// Genetic programming configuration
message GeneticProgrammingConfig {
  int32 population_size = 1;
  int32 max_generations = 2;
  float mutation_rate = 3;
  float crossover_rate = 4;
  float elitism_rate = 5;
  repeated string allowed_operations = 6;
  int32 max_tree_depth = 7;
  bool enable_bloat_control = 8;
}

// RLHF configuration
message RLHFConfig {
  bool enable_human_feedback = 1;
  int32 feedback_batch_size = 2;
  float learning_rate = 3;
  int32 max_iterations = 4;
  float reward_threshold = 5;
  bool enable_preference_learning = 6;
}

// Evolutionary strategies configuration
message EvolutionaryConfig {
  string strategy_type = 1;  // CMA-ES, ES, etc.
  int32 population_size = 2;
  float sigma = 3;
  int32 max_evaluations = 4;
  bool enable_adaptive_sigma = 5;
}

// Safety configuration
message SafetyConfig {
  bool enable_sandboxing = 1;
  bool require_human_approval = 2;
  float max_performance_degradation = 3;
  int32 rollback_timeout_seconds = 4;
  repeated string safety_checks = 5;
}

// Performance targets
message PerformanceTargets {
  float min_accuracy_improvement = 1;
  float max_latency_increase = 2;
  float min_throughput_improvement = 3;
  float max_memory_increase = 4;
  map<string, float> custom_targets = 5;
}

// Start improvement response
message StartImprovementResponse {
  bool success = 1;
  string cycle_id = 2;
  string error_message = 3;
  google.protobuf.Timestamp estimated_completion = 4;
}

// Stream progress request
message StreamProgressRequest {
  string cycle_id = 1;
  bool include_detailed_logs = 2;
}

// Stream progress response
message StreamProgressResponse {
  string cycle_id = 1;
  ImprovementStatus status = 2;
  float progress_percentage = 3;
  string current_phase = 4;
  repeated ImprovementResult intermediate_results = 5;
  string message = 6;
  google.protobuf.Timestamp timestamp = 7;
}

// Feedback request
message FeedbackRequest {
  string improvement_id = 1;
  FeedbackType feedback_type = 2;
  FeedbackData feedback_data = 3;
  string user_id = 4;
  map<string, string> metadata = 5;
}

// Feedback types
enum FeedbackType {
  FEEDBACK_TYPE_UNSPECIFIED = 0;
  FEEDBACK_TYPE_PERFORMANCE = 1;
  FEEDBACK_TYPE_QUALITY = 2;
  FEEDBACK_TYPE_PREFERENCE = 3;
  FEEDBACK_TYPE_SAFETY = 4;
  FEEDBACK_TYPE_USABILITY = 5;
}

// Feedback data
message FeedbackData {
  float rating = 1;  // 0.0 to 1.0
  string comment = 2;
  map<string, float> detailed_scores = 3;
  repeated string tags = 4;
  google.protobuf.Any additional_data = 5;
}

// Feedback response
message FeedbackResponse {
  bool success = 1;
  string feedback_id = 2;
  string error_message = 3;
}

// Improvement suggestions request
message SuggestionsRequest {
  repeated ImprovementType improvement_types = 1;
  PerformanceMetrics current_metrics = 2;
  int32 max_suggestions = 3;
  bool include_rationale = 4;
}

// Improvement suggestions response
message SuggestionsResponse {
  repeated ImprovementSuggestion suggestions = 1;
  string analysis_summary = 2;
}

// Improvement suggestion
message ImprovementSuggestion {
  string suggestion_id = 1;
  ImprovementType improvement_type = 2;
  string description = 3;
  float expected_benefit = 4;
  float implementation_cost = 5;
  float risk_level = 6;
  string rationale = 7;
  map<string, google.protobuf.Any> parameters = 8;
}

// Apply improvement request
message ApplyImprovementRequest {
  string suggestion_id = 1;
  bool enable_gradual_rollout = 2;
  float rollout_percentage = 3;
  bool require_validation = 4;
}

// Apply improvement response
message ApplyImprovementResponse {
  bool success = 1;
  string improvement_id = 2;
  string deployment_id = 3;
  string error_message = 4;
  google.protobuf.Timestamp estimated_completion = 5;
}

// Improvement result
message ImprovementResult {
  string improvement_id = 1;
  ImprovementType improvement_type = 2;
  ImprovementStatus status = 3;
  PerformanceMetrics before_metrics = 4;
  PerformanceMetrics after_metrics = 5;
  float improvement_score = 6;
  string description = 7;
  repeated string changes_made = 8;
  google.protobuf.Timestamp timestamp = 9;
}

// Performance metrics
message PerformanceMetrics {
  float accuracy = 1;
  float precision = 2;
  float recall = 3;
  float f1_score = 4;
  int64 latency_ms = 5;
  int64 throughput_rps = 6;
  int64 memory_usage_mb = 7;
  float cpu_utilization = 8;
  float gpu_utilization = 9;
  map<string, float> custom_metrics = 10;
  google.protobuf.Timestamp timestamp = 11;
}

// Evolve code request
message EvolveCodeRequest {
  string code_id = 1;
  string source_code = 2;
  string language = 3;
  GeneticProgrammingConfig config = 4;
  repeated string fitness_criteria = 5;
}

// Evolve code response
message EvolveCodeResponse {
  bool success = 1;
  string evolved_code = 2;
  float fitness_score = 3;
  int32 generations_evolved = 4;
  string error_message = 5;
  repeated string evolution_log = 6;
}

// Mutate AST request
message MutateASTRequest {
  string ast_representation = 1;
  string language = 2;
  float mutation_rate = 3;
  repeated string allowed_mutations = 4;
}

// Mutate AST response
message MutateASTResponse {
  bool success = 1;
  string mutated_ast = 2;
  repeated string mutations_applied = 3;
  string error_message = 4;
}

// Retrain model request
message RetrainModelRequest {
  string model_id = 1;
  string model_type = 2;
  RetrainingConfig config = 3;
  repeated TrainingData training_data = 4;
}

// Retraining configuration
message RetrainingConfig {
  float learning_rate = 1;
  int32 epochs = 2;
  int32 batch_size = 3;
  string optimizer = 4;
  bool use_early_stopping = 5;
  float validation_split = 6;
  map<string, google.protobuf.Any> hyperparameters = 7;
}

// Training data
message TrainingData {
  google.protobuf.Any input_data = 1;
  google.protobuf.Any target_data = 2;
  float weight = 3;
  map<string, string> metadata = 4;
}

// Retrain model response
message RetrainModelResponse {
  bool success = 1;
  string job_id = 2;
  string new_model_version = 3;
  PerformanceMetrics training_metrics = 4;
  string error_message = 5;
}

// Optimize hyperparameters request
message OptimizeHyperparametersRequest {
  string model_id = 1;
  HyperparameterSpace search_space = 2;
  OptimizationConfig optimization_config = 3;
}

// Hyperparameter search space
message HyperparameterSpace {
  map<string, ParameterRange> parameters = 1;
}

// Parameter range
message ParameterRange {
  ParameterType type = 1;
  google.protobuf.Any min_value = 2;
  google.protobuf.Any max_value = 3;
  repeated google.protobuf.Any discrete_values = 4;
}

// Parameter types
enum ParameterType {
  PARAMETER_TYPE_UNSPECIFIED = 0;
  PARAMETER_TYPE_FLOAT = 1;
  PARAMETER_TYPE_INT = 2;
  PARAMETER_TYPE_CATEGORICAL = 3;
  PARAMETER_TYPE_BOOLEAN = 4;
}

// Optimization configuration
message OptimizationConfig {
  string algorithm = 1;  // random, grid, bayesian, etc.
  int32 max_trials = 2;
  int32 max_parallel_trials = 3;
  string objective_metric = 4;
  string objective_direction = 5;  // minimize, maximize
}

// Optimize hyperparameters response
message OptimizeHyperparametersResponse {
  bool success = 1;
  string optimization_id = 2;
  map<string, google.protobuf.Any> best_parameters = 3;
  float best_score = 4;
  string error_message = 5;
}

// Performance metrics request
message PerformanceMetricsRequest {
  google.protobuf.Timestamp start_time = 1;
  google.protobuf.Timestamp end_time = 2;
  repeated string metric_names = 3;
  string aggregation = 4;
}

// Performance metrics response
message PerformanceMetricsResponse {
  repeated PerformanceMetrics metrics = 1;
  string summary = 2;
}

// Report request
message ReportRequest {
  string report_type = 1;
  google.protobuf.Timestamp start_time = 2;
  google.protobuf.Timestamp end_time = 3;
  bool include_visualizations = 4;
}

// Report response
message ReportResponse {
  string report_id = 1;
  string report_content = 2;
  repeated bytes visualizations = 3;
  string format = 4;
}

// Rollback request
message RollbackRequest {
  string improvement_id = 1;
  string reason = 2;
  bool force_rollback = 3;
}

// Rollback response
message RollbackResponse {
  bool success = 1;
  string rollback_id = 2;
  string error_message = 3;
  google.protobuf.Timestamp completed_at = 4;
}

// History request
message HistoryRequest {
  google.protobuf.Timestamp start_time = 1;
  google.protobuf.Timestamp end_time = 2;
  repeated ImprovementType improvement_types = 3;
  int32 page_size = 4;
  string page_token = 5;
}

// History response
message HistoryResponse {
  repeated ImprovementResult improvements = 1;
  string next_page_token = 2;
  int32 total_count = 3;
}

// Health check request
message HealthCheckRequest {
  string service = 1;
}

// Health check response
message HealthCheckResponse {
  bool healthy = 1;
  string status = 2;
  map<string, string> details = 3;
  google.protobuf.Timestamp timestamp = 4;
}
