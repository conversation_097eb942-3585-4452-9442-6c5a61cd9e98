;;;; AST Mutation and Symbolic Refactoring for ASI Self-Improvement Engine
;;;; 
;;;; This module provides Abstract Syntax Tree (AST) manipulation and mutation
;;;; capabilities for symbolic code refactoring and genetic programming.

(defpackage :asi-ast-mutation
  (:use :cl)
  (:export #:mutate-ast
           #:random-mutation
           #:targeted-mutation
           #:crossover-asts
           #:evaluate-fitness
           #:generate-random-ast
           #:ast-to-code
           #:code-to-ast
           #:mutation-operators
           #:fitness-functions))

(in-package :asi-ast-mutation)

;;; AST Node Structure
(defstruct ast-node
  type          ; :function, :variable, :constant, :operator, :conditional
  value         ; The actual value or operator
  children      ; List of child AST nodes
  metadata      ; Additional metadata (line numbers, types, etc.)
  fitness-score ; Cached fitness score
  generation    ; Generation number for tracking evolution
  parent        ; Reference to parent node
  depth         ; Depth in the tree
  size          ; Number of nodes in subtree
  complexity    ; Complexity measure
  )

;;; Mutation Operators
(defparameter *mutation-operators*
  '(:point-mutation
    :subtree-mutation
    :hoist-mutation
    :shrink-mutation
    :expand-mutation
    :permutation-mutation
    :constant-mutation
    :operator-mutation))

(defparameter *mutation-rates*
  '((:point-mutation . 0.3)
    (:subtree-mutation . 0.2)
    (:hoist-mutation . 0.1)
    (:shrink-mutation . 0.1)
    (:expand-mutation . 0.1)
    (:permutation-mutation . 0.1)
    (:constant-mutation . 0.05)
    (:operator-mutation . 0.05)))

;;; Fitness Function Registry
(defparameter *fitness-functions* (make-hash-table :test 'equal))

(defmacro define-fitness-function (name params &body body)
  "Define a fitness function for AST evaluation."
  `(setf (gethash ,(string name) *fitness-functions*)
         (lambda ,params ,@body)))

;;; Basic Mutation Functions

(defun mutate-ast (ast &key (mutation-rate 0.1) (max-depth 10) (operators *mutation-operators*))
  "Apply random mutations to an AST with given probability."
  (when (< (random 1.0) mutation-rate)
    (let ((mutation-type (select-mutation-operator operators)))
      (case mutation-type
        (:point-mutation (point-mutation ast))
        (:subtree-mutation (subtree-mutation ast max-depth))
        (:hoist-mutation (hoist-mutation ast))
        (:shrink-mutation (shrink-mutation ast))
        (:expand-mutation (expand-mutation ast max-depth))
        (:permutation-mutation (permutation-mutation ast))
        (:constant-mutation (constant-mutation ast))
        (:operator-mutation (operator-mutation ast))
        (otherwise ast))))
  ast)

(defun select-mutation-operator (operators)
  "Select a mutation operator based on weighted probabilities."
  (let ((total-weight (reduce #'+ (mapcar (lambda (op) (cdr (assoc op *mutation-rates*))) operators)))
        (random-value (random 1.0))
        (cumulative-weight 0.0))
    (dolist (operator operators)
      (incf cumulative-weight (/ (cdr (assoc operator *mutation-rates*)) total-weight))
      (when (< random-value cumulative-weight)
        (return operator)))))

(defun point-mutation (ast)
  "Mutate a single node in the AST."
  (let ((target-node (select-random-node ast)))
    (when target-node
      (case (ast-node-type target-node)
        (:constant (setf (ast-node-value target-node) (generate-random-constant)))
        (:variable (setf (ast-node-value target-node) (generate-random-variable)))
        (:operator (setf (ast-node-value target-node) (generate-random-operator)))
        (:function (setf (ast-node-value target-node) (generate-random-function)))))
    ast))

(defun subtree-mutation (ast max-depth)
  "Replace a subtree with a randomly generated subtree."
  (let ((target-node (select-random-node ast)))
    (when target-node
      (let ((new-subtree (generate-random-ast :max-depth (min max-depth 
                                                              (- max-depth (ast-node-depth target-node))))))
        (replace-node target-node new-subtree)))
    ast))

(defun hoist-mutation (ast)
  "Replace a node with one of its subtrees (hoisting)."
  (let ((target-node (select-random-node ast)))
    (when (and target-node (ast-node-children target-node))
      (let ((child-to-hoist (nth (random (length (ast-node-children target-node)))
                                 (ast-node-children target-node))))
        (replace-node target-node child-to-hoist)))
    ast))

(defun shrink-mutation (ast)
  "Remove a random subtree, replacing it with a terminal."
  (let ((target-node (select-random-node ast)))
    (when target-node
      (let ((terminal (generate-random-terminal)))
        (replace-node target-node terminal)))
    ast))

(defun expand-mutation (ast max-depth)
  "Replace a terminal with a small subtree."
  (let ((terminal-nodes (collect-terminal-nodes ast)))
    (when terminal-nodes
      (let ((target-terminal (nth (random (length terminal-nodes)) terminal-nodes)))
        (when (< (ast-node-depth target-terminal) max-depth)
          (let ((new-subtree (generate-random-ast :max-depth 2)))
            (replace-node target-terminal new-subtree)))))
    ast))

(defun permutation-mutation (ast)
  "Randomly permute the children of a node."
  (let ((target-node (select-random-node ast)))
    (when (and target-node (> (length (ast-node-children target-node)) 1))
      (setf (ast-node-children target-node)
            (shuffle-list (ast-node-children target-node))))
    ast))

(defun constant-mutation (ast)
  "Mutate constants by adding small random values."
  (let ((constant-nodes (collect-constant-nodes ast)))
    (when constant-nodes
      (let ((target-constant (nth (random (length constant-nodes)) constant-nodes)))
        (when (numberp (ast-node-value target-constant))
          (setf (ast-node-value target-constant)
                (+ (ast-node-value target-constant)
                   (* (ast-node-value target-constant) (- (random 0.2) 0.1)))))))
    ast))

(defun operator-mutation (ast)
  "Replace an operator with a similar operator."
  (let ((operator-nodes (collect-operator-nodes ast)))
    (when operator-nodes
      (let ((target-operator (nth (random (length operator-nodes)) operator-nodes)))
        (setf (ast-node-value target-operator)
              (select-similar-operator (ast-node-value target-operator)))))
    ast))

;;; Crossover Operations

(defun crossover-asts (parent1 parent2 &key (crossover-rate 0.7))
  "Perform crossover between two ASTs to create offspring."
  (when (< (random 1.0) crossover-rate)
    (let ((child1 (copy-ast parent1))
          (child2 (copy-ast parent2)))
      (multiple-value-bind (node1 node2)
          (select-crossover-points child1 child2)
        (when (and node1 node2)
          (swap-subtrees node1 node2)))
      (values child1 child2)))
  (values parent1 parent2))

(defun select-crossover-points (ast1 ast2)
  "Select compatible crossover points in two ASTs."
  (let ((nodes1 (collect-all-nodes ast1))
        (nodes2 (collect-all-nodes ast2)))
    (loop for attempts from 0 below 10
          for node1 = (nth (random (length nodes1)) nodes1)
          for compatible-nodes2 = (find-compatible-nodes node1 nodes2)
          when compatible-nodes2
            do (return (values node1 (nth (random (length compatible-nodes2)) compatible-nodes2)))
          finally (return (values nil nil)))))

(defun find-compatible-nodes (node nodes)
  "Find nodes compatible for crossover with the given node."
  (remove-if-not (lambda (n) (compatible-for-crossover-p node n)) nodes))

(defun compatible-for-crossover-p (node1 node2)
  "Check if two nodes are compatible for crossover."
  (and (eq (ast-node-type node1) (ast-node-type node2))
       (= (length (ast-node-children node1)) (length (ast-node-children node2)))))

;;; AST Generation

(defun generate-random-ast (&key (max-depth 5) (min-depth 1) (terminal-probability 0.3))
  "Generate a random AST with specified constraints."
  (labels ((generate-node (current-depth)
             (cond
               ((>= current-depth max-depth)
                (generate-random-terminal))
               ((and (>= current-depth min-depth) (< (random 1.0) terminal-probability))
                (generate-random-terminal))
               (t
                (generate-random-function-node current-depth)))))
    (generate-node 0)))

(defun generate-random-terminal ()
  "Generate a random terminal node (constant or variable)."
  (if (< (random 1.0) 0.5)
      (make-ast-node :type :constant
                     :value (generate-random-constant)
                     :children nil)
      (make-ast-node :type :variable
                     :value (generate-random-variable)
                     :children nil)))

(defun generate-random-function-node (current-depth)
  "Generate a random function node with children."
  (let* ((function-name (generate-random-function))
         (arity (get-function-arity function-name))
         (children (loop repeat arity
                         collect (generate-random-ast :max-depth (1+ current-depth)))))
    (make-ast-node :type :function
                   :value function-name
                   :children children)))

;;; Utility Functions

(defun select-random-node (ast)
  "Select a random node from the AST."
  (let ((all-nodes (collect-all-nodes ast)))
    (when all-nodes
      (nth (random (length all-nodes)) all-nodes))))

(defun collect-all-nodes (ast)
  "Collect all nodes in the AST into a flat list."
  (cons ast (mapcan #'collect-all-nodes (ast-node-children ast))))

(defun collect-terminal-nodes (ast)
  "Collect all terminal nodes (leaves) in the AST."
  (if (null (ast-node-children ast))
      (list ast)
      (mapcan #'collect-terminal-nodes (ast-node-children ast))))

(defun collect-constant-nodes (ast)
  "Collect all constant nodes in the AST."
  (append (when (eq (ast-node-type ast) :constant) (list ast))
          (mapcan #'collect-constant-nodes (ast-node-children ast))))

(defun collect-operator-nodes (ast)
  "Collect all operator nodes in the AST."
  (append (when (eq (ast-node-type ast) :operator) (list ast))
          (mapcan #'collect-operator-nodes (ast-node-children ast))))

(defun copy-ast (ast)
  "Create a deep copy of an AST."
  (make-ast-node :type (ast-node-type ast)
                 :value (ast-node-value ast)
                 :children (mapcar #'copy-ast (ast-node-children ast))
                 :metadata (copy-list (ast-node-metadata ast))
                 :fitness-score (ast-node-fitness-score ast)
                 :generation (ast-node-generation ast)))

(defun replace-node (old-node new-node)
  "Replace old-node with new-node in the AST."
  (when (ast-node-parent old-node)
    (setf (ast-node-children (ast-node-parent old-node))
          (substitute new-node old-node (ast-node-children (ast-node-parent old-node)))))
  (setf (ast-node-parent new-node) (ast-node-parent old-node)))

(defun swap-subtrees (node1 node2)
  "Swap two subtrees in their respective ASTs."
  (let ((parent1 (ast-node-parent node1))
        (parent2 (ast-node-parent node2)))
    (when parent1
      (setf (ast-node-children parent1)
            (substitute node2 node1 (ast-node-children parent1))))
    (when parent2
      (setf (ast-node-children parent2)
            (substitute node1 node2 (ast-node-children parent2))))
    (setf (ast-node-parent node1) parent2)
    (setf (ast-node-parent node2) parent1)))

(defun shuffle-list (list)
  "Randomly shuffle a list."
  (let ((array (make-array (length list) :initial-contents list)))
    (loop for i from (1- (length array)) downto 1
          do (rotatef (aref array i) (aref array (random (1+ i)))))
    (coerce array 'list)))

;;; Random Generation Helpers

(defun generate-random-constant ()
  "Generate a random constant value."
  (case (random 3)
    (0 (random 100))           ; Integer
    (1 (random 10.0))          ; Float
    (2 (if (< (random 1.0) 0.5) t nil)))) ; Boolean

(defun generate-random-variable ()
  "Generate a random variable name."
  (nth (random (length *variable-names*)) *variable-names*))

(defun generate-random-function ()
  "Generate a random function name."
  (nth (random (length *function-names*)) *function-names*))

(defun generate-random-operator ()
  "Generate a random operator."
  (nth (random (length *operators*)) *operators*))

(defun select-similar-operator (operator)
  "Select an operator similar to the given operator."
  (let ((similar-ops (gethash operator *similar-operators*)))
    (if similar-ops
        (nth (random (length similar-ops)) similar-ops)
        operator)))

(defun get-function-arity (function-name)
  "Get the arity (number of arguments) for a function."
  (gethash function-name *function-arities* 2)) ; Default arity of 2

;;; Configuration Parameters

(defparameter *variable-names* '(x y z a b c input output data result))
(defparameter *function-names* '(+ - * / max min abs sqrt exp log sin cos))
(defparameter *operators* '(+ - * / = < > <= >= and or not))

(defparameter *similar-operators* 
  (let ((table (make-hash-table)))
    (setf (gethash '+ table) '(- *))
    (setf (gethash '- table) '(+ /))
    (setf (gethash '* table) '(+ /))
    (setf (gethash '/ table) '(- *))
    (setf (gethash '< table) '(<= > >=))
    (setf (gethash '> table) '(>= < <=))
    (setf (gethash '<= table) '(< >= >))
    (setf (gethash '>= table) '(> <= <))
    (setf (gethash 'and table) '(or))
    (setf (gethash 'or table) '(and))
    table))

(defparameter *function-arities*
  (let ((table (make-hash-table)))
    (setf (gethash '+ table) 2)
    (setf (gethash '- table) 2)
    (setf (gethash '* table) 2)
    (setf (gethash '/ table) 2)
    (setf (gethash 'max table) 2)
    (setf (gethash 'min table) 2)
    (setf (gethash 'abs table) 1)
    (setf (gethash 'sqrt table) 1)
    (setf (gethash 'exp table) 1)
    (setf (gethash 'log table) 1)
    (setf (gethash 'sin table) 1)
    (setf (gethash 'cos table) 1)
    table))

;;; Fitness Evaluation

(define-fitness-function code-size (ast)
  "Fitness function based on code size (smaller is better)."
  (/ 1.0 (1+ (count-nodes ast))))

(define-fitness-function code-complexity (ast)
  "Fitness function based on code complexity."
  (/ 1.0 (1+ (calculate-complexity ast))))

(defun count-nodes (ast)
  "Count the total number of nodes in the AST."
  (1+ (reduce #'+ (mapcar #'count-nodes (ast-node-children ast)) :initial-value 0)))

(defun calculate-complexity (ast)
  "Calculate the complexity of the AST."
  (+ (if (ast-node-children ast) 1 0)
     (reduce #'+ (mapcar #'calculate-complexity (ast-node-children ast)) :initial-value 0)))

(defun evaluate-fitness (ast fitness-function-name &rest args)
  "Evaluate the fitness of an AST using the specified fitness function."
  (let ((fitness-fn (gethash fitness-function-name *fitness-functions*)))
    (if fitness-fn
        (apply fitness-fn ast args)
        (error "Unknown fitness function: ~A" fitness-function-name))))

;;; Export the main interface
(defun mutation-operators ()
  "Return the list of available mutation operators."
  *mutation-operators*)

(defun fitness-functions ()
  "Return the list of available fitness functions."
  (loop for key being the hash-keys of *fitness-functions*
        collect key))
