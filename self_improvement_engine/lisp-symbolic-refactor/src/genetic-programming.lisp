;;;; Genetic Programming Engine for ASI Self-Improvement
;;;; 
;;;; This module implements a complete genetic programming system for evolving
;;;; code, algorithms, and symbolic expressions using evolutionary computation.

(defpackage :asi-genetic-programming
  (:use :cl :asi-ast-mutation)
  (:export #:genetic-programming
           #:evolve-population
           #:tournament-selection
           #:fitness-proportionate-selection
           #:elitism-selection
           #:create-initial-population
           #:evaluate-population
           #:genetic-programming-config
           #:run-evolution
           #:best-individual
           #:population-statistics))

(in-package :asi-genetic-programming)

;;; Genetic Programming Configuration
(defstruct genetic-programming-config
  population-size
  max-generations
  mutation-rate
  crossover-rate
  elitism-rate
  tournament-size
  max-tree-depth
  min-tree-depth
  fitness-function
  termination-criteria
  bloat-control
  diversity-pressure
  selection-method
  replacement-strategy)

(defparameter *default-gp-config*
  (make-genetic-programming-config
   :population-size 100
   :max-generations 50
   :mutation-rate 0.1
   :crossover-rate 0.7
   :elitism-rate 0.1
   :tournament-size 3
   :max-tree-depth 8
   :min-tree-depth 2
   :fitness-function "code-size"
   :termination-criteria '(:max-generations :fitness-threshold)
   :bloat-control t
   :diversity-pressure 0.1
   :selection-method :tournament
   :replacement-strategy :generational))

;;; Individual Structure
(defstruct individual
  genotype        ; The AST representation
  phenotype       ; The executable code (if applicable)
  fitness         ; Fitness score
  age             ; Age in generations
  birth-generation ; Generation when created
  parent-ids      ; IDs of parents (for genealogy)
  mutation-history ; History of mutations applied
  evaluation-count ; Number of times evaluated
  raw-fitness     ; Raw fitness before normalization
  adjusted-fitness ; Fitness adjusted for diversity/bloat
  size            ; Size of the individual (number of nodes)
  depth           ; Depth of the tree
  complexity      ; Complexity measure
  id              ; Unique identifier
  metadata        ; Additional metadata
  )

;;; Population Structure
(defstruct population
  individuals     ; List of individuals
  generation      ; Current generation number
  best-individual ; Best individual so far
  best-fitness    ; Best fitness so far
  average-fitness ; Average fitness of population
  diversity       ; Diversity measure
  statistics      ; Population statistics
  config          ; GP configuration
  fitness-history ; History of fitness values
  size-history    ; History of size values
  )

;;; Main Genetic Programming Function
(defun genetic-programming (&key (config *default-gp-config*) (verbose t))
  "Run a complete genetic programming evolution."
  (let ((population (create-initial-population config)))
    (when verbose
      (format t "Starting genetic programming with population size ~A~%" 
              (genetic-programming-config-population-size config)))
    
    (loop for generation from 0 below (genetic-programming-config-max-generations config)
          do (progn
               (evaluate-population population config)
               (update-population-statistics population)
               
               (when verbose
                 (format t "Generation ~A: Best fitness = ~,4F, Average fitness = ~,4F~%"
                         generation
                         (population-best-fitness population)
                         (population-average-fitness population)))
               
               ;; Check termination criteria
               (when (termination-criteria-met-p population config)
                 (when verbose
                   (format t "Termination criteria met at generation ~A~%" generation))
                 (return population))
               
               ;; Evolve to next generation
               (setf population (evolve-population population config))
               (incf (population-generation population)))
          finally (return population))))

;;; Population Initialization
(defun create-initial-population (config)
  "Create an initial population of random individuals."
  (let ((individuals (loop repeat (genetic-programming-config-population-size config)
                           collect (create-random-individual config))))
    (make-population :individuals individuals
                     :generation 0
                     :config config
                     :fitness-history '()
                     :size-history '())))

(defun create-random-individual (config)
  "Create a random individual."
  (let ((ast (generate-random-ast 
              :max-depth (genetic-programming-config-max-tree-depth config)
              :min-depth (genetic-programming-config-min-tree-depth config))))
    (make-individual :genotype ast
                     :id (generate-unique-id)
                     :birth-generation 0
                     :age 0
                     :evaluation-count 0
                     :mutation-history '()
                     :size (count-nodes ast)
                     :depth (calculate-tree-depth ast)
                     :complexity (calculate-complexity ast))))

;;; Population Evolution
(defun evolve-population (population config)
  "Evolve the population to the next generation."
  (let ((new-individuals '())
        (population-size (genetic-programming-config-population-size config))
        (elitism-count (floor (* (genetic-programming-config-elitism-rate config) population-size))))
    
    ;; Elitism: Keep best individuals
    (when (> elitism-count 0)
      (let ((elite (subseq (sort (copy-list (population-individuals population))
                                 (lambda (a b) (> (individual-fitness a) (individual-fitness b))))
                           0 elitism-count)))
        (setf new-individuals (append new-individuals elite))))
    
    ;; Generate offspring to fill the rest of the population
    (loop while (< (length new-individuals) population-size)
          do (multiple-value-bind (offspring1 offspring2)
                 (create-offspring population config)
               (when offspring1
                 (push offspring1 new-individuals))
               (when (and offspring2 (< (length new-individuals) population-size))
                 (push offspring2 new-individuals))))
    
    ;; Create new population
    (make-population :individuals (subseq new-individuals 0 population-size)
                     :generation (1+ (population-generation population))
                     :config config
                     :best-individual (population-best-individual population)
                     :best-fitness (population-best-fitness population)
                     :fitness-history (cons (population-average-fitness population)
                                            (population-fitness-history population))
                     :size-history (cons (average-population-size population)
                                         (population-size-history population)))))

(defun create-offspring (population config)
  "Create offspring through selection, crossover, and mutation."
  (let ((parent1 (select-individual population config))
        (parent2 (select-individual population config)))
    
    ;; Crossover
    (multiple-value-bind (child1-ast child2-ast)
        (if (< (random 1.0) (genetic-programming-config-crossover-rate config))
            (crossover-asts (individual-genotype parent1) (individual-genotype parent2))
            (values (copy-ast (individual-genotype parent1))
                    (copy-ast (individual-genotype parent2))))
      
      ;; Mutation
      (when (< (random 1.0) (genetic-programming-config-mutation-rate config))
        (setf child1-ast (mutate-ast child1-ast 
                                     :max-depth (genetic-programming-config-max-tree-depth config))))
      
      (when (< (random 1.0) (genetic-programming-config-mutation-rate config))
        (setf child2-ast (mutate-ast child2-ast 
                                     :max-depth (genetic-programming-config-max-tree-depth config))))
      
      ;; Create offspring individuals
      (values (create-individual-from-ast child1-ast (population-generation population) 
                                          (list (individual-id parent1) (individual-id parent2)))
              (create-individual-from-ast child2-ast (population-generation population)
                                          (list (individual-id parent1) (individual-id parent2)))))))

(defun create-individual-from-ast (ast generation parent-ids)
  "Create an individual from an AST."
  (make-individual :genotype ast
                   :id (generate-unique-id)
                   :birth-generation generation
                   :age 0
                   :parent-ids parent-ids
                   :evaluation-count 0
                   :mutation-history '()
                   :size (count-nodes ast)
                   :depth (calculate-tree-depth ast)
                   :complexity (calculate-complexity ast)))

;;; Selection Methods
(defun select-individual (population config)
  "Select an individual from the population based on the selection method."
  (case (genetic-programming-config-selection-method config)
    (:tournament (tournament-selection population config))
    (:fitness-proportionate (fitness-proportionate-selection population))
    (:rank (rank-selection population))
    (:random (random-selection population))
    (otherwise (tournament-selection population config))))

(defun tournament-selection (population config)
  "Tournament selection: select best from random tournament."
  (let ((tournament-size (genetic-programming-config-tournament-size config))
        (individuals (population-individuals population)))
    (let ((tournament (loop repeat tournament-size
                            collect (nth (random (length individuals)) individuals))))
      (reduce (lambda (a b) (if (> (individual-fitness a) (individual-fitness b)) a b))
              tournament))))

(defun fitness-proportionate-selection (population)
  "Fitness proportionate selection (roulette wheel)."
  (let* ((individuals (population-individuals population))
         (total-fitness (reduce #'+ individuals :key #'individual-fitness))
         (random-value (* (random 1.0) total-fitness))
         (cumulative-fitness 0))
    (dolist (individual individuals)
      (incf cumulative-fitness (individual-fitness individual))
      (when (>= cumulative-fitness random-value)
        (return individual)))))

(defun rank-selection (population)
  "Rank-based selection."
  (let* ((individuals (sort (copy-list (population-individuals population))
                            (lambda (a b) (> (individual-fitness a) (individual-fitness b)))))
         (ranks (loop for i from 1 to (length individuals) collect i))
         (total-rank (reduce #'+ ranks))
         (random-value (* (random 1.0) total-rank))
         (cumulative-rank 0))
    (loop for individual in individuals
          for rank in ranks
          do (incf cumulative-rank rank)
          when (>= cumulative-rank random-value)
            do (return individual))))

(defun random-selection (population)
  "Random selection."
  (let ((individuals (population-individuals population)))
    (nth (random (length individuals)) individuals)))

;;; Fitness Evaluation
(defun evaluate-population (population config)
  "Evaluate the fitness of all individuals in the population."
  (dolist (individual (population-individuals population))
    (unless (individual-fitness individual)
      (evaluate-individual individual config))))

(defun evaluate-individual (individual config)
  "Evaluate the fitness of a single individual."
  (let ((fitness (evaluate-fitness (individual-genotype individual)
                                   (genetic-programming-config-fitness-function config))))
    ;; Apply bloat control if enabled
    (when (genetic-programming-config-bloat-control config)
      (setf fitness (apply-bloat-penalty fitness (individual-size individual))))
    
    ;; Apply diversity pressure if enabled
    (when (> (genetic-programming-config-diversity-pressure config) 0)
      (setf fitness (apply-diversity-pressure fitness individual)))
    
    (setf (individual-fitness individual) fitness)
    (setf (individual-raw-fitness individual) fitness)
    (incf (individual-evaluation-count individual))
    fitness))

(defun apply-bloat-penalty (fitness size)
  "Apply a penalty for large individuals to control bloat."
  (let ((penalty-factor (max 0.1 (- 1.0 (* 0.01 (- size 50))))))
    (* fitness penalty-factor)))

(defun apply-diversity-pressure (fitness individual)
  "Apply diversity pressure to maintain population diversity."
  ;; Simplified diversity pressure based on uniqueness
  (let ((uniqueness-bonus (if (< (random 1.0) 0.1) 1.1 1.0)))
    (* fitness uniqueness-bonus)))

;;; Population Statistics
(defun update-population-statistics (population)
  "Update population statistics."
  (let ((individuals (population-individuals population))
        (fitnesses (mapcar #'individual-fitness (population-individuals population))))
    
    ;; Update best individual
    (let ((best (reduce (lambda (a b) (if (> (individual-fitness a) (individual-fitness b)) a b))
                        individuals)))
      (when (or (null (population-best-individual population))
                (> (individual-fitness best) (population-best-fitness population)))
        (setf (population-best-individual population) best)
        (setf (population-best-fitness population) (individual-fitness best))))
    
    ;; Update average fitness
    (setf (population-average-fitness population)
          (/ (reduce #'+ fitnesses) (length fitnesses)))
    
    ;; Update diversity
    (setf (population-diversity population) (calculate-population-diversity population))
    
    ;; Update statistics
    (setf (population-statistics population)
          (list :generation (population-generation population)
                :best-fitness (population-best-fitness population)
                :average-fitness (population-average-fitness population)
                :worst-fitness (reduce #'min fitnesses)
                :fitness-std-dev (calculate-standard-deviation fitnesses)
                :average-size (average-population-size population)
                :average-depth (average-population-depth population)
                :diversity (population-diversity population)))))

(defun calculate-population-diversity (population)
  "Calculate population diversity based on genotype differences."
  (let ((individuals (population-individuals population))
        (total-comparisons 0)
        (different-pairs 0))
    (loop for i from 0 below (length individuals)
          do (loop for j from (1+ i) below (length individuals)
                   do (incf total-comparisons)
                   when (not (ast-equal-p (individual-genotype (nth i individuals))
                                          (individual-genotype (nth j individuals))))
                     do (incf different-pairs)))
    (if (> total-comparisons 0)
        (/ different-pairs total-comparisons)
        0.0)))

(defun ast-equal-p (ast1 ast2)
  "Check if two ASTs are structurally equal."
  (and (eq (ast-node-type ast1) (ast-node-type ast2))
       (equal (ast-node-value ast1) (ast-node-value ast2))
       (= (length (ast-node-children ast1)) (length (ast-node-children ast2)))
       (every #'ast-equal-p (ast-node-children ast1) (ast-node-children ast2))))

;;; Termination Criteria
(defun termination-criteria-met-p (population config)
  "Check if any termination criteria are met."
  (let ((criteria (genetic-programming-config-termination-criteria config)))
    (or (and (member :max-generations criteria)
             (>= (population-generation population)
                 (genetic-programming-config-max-generations config)))
        (and (member :fitness-threshold criteria)
             (>= (population-best-fitness population) 0.99))
        (and (member :convergence criteria)
             (population-converged-p population))
        (and (member :stagnation criteria)
             (population-stagnated-p population)))))

(defun population-converged-p (population)
  "Check if the population has converged."
  (let ((diversity (population-diversity population)))
    (< diversity 0.01)))

(defun population-stagnated-p (population)
  "Check if the population has stagnated."
  (let ((history (population-fitness-history population)))
    (and (>= (length history) 10)
         (< (abs (- (first history) (nth 9 history))) 0.001))))

;;; Utility Functions
(defun calculate-tree-depth (ast)
  "Calculate the depth of an AST."
  (if (null (ast-node-children ast))
      1
      (1+ (reduce #'max (mapcar #'calculate-tree-depth (ast-node-children ast))
                  :initial-value 0))))

(defun average-population-size (population)
  "Calculate the average size of individuals in the population."
  (let ((sizes (mapcar #'individual-size (population-individuals population))))
    (/ (reduce #'+ sizes) (length sizes))))

(defun average-population-depth (population)
  "Calculate the average depth of individuals in the population."
  (let ((depths (mapcar #'individual-depth (population-individuals population))))
    (/ (reduce #'+ depths) (length depths))))

(defun calculate-standard-deviation (values)
  "Calculate the standard deviation of a list of values."
  (let* ((mean (/ (reduce #'+ values) (length values)))
         (variance (/ (reduce #'+ (mapcar (lambda (x) (expt (- x mean) 2)) values))
                      (length values))))
    (sqrt variance)))

(defun generate-unique-id ()
  "Generate a unique identifier."
  (format nil "~A-~A" (get-universal-time) (random 1000000)))

;;; High-level Interface Functions
(defun run-evolution (&key (config *default-gp-config*) (verbose t))
  "Run a complete evolution and return the best individual."
  (let ((final-population (genetic-programming :config config :verbose verbose)))
    (population-best-individual final-population)))

(defun best-individual (population)
  "Get the best individual from a population."
  (population-best-individual population))

(defun population-statistics (population)
  "Get statistics for a population."
  (population-statistics population))
