# ASI Self-Improvement Engine Configuration
# ========================================

# General settings
debug: false
log_level: "INFO"
environment: "development"

# gRPC configuration for external APIs
grpc:
  server_port: 50080
  max_workers: 10
  max_message_length: 4194304  # 4MB
  keepalive_time_ms: 30000
  keepalive_timeout_ms: 5000
  
  # Integration endpoints
  learning_engine_endpoint: "localhost:50060"
  decision_engine_endpoint: "localhost:50070"
  ui_endpoint: "localhost:3000"
  
  # Timeouts and retries
  request_timeout_ms: 10000
  retry_attempts: 3
  retry_delay_ms: 1000

# Genetic Programming configuration (DEAP)
genetic_programming:
  population_size: 100
  max_generations: 50
  mutation_rate: 0.1
  crossover_rate: 0.7
  elitism_rate: 0.1
  tournament_size: 3
  max_tree_depth: 8
  min_tree_depth: 2
  
  # Fitness and selection
  fitness_weights: [1.0]  # Maximizing fitness
  selection_method: "tournament"  # tournament, roulette, rank
  replacement_strategy: "generational"  # generational, elitist
  
  # Bloat control and diversity
  enable_bloat_control: true
  diversity_pressure: 0.1
  parsimony_coefficient: 0.01
  
  # Parallelization
  enable_parallel: true
  num_processes: 4
  
  # Termination criteria
  max_fitness: 1.0
  convergence_threshold: 0.001
  stagnation_generations: 10

# RLHF (Reinforcement Learning from Human Feedback) configuration
rlhf:
  # Feedback collection
  max_feedback_queue_size: 10000
  feedback_batch_size: 32
  min_feedback_for_training: 100
  feedback_timeout_hours: 24
  
  # Reward model training
  reward_model_lr: 0.0001
  reward_model_epochs: 10
  reward_model_batch_size: 16
  validation_split: 0.2
  
  # Preference learning
  preference_model_type: "bradley_terry"  # bradley_terry, elo, neural
  preference_learning_rate: 0.001
  preference_regularization: 0.01
  
  # Policy optimization (PPO)
  ppo_lr: 0.0003
  ppo_epochs: 4
  ppo_clip_ratio: 0.2
  ppo_value_coef: 0.5
  ppo_entropy_coef: 0.01
  
  # Human feedback integration
  human_feedback_weight: 1.0
  automated_feedback_weight: 0.5
  feedback_decay_rate: 0.95
  
  # Quality control
  min_human_confidence: 0.5
  require_consensus: false
  consensus_threshold: 0.7
  outlier_detection: true

# Evolutionary Strategies configuration
evolutionary_strategies:
  strategy_type: "CMA-ES"  # CMA-ES, ES, DE, PSO
  population_size: 50
  sigma: 0.1
  max_evaluations: 1000
  enable_adaptive_sigma: true
  
  # CMA-ES specific
  cma_restart: true
  cma_restart_factor: 2
  
  # Differential Evolution specific
  de_mutation_factor: 0.8
  de_crossover_prob: 0.9
  
  # Particle Swarm Optimization specific
  pso_inertia: 0.9
  pso_cognitive: 2.0
  pso_social: 2.0

# Lisp Symbolic Refactoring configuration
lisp_refactoring:
  enable_symbolic_refactoring: true
  max_ast_depth: 15
  mutation_operators:
    - "point_mutation"
    - "subtree_mutation"
    - "hoist_mutation"
    - "shrink_mutation"
    - "expand_mutation"
  
  mutation_rates:
    point_mutation: 0.3
    subtree_mutation: 0.2
    hoist_mutation: 0.1
    shrink_mutation: 0.1
    expand_mutation: 0.1
    permutation_mutation: 0.1
    constant_mutation: 0.05
    operator_mutation: 0.05
  
  # Code generation settings
  max_code_size: 1000
  enable_syntax_validation: true
  enable_semantic_validation: true

# Julia Performance Analytics configuration
julia_analytics:
  enable_performance_analytics: true
  update_interval_seconds: 30
  history_window_days: 30
  anomaly_threshold: 2.0  # Standard deviations
  prediction_horizon_hours: 24
  dashboard_port: 8090
  
  # Real-time monitoring
  enable_real_time: true
  enable_predictions: true
  enable_optimization: true
  
  # Data collection
  collect_system_metrics: true
  collect_model_metrics: true
  collect_decision_metrics: true
  
  # Anomaly detection
  anomaly_methods:
    - "statistical"
    - "isolation_forest"
    - "lstm"
  
  # Performance prediction
  prediction_models:
    - "arima"
    - "linear_trend"
    - "exponential_smoothing"
    - "lstm"

# Self-Improvement Cycle configuration
improvement_cycle:
  # Cycle timing
  cycle_interval_hours: 24
  emergency_cycle_threshold: 0.05  # 5% performance degradation
  
  # Improvement identification
  min_performance_history: 100
  improvement_threshold: 0.02  # 2% minimum improvement
  confidence_threshold: 0.7
  
  # Safety and validation
  enable_sandboxed_testing: true
  validation_period_hours: 4
  rollback_threshold: 0.03  # 3% performance degradation
  require_human_approval: false
  approval_timeout_hours: 8
  
  # Improvement types
  enable_model_retraining: true
  enable_rule_optimization: true
  enable_architecture_evolution: true
  enable_hyperparameter_tuning: true
  enable_code_refactoring: true
  
  # Resource limits
  max_parallel_improvements: 3
  max_improvement_duration_hours: 12
  max_compute_budget: 1000  # Compute units
  max_memory_usage_gb: 32

# Integration with ASI components
integration:
  # Learning Engine integration
  learning_engine:
    enable_model_updates: true
    enable_architecture_search: true
    enable_hyperparameter_optimization: true
    model_deployment_strategy: "blue_green"
    
  # Decision Engine integration
  decision_engine:
    enable_rule_updates: true
    enable_logic_optimization: true
    enable_fallback_improvement: true
    rule_deployment_strategy: "gradual_rollout"
  
  # Data Integration module
  data_integration:
    enable_pipeline_optimization: true
    enable_schema_evolution: true
    
  # UI/UX module
  ui_integration:
    enable_dashboard_updates: true
    enable_user_feedback_collection: true
    feedback_collection_rate: 0.1  # 10% of interactions

# Monitoring and observability
monitoring:
  enable_metrics: true
  metrics_port: 8080
  metrics_path: "/metrics"
  enable_tracing: true
  tracing_endpoint: "http://localhost:14268/api/traces"
  tracing_service_name: "asi-self-improvement"
  
  # Health checks
  health_check_interval_seconds: 30
  health_check_timeout_seconds: 5
  
  # Performance monitoring
  enable_performance_logging: true
  slow_operation_threshold_ms: 5000
  memory_usage_threshold_mb: 2000
  
  # Alerting
  enable_alerting: true
  alert_channels:
    - "email"
    - "slack"
    - "webhook"
  
  critical_alerts:
    - "improvement_failure"
    - "performance_degradation"
    - "system_health_critical"

# Security configuration
security:
  enable_authentication: false
  enable_authorization: false
  enable_audit_logging: true
  
  # Code execution security
  enable_sandboxing: true
  sandbox_timeout_seconds: 300
  allowed_operations:
    - "model_training"
    - "rule_evaluation"
    - "data_processing"
  
  # Data protection
  enable_encryption: false
  encryption_key: null
  enable_data_anonymization: true

# Storage configuration
storage:
  # Improvement history
  improvement_history:
    backend: "postgresql"
    connection_string: "postgresql://asi_user:asi_password@localhost:5432/asi_improvements"
    retention_days: 365
  
  # Model artifacts
  model_storage:
    backend: "s3"  # s3, gcs, azure, local
    bucket: "asi-model-artifacts"
    versioning: true
    compression: true
  
  # Code artifacts
  code_storage:
    backend: "git"
    repository: "asi-code-evolution"
    branch_strategy: "feature_branches"
  
  # Cache configuration
  cache:
    backend: "redis"
    connection_string: "redis://localhost:6379/1"
    default_ttl_seconds: 3600
    max_memory: "2GB"

# Development and testing
development:
  enable_mock_components: false
  mock_improvement_latency_ms: 100
  enable_improvement_replay: false
  replay_file_path: "test_data/improvement_replay.json"
  
  # Load testing
  load_testing:
    enable: false
    improvements_per_hour: 10
    duration_hours: 24
    concurrent_cycles: 2

# Production optimizations
production:
  enable_jit_compilation: true
  precompile_models: true
  warm_up_components: true
  enable_connection_pooling: true
  
  # Resource limits
  max_memory_usage_gb: 16
  max_cpu_usage_percent: 80
  max_open_files: 65536
  
  # Scaling configuration
  auto_scaling:
    enable: true
    min_instances: 1
    max_instances: 5
    scale_up_threshold: 70
    scale_down_threshold: 30
    cooldown_seconds: 300
  
  # Backup and recovery
  backup:
    enable: true
    backup_interval_hours: 6
    retention_days: 30
    backup_location: "s3://asi-backups/self-improvement"
  
  # Disaster recovery
  disaster_recovery:
    enable: true
    replication_factor: 2
    recovery_time_objective_minutes: 15
    recovery_point_objective_minutes: 5
