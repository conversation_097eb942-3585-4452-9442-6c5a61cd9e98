#!/usr/bin/env python3
"""
Integration tests for the ASI Self-Improvement Engine.

Tests the complete self-improvement pipeline including:
- Genetic programming and evolution
- RLHF feedback loops
- Performance analytics
- ASI system integration
- Lisp symbolic refactoring
- Julia performance monitoring
"""

import asyncio
import json
import logging
import os
import sys
import time
import unittest
import tempfile
import uuid
from pathlib import Path
from typing import Dict, List, Any
import subprocess

import numpy as np
import torch
from deap import base, creator, tools, gp

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "python-model-retraining" / "src"))

from asi_self_improvement.genetic.deap_engine import DEAPGeneticEngine, GeneticConfig
from asi_self_improvement.rlhf.feedback_loop import RL<PERSON><PERSON>eedbackLoop, RLHFConfig, HumanFeedback
from asi_self_improvement.integration.asi_connector import ASIConnector, SystemPerformanceData
from asi_self_improvement.utils.config import Config
from asi_self_improvement.utils.logger import get_logger
from asi_self_improvement.utils.metrics import MetricsCollector, get_metrics_collector

logger = get_logger(__name__)


class TestSelfImprovementIntegration(unittest.TestCase):
    """Integration tests for the Self-Improvement Engine."""
    
    @classmethod
    def setUpClass(cls):
        """Set up test environment."""
        cls.logger = get_logger(__name__)
        cls.logger.info("Setting up Self-Improvement Engine integration tests")
        
        # Create test configuration
        cls.config = cls._create_test_config()
        
        # Initialize components
        cls.metrics = MetricsCollector()
        
        cls.logger.info("Test environment setup complete")
    
    @classmethod
    def _create_test_config(cls) -> Config:
        """Create test configuration."""
        config = Config()
        
        # Override for testing
        config.grpc.learning_engine_endpoint = "localhost:50060"
        config.grpc.decision_engine_endpoint = "localhost:50070"
        config.genetic_programming.population_size = 20
        config.genetic_programming.max_generations = 5
        config.rlhf.min_feedback_for_training = 10
        config.julia_analytics.enable_performance_analytics = True
        
        return config
    
    def test_genetic_programming_basic(self):
        """Test basic genetic programming functionality."""
        self.logger.info("Testing genetic programming basic functionality")
        
        # Create genetic config
        genetic_config = GeneticConfig(
            population_size=20,
            max_generations=5,
            mutation_rate=0.1,
            crossover_rate=0.7
        )
        
        # Initialize genetic engine
        genetic_engine = DEAPGeneticEngine(genetic_config)
        
        # Define a simple primitive set for symbolic regression
        pset = gp.PrimitiveSet("MAIN", 1)
        pset.addPrimitive(lambda x, y: x + y, 2, name="add")
        pset.addPrimitive(lambda x, y: x - y, 2, name="sub")
        pset.addPrimitive(lambda x, y: x * y, 2, name="mul")
        pset.addPrimitive(lambda x: -x, 1, name="neg")
        pset.addEphemeralConstant("rand101", lambda: np.random.uniform(-1, 1))
        pset.renameArguments(ARG0='x')
        
        # Define fitness function
        def evaluate_individual(individual):
            # Transform the tree expression in a callable function
            func = genetic_engine.toolbox.compile(expr=individual)
            
            # Evaluate the function on test data
            test_data = [(x, x**2 + 2*x + 1) for x in range(-10, 11)]
            mse = 0
            
            for x, expected in test_data:
                try:
                    predicted = func(x)
                    mse += (predicted - expected) ** 2
                except (OverflowError, ZeroDivisionError, ValueError):
                    return (float('inf'),)
            
            return (1.0 / (1.0 + mse),)  # Fitness is inverse of MSE
        
        # Setup genetic programming
        genetic_engine.setup_genetic_programming(pset, evaluate_individual)
        
        # Run evolution
        result = genetic_engine.evolve()
        
        # Verify results
        self.assertIsNotNone(result)
        self.assertIsNotNone(result.best_individual)
        self.assertGreater(result.best_fitness, 0)
        self.assertEqual(result.generation, genetic_config.max_generations)
        self.assertEqual(len(result.population), genetic_config.population_size)
        
        # Cleanup
        genetic_engine.cleanup()
        
        self.logger.info("Genetic programming basic functionality test passed")
    
    def test_genetic_algorithm_optimization(self):
        """Test genetic algorithm for parameter optimization."""
        self.logger.info("Testing genetic algorithm optimization")
        
        # Create genetic config
        genetic_config = GeneticConfig(
            population_size=30,
            max_generations=10,
            mutation_rate=0.2,
            crossover_rate=0.8
        )
        
        # Initialize genetic engine
        genetic_engine = DEAPGeneticEngine(genetic_config)
        
        # Define optimization problem (minimize Rastrigin function)
        def rastrigin_fitness(individual):
            A = 10
            n = len(individual)
            result = A * n + sum(x**2 - A * np.cos(2 * np.pi * x) for x in individual)
            return (-result,)  # Negative because DEAP maximizes
        
        # Setup genetic algorithm
        genetic_engine.setup_genetic_algorithm(
            individual_size=5,
            fitness_function=rastrigin_fitness,
            gene_type="float",
            bounds=(-5.12, 5.12)
        )
        
        # Run evolution
        result = genetic_engine.evolve()
        
        # Verify results
        self.assertIsNotNone(result)
        self.assertIsNotNone(result.best_individual)
        self.assertLess(result.best_fitness, 0)  # Should be negative (minimizing)
        self.assertEqual(len(result.best_individual), 5)
        
        # Check that solution is reasonable (close to global optimum at [0,0,0,0,0])
        best_solution = result.best_individual
        distance_from_optimum = np.sqrt(sum(x**2 for x in best_solution))
        self.assertLess(distance_from_optimum, 5.0)  # Should be reasonably close
        
        # Cleanup
        genetic_engine.cleanup()
        
        self.logger.info("Genetic algorithm optimization test passed")
    
    async def test_rlhf_feedback_loop(self):
        """Test RLHF feedback collection and training."""
        self.logger.info("Testing RLHF feedback loop")
        
        # Create RLHF config
        rlhf_config = RLHFConfig(
            min_feedback_for_training=5,
            reward_model_epochs=3,
            feedback_batch_size=4
        )
        
        # Initialize RLHF system
        rlhf_system = RLHFFeedbackLoop(rlhf_config)
        await rlhf_system.initialize_models()
        
        # Simulate feedback collection
        feedback_ids = []
        for i in range(10):
            feedback_id = await rlhf_system.collect_feedback(
                input_data={"text": f"Test input {i}"},
                model_output=f"Model output {i}",
                user_id=f"user_{i % 3}",
                alternative_outputs=[f"Alternative {i}_1", f"Alternative {i}_2"]
            )
            feedback_ids.append(feedback_id)
        
        # Submit preference feedback
        for i, feedback_id in enumerate(feedback_ids[:5]):
            await rlhf_system.submit_preference_feedback(
                feedback_id=feedback_id,
                preference_ranking=[0, 1, 2],  # Main output preferred
                confidence=0.8 + 0.1 * (i % 3)
            )
        
        # Submit scalar feedback
        for i, feedback_id in enumerate(feedback_ids[5:]):
            await rlhf_system.submit_scalar_feedback(
                feedback_id=feedback_id,
                rating=0.7 + 0.2 * np.random.random(),
                confidence=0.9
            )
        
        # Train models
        reward_metrics = await rlhf_system.train_reward_model()
        preference_metrics = await rlhf_system.train_preference_model()
        
        # Verify training results
        self.assertIsInstance(reward_metrics, dict)
        self.assertIsInstance(preference_metrics, dict)
        
        if reward_metrics:
            self.assertIn("final_train_loss", reward_metrics)
            self.assertIn("training_samples", reward_metrics)
        
        if preference_metrics:
            self.assertIn("final_train_loss", preference_metrics)
            self.assertIn("training_pairs", preference_metrics)
        
        # Test predictions
        if rlhf_system.reward_model_trained:
            reward = rlhf_system.predict_reward("Test prediction input")
            self.assertIsInstance(reward, float)
        
        if rlhf_system.preference_model_trained:
            preference = rlhf_system.predict_preference("Option A", "Option B")
            self.assertIsInstance(preference, float)
            self.assertGreaterEqual(preference, 0.0)
            self.assertLessEqual(preference, 1.0)
        
        # Get statistics
        stats = rlhf_system.get_feedback_statistics()
        self.assertIsInstance(stats, dict)
        self.assertEqual(stats["total_feedback"], 10)
        
        # Cleanup
        await rlhf_system.cleanup()
        
        self.logger.info("RLHF feedback loop test passed")
    
    async def test_asi_connector_integration(self):
        """Test ASI system connector integration."""
        self.logger.info("Testing ASI connector integration")
        
        # Initialize ASI connector
        asi_connector = ASIConnector(self.config)
        
        # Note: In a real test environment, ASI components would be running
        # For this test, we'll mock the initialization
        try:
            initialized = await asi_connector.initialize()
            # If components are not running, initialization may fail
            if not initialized:
                self.logger.warning("ASI components not available, testing with mock data")
        except Exception as e:
            self.logger.warning(f"ASI initialization failed: {e}, using mock data")
        
        # Test performance data collection (with mock data)
        performance_data = await asi_connector.collect_system_performance()
        
        self.assertIsInstance(performance_data, SystemPerformanceData)
        self.assertIsInstance(performance_data.metrics, dict)
        self.assertIsInstance(performance_data.model_performance, dict)
        self.assertIsInstance(performance_data.decision_quality, dict)
        
        # Test improvement opportunity identification
        opportunities = await asi_connector.identify_improvement_opportunities()
        
        self.assertIsInstance(opportunities, list)
        # May be empty if insufficient performance history
        
        # If we have opportunities, test improvement plan creation
        if opportunities:
            plan = await asi_connector.create_improvement_plan(opportunities[:2])
            
            self.assertIsNotNone(plan)
            self.assertIsNotNone(plan.plan_id)
            self.assertIsInstance(plan.opportunities, list)
            self.assertIsInstance(plan.execution_order, list)
        
        # Cleanup
        await asi_connector.cleanup()
        
        self.logger.info("ASI connector integration test passed")
    
    def test_lisp_symbolic_refactoring(self):
        """Test Lisp symbolic refactoring integration."""
        self.logger.info("Testing Lisp symbolic refactoring")
        
        # Check if SBCL is available
        try:
            result = subprocess.run(['sbcl', '--version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode != 0:
                self.skipTest("SBCL not available")
        except (subprocess.TimeoutExpired, FileNotFoundError):
            self.skipTest("SBCL not available")
        
        # Test Lisp AST mutation system
        lisp_dir = Path(__file__).parent.parent / "lisp-symbolic-refactor"
        test_script = lisp_dir / "tests" / "test-ast-mutation.lisp"
        
        if test_script.exists():
            try:
                # Run Lisp tests
                result = subprocess.run([
                    'sbcl', '--noinform', '--disable-debugger',
                    '--eval', f'(load "{test_script}")',
                    '--eval', '(run-ast-mutation-tests)',
                    '--quit'
                ], capture_output=True, text=True, timeout=30)
                
                # Check if tests passed
                if result.returncode == 0:
                    self.logger.info("Lisp AST mutation tests passed")
                else:
                    self.logger.warning(f"Lisp tests failed: {result.stderr}")
                    
            except subprocess.TimeoutExpired:
                self.logger.warning("Lisp tests timed out")
        else:
            self.logger.warning("Lisp test file not found, skipping")
        
        self.logger.info("Lisp symbolic refactoring test completed")
    
    def test_julia_performance_analytics(self):
        """Test Julia performance analytics integration."""
        self.logger.info("Testing Julia performance analytics")
        
        # Check if Julia is available
        try:
            result = subprocess.run(['julia', '--version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode != 0:
                self.skipTest("Julia not available")
        except (subprocess.TimeoutExpired, FileNotFoundError):
            self.skipTest("Julia not available")
        
        # Test Julia performance analytics
        julia_dir = Path(__file__).parent.parent / "julia-performance-analytics"
        analytics_script = julia_dir / "src" / "PerformanceAnalytics.jl"
        
        if analytics_script.exists():
            try:
                # Run Julia analytics test
                julia_test_code = '''
                include("src/PerformanceAnalytics.jl")
                using .PerformanceAnalytics
                
                # Create monitor and collect some test metrics
                monitor = PerformanceMonitor()
                
                # Simulate metric collection
                for i in 1:10
                    collect_metrics(monitor, "test_metric", rand() * 100, "units", "test_source")
                    collect_metrics(monitor, "cpu_usage", rand() * 100, "percent", "system")
                end
                
                # Test analysis
                analysis = analyze_performance(monitor, 1)
                println("Analysis completed: ", length(analysis), " metrics analyzed")
                
                # Test anomaly detection
                anomalies = detect_anomalies(monitor, "test_metric")
                println("Anomalies detected: ", length(anomalies))
                
                println("Julia performance analytics test passed")
                '''
                
                result = subprocess.run([
                    'julia', '--project=' + str(julia_dir),
                    '-e', julia_test_code
                ], capture_output=True, text=True, timeout=60, cwd=julia_dir)
                
                if result.returncode == 0:
                    self.logger.info("Julia performance analytics test passed")
                    self.assertIn("test passed", result.stdout)
                else:
                    self.logger.warning(f"Julia test failed: {result.stderr}")
                    
            except subprocess.TimeoutExpired:
                self.logger.warning("Julia test timed out")
        else:
            self.logger.warning("Julia analytics script not found, skipping")
        
        self.logger.info("Julia performance analytics test completed")
    
    def test_end_to_end_improvement_cycle(self):
        """Test complete end-to-end improvement cycle."""
        self.logger.info("Testing end-to-end improvement cycle")
        
        # This test simulates a complete improvement cycle
        # In a real environment, this would involve all components
        
        # 1. Performance monitoring (simulated)
        performance_metrics = {
            "model_accuracy": 0.85,
            "decision_latency_ms": 25,
            "system_cpu_usage": 75,
            "memory_usage_mb": 2048
        }
        
        # 2. Identify improvement opportunities (simulated)
        improvement_needed = performance_metrics["model_accuracy"] < 0.9
        
        if improvement_needed:
            # 3. Genetic programming for model architecture optimization
            genetic_config = GeneticConfig(
                population_size=10,
                max_generations=3
            )
            
            genetic_engine = DEAPGeneticEngine(genetic_config)
            
            # Simple optimization problem (hyperparameter tuning simulation)
            def hyperparameter_fitness(individual):
                # Simulate model training with these hyperparameters
                lr, batch_size, hidden_size = individual
                
                # Mock fitness based on hyperparameters
                fitness = 0.8 + 0.1 * (1 - abs(lr - 0.001)) + 0.05 * (1 - abs(batch_size - 32) / 32) + 0.05 * (1 - abs(hidden_size - 128) / 128)
                return (fitness,)
            
            genetic_engine.setup_genetic_algorithm(
                individual_size=3,
                fitness_function=hyperparameter_fitness,
                gene_type="float",
                bounds=(0.0001, 0.01)  # Learning rate bounds
            )
            
            result = genetic_engine.evolve()
            
            # 4. Validate improvement
            if result.best_fitness > performance_metrics["model_accuracy"]:
                improvement_achieved = True
                new_accuracy = result.best_fitness
            else:
                improvement_achieved = False
                new_accuracy = performance_metrics["model_accuracy"]
            
            genetic_engine.cleanup()
        else:
            improvement_achieved = False
            new_accuracy = performance_metrics["model_accuracy"]
        
        # 5. Record results
        cycle_results = {
            "initial_accuracy": performance_metrics["model_accuracy"],
            "final_accuracy": new_accuracy,
            "improvement_achieved": improvement_achieved,
            "cycle_duration_seconds": 10  # Simulated
        }
        
        # Verify cycle completed successfully
        self.assertIsInstance(cycle_results, dict)
        self.assertIn("improvement_achieved", cycle_results)
        self.assertGreaterEqual(cycle_results["final_accuracy"], cycle_results["initial_accuracy"])
        
        self.logger.info(f"End-to-end improvement cycle completed: {cycle_results}")
    
    def test_metrics_collection(self):
        """Test metrics collection and reporting."""
        self.logger.info("Testing metrics collection")
        
        metrics = get_metrics_collector()
        
        # Record some test metrics
        metrics.record_genetic_evolution(
            generation=10,
            best_fitness=0.95,
            avg_fitness=0.75,
            diversity=0.3,
            population_size=50
        )
        
        metrics.record_human_feedback(
            feedback_type="preference",
            user_id="test_user",
            confidence=0.8
        )
        
        # Get performance summary
        summary = metrics.get_performance_summary(window_minutes=60)
        
        self.assertIsInstance(summary, dict)
        self.assertIn("genetic_evolution", summary)
        self.assertIn("human_feedback", summary)
        
        self.logger.info("Metrics collection test passed")


def run_integration_tests():
    """Run all integration tests."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Create test suite
    suite = unittest.TestSuite()
    
    # Add test cases
    suite.addTest(unittest.makeSuite(TestSelfImprovementIntegration))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Return success status
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_integration_tests()
    sys.exit(0 if success else 1)
