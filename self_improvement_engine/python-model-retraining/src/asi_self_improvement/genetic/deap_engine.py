"""
DEAP-based Genetic Programming Engine for ASI Self-Improvement.

Provides genetic algorithms and programming capabilities using the DEAP framework
for evolving neural network architectures, hyperparameters, and code structures.
"""

import random
import numpy as np
import pickle
import json
from typing import Any, Dict, List, Optional, Tuple, Callable, Union
from dataclasses import dataclass, field
from datetime import datetime
import multiprocessing as mp

from deap import base, creator, tools, algorithms, gp
import networkx as nx

from ..utils.logger import get_logger, PerformanceLogger
from ..utils.metrics import MetricsCollector, get_metrics_collector
from ..utils.config import Config

logger = get_logger(__name__)


@dataclass
class GeneticConfig:
    """Configuration for genetic algorithms."""
    population_size: int = 100
    max_generations: int = 50
    mutation_rate: float = 0.1
    crossover_rate: float = 0.7
    elitism_rate: float = 0.1
    tournament_size: int = 3
    max_tree_depth: int = 8
    min_tree_depth: int = 2
    
    # Fitness and selection
    fitness_weights: Tuple[float, ...] = (1.0,)  # Maximizing fitness
    selection_method: str = "tournament"
    replacement_strategy: str = "generational"
    
    # Bloat control and diversity
    enable_bloat_control: bool = True
    diversity_pressure: float = 0.1
    parsimony_coefficient: float = 0.01
    
    # Parallelization
    enable_parallel: bool = True
    num_processes: int = 4
    
    # Termination criteria
    max_fitness: float = 1.0
    convergence_threshold: float = 0.001
    stagnation_generations: int = 10


@dataclass
class EvolutionResult:
    """Result of genetic evolution."""
    best_individual: Any
    best_fitness: float
    generation: int
    population: List[Any]
    fitness_history: List[float]
    diversity_history: List[float]
    size_history: List[float]
    statistics: Dict[str, Any]
    convergence_data: Dict[str, Any]


class DEAPGeneticEngine:
    """DEAP-based genetic programming engine."""
    
    def __init__(self, config: GeneticConfig):
        self.config = config
        self.logger = get_logger(__name__)
        self.metrics = get_metrics_collector()
        
        # DEAP setup
        self.toolbox = base.Toolbox()
        self.stats = tools.Statistics()
        self.logbook = tools.Logbook()
        
        # Evolution tracking
        self.generation = 0
        self.best_individual = None
        self.best_fitness = float('-inf')
        self.fitness_history = []
        self.diversity_history = []
        self.size_history = []
        
        # Parallel processing
        if self.config.enable_parallel:
            self.pool = mp.Pool(processes=self.config.num_processes)
            self.toolbox.register("map", self.pool.map)
        
        self.logger.info("DEAP Genetic Engine initialized")
    
    def setup_genetic_programming(self, 
                                  primitive_set: gp.PrimitiveSet,
                                  fitness_function: Callable,
                                  individual_class: str = "Individual"):
        """Setup genetic programming with primitive set and fitness function."""
        
        # Create fitness and individual classes
        if not hasattr(creator, "FitnessMax"):
            creator.create("FitnessMax", base.Fitness, weights=self.config.fitness_weights)
        
        if not hasattr(creator, individual_class):
            creator.create(individual_class, gp.PrimitiveTree, fitness=creator.FitnessMax)
        
        # Register genetic operators
        self.toolbox.register("expr", gp.genHalfAndHalf, 
                             pset=primitive_set, 
                             min_=self.config.min_tree_depth, 
                             max_=self.config.max_tree_depth)
        
        self.toolbox.register("individual", tools.initIterate, 
                             getattr(creator, individual_class), 
                             self.toolbox.expr)
        
        self.toolbox.register("population", tools.initRepeat, 
                             list, self.toolbox.individual)
        
        self.toolbox.register("compile", gp.compile, pset=primitive_set)
        self.toolbox.register("evaluate", fitness_function)
        
        # Selection and genetic operators
        if self.config.selection_method == "tournament":
            self.toolbox.register("select", tools.selTournament, 
                                 tournsize=self.config.tournament_size)
        elif self.config.selection_method == "roulette":
            self.toolbox.register("select", tools.selRoulette)
        elif self.config.selection_method == "rank":
            self.toolbox.register("select", tools.selRank)
        
        self.toolbox.register("mate", gp.cxOnePoint)
        self.toolbox.register("expr_mut", gp.genFull, 
                             min_=0, max_=self.config.max_tree_depth)
        self.toolbox.register("mutate", gp.mutUniform, 
                             expr=self.toolbox.expr_mut, 
                             pset=primitive_set)
        
        # Bloat control
        if self.config.enable_bloat_control:
            self.toolbox.decorate("mate", gp.staticLimit(
                key=len, max_value=self.config.max_tree_depth * 2))
            self.toolbox.decorate("mutate", gp.staticLimit(
                key=len, max_value=self.config.max_tree_depth * 2))
        
        # Statistics
        self.stats.register("avg", np.mean)
        self.stats.register("std", np.std)
        self.stats.register("min", np.min)
        self.stats.register("max", np.max)
        self.stats.register("size", lambda pop: np.mean([len(ind) for ind in pop]))
        
        self.logger.info("Genetic programming setup completed")
    
    def setup_genetic_algorithm(self, 
                                individual_size: int,
                                fitness_function: Callable,
                                gene_type: str = "float",
                                bounds: Optional[Tuple[float, float]] = None):
        """Setup genetic algorithm for parameter optimization."""
        
        # Create fitness and individual classes
        if not hasattr(creator, "FitnessMax"):
            creator.create("FitnessMax", base.Fitness, weights=self.config.fitness_weights)
        
        if not hasattr(creator, "Individual"):
            creator.create("Individual", list, fitness=creator.FitnessMax)
        
        # Register genetic operators based on gene type
        if gene_type == "float":
            if bounds:
                self.toolbox.register("attr_float", random.uniform, bounds[0], bounds[1])
            else:
                self.toolbox.register("attr_float", random.gauss, 0, 1)
            self.toolbox.register("individual", tools.initRepeat, 
                                 creator.Individual, self.toolbox.attr_float, individual_size)
            self.toolbox.register("mutate", tools.mutGaussian, mu=0, sigma=0.1, indpb=0.1)
        
        elif gene_type == "int":
            if bounds:
                self.toolbox.register("attr_int", random.randint, int(bounds[0]), int(bounds[1]))
            else:
                self.toolbox.register("attr_int", random.randint, 0, 100)
            self.toolbox.register("individual", tools.initRepeat, 
                                 creator.Individual, self.toolbox.attr_int, individual_size)
            self.toolbox.register("mutate", tools.mutUniformInt, low=int(bounds[0]) if bounds else 0,
                                 up=int(bounds[1]) if bounds else 100, indpb=0.1)
        
        elif gene_type == "binary":
            self.toolbox.register("attr_bool", random.randint, 0, 1)
            self.toolbox.register("individual", tools.initRepeat, 
                                 creator.Individual, self.toolbox.attr_bool, individual_size)
            self.toolbox.register("mutate", tools.mutFlipBit, indpb=0.1)
        
        self.toolbox.register("population", tools.initRepeat, list, self.toolbox.individual)
        self.toolbox.register("evaluate", fitness_function)
        
        # Selection and crossover
        if self.config.selection_method == "tournament":
            self.toolbox.register("select", tools.selTournament, 
                                 tournsize=self.config.tournament_size)
        
        if gene_type in ["float", "int"]:
            self.toolbox.register("mate", tools.cxBlend, alpha=0.5)
        else:
            self.toolbox.register("mate", tools.cxTwoPoint)
        
        # Statistics
        self.stats.register("avg", np.mean)
        self.stats.register("std", np.std)
        self.stats.register("min", np.min)
        self.stats.register("max", np.max)
        
        self.logger.info("Genetic algorithm setup completed")
    
    def evolve(self, 
               initial_population: Optional[List] = None,
               callback: Optional[Callable] = None) -> EvolutionResult:
        """Run genetic evolution."""
        
        with PerformanceLogger(self.logger, "genetic_evolution"):
            # Initialize population
            if initial_population is None:
                population = self.toolbox.population(n=self.config.population_size)
            else:
                population = initial_population
            
            # Initialize logbook
            self.logbook.header = ['gen', 'nevals'] + (self.stats.fields if self.stats else [])
            
            # Evaluate initial population
            fitnesses = self.toolbox.map(self.toolbox.evaluate, population)
            for ind, fit in zip(population, fitnesses):
                ind.fitness.values = fit if isinstance(fit, tuple) else (fit,)
            
            # Record initial statistics
            record = self.stats.compile(population) if self.stats else {}
            self.logbook.record(gen=0, nevals=len(population), **record)
            
            # Evolution loop
            for generation in range(1, self.config.max_generations + 1):
                self.generation = generation
                
                # Selection
                offspring = self.toolbox.select(population, len(population))
                offspring = list(map(self.toolbox.clone, offspring))
                
                # Crossover
                for child1, child2 in zip(offspring[::2], offspring[1::2]):
                    if random.random() < self.config.crossover_rate:
                        self.toolbox.mate(child1, child2)
                        del child1.fitness.values
                        del child2.fitness.values
                
                # Mutation
                for mutant in offspring:
                    if random.random() < self.config.mutation_rate:
                        self.toolbox.mutate(mutant)
                        del mutant.fitness.values
                
                # Evaluate invalid individuals
                invalid_ind = [ind for ind in offspring if not ind.fitness.valid]
                fitnesses = self.toolbox.map(self.toolbox.evaluate, invalid_ind)
                for ind, fit in zip(invalid_ind, fitnesses):
                    ind.fitness.values = fit if isinstance(fit, tuple) else (fit,)
                
                # Replacement
                if self.config.replacement_strategy == "generational":
                    population[:] = offspring
                elif self.config.replacement_strategy == "elitist":
                    # Keep best individuals from previous generation
                    elite_size = int(self.config.elitism_rate * len(population))
                    elite = tools.selBest(population, elite_size)
                    population[:] = elite + offspring[:-elite_size]
                
                # Update statistics
                record = self.stats.compile(population) if self.stats else {}
                self.logbook.record(gen=generation, nevals=len(invalid_ind), **record)
                
                # Track evolution metrics
                self._update_evolution_metrics(population, record)
                
                # Check termination criteria
                if self._check_termination_criteria(population, record):
                    self.logger.info(f"Termination criteria met at generation {generation}")
                    break
                
                # Callback for custom processing
                if callback:
                    callback(generation, population, record)
                
                # Log progress
                if generation % 10 == 0:
                    self.logger.info(f"Generation {generation}: "
                                   f"Best={record.get('max', 'N/A'):.4f}, "
                                   f"Avg={record.get('avg', 'N/A'):.4f}")
            
            # Get best individual
            best_individual = tools.selBest(population, 1)[0]
            best_fitness = best_individual.fitness.values[0]
            
            # Create result
            result = EvolutionResult(
                best_individual=best_individual,
                best_fitness=best_fitness,
                generation=self.generation,
                population=population,
                fitness_history=self.fitness_history,
                diversity_history=self.diversity_history,
                size_history=self.size_history,
                statistics=dict(record) if record else {},
                convergence_data=self._get_convergence_data()
            )
            
            self.logger.info(f"Evolution completed: Best fitness = {best_fitness:.4f}")
            return result
    
    def _update_evolution_metrics(self, population: List, record: Dict):
        """Update evolution tracking metrics."""
        if 'max' in record:
            self.fitness_history.append(record['max'])
            if record['max'] > self.best_fitness:
                self.best_fitness = record['max']
                self.best_individual = tools.selBest(population, 1)[0]
        
        # Calculate diversity
        diversity = self._calculate_diversity(population)
        self.diversity_history.append(diversity)
        
        # Calculate average size (for GP)
        if hasattr(population[0], '__len__'):
            avg_size = np.mean([len(ind) for ind in population])
            self.size_history.append(avg_size)
        
        # Record metrics
        self.metrics.record_evolution_metrics(
            generation=self.generation,
            best_fitness=self.best_fitness,
            avg_fitness=record.get('avg', 0),
            diversity=diversity,
            population_size=len(population)
        )
    
    def _calculate_diversity(self, population: List) -> float:
        """Calculate population diversity."""
        if len(population) < 2:
            return 0.0
        
        # For genetic programming (tree-based)
        if hasattr(population[0], 'height'):
            # Use tree edit distance or structural similarity
            return self._calculate_tree_diversity(population)
        
        # For genetic algorithms (vector-based)
        else:
            # Use Euclidean distance in genotype space
            return self._calculate_vector_diversity(population)
    
    def _calculate_tree_diversity(self, population: List) -> float:
        """Calculate diversity for tree-based individuals."""
        # Simplified diversity measure based on tree sizes and depths
        sizes = [len(ind) for ind in population]
        heights = [ind.height for ind in population if hasattr(ind, 'height')]
        
        size_diversity = np.std(sizes) / (np.mean(sizes) + 1e-6)
        height_diversity = np.std(heights) / (np.mean(heights) + 1e-6) if heights else 0
        
        return (size_diversity + height_diversity) / 2
    
    def _calculate_vector_diversity(self, population: List) -> float:
        """Calculate diversity for vector-based individuals."""
        if not population:
            return 0.0
        
        # Convert to numpy array
        vectors = np.array([list(ind) for ind in population])
        
        # Calculate pairwise distances
        distances = []
        for i in range(len(vectors)):
            for j in range(i + 1, len(vectors)):
                dist = np.linalg.norm(vectors[i] - vectors[j])
                distances.append(dist)
        
        return np.mean(distances) if distances else 0.0
    
    def _check_termination_criteria(self, population: List, record: Dict) -> bool:
        """Check if termination criteria are met."""
        # Maximum fitness reached
        if 'max' in record and record['max'] >= self.config.max_fitness:
            return True
        
        # Convergence check
        if len(self.fitness_history) >= self.config.stagnation_generations:
            recent_fitness = self.fitness_history[-self.config.stagnation_generations:]
            if max(recent_fitness) - min(recent_fitness) < self.config.convergence_threshold:
                return True
        
        # Diversity check (population converged)
        if self.diversity_history and self.diversity_history[-1] < 0.001:
            return True
        
        return False
    
    def _get_convergence_data(self) -> Dict[str, Any]:
        """Get convergence analysis data."""
        return {
            "fitness_trend": self._calculate_fitness_trend(),
            "diversity_trend": self._calculate_diversity_trend(),
            "convergence_rate": self._calculate_convergence_rate(),
            "stagnation_periods": self._identify_stagnation_periods()
        }
    
    def _calculate_fitness_trend(self) -> float:
        """Calculate fitness improvement trend."""
        if len(self.fitness_history) < 2:
            return 0.0
        
        x = np.arange(len(self.fitness_history))
        y = np.array(self.fitness_history)
        
        # Linear regression slope
        slope = np.polyfit(x, y, 1)[0]
        return slope
    
    def _calculate_diversity_trend(self) -> float:
        """Calculate diversity change trend."""
        if len(self.diversity_history) < 2:
            return 0.0
        
        x = np.arange(len(self.diversity_history))
        y = np.array(self.diversity_history)
        
        # Linear regression slope
        slope = np.polyfit(x, y, 1)[0]
        return slope
    
    def _identify_stagnation_periods(self) -> List[Tuple[int, int]]:
        """Identify periods of fitness stagnation."""
        stagnation_periods = []
        if len(self.fitness_history) < 5:
            return stagnation_periods
        
        threshold = self.config.convergence_threshold
        current_start = None
        
        for i in range(1, len(self.fitness_history)):
            improvement = self.fitness_history[i] - self.fitness_history[i-1]
            
            if improvement < threshold:
                if current_start is None:
                    current_start = i - 1
            else:
                if current_start is not None:
                    stagnation_periods.append((current_start, i - 1))
                    current_start = None
        
        # Handle ongoing stagnation
        if current_start is not None:
            stagnation_periods.append((current_start, len(self.fitness_history) - 1))
        
        return stagnation_periods
    
    def _calculate_convergence_rate(self) -> float:
        """Calculate the rate of convergence."""
        if len(self.fitness_history) < 2:
            return 0.0
        
        # Calculate the rate of fitness improvement
        improvements = []
        for i in range(1, len(self.fitness_history)):
            improvement = self.fitness_history[i] - self.fitness_history[i-1]
            improvements.append(max(0, improvement))  # Only positive improvements
        
        return np.mean(improvements) if improvements else 0.0
    
    def save_evolution_state(self, filepath: str):
        """Save the current evolution state."""
        state = {
            "config": self.config,
            "generation": self.generation,
            "best_individual": self.best_individual,
            "best_fitness": self.best_fitness,
            "fitness_history": self.fitness_history,
            "diversity_history": self.diversity_history,
            "size_history": self.size_history,
            "logbook": self.logbook
        }
        
        with open(filepath, 'wb') as f:
            pickle.dump(state, f)
        
        self.logger.info(f"Evolution state saved to {filepath}")
    
    def load_evolution_state(self, filepath: str):
        """Load evolution state from file."""
        with open(filepath, 'rb') as f:
            state = pickle.load(f)
        
        self.config = state["config"]
        self.generation = state["generation"]
        self.best_individual = state["best_individual"]
        self.best_fitness = state["best_fitness"]
        self.fitness_history = state["fitness_history"]
        self.diversity_history = state["diversity_history"]
        self.size_history = state["size_history"]
        self.logbook = state["logbook"]
        
        self.logger.info(f"Evolution state loaded from {filepath}")
    
    def cleanup(self):
        """Cleanup resources."""
        if hasattr(self, 'pool') and self.pool:
            self.pool.close()
            self.pool.join()
        
        self.logger.info("DEAP Genetic Engine cleanup completed")
