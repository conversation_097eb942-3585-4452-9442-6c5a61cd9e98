"""
Reinforcement Learning from Human Feedback (RLHF) for ASI Self-Improvement.

Implements human feedback collection, preference learning, and reward model training
for continuous improvement of AI systems through human guidance.
"""

import asyncio
import json
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from typing import Any, Dict, List, Optional, Tuple, Callable, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import deque
import uuid

from transformers import AutoTokenizer, AutoModel
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, precision_recall_fscore_support

from ..utils.logger import get_logger, PerformanceLogger
from ..utils.metrics import MetricsCollector, get_metrics_collector
from ..utils.config import Config

logger = get_logger(__name__)


@dataclass
class HumanFeedback:
    """Structure for human feedback data."""
    feedback_id: str
    session_id: str
    user_id: str
    timestamp: datetime
    
    # Input context
    input_data: Dict[str, Any]
    model_output: Any
    alternative_outputs: List[Any] = field(default_factory=list)
    
    # Feedback data
    preference_ranking: List[int] = field(default_factory=list)  # Ranking of outputs
    binary_preference: Optional[bool] = None  # Better/worse than baseline
    scalar_rating: Optional[float] = None  # 0-1 rating
    categorical_feedback: Optional[str] = None  # Good/Bad/Neutral
    
    # Detailed feedback
    explanation: Optional[str] = None
    improvement_suggestions: List[str] = field(default_factory=list)
    confidence: Optional[float] = None  # Human's confidence in feedback
    
    # Metadata
    feedback_type: str = "preference"  # preference, rating, binary, categorical
    domain: Optional[str] = None
    task_type: Optional[str] = None
    difficulty: Optional[str] = None
    tags: List[str] = field(default_factory=list)


@dataclass
class RLHFConfig:
    """Configuration for RLHF system."""
    # Feedback collection
    max_feedback_queue_size: int = 10000
    feedback_batch_size: int = 32
    min_feedback_for_training: int = 100
    
    # Reward model training
    reward_model_lr: float = 1e-4
    reward_model_epochs: int = 10
    reward_model_batch_size: int = 16
    validation_split: float = 0.2
    
    # Preference learning
    preference_model_type: str = "bradley_terry"  # bradley_terry, elo, neural
    preference_learning_rate: float = 1e-3
    preference_regularization: float = 0.01
    
    # Policy optimization
    ppo_lr: float = 3e-4
    ppo_epochs: int = 4
    ppo_clip_ratio: float = 0.2
    ppo_value_coef: float = 0.5
    ppo_entropy_coef: float = 0.01
    
    # Human feedback integration
    human_feedback_weight: float = 1.0
    automated_feedback_weight: float = 0.5
    feedback_decay_rate: float = 0.95  # Decay older feedback
    
    # Quality control
    min_human_confidence: float = 0.5
    require_consensus: bool = False
    consensus_threshold: float = 0.7
    outlier_detection: bool = True


class RewardModel(nn.Module):
    """Neural network reward model for RLHF."""
    
    def __init__(self, input_dim: int, hidden_dims: List[int] = [512, 256, 128]):
        super().__init__()
        
        layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.1)
            ])
            prev_dim = hidden_dim
        
        # Output layer for reward prediction
        layers.append(nn.Linear(prev_dim, 1))
        
        self.network = nn.Sequential(*layers)
        self.input_dim = input_dim
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return self.network(x)


class PreferenceModel(nn.Module):
    """Neural network for learning human preferences."""
    
    def __init__(self, input_dim: int, hidden_dims: List[int] = [256, 128]):
        super().__init__()
        
        layers = []
        prev_dim = input_dim * 2  # Concatenate two options for comparison
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.1)
            ])
            prev_dim = hidden_dim
        
        # Output probability that first option is preferred
        layers.append(nn.Linear(prev_dim, 1))
        layers.append(nn.Sigmoid())
        
        self.network = nn.Sequential(*layers)
    
    def forward(self, option1: torch.Tensor, option2: torch.Tensor) -> torch.Tensor:
        combined = torch.cat([option1, option2], dim=-1)
        return self.network(combined)


class RLHFFeedbackLoop:
    """Main RLHF feedback loop implementation."""
    
    def __init__(self, config: RLHFConfig):
        self.config = config
        self.logger = get_logger(__name__)
        self.metrics = get_metrics_collector()
        
        # Feedback storage
        self.feedback_queue = deque(maxlen=config.max_feedback_queue_size)
        self.feedback_history = []
        
        # Models
        self.reward_model = None
        self.preference_model = None
        self.tokenizer = None
        self.feature_extractor = None
        
        # Training state
        self.reward_model_trained = False
        self.preference_model_trained = False
        self.training_metrics = {}
        
        # Human feedback sessions
        self.active_sessions = {}
        self.user_profiles = {}
        
        self.logger.info("RLHF Feedback Loop initialized")
    
    async def initialize_models(self, model_name: str = "sentence-transformers/all-MiniLM-L6-v2"):
        """Initialize the reward and preference models."""
        try:
            # Initialize feature extractor for text inputs
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            self.feature_extractor = AutoModel.from_pretrained(model_name)
            
            # Get feature dimension
            sample_input = self.tokenizer("sample text", return_tensors="pt", padding=True, truncation=True)
            with torch.no_grad():
                sample_features = self.feature_extractor(**sample_input)
                feature_dim = sample_features.last_hidden_state.mean(dim=1).shape[-1]
            
            # Initialize models
            self.reward_model = RewardModel(feature_dim)
            self.preference_model = PreferenceModel(feature_dim)
            
            self.logger.info(f"Models initialized with feature dimension: {feature_dim}")
            
        except Exception as e:
            self.logger.error(f"Error initializing models: {e}")
            raise
    
    def extract_features(self, text_input: str) -> torch.Tensor:
        """Extract features from text input."""
        if self.tokenizer is None or self.feature_extractor is None:
            raise ValueError("Models not initialized. Call initialize_models() first.")
        
        # Tokenize input
        inputs = self.tokenizer(text_input, return_tensors="pt", 
                               padding=True, truncation=True, max_length=512)
        
        # Extract features
        with torch.no_grad():
            outputs = self.feature_extractor(**inputs)
            # Use mean pooling of last hidden states
            features = outputs.last_hidden_state.mean(dim=1)
        
        return features
    
    async def collect_feedback(self, 
                              input_data: Dict[str, Any],
                              model_output: Any,
                              user_id: str,
                              session_id: Optional[str] = None,
                              alternative_outputs: Optional[List[Any]] = None) -> str:
        """Collect human feedback for a model output."""
        
        feedback_id = str(uuid.uuid4())
        session_id = session_id or str(uuid.uuid4())
        
        feedback = HumanFeedback(
            feedback_id=feedback_id,
            session_id=session_id,
            user_id=user_id,
            timestamp=datetime.utcnow(),
            input_data=input_data,
            model_output=model_output,
            alternative_outputs=alternative_outputs or []
        )
        
        # Store in queue for processing
        self.feedback_queue.append(feedback)
        
        # Update user session
        if session_id not in self.active_sessions:
            self.active_sessions[session_id] = {
                "user_id": user_id,
                "start_time": datetime.utcnow(),
                "feedback_count": 0
            }
        
        self.active_sessions[session_id]["feedback_count"] += 1
        
        self.logger.debug(f"Feedback collected: {feedback_id}")
        return feedback_id
    
    async def submit_preference_feedback(self,
                                       feedback_id: str,
                                       preference_ranking: List[int],
                                       explanation: Optional[str] = None,
                                       confidence: Optional[float] = None) -> bool:
        """Submit preference ranking feedback."""
        
        # Find feedback in queue
        feedback = None
        for fb in self.feedback_queue:
            if fb.feedback_id == feedback_id:
                feedback = fb
                break
        
        if feedback is None:
            self.logger.warning(f"Feedback {feedback_id} not found")
            return False
        
        # Update feedback with preference data
        feedback.preference_ranking = preference_ranking
        feedback.explanation = explanation
        feedback.confidence = confidence
        feedback.feedback_type = "preference"
        
        # Move to history
        self.feedback_history.append(feedback)
        self.feedback_queue.remove(feedback)
        
        # Record metrics
        self.metrics.record_human_feedback(
            feedback_type="preference",
            user_id=feedback.user_id,
            confidence=confidence or 1.0
        )
        
        self.logger.info(f"Preference feedback submitted: {feedback_id}")
        return True
    
    async def submit_scalar_feedback(self,
                                   feedback_id: str,
                                   rating: float,
                                   explanation: Optional[str] = None,
                                   confidence: Optional[float] = None) -> bool:
        """Submit scalar rating feedback."""
        
        # Find and update feedback
        feedback = None
        for fb in self.feedback_queue:
            if fb.feedback_id == feedback_id:
                feedback = fb
                break
        
        if feedback is None:
            return False
        
        feedback.scalar_rating = rating
        feedback.explanation = explanation
        feedback.confidence = confidence
        feedback.feedback_type = "rating"
        
        # Move to history
        self.feedback_history.append(feedback)
        self.feedback_queue.remove(feedback)
        
        # Record metrics
        self.metrics.record_human_feedback(
            feedback_type="rating",
            user_id=feedback.user_id,
            rating=rating,
            confidence=confidence or 1.0
        )
        
        self.logger.info(f"Scalar feedback submitted: {feedback_id}")
        return True
    
    async def train_reward_model(self) -> Dict[str, float]:
        """Train the reward model on collected feedback."""
        
        if len(self.feedback_history) < self.config.min_feedback_for_training:
            self.logger.warning(f"Insufficient feedback for training: {len(self.feedback_history)}")
            return {}
        
        with PerformanceLogger(self.logger, "reward_model_training"):
            # Prepare training data
            X, y = self._prepare_reward_training_data()
            
            if len(X) == 0:
                self.logger.warning("No valid training data for reward model")
                return {}
            
            # Split data
            X_train, X_val, y_train, y_val = train_test_split(
                X, y, test_size=self.config.validation_split, random_state=42
            )
            
            # Convert to tensors
            X_train = torch.FloatTensor(X_train)
            X_val = torch.FloatTensor(X_val)
            y_train = torch.FloatTensor(y_train).unsqueeze(1)
            y_val = torch.FloatTensor(y_val).unsqueeze(1)
            
            # Training setup
            optimizer = optim.Adam(self.reward_model.parameters(), lr=self.config.reward_model_lr)
            criterion = nn.MSELoss()
            
            # Training loop
            train_losses = []
            val_losses = []
            
            for epoch in range(self.config.reward_model_epochs):
                # Training
                self.reward_model.train()
                train_loss = 0.0
                
                for i in range(0, len(X_train), self.config.reward_model_batch_size):
                    batch_X = X_train[i:i + self.config.reward_model_batch_size]
                    batch_y = y_train[i:i + self.config.reward_model_batch_size]
                    
                    optimizer.zero_grad()
                    predictions = self.reward_model(batch_X)
                    loss = criterion(predictions, batch_y)
                    loss.backward()
                    optimizer.step()
                    
                    train_loss += loss.item()
                
                # Validation
                self.reward_model.eval()
                with torch.no_grad():
                    val_predictions = self.reward_model(X_val)
                    val_loss = criterion(val_predictions, y_val).item()
                
                train_losses.append(train_loss / len(X_train))
                val_losses.append(val_loss)
                
                if epoch % 5 == 0:
                    self.logger.info(f"Epoch {epoch}: Train Loss = {train_losses[-1]:.4f}, "
                                   f"Val Loss = {val_losses[-1]:.4f}")
            
            self.reward_model_trained = True
            
            # Calculate metrics
            metrics = {
                "final_train_loss": train_losses[-1],
                "final_val_loss": val_losses[-1],
                "training_samples": len(X_train),
                "validation_samples": len(X_val)
            }
            
            self.training_metrics["reward_model"] = metrics
            self.logger.info("Reward model training completed")
            
            return metrics
    
    async def train_preference_model(self) -> Dict[str, float]:
        """Train the preference model on collected feedback."""
        
        with PerformanceLogger(self.logger, "preference_model_training"):
            # Prepare preference pairs
            pairs, labels = self._prepare_preference_training_data()
            
            if len(pairs) == 0:
                self.logger.warning("No valid preference pairs for training")
                return {}
            
            # Split data
            train_pairs, val_pairs, train_labels, val_labels = train_test_split(
                pairs, labels, test_size=self.config.validation_split, random_state=42
            )
            
            # Training setup
            optimizer = optim.Adam(self.preference_model.parameters(), 
                                 lr=self.config.preference_learning_rate)
            criterion = nn.BCELoss()
            
            # Training loop
            train_losses = []
            val_accuracies = []
            
            for epoch in range(self.config.reward_model_epochs):
                # Training
                self.preference_model.train()
                train_loss = 0.0
                
                for i in range(0, len(train_pairs), self.config.reward_model_batch_size):
                    batch_pairs = train_pairs[i:i + self.config.reward_model_batch_size]
                    batch_labels = train_labels[i:i + self.config.reward_model_batch_size]
                    
                    optimizer.zero_grad()
                    
                    # Extract features for each pair
                    batch_loss = 0.0
                    for (option1, option2), label in zip(batch_pairs, batch_labels):
                        feat1 = torch.FloatTensor(option1).unsqueeze(0)
                        feat2 = torch.FloatTensor(option2).unsqueeze(0)
                        target = torch.FloatTensor([label])
                        
                        prediction = self.preference_model(feat1, feat2)
                        loss = criterion(prediction, target)
                        batch_loss += loss
                    
                    batch_loss.backward()
                    optimizer.step()
                    train_loss += batch_loss.item()
                
                # Validation
                self.preference_model.eval()
                val_predictions = []
                val_targets = []
                
                with torch.no_grad():
                    for (option1, option2), label in zip(val_pairs, val_labels):
                        feat1 = torch.FloatTensor(option1).unsqueeze(0)
                        feat2 = torch.FloatTensor(option2).unsqueeze(0)
                        
                        prediction = self.preference_model(feat1, feat2)
                        val_predictions.append(prediction.item() > 0.5)
                        val_targets.append(label)
                
                val_accuracy = accuracy_score(val_targets, val_predictions)
                
                train_losses.append(train_loss / len(train_pairs))
                val_accuracies.append(val_accuracy)
                
                if epoch % 5 == 0:
                    self.logger.info(f"Epoch {epoch}: Train Loss = {train_losses[-1]:.4f}, "
                                   f"Val Accuracy = {val_accuracies[-1]:.4f}")
            
            self.preference_model_trained = True
            
            # Calculate metrics
            metrics = {
                "final_train_loss": train_losses[-1],
                "final_val_accuracy": val_accuracies[-1],
                "training_pairs": len(train_pairs),
                "validation_pairs": len(val_pairs)
            }
            
            self.training_metrics["preference_model"] = metrics
            self.logger.info("Preference model training completed")
            
            return metrics
    
    def _prepare_reward_training_data(self) -> Tuple[List, List]:
        """Prepare training data for reward model."""
        X, y = [], []
        
        for feedback in self.feedback_history:
            if feedback.scalar_rating is not None:
                try:
                    # Extract features from model output
                    if isinstance(feedback.model_output, str):
                        features = self.extract_features(feedback.model_output)
                        X.append(features.squeeze().numpy())
                        y.append(feedback.scalar_rating)
                except Exception as e:
                    self.logger.warning(f"Error processing feedback {feedback.feedback_id}: {e}")
                    continue
        
        return X, y
    
    def _prepare_preference_training_data(self) -> Tuple[List, List]:
        """Prepare preference pairs for training."""
        pairs, labels = [], []
        
        for feedback in self.feedback_history:
            if feedback.preference_ranking and len(feedback.alternative_outputs) > 0:
                try:
                    # Extract features for main output
                    main_features = self.extract_features(str(feedback.model_output))
                    
                    # Create pairs with alternative outputs
                    for i, alt_output in enumerate(feedback.alternative_outputs):
                        alt_features = self.extract_features(str(alt_output))
                        
                        # Determine preference based on ranking
                        main_rank = feedback.preference_ranking[0] if feedback.preference_ranking else 0
                        alt_rank = feedback.preference_ranking[i + 1] if len(feedback.preference_ranking) > i + 1 else 1
                        
                        # Create pair (main preferred if rank is lower)
                        if main_rank < alt_rank:
                            pairs.append((main_features.squeeze().numpy(), alt_features.squeeze().numpy()))
                            labels.append(1.0)  # Main output preferred
                        else:
                            pairs.append((alt_features.squeeze().numpy(), main_features.squeeze().numpy()))
                            labels.append(1.0)  # Alternative preferred
                        
                except Exception as e:
                    self.logger.warning(f"Error processing preference feedback {feedback.feedback_id}: {e}")
                    continue
        
        return pairs, labels
    
    def predict_reward(self, text_input: str) -> float:
        """Predict reward for a given input."""
        if not self.reward_model_trained:
            self.logger.warning("Reward model not trained yet")
            return 0.0
        
        try:
            features = self.extract_features(text_input)
            with torch.no_grad():
                reward = self.reward_model(features)
            return reward.item()
        except Exception as e:
            self.logger.error(f"Error predicting reward: {e}")
            return 0.0
    
    def predict_preference(self, option1: str, option2: str) -> float:
        """Predict preference between two options."""
        if not self.preference_model_trained:
            self.logger.warning("Preference model not trained yet")
            return 0.5
        
        try:
            feat1 = self.extract_features(option1)
            feat2 = self.extract_features(option2)
            
            with torch.no_grad():
                preference = self.preference_model(feat1, feat2)
            return preference.item()
        except Exception as e:
            self.logger.error(f"Error predicting preference: {e}")
            return 0.5
    
    def get_feedback_statistics(self) -> Dict[str, Any]:
        """Get statistics about collected feedback."""
        total_feedback = len(self.feedback_history)
        
        if total_feedback == 0:
            return {"total_feedback": 0}
        
        # Count by type
        type_counts = {}
        confidence_scores = []
        user_counts = {}
        
        for feedback in self.feedback_history:
            # Count by type
            feedback_type = feedback.feedback_type
            type_counts[feedback_type] = type_counts.get(feedback_type, 0) + 1
            
            # Collect confidence scores
            if feedback.confidence is not None:
                confidence_scores.append(feedback.confidence)
            
            # Count by user
            user_id = feedback.user_id
            user_counts[user_id] = user_counts.get(user_id, 0) + 1
        
        return {
            "total_feedback": total_feedback,
            "feedback_by_type": type_counts,
            "average_confidence": np.mean(confidence_scores) if confidence_scores else None,
            "unique_users": len(user_counts),
            "feedback_per_user": dict(user_counts),
            "reward_model_trained": self.reward_model_trained,
            "preference_model_trained": self.preference_model_trained,
            "training_metrics": self.training_metrics
        }
    
    async def cleanup(self):
        """Cleanup resources."""
        # Save feedback history if needed
        # Clear queues and sessions
        self.feedback_queue.clear()
        self.active_sessions.clear()
        
        self.logger.info("RLHF Feedback Loop cleanup completed")
