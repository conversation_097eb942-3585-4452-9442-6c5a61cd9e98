"""
RLHF Training Loop for ASI Self-Improvement Engine.

Implements Reinforcement Learning from Human Feedback training loops for
continuous model improvement based on human preferences and feedback.
"""

import asyncio
import time
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import numpy as np
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple, Callable
import logging
from collections import deque
import pickle
import json

from .feedback_loop import R<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HumanFeedback
from ..utils.logger import get_logger, PerformanceLogger
from ..utils.metrics import get_metrics_collector, RLHFMetrics
from ..integration.learning_engine import LearningEngineClient

logger = get_logger(__name__)


@dataclass
class RLHFTrainingConfig:
    """Configuration for RLHF training."""
    # Training parameters
    batch_size: int = 32
    learning_rate: float = 1e-4
    num_epochs: int = 10
    max_iterations: int = 1000
    
    # PPO parameters
    ppo_epochs: int = 4
    clip_epsilon: float = 0.2
    value_loss_coeff: float = 0.5
    entropy_coeff: float = 0.01
    max_grad_norm: float = 0.5
    
    # Reward model parameters
    reward_model_lr: float = 1e-5
    reward_model_epochs: int = 5
    preference_batch_size: int = 16
    
    # Training schedule
    training_interval_hours: int = 6
    min_feedback_samples: int = 100
    max_feedback_age_hours: int = 168  # 1 week
    
    # Safety and validation
    enable_safety_checks: bool = True
    validation_split: float = 0.2
    early_stopping_patience: int = 5
    min_improvement_threshold: float = 0.01
    
    # Model checkpointing
    save_checkpoints: bool = True
    checkpoint_interval: int = 100
    max_checkpoints: int = 10


class RewardModel(nn.Module):
    """Reward model for RLHF training."""
    
    def __init__(self, input_dim: int, hidden_dims: List[int] = [512, 256, 128]):
        super().__init__()
        
        layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.1)
            ])
            prev_dim = hidden_dim
        
        layers.append(nn.Linear(prev_dim, 1))  # Single reward value
        
        self.network = nn.Sequential(*layers)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return self.network(x)


class PreferenceDataset(Dataset):
    """Dataset for preference learning."""
    
    def __init__(self, preferences: List[Tuple[torch.Tensor, torch.Tensor, float]]):
        self.preferences = preferences
    
    def __len__(self):
        return len(self.preferences)
    
    def __getitem__(self, idx):
        option1, option2, preference = self.preferences[idx]
        return option1, option2, torch.tensor(preference, dtype=torch.float32)


class RLHFTrainer:
    """
    RLHF trainer for continuous model improvement based on human feedback.
    """
    
    def __init__(self, config: RLHFTrainingConfig):
        self.config = config
        self.logger = get_logger(__name__)
        self.metrics = get_metrics_collector()
        
        # Initialize components
        self.feedback_loop = RLHFFeedbackLoop()
        self.learning_client = LearningEngineClient()
        
        # Training state
        self.reward_model = None
        self.policy_model = None
        self.value_model = None
        self.training_history = deque(maxlen=1000)
        self.last_training_time = None
        
        # Optimizers
        self.reward_optimizer = None
        self.policy_optimizer = None
        self.value_optimizer = None
        
        self.logger.info("RLHF trainer initialized")
    
    async def start_training_loop(self):
        """Start the continuous RLHF training loop."""
        self.logger.info("Starting RLHF training loop")
        
        while True:
            try:
                # Check if training is needed
                if self._should_train():
                    await self._run_training_cycle()
                
                # Wait for next training interval
                await asyncio.sleep(self.config.training_interval_hours * 3600)
                
            except Exception as e:
                self.logger.error(f"Error in RLHF training loop: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes before retrying
    
    def _should_train(self) -> bool:
        """Check if training should be triggered."""
        # Check if enough time has passed
        if self.last_training_time:
            time_since_last = datetime.utcnow() - self.last_training_time
            if time_since_last < timedelta(hours=self.config.training_interval_hours):
                return False
        
        # Check if enough feedback is available
        feedback_count = len(self.feedback_loop.feedback_history)
        if feedback_count < self.config.min_feedback_samples:
            self.logger.info(f"Not enough feedback samples: {feedback_count}/{self.config.min_feedback_samples}")
            return False
        
        return True
    
    async def _run_training_cycle(self):
        """Run a complete RLHF training cycle."""
        self.logger.info("Starting RLHF training cycle")
        start_time = time.time()
        
        try:
            # 1. Prepare training data
            training_data = await self._prepare_training_data()
            
            if not training_data:
                self.logger.warning("No training data available")
                return
            
            # 2. Train reward model
            reward_metrics = await self._train_reward_model(training_data['preferences'])
            
            # 3. Train policy with PPO
            policy_metrics = await self._train_policy_ppo(training_data['trajectories'])
            
            # 4. Validate improvements
            validation_metrics = await self._validate_improvements()
            
            # 5. Update models in Learning Engine
            if validation_metrics['improvement'] > self.config.min_improvement_threshold:
                await self._deploy_improved_models()
            
            # Record training metrics
            training_time = time.time() - start_time
            self.metrics.record_rlhf_training(RLHFMetrics(
                training_time_seconds=training_time,
                reward_loss=reward_metrics['final_loss'],
                policy_loss=policy_metrics['final_loss'],
                improvement_score=validation_metrics['improvement'],
                feedback_samples=len(training_data['preferences'])
            ))
            
            self.last_training_time = datetime.utcnow()
            self.logger.info(f"RLHF training cycle completed in {training_time:.2f}s")
            
        except Exception as e:
            self.logger.error(f"RLHF training cycle failed: {e}")
            raise
    
    async def _prepare_training_data(self) -> Dict[str, Any]:
        """Prepare training data from collected feedback."""
        # Get recent feedback
        recent_feedback = [
            fb for fb in self.feedback_loop.feedback_history
            if (datetime.utcnow() - fb.timestamp).total_seconds() < self.config.max_feedback_age_hours * 3600
        ]
        
        if not recent_feedback:
            return {}
        
        # Prepare preference pairs
        preferences = []
        for feedback in recent_feedback:
            if feedback.feedback_type == "preference" and feedback.preferred_option is not None:
                # Convert to feature vectors
                option1_features = self._extract_features(feedback.model_output)
                option2_features = self._extract_features(feedback.alternative_outputs[feedback.preferred_option])
                
                # Create preference pair (preferred option gets score 1.0, other gets 0.0)
                preferences.append((option2_features, option1_features, 1.0))  # Preferred > Original
                
            elif feedback.feedback_type == "rating" and feedback.scalar_rating is not None:
                # Convert rating to preference
                features = self._extract_features(feedback.model_output)
                baseline_features = torch.zeros_like(features)  # Baseline comparison
                
                # Normalize rating to [0, 1]
                normalized_rating = feedback.scalar_rating / 5.0  # Assuming 5-point scale
                preferences.append((features, baseline_features, normalized_rating))
        
        # Prepare trajectory data for policy training
        trajectories = []
        for feedback in recent_feedback:
            if hasattr(feedback, 'trajectory_data'):
                trajectories.append(feedback.trajectory_data)
        
        return {
            'preferences': preferences,
            'trajectories': trajectories,
            'feedback_count': len(recent_feedback)
        }
    
    def _extract_features(self, output: Any) -> torch.Tensor:
        """Extract features from model output for reward model training."""
        # This is a simplified feature extraction
        # In practice, this would be more sophisticated based on the output type
        
        if isinstance(output, str):
            # Text output - use simple features
            features = [
                len(output),  # Length
                output.count(' '),  # Word count
                output.count('.'),  # Sentence count
                len(set(output.lower())),  # Unique characters
            ]
        elif isinstance(output, dict):
            # Structured output
            features = [
                len(output),  # Number of fields
                sum(len(str(v)) for v in output.values()),  # Total content length
            ]
        else:
            # Default features
            features = [1.0, 0.0, 0.0, 0.0]
        
        # Pad or truncate to fixed size
        feature_size = 64
        if len(features) < feature_size:
            features.extend([0.0] * (feature_size - len(features)))
        else:
            features = features[:feature_size]
        
        return torch.tensor(features, dtype=torch.float32)
    
    async def _train_reward_model(self, preferences: List[Tuple[torch.Tensor, torch.Tensor, float]]) -> Dict[str, float]:
        """Train the reward model on preference data."""
        if not preferences:
            return {'final_loss': 0.0}
        
        self.logger.info(f"Training reward model on {len(preferences)} preference pairs")
        
        # Initialize reward model if needed
        if self.reward_model is None:
            feature_dim = preferences[0][0].shape[0]
            self.reward_model = RewardModel(feature_dim)
            self.reward_optimizer = optim.Adam(self.reward_model.parameters(), lr=self.config.reward_model_lr)
        
        # Create dataset and dataloader
        dataset = PreferenceDataset(preferences)
        dataloader = DataLoader(dataset, batch_size=self.config.preference_batch_size, shuffle=True)
        
        # Training loop
        total_loss = 0.0
        num_batches = 0
        
        self.reward_model.train()
        for epoch in range(self.config.reward_model_epochs):
            epoch_loss = 0.0
            
            for batch_idx, (option1, option2, preference) in enumerate(dataloader):
                self.reward_optimizer.zero_grad()
                
                # Get reward predictions
                reward1 = self.reward_model(option1)
                reward2 = self.reward_model(option2)
                
                # Preference loss (Bradley-Terry model)
                preference_prob = torch.sigmoid(reward2 - reward1)
                loss = nn.BCELoss()(preference_prob.squeeze(), preference)
                
                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.reward_model.parameters(), self.config.max_grad_norm)
                self.reward_optimizer.step()
                
                epoch_loss += loss.item()
                total_loss += loss.item()
                num_batches += 1
            
            self.logger.debug(f"Reward model epoch {epoch + 1}/{self.config.reward_model_epochs}, loss: {epoch_loss / len(dataloader):.4f}")
        
        final_loss = total_loss / num_batches if num_batches > 0 else 0.0
        self.logger.info(f"Reward model training completed, final loss: {final_loss:.4f}")
        
        return {'final_loss': final_loss}
    
    async def _train_policy_ppo(self, trajectories: List[Any]) -> Dict[str, float]:
        """Train policy using PPO algorithm."""
        # Simplified PPO implementation
        # In practice, this would be more sophisticated
        
        self.logger.info("Training policy with PPO")
        
        # For now, return dummy metrics
        # Real implementation would train the policy model
        
        return {
            'final_loss': 0.1,
            'policy_improvement': 0.05,
            'value_loss': 0.08
        }
    
    async def _validate_improvements(self) -> Dict[str, float]:
        """Validate model improvements."""
        # Simplified validation
        # In practice, this would run comprehensive tests
        
        self.logger.info("Validating model improvements")
        
        # Simulate validation metrics
        improvement_score = np.random.uniform(0.0, 0.1)  # Random improvement for demo
        
        return {
            'improvement': improvement_score,
            'validation_accuracy': 0.85,
            'safety_score': 0.95
        }
    
    async def _deploy_improved_models(self):
        """Deploy improved models to the Learning Engine."""
        self.logger.info("Deploying improved models")
        
        try:
            # Save model checkpoints
            if self.config.save_checkpoints and self.reward_model is not None:
                checkpoint_path = f"checkpoints/reward_model_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.pt"
                torch.save({
                    'model_state_dict': self.reward_model.state_dict(),
                    'optimizer_state_dict': self.reward_optimizer.state_dict(),
                    'training_config': self.config
                }, checkpoint_path)
                
                self.logger.info(f"Saved reward model checkpoint: {checkpoint_path}")
            
            # Update Learning Engine with improved models
            # This would involve calling the Learning Engine API
            await self.learning_client.update_model("reward_model", self.reward_model)
            
        except Exception as e:
            self.logger.error(f"Failed to deploy improved models: {e}")
            raise
    
    def get_training_status(self) -> Dict[str, Any]:
        """Get current training status."""
        return {
            'last_training_time': self.last_training_time.isoformat() if self.last_training_time else None,
            'feedback_samples': len(self.feedback_loop.feedback_history),
            'reward_model_trained': self.reward_model is not None,
            'training_history_length': len(self.training_history),
            'next_training_due': self._should_train()
        }
    
    async def force_training_cycle(self):
        """Force a training cycle regardless of schedule."""
        self.logger.info("Forcing RLHF training cycle")
        await self._run_training_cycle()
