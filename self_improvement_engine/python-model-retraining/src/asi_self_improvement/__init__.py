"""
ASI Self-Improvement Engine

Autonomous self-modification and optimization platform for the Artificial Super Intelligence (ASI) System.
Combines genetic programming, reinforcement learning from human feedback (RLHF), and evolutionary strategies
to continuously improve system performance, model accuracy, and code quality.

Modules:
    genetic: DEAP-based genetic algorithms and programming
    rlhf: Reinforcement Learning from Human Feedback loops
    evolution: Evolutionary strategies and optimization
    integration: Learning Engine and Decision Engine integration
    utils: Shared utilities and helpers
"""

__version__ = "1.0.0"
__author__ = "ASI System Team"
__email__ = "<EMAIL>"

from .utils.logger import get_logger
from .utils.config import Config
from .utils.metrics import MetricsCollector

# Initialize global logger
logger = get_logger(__name__)

# Package-level exports
__all__ = [
    "logger",
    "Config",
    "MetricsCollector",
]
