"""
Evolutionary Strategies for ASI Self-Improvement Engine.

Implements advanced evolutionary algorithms for optimizing neural network
architectures, hyperparameters, and system configurations.
"""

import numpy as np
import torch
import torch.nn as nn
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Tuple, Callable, Union
import logging
import copy
import random
from collections import deque
import json
import pickle

from ..utils.logger import get_logger, PerformanceLogger
from ..utils.metrics import get_metrics_collector, EvolutionMetrics
from ..integration.learning_engine import LearningEngineClient

logger = get_logger(__name__)


@dataclass
class EvolutionConfig:
    """Configuration for evolutionary strategies."""
    # Population parameters
    population_size: int = 50
    elite_size: int = 10
    offspring_size: int = 40
    max_generations: int = 100
    
    # Mutation parameters
    mutation_rate: float = 0.1
    mutation_strength: float = 0.01
    adaptive_mutation: bool = True
    mutation_decay: float = 0.99
    
    # Selection parameters
    selection_method: str = "tournament"  # tournament, roulette, rank
    tournament_size: int = 3
    selection_pressure: float = 1.5
    
    # Diversity and convergence
    diversity_threshold: float = 0.01
    convergence_threshold: float = 1e-6
    stagnation_limit: int = 10
    
    # Parallelization
    enable_parallel: bool = True
    num_workers: int = 4
    
    # Optimization targets
    objectives: List[str] = field(default_factory=lambda: ["accuracy", "efficiency"])
    weights: List[float] = field(default_factory=lambda: [0.7, 0.3])


class Individual:
    """Individual in the evolutionary population."""
    
    def __init__(self, genome: Dict[str, Any], generation: int = 0):
        self.genome = genome
        self.fitness = None
        self.objectives = {}
        self.generation = generation
        self.age = 0
        self.parent_ids = []
        self.mutation_history = []
        
    def copy(self):
        """Create a deep copy of the individual."""
        new_individual = Individual(copy.deepcopy(self.genome), self.generation)
        new_individual.fitness = self.fitness
        new_individual.objectives = self.objectives.copy()
        new_individual.age = self.age
        new_individual.parent_ids = self.parent_ids.copy()
        new_individual.mutation_history = self.mutation_history.copy()
        return new_individual
    
    def mutate(self, mutation_rate: float, mutation_strength: float):
        """Apply mutations to the individual."""
        mutations_applied = []
        
        for key, value in self.genome.items():
            if random.random() < mutation_rate:
                if isinstance(value, (int, float)):
                    # Numerical mutation
                    if isinstance(value, int):
                        mutation = int(np.random.normal(0, mutation_strength * abs(value) + 1))
                        self.genome[key] = max(1, value + mutation)
                    else:
                        mutation = np.random.normal(0, mutation_strength * abs(value) + 0.01)
                        self.genome[key] = value + mutation
                    mutations_applied.append(f"{key}: {value} -> {self.genome[key]}")
                
                elif isinstance(value, str):
                    # Categorical mutation
                    if key == "activation":
                        options = ["relu", "tanh", "sigmoid", "leaky_relu", "gelu"]
                        self.genome[key] = random.choice(options)
                        mutations_applied.append(f"{key}: {value} -> {self.genome[key]}")
                    elif key == "optimizer":
                        options = ["adam", "sgd", "rmsprop", "adamw"]
                        self.genome[key] = random.choice(options)
                        mutations_applied.append(f"{key}: {value} -> {self.genome[key]}")
                
                elif isinstance(value, list):
                    # List mutation (e.g., layer sizes)
                    if len(value) > 0 and isinstance(value[0], (int, float)):
                        idx = random.randint(0, len(value) - 1)
                        old_val = value[idx]
                        if isinstance(old_val, int):
                            value[idx] = max(1, old_val + int(np.random.normal(0, mutation_strength * old_val + 1)))
                        else:
                            value[idx] = old_val + np.random.normal(0, mutation_strength * abs(old_val) + 0.01)
                        mutations_applied.append(f"{key}[{idx}]: {old_val} -> {value[idx]}")
        
        self.mutation_history.extend(mutations_applied)
        return len(mutations_applied) > 0


class EvolutionaryOptimizer:
    """
    Evolutionary optimizer for neural network architectures and hyperparameters.
    """
    
    def __init__(self, config: EvolutionConfig):
        self.config = config
        self.logger = get_logger(__name__)
        self.metrics = get_metrics_collector()
        
        # Evolution state
        self.population = []
        self.generation = 0
        self.best_individual = None
        self.fitness_history = deque(maxlen=1000)
        self.diversity_history = deque(maxlen=100)
        
        # Adaptive parameters
        self.current_mutation_rate = config.mutation_rate
        self.current_mutation_strength = config.mutation_strength
        self.stagnation_counter = 0
        
        self.logger.info("Evolutionary optimizer initialized")
    
    def initialize_population(self, genome_template: Dict[str, Any]) -> List[Individual]:
        """Initialize the population with random individuals."""
        population = []
        
        for i in range(self.config.population_size):
            # Create random genome based on template
            genome = self._create_random_genome(genome_template)
            individual = Individual(genome, generation=0)
            population.append(individual)
        
        self.population = population
        self.logger.info(f"Initialized population with {len(population)} individuals")
        return population
    
    def _create_random_genome(self, template: Dict[str, Any]) -> Dict[str, Any]:
        """Create a random genome based on template."""
        genome = {}
        
        for key, value in template.items():
            if isinstance(value, dict) and "type" in value:
                # Parameterized value
                if value["type"] == "int":
                    genome[key] = random.randint(value["min"], value["max"])
                elif value["type"] == "float":
                    genome[key] = random.uniform(value["min"], value["max"])
                elif value["type"] == "choice":
                    genome[key] = random.choice(value["options"])
                elif value["type"] == "list_int":
                    size = random.randint(value["min_size"], value["max_size"])
                    genome[key] = [random.randint(value["min_val"], value["max_val"]) for _ in range(size)]
                else:
                    genome[key] = value["default"]
            else:
                # Direct value
                genome[key] = value
        
        return genome
    
    async def evolve(self, 
                     fitness_function: Callable[[Individual], float],
                     max_generations: Optional[int] = None) -> Individual:
        """Run evolutionary optimization."""
        max_generations = max_generations or self.config.max_generations
        
        self.logger.info(f"Starting evolution for {max_generations} generations")
        
        with PerformanceLogger(self.logger, "evolutionary_optimization"):
            for generation in range(max_generations):
                self.generation = generation
                
                # Evaluate population
                await self._evaluate_population(fitness_function)
                
                # Check convergence
                if self._check_convergence():
                    self.logger.info(f"Convergence reached at generation {generation}")
                    break
                
                # Selection and reproduction
                self._selection()
                self._reproduction()
                
                # Adaptive parameter updates
                self._update_adaptive_parameters()
                
                # Log progress
                if generation % 10 == 0:
                    self._log_generation_stats(generation)
        
        self.logger.info(f"Evolution completed after {self.generation + 1} generations")
        return self.best_individual
    
    async def _evaluate_population(self, fitness_function: Callable[[Individual], float]):
        """Evaluate fitness for all individuals in the population."""
        if self.config.enable_parallel:
            # Parallel evaluation (simplified)
            tasks = []
            for individual in self.population:
                if individual.fitness is None:
                    tasks.append(self._evaluate_individual(individual, fitness_function))
            
            if tasks:
                await asyncio.gather(*tasks)
        else:
            # Sequential evaluation
            for individual in self.population:
                if individual.fitness is None:
                    await self._evaluate_individual(individual, fitness_function)
        
        # Update best individual
        self.population.sort(key=lambda x: x.fitness, reverse=True)
        if self.best_individual is None or self.population[0].fitness > self.best_individual.fitness:
            self.best_individual = self.population[0].copy()
        
        # Record fitness statistics
        fitnesses = [ind.fitness for ind in self.population]
        self.fitness_history.append({
            'generation': self.generation,
            'best': max(fitnesses),
            'mean': np.mean(fitnesses),
            'std': np.std(fitnesses)
        })
    
    async def _evaluate_individual(self, individual: Individual, fitness_function: Callable):
        """Evaluate a single individual."""
        try:
            individual.fitness = await fitness_function(individual)
            individual.age += 1
        except Exception as e:
            self.logger.error(f"Error evaluating individual: {e}")
            individual.fitness = 0.0
    
    def _selection(self):
        """Select individuals for reproduction."""
        if self.config.selection_method == "tournament":
            self._tournament_selection()
        elif self.config.selection_method == "roulette":
            self._roulette_selection()
        elif self.config.selection_method == "rank":
            self._rank_selection()
        else:
            self._tournament_selection()  # Default
    
    def _tournament_selection(self):
        """Tournament selection."""
        selected = []
        
        # Keep elite individuals
        elite_count = min(self.config.elite_size, len(self.population))
        selected.extend(self.population[:elite_count])
        
        # Tournament selection for remaining slots
        remaining_slots = self.config.population_size - elite_count
        
        for _ in range(remaining_slots):
            tournament = random.sample(self.population, min(self.config.tournament_size, len(self.population)))
            winner = max(tournament, key=lambda x: x.fitness)
            selected.append(winner.copy())
        
        self.population = selected
    
    def _roulette_selection(self):
        """Roulette wheel selection."""
        # Ensure all fitness values are positive
        min_fitness = min(ind.fitness for ind in self.population)
        if min_fitness < 0:
            adjusted_fitnesses = [ind.fitness - min_fitness + 1 for ind in self.population]
        else:
            adjusted_fitnesses = [ind.fitness for ind in self.population]
        
        total_fitness = sum(adjusted_fitnesses)
        if total_fitness == 0:
            # Fallback to uniform selection
            self.population = random.choices(self.population, k=self.config.population_size)
            return
        
        probabilities = [f / total_fitness for f in adjusted_fitnesses]
        selected = np.random.choice(self.population, size=self.config.population_size, p=probabilities)
        self.population = [ind.copy() for ind in selected]
    
    def _rank_selection(self):
        """Rank-based selection."""
        # Sort by fitness (already done in evaluation)
        ranks = list(range(len(self.population), 0, -1))
        total_rank = sum(ranks)
        
        probabilities = [rank / total_rank for rank in ranks]
        selected = np.random.choice(self.population, size=self.config.population_size, p=probabilities)
        self.population = [ind.copy() for ind in selected]
    
    def _reproduction(self):
        """Create offspring through mutation and crossover."""
        offspring = []
        
        # Generate offspring
        for _ in range(self.config.offspring_size):
            if random.random() < 0.7:  # Crossover probability
                parent1, parent2 = random.sample(self.population, 2)
                child = self._crossover(parent1, parent2)
            else:
                parent = random.choice(self.population)
                child = parent.copy()
            
            # Apply mutation
            child.mutate(self.current_mutation_rate, self.current_mutation_strength)
            child.generation = self.generation + 1
            child.fitness = None  # Reset fitness for re-evaluation
            
            offspring.append(child)
        
        # Replace worst individuals with offspring
        self.population.extend(offspring)
        self.population.sort(key=lambda x: x.fitness, reverse=True)
        self.population = self.population[:self.config.population_size]
    
    def _crossover(self, parent1: Individual, parent2: Individual) -> Individual:
        """Create offspring through crossover."""
        child_genome = {}
        
        for key in parent1.genome:
            if random.random() < 0.5:
                child_genome[key] = copy.deepcopy(parent1.genome[key])
            else:
                child_genome[key] = copy.deepcopy(parent2.genome[key])
        
        child = Individual(child_genome, generation=self.generation + 1)
        child.parent_ids = [id(parent1), id(parent2)]
        
        return child
    
    def _check_convergence(self) -> bool:
        """Check if evolution has converged."""
        if len(self.fitness_history) < 10:
            return False
        
        # Check fitness stagnation
        recent_best = [entry['best'] for entry in list(self.fitness_history)[-10:]]
        if max(recent_best) - min(recent_best) < self.config.convergence_threshold:
            self.stagnation_counter += 1
        else:
            self.stagnation_counter = 0
        
        return self.stagnation_counter >= self.config.stagnation_limit
    
    def _update_adaptive_parameters(self):
        """Update adaptive parameters based on evolution progress."""
        if self.config.adaptive_mutation:
            # Decrease mutation rate over time
            self.current_mutation_rate *= self.config.mutation_decay
            self.current_mutation_strength *= self.config.mutation_decay
            
            # Increase mutation if stagnating
            if self.stagnation_counter > 5:
                self.current_mutation_rate = min(0.5, self.current_mutation_rate * 1.1)
                self.current_mutation_strength = min(0.1, self.current_mutation_strength * 1.1)
    
    def _log_generation_stats(self, generation: int):
        """Log statistics for the current generation."""
        if not self.fitness_history:
            return
        
        stats = self.fitness_history[-1]
        diversity = self._calculate_diversity()
        
        self.logger.info(
            f"Generation {generation}: "
            f"Best={stats['best']:.4f}, "
            f"Mean={stats['mean']:.4f}, "
            f"Std={stats['std']:.4f}, "
            f"Diversity={diversity:.4f}, "
            f"MutRate={self.current_mutation_rate:.4f}"
        )
        
        # Record metrics
        self.metrics.record_evolution(EvolutionMetrics(
            generation=generation,
            best_fitness=stats['best'],
            mean_fitness=stats['mean'],
            diversity=diversity,
            mutation_rate=self.current_mutation_rate
        ))
    
    def _calculate_diversity(self) -> float:
        """Calculate population diversity."""
        if len(self.population) < 2:
            return 0.0
        
        # Simple diversity measure based on genome differences
        total_distance = 0.0
        comparisons = 0
        
        for i in range(len(self.population)):
            for j in range(i + 1, len(self.population)):
                distance = self._genome_distance(self.population[i].genome, self.population[j].genome)
                total_distance += distance
                comparisons += 1
        
        return total_distance / comparisons if comparisons > 0 else 0.0
    
    def _genome_distance(self, genome1: Dict[str, Any], genome2: Dict[str, Any]) -> float:
        """Calculate distance between two genomes."""
        distance = 0.0
        count = 0
        
        for key in genome1:
            if key in genome2:
                val1, val2 = genome1[key], genome2[key]
                if isinstance(val1, (int, float)) and isinstance(val2, (int, float)):
                    distance += abs(val1 - val2) / (abs(val1) + abs(val2) + 1e-8)
                    count += 1
                elif val1 != val2:
                    distance += 1.0
                    count += 1
        
        return distance / count if count > 0 else 0.0
    
    def get_evolution_summary(self) -> Dict[str, Any]:
        """Get summary of evolution progress."""
        return {
            'generation': self.generation,
            'population_size': len(self.population),
            'best_fitness': self.best_individual.fitness if self.best_individual else None,
            'best_genome': self.best_individual.genome if self.best_individual else None,
            'fitness_history': list(self.fitness_history),
            'current_mutation_rate': self.current_mutation_rate,
            'stagnation_counter': self.stagnation_counter
        }
