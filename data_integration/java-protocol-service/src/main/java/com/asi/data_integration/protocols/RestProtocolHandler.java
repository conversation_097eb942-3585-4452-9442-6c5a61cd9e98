package com.asi.data_integration.protocols;

import com.asi.data_integration.config.RestProtocolConfig;
import com.asi.data_integration.monitoring.MetricsCollector;
import com.asi.data_integration.validation.SchemaValidator;
import com.asi.data_integration.utils.StructuredLogger;
import com.asi.data_integration.model.DataRecord;
import com.asi.data_integration.model.ValidationResult;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * REST Protocol Handler for the ASI Data Integration Service.
 * 
 * Handles:
 * - RESTful API data ingestion endpoints
 * - External REST API polling and integration
 * - Webhook endpoints for real-time data push
 * - Authentication and authorization
 * - Rate limiting and throttling
 * - Data validation and transformation
 * - Asynchronous processing
 */
@RestController
@RequestMapping("/api/v1/data")
@Component
public class RestProtocolHandler {
    
    private static final StructuredLogger logger = new StructuredLogger("RestProtocolHandler");
    
    private final RestProtocolConfig config;
    private final MetricsCollector metricsCollector;
    private final SchemaValidator schemaValidator;
    private final ObjectMapper objectMapper;
    private final WebClient webClient;
    private final KafkaTemplate<String, Object> kafkaTemplate;
    
    private final Map<String, AtomicLong> requestCounters = new ConcurrentHashMap<>();
    private final Map<String, Long> lastRequestTime = new ConcurrentHashMap<>();
    
    public RestProtocolHandler(RestProtocolConfig config,
                              MetricsCollector metricsCollector,
                              SchemaValidator schemaValidator,
                              ObjectMapper objectMapper,
                              WebClient webClient,
                              KafkaTemplate<String, Object> kafkaTemplate) {
        this.config = config;
        this.metricsCollector = metricsCollector;
        this.schemaValidator = schemaValidator;
        this.objectMapper = objectMapper;
        this.webClient = webClient;
        this.kafkaTemplate = kafkaTemplate;
    }
    
    @PostConstruct
    public void initialize() {
        logger.info("Initializing REST Protocol Handler", 
            Map.of(
                "maxRequestsPerMinute", String.valueOf(config.getMaxRequestsPerMinute()),
                "enableAuthentication", String.valueOf(config.isEnableAuthentication()),
                "webhookEndpoints", String.valueOf(config.getWebhookEndpoints().size())
            ));
        
        // Start external API polling if configured
        if (config.getExternalApis() != null && !config.getExternalApis().isEmpty()) {
            startExternalApiPolling();
        }
    }
    
    @PreDestroy
    public void cleanup() {
        logger.info("Shutting down REST Protocol Handler");
    }
    
    /**
     * Ingest single data record via POST
     */
    @PostMapping("/ingest")
    public ResponseEntity<Map<String, Object>> ingestData(
            @RequestBody Map<String, Object> data,
            @RequestHeader(value = "X-Source-Type", defaultValue = "REST") String sourceType,
            @RequestHeader(value = "X-API-Key", required = false) String apiKey) {
        
        String correlationId = UUID.randomUUID().toString();
        long startTime = System.currentTimeMillis();
        
        try {
            // Rate limiting check
            if (!checkRateLimit(sourceType)) {
                return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS)
                    .body(Map.of("error", "Rate limit exceeded", "correlationId", correlationId));
            }
            
            // Authentication check
            if (config.isEnableAuthentication() && !validateApiKey(apiKey)) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(Map.of("error", "Invalid API key", "correlationId", correlationId));
            }
            
            // Create data record
            DataRecord record = createDataRecord(data, sourceType, correlationId);
            
            // Validate schema
            ValidationResult validation = schemaValidator.validate(record);
            if (!validation.isValid()) {
                logger.warn("Schema validation failed", 
                    Map.of("correlationId", correlationId, "errors", String.join(", ", validation.getErrors())));
                
                return ResponseEntity.badRequest()
                    .body(Map.of(
                        "error", "Schema validation failed",
                        "details", validation.getErrors(),
                        "correlationId", correlationId
                    ));
            }
            
            // Process asynchronously
            processDataAsync(record);
            
            // Record metrics
            long processingTime = System.currentTimeMillis() - startTime;
            metricsCollector.recordProcessingTime("rest_ingest", processingTime);
            metricsCollector.incrementCounter("rest_requests_total", Map.of("source", sourceType));
            
            logger.info("Data ingested successfully", 
                Map.of(
                    "correlationId", correlationId,
                    "sourceType", sourceType,
                    "processingTimeMs", String.valueOf(processingTime)
                ));
            
            return ResponseEntity.ok(Map.of(
                "status", "accepted",
                "correlationId", correlationId,
                "timestamp", Instant.now().toString()
            ));
            
        } catch (Exception e) {
            logger.error("Data ingestion failed", 
                Map.of("correlationId", correlationId, "error", e.getMessage()));
            
            metricsCollector.incrementCounter("rest_errors_total", Map.of("source", sourceType));
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "Internal server error", "correlationId", correlationId));
        }
    }
    
    /**
     * Ingest batch of data records via POST
     */
    @PostMapping("/ingest/batch")
    public ResponseEntity<Map<String, Object>> ingestBatch(
            @RequestBody List<Map<String, Object>> dataList,
            @RequestHeader(value = "X-Source-Type", defaultValue = "REST") String sourceType,
            @RequestHeader(value = "X-API-Key", required = false) String apiKey) {
        
        String correlationId = UUID.randomUUID().toString();
        long startTime = System.currentTimeMillis();
        
        try {
            // Rate limiting check
            if (!checkRateLimit(sourceType)) {
                return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS)
                    .body(Map.of("error", "Rate limit exceeded", "correlationId", correlationId));
            }
            
            // Authentication check
            if (config.isEnableAuthentication() && !validateApiKey(apiKey)) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(Map.of("error", "Invalid API key", "correlationId", correlationId));
            }
            
            List<DataRecord> validRecords = new ArrayList<>();
            List<String> errors = new ArrayList<>();
            
            // Process each record in the batch
            for (int i = 0; i < dataList.size(); i++) {
                try {
                    Map<String, Object> data = dataList.get(i);
                    DataRecord record = createDataRecord(data, sourceType, correlationId + "_" + i);
                    
                    ValidationResult validation = schemaValidator.validate(record);
                    if (validation.isValid()) {
                        validRecords.add(record);
                    } else {
                        errors.add("Record " + i + ": " + String.join(", ", validation.getErrors()));
                    }
                } catch (Exception e) {
                    errors.add("Record " + i + ": " + e.getMessage());
                }
            }
            
            // Process valid records asynchronously
            if (!validRecords.isEmpty()) {
                processBatchAsync(validRecords);
            }
            
            // Record metrics
            long processingTime = System.currentTimeMillis() - startTime;
            metricsCollector.recordProcessingTime("rest_batch_ingest", processingTime);
            metricsCollector.incrementCounter("rest_batch_requests_total", Map.of("source", sourceType));
            metricsCollector.recordGauge("rest_batch_size", dataList.size());
            
            logger.info("Batch ingestion completed", 
                Map.of(
                    "correlationId", correlationId,
                    "sourceType", sourceType,
                    "totalRecords", String.valueOf(dataList.size()),
                    "validRecords", String.valueOf(validRecords.size()),
                    "errorCount", String.valueOf(errors.size()),
                    "processingTimeMs", String.valueOf(processingTime)
                ));
            
            Map<String, Object> response = new HashMap<>();
            response.put("status", "processed");
            response.put("correlationId", correlationId);
            response.put("totalRecords", dataList.size());
            response.put("validRecords", validRecords.size());
            response.put("errorCount", errors.size());
            response.put("timestamp", Instant.now().toString());
            
            if (!errors.isEmpty()) {
                response.put("errors", errors);
            }
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Batch ingestion failed", 
                Map.of("correlationId", correlationId, "error", e.getMessage()));
            
            metricsCollector.incrementCounter("rest_batch_errors_total", Map.of("source", sourceType));
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "Internal server error", "correlationId", correlationId));
        }
    }
    
    /**
     * Webhook endpoint for external systems to push data
     */
    @PostMapping("/webhook/{webhookId}")
    public ResponseEntity<Map<String, Object>> handleWebhook(
            @PathVariable String webhookId,
            @RequestBody Map<String, Object> data,
            @RequestHeader Map<String, String> headers) {
        
        String correlationId = UUID.randomUUID().toString();
        
        try {
            // Validate webhook ID
            if (!config.getWebhookEndpoints().containsKey(webhookId)) {
                return ResponseEntity.notFound().build();
            }
            
            // Create data record with webhook context
            DataRecord record = createDataRecord(data, "WEBHOOK_" + webhookId, correlationId);
            record.getMetadata().put("webhook_id", webhookId);
            record.getMetadata().put("webhook_headers", objectMapper.writeValueAsString(headers));
            
            // Process asynchronously
            processDataAsync(record);
            
            logger.info("Webhook data received", 
                Map.of(
                    "correlationId", correlationId,
                    "webhookId", webhookId,
                    "headers", String.valueOf(headers.size())
                ));
            
            return ResponseEntity.ok(Map.of(
                "status", "received",
                "correlationId", correlationId
            ));
            
        } catch (Exception e) {
            logger.error("Webhook processing failed", 
                Map.of("correlationId", correlationId, "webhookId", webhookId, "error", e.getMessage()));
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "Webhook processing failed", "correlationId", correlationId));
        }
    }
    
    /**
     * Health check endpoint
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "healthy");
        health.put("timestamp", Instant.now().toString());
        health.put("component", "RestProtocolHandler");
        health.put("requestsProcessed", getTotalRequestsProcessed());
        
        return ResponseEntity.ok(health);
    }
    
    /**
     * Get protocol statistics
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalRequests", getTotalRequestsProcessed());
        stats.put("requestCounters", new HashMap<>(requestCounters));
        stats.put("rateLimitConfig", Map.of(
            "maxRequestsPerMinute", config.getMaxRequestsPerMinute(),
            "enableAuthentication", config.isEnableAuthentication()
        ));
        
        return ResponseEntity.ok(stats);
    }
    
    // Private helper methods
    
    private DataRecord createDataRecord(Map<String, Object> data, String sourceType, String correlationId) {
        DataRecord record = new DataRecord();
        record.setId(UUID.randomUUID().toString());
        record.setSourceId(correlationId);
        record.setSourceType(sourceType);
        record.setDataType(determineDataType(data));
        record.setTimestamp(Instant.now());
        record.setIngestedAt(Instant.now());
        record.setPayload(data);
        record.setMetadata(new HashMap<>());
        record.getMetadata().put("correlation_id", correlationId);
        record.getMetadata().put("ingestion_method", "REST");
        
        return record;
    }
    
    private String determineDataType(Map<String, Object> data) {
        // Simple heuristic to determine data type
        if (data.containsKey("text") || data.containsKey("content")) {
            return "TEXT";
        } else if (data.containsKey("image") || data.containsKey("imageUrl")) {
            return "IMAGE";
        } else if (data.containsKey("sensor") || data.containsKey("measurement")) {
            return "SENSOR";
        } else {
            return "GENERIC";
        }
    }
    
    @Async
    private CompletableFuture<Void> processDataAsync(DataRecord record) {
        return CompletableFuture.runAsync(() -> {
            try {
                // Send to Kafka for further processing
                kafkaTemplate.send(config.getOutputTopic(), record.getId(), record);
                
                logger.debug("Data sent to Kafka", 
                    Map.of("recordId", record.getId(), "topic", config.getOutputTopic()));
                
            } catch (Exception e) {
                logger.error("Failed to send data to Kafka", 
                    Map.of("recordId", record.getId(), "error", e.getMessage()));
            }
        });
    }
    
    @Async
    private CompletableFuture<Void> processBatchAsync(List<DataRecord> records) {
        return CompletableFuture.runAsync(() -> {
            try {
                for (DataRecord record : records) {
                    kafkaTemplate.send(config.getOutputTopic(), record.getId(), record);
                }
                
                logger.debug("Batch sent to Kafka", 
                    Map.of("recordCount", String.valueOf(records.size()), "topic", config.getOutputTopic()));
                
            } catch (Exception e) {
                logger.error("Failed to send batch to Kafka", 
                    Map.of("recordCount", String.valueOf(records.size()), "error", e.getMessage()));
            }
        });
    }
    
    private boolean checkRateLimit(String sourceType) {
        long currentTime = System.currentTimeMillis();
        long windowStart = currentTime - 60000; // 1 minute window
        
        AtomicLong counter = requestCounters.computeIfAbsent(sourceType, k -> new AtomicLong(0));
        Long lastRequest = lastRequestTime.get(sourceType);
        
        // Reset counter if outside window
        if (lastRequest == null || lastRequest < windowStart) {
            counter.set(0);
        }
        
        lastRequestTime.put(sourceType, currentTime);
        
        return counter.incrementAndGet() <= config.getMaxRequestsPerMinute();
    }
    
    private boolean validateApiKey(String apiKey) {
        if (apiKey == null || apiKey.trim().isEmpty()) {
            return false;
        }
        
        return config.getValidApiKeys().contains(apiKey);
    }
    
    private void startExternalApiPolling() {
        // Implementation for polling external APIs
        logger.info("Starting external API polling", 
            Map.of("apiCount", String.valueOf(config.getExternalApis().size())));
        
        // This would be implemented with scheduled tasks
        // to poll external REST APIs at configured intervals
    }
    
    private long getTotalRequestsProcessed() {
        return requestCounters.values().stream()
            .mapToLong(AtomicLong::get)
            .sum();
    }
}
