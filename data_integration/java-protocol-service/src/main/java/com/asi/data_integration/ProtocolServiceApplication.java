package com.asi.data_integration;

import com.asi.data_integration.config.ApplicationProperties;
import com.asi.data_integration.grpc.DataIntegrationGrpcServer;
import com.asi.data_integration.monitoring.MetricsConfiguration;
import io.micrometer.core.instrument.MeterRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import javax.annotation.PreDestroy;
import java.util.concurrent.TimeUnit;

/**
 * Main application class for ASI Protocol Integration Service
 * 
 * This service provides multi-protocol data integration capabilities including:
 * - REST API endpoints for data ingestion
 * - gRPC services for high-performance communication
 * - Kafka integration for streaming data
 * - Protocol adapters for various data sources
 * - Schema validation and transformation
 * - Monitoring and health checks
 */
@SpringBootApplication
@EnableConfigurationProperties(ApplicationProperties.class)
@EnableKafka
@EnableAsync
@EnableScheduling
public class ProtocolServiceApplication {

    private static final Logger logger = LoggerFactory.getLogger(ProtocolServiceApplication.class);
    
    private static ConfigurableApplicationContext applicationContext;
    private static DataIntegrationGrpcServer grpcServer;

    public static void main(String[] args) {
        try {
            logger.info("Starting ASI Protocol Integration Service...");
            
            // Configure system properties
            configureSystemProperties();
            
            // Start Spring Boot application
            applicationContext = SpringApplication.run(ProtocolServiceApplication.class, args);
            
            // Start gRPC server
            startGrpcServer();
            
            // Register shutdown hook
            registerShutdownHook();
            
            logger.info("ASI Protocol Integration Service started successfully");
            
            // Keep the application running
            Thread.currentThread().join();
            
        } catch (Exception e) {
            logger.error("Failed to start ASI Protocol Integration Service", e);
            System.exit(1);
        }
    }
    
    private static void configureSystemProperties() {
        // Set default timezone
        System.setProperty("user.timezone", "UTC");
        
        // Configure logging
        System.setProperty("logging.pattern.console", 
            "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{36}] - %msg%n");
        
        // Configure JVM options for better performance
        System.setProperty("java.awt.headless", "true");
        
        logger.debug("System properties configured");
    }
    
    private static void startGrpcServer() {
        try {
            ApplicationProperties properties = applicationContext.getBean(ApplicationProperties.class);
            MeterRegistry meterRegistry = applicationContext.getBean(MeterRegistry.class);
            
            grpcServer = applicationContext.getBean(DataIntegrationGrpcServer.class);
            grpcServer.start();
            
            logger.info("gRPC server started on port {}", properties.getGrpc().getPort());
            
        } catch (Exception e) {
            logger.error("Failed to start gRPC server", e);
            throw new RuntimeException("gRPC server startup failed", e);
        }
    }
    
    private static void registerShutdownHook() {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            logger.info("Shutting down ASI Protocol Integration Service...");
            
            try {
                // Stop gRPC server
                if (grpcServer != null) {
                    grpcServer.stop();
                    logger.info("gRPC server stopped");
                }
                
                // Stop Spring application context
                if (applicationContext != null) {
                    applicationContext.close();
                    logger.info("Spring application context closed");
                }
                
                logger.info("ASI Protocol Integration Service shutdown completed");
                
            } catch (Exception e) {
                logger.error("Error during shutdown", e);
            }
        }));
    }
    
    @PreDestroy
    public void onDestroy() {
        logger.info("Application context is being destroyed");
    }
}

/**
 * Configuration class for application-wide settings
 */
@org.springframework.context.annotation.Configuration
class ApplicationConfiguration {
    
    private static final Logger logger = LoggerFactory.getLogger(ApplicationConfiguration.class);
    
    @org.springframework.context.annotation.Bean
    public MetricsConfiguration metricsConfiguration() {
        return new MetricsConfiguration();
    }
    
    /**
     * Configure async task executor
     */
    @org.springframework.context.annotation.Bean(name = "taskExecutor")
    public org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor taskExecutor() {
        org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor executor = 
            new org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor();
        
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(50);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("ASI-Protocol-");
        executor.setRejectedExecutionHandler(new java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        
        logger.info("Configured async task executor with core pool size: {}, max pool size: {}", 
                   executor.getCorePoolSize(), executor.getMaxPoolSize());
        
        return executor;
    }
    
    /**
     * Configure scheduled task executor
     */
    @org.springframework.context.annotation.Bean
    public org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler taskScheduler() {
        org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler scheduler = 
            new org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler();
        
        scheduler.setPoolSize(5);
        scheduler.setThreadNamePrefix("ASI-Scheduler-");
        scheduler.setRejectedExecutionHandler(new java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy());
        scheduler.initialize();
        
        logger.info("Configured scheduled task executor with pool size: {}", scheduler.getPoolSize());
        
        return scheduler;
    }
    
    /**
     * Configure Jackson ObjectMapper for JSON processing
     */
    @org.springframework.context.annotation.Bean
    @org.springframework.context.annotation.Primary
    public com.fasterxml.jackson.databind.ObjectMapper objectMapper() {
        com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
        
        // Configure for better handling of timestamps and unknown properties
        mapper.configure(com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        mapper.registerModule(new com.fasterxml.jackson.datatype.jsr310.JavaTimeModule());
        
        // Configure for better null handling
        mapper.setSerializationInclusion(com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL);
        
        logger.debug("Configured Jackson ObjectMapper");
        
        return mapper;
    }
    
    /**
     * Configure HTTP client for external API calls
     */
    @org.springframework.context.annotation.Bean
    public org.springframework.web.reactive.function.client.WebClient webClient() {
        return org.springframework.web.reactive.function.client.WebClient.builder()
            .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(10 * 1024 * 1024)) // 10MB
            .build();
    }
    
    /**
     * Configure Redis template for caching
     */
    @org.springframework.context.annotation.Bean
    public org.springframework.data.redis.core.RedisTemplate<String, Object> redisTemplate(
            org.springframework.data.redis.connection.RedisConnectionFactory connectionFactory) {
        
        org.springframework.data.redis.core.RedisTemplate<String, Object> template = 
            new org.springframework.data.redis.core.RedisTemplate<>();
        
        template.setConnectionFactory(connectionFactory);
        
        // Configure serializers
        template.setKeySerializer(new org.springframework.data.redis.serializer.StringRedisSerializer());
        template.setValueSerializer(new org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer());
        template.setHashKeySerializer(new org.springframework.data.redis.serializer.StringRedisSerializer());
        template.setHashValueSerializer(new org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer());
        
        template.afterPropertiesSet();
        
        logger.debug("Configured Redis template");
        
        return template;
    }
}
