version: '3.8'

services:
  # Apache Spark Cluster with Enhanced Data Integration
  spark-master:
    image: bitnami/spark:3.5.0
    container_name: asi-spark-master
    environment:
      - SPARK_MODE=master
      - SPARK_RPC_AUTHENTICATION_ENABLED=no
      - SPARK_RPC_ENCRYPTION_ENABLED=no
      - SPARK_LOCAL_STORAGE_ENCRYPTION_ENABLED=no
      - SPARK_SSL_ENABLED=no
      - SPARK_MASTER_OPTS=-Dspark.deploy.defaultCores=2
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USERNAME=neo4j
      - NEO4J_PASSWORD=password
      - POSTGRES_URL=****************************************
      - POSTGRES_USER=asi_user
      - POSTGRES_PASSWORD=asi_password
    ports:
      - "8080:8080"  # Spark Master Web UI
      - "7077:7077"  # Spark Master Port
      - "8090:8090"  # Metrics
    volumes:
      - spark-master-data:/opt/bitnami/spark/data
      - ../configs:/app/configs:ro
      - ../data:/app/data
      - ../logs:/app/logs
    networks:
      - asi-network
    depends_on:
      - kafka
      - neo4j
      - postgres
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080"]
      interval: 30s
      timeout: 10s
      retries: 3

  spark-worker-1:
    image: bitnami/spark:3.5.0
    container_name: asi-spark-worker-1
    environment:
      - SPARK_MODE=worker
      - SPARK_MASTER_URL=spark://spark-master:7077
      - SPARK_WORKER_MEMORY=2g
      - SPARK_WORKER_CORES=2
      - SPARK_RPC_AUTHENTICATION_ENABLED=no
      - SPARK_RPC_ENCRYPTION_ENABLED=no
      - SPARK_LOCAL_STORAGE_ENCRYPTION_ENABLED=no
      - SPARK_SSL_ENABLED=no
    depends_on:
      spark-master:
        condition: service_healthy
    volumes:
      - spark-worker-1-data:/opt/bitnami/spark/data
    networks:
      - asi-network

  spark-worker-2:
    image: bitnami/spark:3.5.0
    container_name: asi-spark-worker-2
    environment:
      - SPARK_MODE=worker
      - SPARK_MASTER_URL=spark://spark-master:7077
      - SPARK_WORKER_MEMORY=2g
      - SPARK_WORKER_CORES=2
      - SPARK_RPC_AUTHENTICATION_ENABLED=no
      - SPARK_RPC_ENCRYPTION_ENABLED=no
      - SPARK_LOCAL_STORAGE_ENCRYPTION_ENABLED=no
      - SPARK_SSL_ENABLED=no
    depends_on:
      spark-master:
        condition: service_healthy
    volumes:
      - spark-worker-2-data:/opt/bitnami/spark/data
    networks:
      - asi-network

  # Scala Spark Pipeline
  spark-pipeline:
    build:
      context: ../scala-spark-pipeline
      dockerfile: Dockerfile
    container_name: asi-spark-pipeline
    environment:
      - SPARK_MASTER_URL=spark://spark-master:7077
      - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USERNAME=neo4j
      - NEO4J_PASSWORD=password
      - SPARK_EXECUTOR_MEMORY=2g
      - SPARK_EXECUTOR_CORES=2
    depends_on:
      spark-master:
        condition: service_healthy
      kafka:
        condition: service_healthy
      neo4j:
        condition: service_healthy
    ports:
      - "4040:4040"  # Spark Application UI
      - "8085:8085"  # Metrics endpoint
    volumes:
      - spark-pipeline-data:/app/data
      - spark-checkpoints:/app/checkpoints
    networks:
      - asi-network
    restart: unless-stopped

  # Java Protocol Service
  java-protocol-service:
    build:
      context: ../java-protocol-service
      dockerfile: Dockerfile
    container_name: asi-java-protocol-service
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USERNAME=neo4j
      - NEO4J_PASSWORD=password
      - GRPC_PORT=50052
      - SERVER_PORT=8083
    depends_on:
      kafka:
        condition: service_healthy
      redis:
        condition: service_healthy
      neo4j:
        condition: service_healthy
    ports:
      - "8083:8083"  # HTTP API
      - "50052:50052"  # gRPC
    volumes:
      - protocol-service-data:/app/data
    networks:
      - asi-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8083/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Rust Device Integration Service
  rust-device-service:
    build:
      context: ../rust-device-integration
      dockerfile: Dockerfile
    container_name: asi-rust-device-service
    environment:
      - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
      - GRPC_PORT=50055
      - METRICS_PORT=8084
      - RUST_LOG=info
      - DEVICE_CONFIG_PATH=/app/config/devices.toml
      - REDIS_URL=redis://redis:6379
      - POSTGRES_URL=************************************************/asi_data
      - LEARNING_ENGINE_ENDPOINT=learning-engine:50061
      - DECISION_ENGINE_ENDPOINT=decision-engine:50062
    depends_on:
      kafka:
        condition: service_healthy
      redis:
        condition: service_healthy
      postgres:
        condition: service_healthy
    ports:
      - "8084:8084"  # Metrics endpoint
      - "50055:50055"  # gRPC
    volumes:
      - device-service-data:/app/data
      - device-service-config:/app/config
      - ../configs:/app/configs:ro
      - ../logs:/app/logs
      - /dev:/dev  # For serial device access
    networks:
      - asi-network
    privileged: true  # For device access
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8084/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Neo4j Graph Database
  neo4j:
    image: neo4j:5.13-community
    container_name: asi-neo4j
    environment:
      - NEO4J_AUTH=neo4j/password
      - NEO4J_PLUGINS=["apoc", "graph-data-science"]
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*,gds.*
      - NEO4J_dbms_memory_heap_initial__size=1G
      - NEO4J_dbms_memory_heap_max__size=2G
      - NEO4J_dbms_memory_pagecache_size=1G
    ports:
      - "7474:7474"  # HTTP
      - "7687:7687"  # Bolt
    volumes:
      - neo4j-data:/data
      - neo4j-logs:/logs
      - neo4j-import:/var/lib/neo4j/import
      - neo4j-plugins:/plugins
    networks:
      - asi-network
    healthcheck:
      test: ["CMD", "cypher-shell", "-u", "neo4j", "-p", "password", "RETURN 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Kafka (reuse from ingestion module)
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: asi-zookeeper-integration
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    volumes:
      - zookeeper-data:/var/lib/zookeeper/data
      - zookeeper-logs:/var/lib/zookeeper/log
    networks:
      - asi-network

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: asi-kafka-integration
    depends_on:
      - zookeeper
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: 'zookeeper:2181'
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
    ports:
      - "9092:9092"
    volumes:
      - kafka-data:/var/lib/kafka/data
    networks:
      - asi-network
    healthcheck:
      test: ["CMD-SHELL", "kafka-broker-api-versions --bootstrap-server localhost:9092"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Schema Registry
  schema-registry:
    image: confluentinc/cp-schema-registry:7.4.0
    container_name: asi-schema-registry
    depends_on:
      kafka:
        condition: service_healthy
    environment:
      SCHEMA_REGISTRY_HOST_NAME: schema-registry
      SCHEMA_REGISTRY_KAFKASTORE_BOOTSTRAP_SERVERS: 'kafka:29092'
      SCHEMA_REGISTRY_LISTENERS: http://0.0.0.0:8081
    ports:
      - "8081:8081"
    networks:
      - asi-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/subjects"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for caching
  redis:
    image: redis:7-alpine
    container_name: asi-redis-integration
    command: redis-server --appendonly yes
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - asi-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL for metadata storage
  postgres:
    image: postgres:15
    container_name: asi-postgres-integration
    environment:
      POSTGRES_DB: asi_integration
      POSTGRES_USER: asi_user
      POSTGRES_PASSWORD: asi_password
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - asi-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U asi_user -d asi_integration"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus for monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: asi-prometheus-integration
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    networks:
      - asi-network

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    container_name: asi-grafana-integration
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    ports:
      - "3000:3000"
    volumes:
      - grafana-data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - asi-network

  # Jaeger for distributed tracing
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: asi-jaeger-integration
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    ports:
      - "16686:16686"  # Jaeger UI
      - "14268:14268"  # Jaeger collector HTTP
      - "14250:14250"  # Jaeger collector gRPC
    networks:
      - asi-network

volumes:
  spark-master-data:
  spark-worker-1-data:
  spark-worker-2-data:
  spark-pipeline-data:
  spark-checkpoints:
  protocol-service-data:
  device-service-data:
  device-service-config:
  neo4j-data:
  neo4j-logs:
  neo4j-import:
  neo4j-plugins:
  zookeeper-data:
  zookeeper-logs:
  kafka-data:
  redis-data:
  postgres-data:
  prometheus-data:
  grafana-data:

networks:
  asi-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
