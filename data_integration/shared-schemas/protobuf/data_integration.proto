syntax = "proto3";

package asi.data_integration.v1;

import "google/protobuf/timestamp.proto";
import "google/protobuf/any.proto";
import "google/protobuf/struct.proto";

option go_package = "github.com/asi-system/data-integration/proto/gen/go";
option java_package = "com.asi.data_integration.v1";
option java_outer_classname = "DataIntegrationProto";

// Global Data Integration Service
service DataIntegrationService {
  // Process and normalize incoming data
  rpc ProcessData(ProcessDataRequest) returns (ProcessDataResponse);
  
  // Stream real-time data processing
  rpc StreamProcessData(stream StreamProcessRequest) returns (stream StreamProcessResponse);
  
  // Query integrated data
  rpc QueryData(QueryDataRequest) returns (QueryDataResponse);
  
  // Get data lineage information
  rpc GetDataLineage(DataLineageRequest) returns (DataLineageResponse);
  
  // Validate data schema
  rpc ValidateSchema(SchemaValidationRequest) returns (SchemaValidationResponse);
  
  // Health check
  rpc HealthCheck(HealthCheckRequest) returns (HealthCheckResponse);
}

// Device Integration Service for real-time data
service DeviceIntegrationService {
  // Register a new device
  rpc RegisterDevice(RegisterDeviceRequest) returns (RegisterDeviceResponse);
  
  // Stream device data
  rpc StreamDeviceData(stream DeviceDataRequest) returns (stream DeviceDataResponse);
  
  // Get device status
  rpc GetDeviceStatus(DeviceStatusRequest) returns (DeviceStatusResponse);
  
  // Update device configuration
  rpc UpdateDeviceConfig(UpdateDeviceConfigRequest) returns (UpdateDeviceConfigResponse);
}

// Data source types
enum DataSourceType {
  DATA_SOURCE_TYPE_UNSPECIFIED = 0;
  DATA_SOURCE_TYPE_DATABASE = 1;
  DATA_SOURCE_TYPE_API = 2;
  DATA_SOURCE_TYPE_FILE = 3;
  DATA_SOURCE_TYPE_STREAM = 4;
  DATA_SOURCE_TYPE_DEVICE = 5;
  DATA_SOURCE_TYPE_CLOUD = 6;
  DATA_SOURCE_TYPE_SENSOR = 7;
}

// Data formats
enum DataFormat {
  DATA_FORMAT_UNSPECIFIED = 0;
  DATA_FORMAT_JSON = 1;
  DATA_FORMAT_XML = 2;
  DATA_FORMAT_CSV = 3;
  DATA_FORMAT_AVRO = 4;
  DATA_FORMAT_PARQUET = 5;
  DATA_FORMAT_PROTOBUF = 6;
  DATA_FORMAT_BINARY = 7;
}

// Processing status
enum ProcessingStatus {
  PROCESSING_STATUS_UNSPECIFIED = 0;
  PROCESSING_STATUS_PENDING = 1;
  PROCESSING_STATUS_PROCESSING = 2;
  PROCESSING_STATUS_COMPLETED = 3;
  PROCESSING_STATUS_FAILED = 4;
  PROCESSING_STATUS_RETRYING = 5;
}

// Data quality levels
enum DataQuality {
  DATA_QUALITY_UNSPECIFIED = 0;
  DATA_QUALITY_RAW = 1;
  DATA_QUALITY_VALIDATED = 2;
  DATA_QUALITY_NORMALIZED = 3;
  DATA_QUALITY_ENRICHED = 4;
  DATA_QUALITY_VERIFIED = 5;
}

// Common data metadata
message DataMetadata {
  string source_id = 1;
  DataSourceType source_type = 2;
  DataFormat format = 3;
  google.protobuf.Timestamp timestamp = 4;
  google.protobuf.Timestamp ingested_at = 5;
  string schema_version = 6;
  DataQuality quality_level = 7;
  map<string, string> tags = 8;
  string checksum = 9;
  int64 size_bytes = 10;
  string correlation_id = 11;
  string tenant_id = 12;
}

// Unified data record
message DataRecord {
  string id = 1;
  DataMetadata metadata = 2;
  google.protobuf.Any payload = 3;
  repeated DataRelationship relationships = 4;
  DataLineage lineage = 5;
  repeated DataValidationResult validation_results = 6;
}

// Data relationships for graph modeling
message DataRelationship {
  string relationship_type = 1;
  string target_id = 2;
  string target_type = 3;
  map<string, string> properties = 4;
  double confidence_score = 5;
}

// Data lineage tracking
message DataLineage {
  string original_source = 1;
  repeated ProcessingStep processing_steps = 2;
  repeated string derived_from = 3;
  google.protobuf.Timestamp created_at = 4;
  string created_by = 5;
}

// Processing step in data lineage
message ProcessingStep {
  string step_id = 1;
  string step_type = 2;
  string processor = 3;
  google.protobuf.Timestamp processed_at = 4;
  map<string, string> parameters = 5;
  ProcessingStatus status = 6;
  string error_message = 7;
}

// Data validation result
message DataValidationResult {
  string rule_id = 1;
  string rule_name = 2;
  bool passed = 3;
  string error_message = 4;
  double confidence_score = 5;
  map<string, string> details = 6;
}

// Process data request
message ProcessDataRequest {
  DataRecord data = 1;
  ProcessingOptions options = 2;
  string target_schema = 3;
  repeated string transformations = 4;
}

// Processing options
message ProcessingOptions {
  bool validate_schema = 1;
  bool normalize_data = 2;
  bool enrich_data = 3;
  bool extract_relationships = 4;
  bool store_lineage = 5;
  int32 retry_attempts = 6;
  int32 timeout_seconds = 7;
}

// Process data response
message ProcessDataResponse {
  bool success = 1;
  string message = 2;
  DataRecord processed_data = 3;
  ProcessingStatus status = 4;
  repeated string warnings = 5;
  ProcessingMetrics metrics = 6;
}

// Processing metrics
message ProcessingMetrics {
  int64 records_processed = 1;
  int64 records_failed = 2;
  int64 processing_time_ms = 3;
  int64 validation_time_ms = 4;
  int64 transformation_time_ms = 5;
  double throughput_per_second = 6;
}

// Stream processing request
message StreamProcessRequest {
  string stream_id = 1;
  DataRecord data = 2;
  bool is_final = 3;
}

// Stream processing response
message StreamProcessResponse {
  string stream_id = 1;
  bool success = 2;
  string error_message = 3;
  int64 records_processed = 4;
}

// Query data request
message QueryDataRequest {
  string query = 1;
  QueryType query_type = 2;
  map<string, string> parameters = 3;
  int32 limit = 4;
  int32 offset = 5;
}

// Query types
enum QueryType {
  QUERY_TYPE_UNSPECIFIED = 0;
  QUERY_TYPE_SQL = 1;
  QUERY_TYPE_SPARQL = 2;
  QUERY_TYPE_CYPHER = 3;
  QUERY_TYPE_GRAPHQL = 4;
}

// Query data response
message QueryDataResponse {
  bool success = 1;
  repeated DataRecord results = 2;
  int64 total_count = 3;
  string error_message = 4;
  QueryMetrics metrics = 5;
}

// Query metrics
message QueryMetrics {
  int64 execution_time_ms = 1;
  int64 records_scanned = 2;
  int64 records_returned = 3;
  string query_plan = 4;
}

// Data lineage request
message DataLineageRequest {
  string data_id = 1;
  int32 depth = 2;
  bool include_downstream = 3;
  bool include_upstream = 4;
}

// Data lineage response
message DataLineageResponse {
  DataLineage lineage = 1;
  repeated DataRecord related_records = 2;
  LineageGraph graph = 3;
}

// Lineage graph representation
message LineageGraph {
  repeated LineageNode nodes = 1;
  repeated LineageEdge edges = 2;
}

// Lineage graph node
message LineageNode {
  string id = 1;
  string type = 2;
  string label = 3;
  map<string, string> properties = 4;
}

// Lineage graph edge
message LineageEdge {
  string from_id = 1;
  string to_id = 2;
  string relationship_type = 3;
  map<string, string> properties = 4;
}

// Schema validation request
message SchemaValidationRequest {
  string schema_id = 1;
  google.protobuf.Any data = 2;
  string schema_format = 3;
}

// Schema validation response
message SchemaValidationResponse {
  bool valid = 1;
  repeated ValidationError errors = 2;
  repeated string warnings = 3;
}

// Validation error
message ValidationError {
  string field_path = 1;
  string error_code = 2;
  string error_message = 3;
  string expected_type = 4;
  string actual_type = 5;
}

// Device registration request
message RegisterDeviceRequest {
  string device_id = 1;
  string device_type = 2;
  string device_name = 3;
  map<string, string> capabilities = 4;
  DeviceConfiguration config = 5;
}

// Device configuration
message DeviceConfiguration {
  int32 sampling_rate_ms = 1;
  repeated string data_types = 2;
  map<string, string> parameters = 3;
  bool encryption_enabled = 4;
  string protocol_version = 5;
}

// Device registration response
message RegisterDeviceResponse {
  bool success = 1;
  string device_token = 2;
  string error_message = 3;
  DeviceConfiguration assigned_config = 4;
}

// Device data request
message DeviceDataRequest {
  string device_id = 1;
  string device_token = 2;
  google.protobuf.Timestamp timestamp = 3;
  repeated SensorReading readings = 4;
  DeviceStatus status = 5;
}

// Sensor reading
message SensorReading {
  string sensor_id = 1;
  string sensor_type = 2;
  google.protobuf.Value value = 3;
  string unit = 4;
  double accuracy = 5;
  google.protobuf.Timestamp timestamp = 6;
}

// Device status
message DeviceStatus {
  bool online = 1;
  double battery_level = 2;
  double cpu_usage = 3;
  double memory_usage = 4;
  double temperature = 5;
  string firmware_version = 6;
  google.protobuf.Timestamp last_heartbeat = 7;
}

// Device data response
message DeviceDataResponse {
  bool success = 1;
  string error_message = 2;
  string acknowledgment_id = 3;
  google.protobuf.Timestamp processed_at = 4;
}

// Device status request
message DeviceStatusRequest {
  string device_id = 1;
  string device_token = 2;
}

// Device status response
message DeviceStatusResponse {
  DeviceStatus status = 1;
  repeated string alerts = 2;
  DeviceConfiguration current_config = 3;
}

// Update device config request
message UpdateDeviceConfigRequest {
  string device_id = 1;
  string device_token = 2;
  DeviceConfiguration new_config = 3;
}

// Update device config response
message UpdateDeviceConfigResponse {
  bool success = 1;
  string error_message = 2;
  DeviceConfiguration applied_config = 3;
}

// Health check request/response
message HealthCheckRequest {}

message HealthCheckResponse {
  bool healthy = 1;
  string version = 2;
  google.protobuf.Timestamp timestamp = 3;
  map<string, string> dependencies = 4;
  SystemMetrics system_metrics = 5;
}

// System metrics
message SystemMetrics {
  double cpu_usage = 1;
  double memory_usage = 2;
  double disk_usage = 3;
  int64 active_connections = 4;
  int64 processed_records = 5;
  double throughput_per_second = 6;
}
