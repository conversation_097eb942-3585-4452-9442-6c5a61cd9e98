{"type": "record", "name": "UnifiedDataRecord", "namespace": "com.asi.data_integration.avro", "doc": "Unified data record for ASI data integration pipeline", "fields": [{"name": "id", "type": "string", "doc": "Unique identifier for the data record"}, {"name": "metadata", "type": {"type": "record", "name": "DataMetadata", "fields": [{"name": "sourceId", "type": "string", "doc": "Identifier of the data source"}, {"name": "sourceType", "type": {"type": "enum", "name": "DataSourceType", "symbols": ["DATABASE", "API", "FILE", "STREAM", "DEVICE", "CLOUD", "SENSOR"]}}, {"name": "format", "type": {"type": "enum", "name": "DataFormat", "symbols": ["JSON", "XML", "CSV", "AVRO", "PARQUET", "PROTOBUF", "BINARY"]}}, {"name": "timestamp", "type": {"type": "long", "logicalType": "timestamp-millis"}, "doc": "Original timestamp of the data"}, {"name": "ingestedAt", "type": {"type": "long", "logicalType": "timestamp-millis"}, "doc": "Timestamp when data was ingested"}, {"name": "schemaVersion", "type": "string", "doc": "Version of the schema used"}, {"name": "qualityLevel", "type": {"type": "enum", "name": "DataQuality", "symbols": ["RAW", "VALIDATED", "NORMALIZED", "ENRICHED", "VERIFIED"]}}, {"name": "tags", "type": {"type": "map", "values": "string"}, "default": {}}, {"name": "checksum", "type": ["null", "string"], "default": null}, {"name": "sizeBytes", "type": "long", "default": 0}, {"name": "correlationId", "type": ["null", "string"], "default": null}, {"name": "tenantId", "type": ["null", "string"], "default": null}]}}, {"name": "payload", "type": ["null", "string", "bytes"], "doc": "The actual data payload (JSON string or binary data)"}, {"name": "relationships", "type": {"type": "array", "items": {"type": "record", "name": "DataRelationship", "fields": [{"name": "relationshipType", "type": "string"}, {"name": "targetId", "type": "string"}, {"name": "targetType", "type": "string"}, {"name": "properties", "type": {"type": "map", "values": "string"}, "default": {}}, {"name": "confidenceScore", "type": "double", "default": 1.0}]}}, "default": []}, {"name": "lineage", "type": ["null", {"type": "record", "name": "DataLineage", "fields": [{"name": "originalSource", "type": "string"}, {"name": "processingSteps", "type": {"type": "array", "items": {"type": "record", "name": "ProcessingStep", "fields": [{"name": "stepId", "type": "string"}, {"name": "stepType", "type": "string"}, {"name": "processor", "type": "string"}, {"name": "processedAt", "type": {"type": "long", "logicalType": "timestamp-millis"}}, {"name": "parameters", "type": {"type": "map", "values": "string"}, "default": {}}, {"name": "status", "type": {"type": "enum", "name": "ProcessingStatus", "symbols": ["PENDING", "PROCESSING", "COMPLETED", "FAILED", "RETRYING"]}}, {"name": "errorMessage", "type": ["null", "string"], "default": null}]}}, "default": []}, {"name": "derivedFrom", "type": {"type": "array", "items": "string"}, "default": []}, {"name": "createdAt", "type": {"type": "long", "logicalType": "timestamp-millis"}}, {"name": "created<PERSON>y", "type": "string"}]}], "default": null}, {"name": "validationResults", "type": {"type": "array", "items": {"type": "record", "name": "DataValidationResult", "fields": [{"name": "ruleId", "type": "string"}, {"name": "ruleName", "type": "string"}, {"name": "passed", "type": "boolean"}, {"name": "errorMessage", "type": ["null", "string"], "default": null}, {"name": "confidenceScore", "type": "double", "default": 1.0}, {"name": "details", "type": {"type": "map", "values": "string"}, "default": {}}]}}, "default": []}]}