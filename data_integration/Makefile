# ASI Global Data Integration Module Makefile
.PHONY: help build start stop restart logs clean test proto deps check-deps

# Default target
help:
	@echo "ASI Global Data Integration Module"
	@echo "=================================="
	@echo ""
	@echo "Available targets:"
	@echo "  build          - Build all services"
	@echo "  start          - Start all services"
	@echo "  stop           - Stop all services"
	@echo "  restart        - Restart all services"
	@echo "  logs           - Show logs for all services"
	@echo "  clean          - Clean up containers and volumes"
	@echo "  test           - Run all tests"
	@echo "  proto          - Generate protobuf code"
	@echo "  deps           - Install dependencies"
	@echo "  check-deps     - Check if dependencies are installed"
	@echo ""
	@echo "Individual service targets:"
	@echo "  start-spark    - Start Spark cluster"
	@echo "  start-java     - Start Java protocol service"
	@echo "  start-rust     - Start Rust device service"
	@echo "  start-neo4j    - Start Neo4j database"
	@echo "  start-kafka    - Start Kafka infrastructure"
	@echo ""
	@echo "Development targets:"
	@echo "  dev-scala      - Run Scala Spark pipeline in development"
	@echo "  dev-java       - Run Java service in development"
	@echo "  dev-rust       - Run Rust service in development"
	@echo ""
	@echo "Testing targets:"
	@echo "  test-scala     - Run Scala tests"
	@echo "  test-java      - Run Java tests"
	@echo "  test-rust      - Run Rust tests"
	@echo "  test-integration - Run integration tests"

# Check dependencies
check-deps:
	@echo "Checking dependencies..."
	@command -v docker >/dev/null 2>&1 || { echo "Docker is required but not installed. Aborting." >&2; exit 1; }
	@command -v docker-compose >/dev/null 2>&1 || { echo "Docker Compose is required but not installed. Aborting." >&2; exit 1; }
	@command -v sbt >/dev/null 2>&1 || { echo "SBT is required for Scala development but not installed." >&2; }
	@command -v mvn >/dev/null 2>&1 || { echo "Maven is required for Java development but not installed." >&2; }
	@command -v cargo >/dev/null 2>&1 || { echo "Rust/Cargo is required for Rust development but not installed." >&2; }
	@echo "Dependencies check completed."

# Install dependencies
deps: check-deps
	@echo "Installing dependencies..."
	cd scala-spark-pipeline && sbt update
	cd java-protocol-service && mvn dependency:resolve
	cd rust-device-integration && cargo fetch
	@echo "Dependencies installed."

# Generate protobuf code
proto:
	@echo "Generating protobuf code..."
	# Generate Java code
	cd java-protocol-service && mvn protobuf:compile protobuf:compile-custom
	# Generate Rust code (done during build)
	cd rust-device-integration && cargo build
	# Generate Scala code (if needed)
	@echo "Protobuf code generated."

# Build all services
build: check-deps proto
	@echo "Building all services..."
	cd docker && docker-compose build
	@echo "Build completed."

# Build individual services
build-scala:
	@echo "Building Scala Spark pipeline..."
	cd scala-spark-pipeline && sbt assembly
	cd docker && docker-compose build spark-pipeline

build-java:
	@echo "Building Java protocol service..."
	cd java-protocol-service && mvn clean package -DskipTests
	cd docker && docker-compose build java-protocol-service

build-rust:
	@echo "Building Rust device service..."
	cd rust-device-integration && cargo build --release
	cd docker && docker-compose build rust-device-service

# Start all services
start: check-deps
	@echo "Starting ASI Global Data Integration services..."
	cd docker && docker-compose up -d
	@echo "Services started. Access points:"
	@echo "  - Spark Master UI: http://localhost:8080"
	@echo "  - Spark Application UI: http://localhost:4040"
	@echo "  - Java Protocol Service: http://localhost:8083"
	@echo "  - Rust Device Service Metrics: http://localhost:8084/metrics"
	@echo "  - Neo4j Browser: http://localhost:7474 (neo4j/password)"
	@echo "  - Kafka: localhost:9092"
	@echo "  - Schema Registry: http://localhost:8081"
	@echo "  - Prometheus: http://localhost:9090"
	@echo "  - Grafana: http://localhost:3000 (admin/admin)"
	@echo "  - Jaeger UI: http://localhost:16686"

# Start individual services
start-spark:
	cd docker && docker-compose up -d spark-master spark-worker-1 spark-worker-2

start-java: start-kafka start-neo4j
	cd docker && docker-compose up -d java-protocol-service

start-rust: start-kafka
	cd docker && docker-compose up -d rust-device-service

start-neo4j:
	cd docker && docker-compose up -d neo4j

start-kafka:
	cd docker && docker-compose up -d zookeeper kafka schema-registry redis

# Stop all services
stop:
	@echo "Stopping services..."
	cd docker && docker-compose down
	@echo "Services stopped."

# Restart all services
restart: stop start

# Show logs
logs:
	cd docker && docker-compose logs -f

# Show logs for specific services
logs-spark:
	cd docker && docker-compose logs -f spark-master spark-worker-1 spark-worker-2 spark-pipeline

logs-java:
	cd docker && docker-compose logs -f java-protocol-service

logs-rust:
	cd docker && docker-compose logs -f rust-device-service

logs-neo4j:
	cd docker && docker-compose logs -f neo4j

logs-kafka:
	cd docker && docker-compose logs -f kafka zookeeper schema-registry

# Clean up
clean:
	@echo "Cleaning up..."
	cd docker && docker-compose down -v --remove-orphans
	docker system prune -f
	@echo "Cleanup completed."

# Run tests
test: test-scala test-java test-rust test-integration

test-scala:
	@echo "Running Scala tests..."
	cd scala-spark-pipeline && sbt test

test-java:
	@echo "Running Java tests..."
	cd java-protocol-service && mvn test

test-rust:
	@echo "Running Rust tests..."
	cd rust-device-integration && cargo test

test-integration:
	@echo "Running integration tests..."
	cd tests && python integration_test.py

# Development mode targets
dev-scala:
	@echo "Starting Scala Spark pipeline in development mode..."
	cd scala-spark-pipeline && sbt run

dev-java:
	@echo "Starting Java protocol service in development mode..."
	cd java-protocol-service && mvn spring-boot:run

dev-rust:
	@echo "Starting Rust device service in development mode..."
	cd rust-device-integration && cargo run

# Monitoring and debugging
status:
	cd docker && docker-compose ps

health:
	@echo "Checking service health..."
	@curl -s http://localhost:8083/actuator/health || echo "Java Service: DOWN"
	@curl -s http://localhost:8084/health || echo "Rust Service: DOWN"
	@curl -s http://localhost:7474/db/data/ || echo "Neo4j: DOWN"
	@curl -s http://localhost:8081/subjects || echo "Schema Registry: DOWN"

metrics:
	@echo "Fetching metrics..."
	@echo "=== Java Service Metrics ==="
	@curl -s http://localhost:8083/actuator/prometheus | head -20
	@echo ""
	@echo "=== Rust Service Metrics ==="
	@curl -s http://localhost:8084/metrics | head -20
	@echo ""
	@echo "=== Spark Metrics ==="
	@curl -s http://localhost:8085/metrics | head -20

# Neo4j operations
neo4j-shell:
	cd docker && docker-compose exec neo4j cypher-shell -u neo4j -p password

neo4j-import-sample:
	@echo "Importing sample data to Neo4j..."
	cd docker && docker-compose exec neo4j cypher-shell -u neo4j -p password -f /var/lib/neo4j/import/sample_data.cypher

# Kafka operations
kafka-topics:
	cd docker && docker-compose exec kafka kafka-topics --list --bootstrap-server localhost:9092

kafka-create-topics:
	@echo "Creating Kafka topics..."
	cd docker && docker-compose exec kafka kafka-topics --create --topic asi-data-integration --bootstrap-server localhost:9092 --partitions 3 --replication-factor 1
	cd docker && docker-compose exec kafka kafka-topics --create --topic asi-device-data --bootstrap-server localhost:9092 --partitions 3 --replication-factor 1
	cd docker && docker-compose exec kafka kafka-topics --create --topic asi-processed-data --bootstrap-server localhost:9092 --partitions 3 --replication-factor 1

# Schema Registry operations
schema-list:
	curl -s http://localhost:8081/subjects

schema-register:
	@echo "Registering schemas..."
	curl -X POST -H "Content-Type: application/vnd.schemaregistry.v1+json" \
		--data @shared-schemas/avro/unified_data_record.avsc \
		http://localhost:8081/subjects/unified-data-record-value/versions

# Performance testing
load-test:
	@echo "Running load test..."
	@echo "This would typically use tools like JMeter, Gatling, or custom scripts"

# Security scan
security-scan:
	@echo "Running security scan..."
	docker run --rm -v $(PWD):/app securecodewarrior/docker-security-scan /app

# Backup operations
backup:
	@echo "Creating backup..."
	mkdir -p backups/$(shell date +%Y%m%d_%H%M%S)
	cd docker && docker-compose exec -T postgres pg_dump -U asi_user asi_integration > backups/$(shell date +%Y%m%d_%H%M%S)/postgres_backup.sql
	cd docker && docker-compose exec neo4j neo4j-admin dump --database=neo4j --to=/backups/neo4j_backup_$(shell date +%Y%m%d_%H%M%S).dump
	@echo "Backup created in backups/ directory."

# Documentation
docs:
	@echo "Generating documentation..."
	cd scala-spark-pipeline && sbt doc
	cd java-protocol-service && mvn javadoc:javadoc
	cd rust-device-integration && cargo doc --open
	@echo "Documentation generated."

# Environment setup
setup-dev:
	@echo "Setting up development environment..."
	make deps
	make proto
	make kafka-create-topics
	make schema-register
	@echo "Development environment setup completed."

# Production deployment helpers
deploy-staging:
	@echo "Deploying to staging environment..."
	# This would typically involve Kubernetes deployment
	kubectl apply -f k8s/staging/

deploy-production:
	@echo "Deploying to production environment..."
	# This would typically involve Kubernetes deployment with additional checks
	kubectl apply -f k8s/production/

# Utility targets
format:
	@echo "Formatting code..."
	cd scala-spark-pipeline && sbt scalafmt
	cd java-protocol-service && mvn spotless:apply
	cd rust-device-integration && cargo fmt

lint:
	@echo "Running linters..."
	cd scala-spark-pipeline && sbt scalafmtCheck
	cd java-protocol-service && mvn spotless:check
	cd rust-device-integration && cargo clippy
