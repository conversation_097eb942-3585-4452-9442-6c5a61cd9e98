# ASI Global Data Integration Configuration
# ========================================

# Application metadata
application {
  name = "asi-data-integration"
  version = "1.0.0"
  description = "Global Data Integration Platform for ASI System"
}

# Environment-specific configurations
development {
  # Spark configuration
  spark {
    master = "local[*]"
    appName = "ASI-Data-Integration-Dev"
    logLevel = "INFO"
    checkpointLocation = "/tmp/spark-checkpoints"
    triggerInterval = "10 seconds"
    
    # Spark SQL settings
    sql {
      adaptive.enabled = true
      adaptive.coalescePartitions.enabled = true
      streaming.stateStore.providerClass = "org.apache.spark.sql.execution.streaming.state.HDFSBackedStateStoreProvider"
    }
  }
  
  # Kafka configuration
  kafka {
    bootstrapServers = "localhost:9092"
    schemaRegistryUrl = "http://localhost:8081"
    inputTopics = ["asi-ingestion", "asi-web-scraping", "asi-device-data"]
    outputTopics = ["asi-processed-data", "asi-learning-engine", "asi-decision-engine"]
    
    # Producer settings
    producer {
      acks = "all"
      retries = 3
      batchSize = 16384
      lingerMs = 10
      bufferMemory = 33554432
      compressionType = "snappy"
      maxInFlightRequests = 5
      enableIdempotence = true
    }
    
    # Consumer settings
    consumer {
      groupId = "asi-data-integration-dev"
      autoOffsetReset = "earliest"
      enableAutoCommit = false
      maxPollRecords = 500
      maxPollIntervalMs = 300000
      sessionTimeoutMs = 30000
      heartbeatIntervalMs = 3000
    }
    
    # Additional properties
    properties {
      "security.protocol" = "PLAINTEXT"
    }
  }
  
  # Neo4j configuration
  neo4j {
    uri = "bolt://localhost:7687"
    username = "neo4j"
    password = "password"
    database = "neo4j"
    
    # Connection pool settings
    maxConnectionPoolSize = 50
    connectionAcquisitionTimeoutMs = 60000
    connectionTimeoutMs = 5000
    maxTransactionRetryTimeMs = 30000
    
    # Batch settings
    batchSize = 1000
    parallelism = 4
  }
  
  # Database configuration
  database {
    enabled = true
    driver = "org.postgresql.Driver"
    url = "************************************************"
    username = "asi_user"
    password = "asi_password"
    
    # Connection pool
    maximumPoolSize = 20
    minimumIdle = 5
    connectionTimeout = 30000
    idleTimeout = 600000
    maxLifetime = 1800000
    
    # Streaming settings
    triggerInterval = "1 minute"
    fetchSize = 1000
  }
  
  # Schema Registry configuration
  schemaRegistry {
    url = "http://localhost:8081"
    basicAuthCredentialsSource = "USER_INFO"
    basicAuthUserInfo = ""
    
    # Schema evolution settings
    compatibilityLevel = "BACKWARD"
    autoRegisterSchemas = true
    useLatestVersion = true
  }
  
  # Validation configuration
  validation {
    enabled = true
    strictMode = false
    maxErrors = 100
    
    # Schema validation
    schemaValidation {
      enabled = true
      failOnSchemaError = false
      logSchemaErrors = true
    }
    
    # Data quality rules
    qualityRules = [
      {
        name = "not_null_check"
        fields = ["id", "timestamp", "source_id"]
        enabled = true
      },
      {
        name = "data_freshness"
        maxAgeMinutes = 60
        enabled = true
      },
      {
        name = "data_completeness"
        requiredFields = ["id", "metadata", "payload"]
        enabled = true
      }
    ]
  }
  
  # Normalization configuration
  normalization {
    enabled = true
    
    # Field mappings
    fieldMappings = {
      "timestamp" = "metadata.timestamp"
      "source" = "metadata.sourceId"
      "type" = "metadata.sourceType"
    }
    
    # Data transformations
    transformations = [
      {
        name = "timestamp_normalization"
        type = "timestamp"
        inputFormat = "auto"
        outputFormat = "ISO8601"
        timezone = "UTC"
      },
      {
        name = "text_normalization"
        type = "text"
        operations = ["trim", "lowercase", "remove_special_chars"]
      }
    ]
  }
  
  # Monitoring configuration
  monitoring {
    enabled = true
    metricsPort = 8085
    healthPort = 8086
    
    # Prometheus settings
    prometheus {
      enabled = true
      path = "/metrics"
      updateIntervalMs = 10000
    }
    
    # Health checks
    healthChecks = [
      {
        name = "kafka_connectivity"
        intervalMs = 30000
        timeoutMs = 5000
      },
      {
        name = "neo4j_connectivity"
        intervalMs = 30000
        timeoutMs = 5000
      },
      {
        name = "database_connectivity"
        intervalMs = 30000
        timeoutMs = 5000
      }
    ]
  }
}

# Staging configuration (inherits from development)
staging = ${development} {
  spark {
    master = "spark://spark-master:7077"
    checkpointLocation = "/app/checkpoints"
  }
  
  kafka {
    bootstrapServers = "kafka-staging:9092"
    schemaRegistryUrl = "http://schema-registry-staging:8081"
  }
  
  neo4j {
    uri = "bolt://neo4j-staging:7687"
  }
  
  database {
    url = "*******************************************************"
  }
}

# Production configuration (inherits from development)
production = ${development} {
  spark {
    master = "spark://spark-master:7077"
    checkpointLocation = "/app/checkpoints"
    logLevel = "WARN"
    
    # Production-specific settings
    sql {
      adaptive.enabled = true
      adaptive.coalescePartitions.enabled = true
      adaptive.skewJoin.enabled = true
      adaptive.localShuffleReader.enabled = true
    }
  }
  
  kafka {
    bootstrapServers = "kafka-1:9092,kafka-2:9092,kafka-3:9092"
    schemaRegistryUrl = "http://schema-registry:8081"
    
    # Production security settings
    properties {
      "security.protocol" = "SASL_SSL"
      "sasl.mechanism" = "SCRAM-SHA-512"
      "ssl.truststore.location" = "/etc/ssl/certs/kafka.truststore.jks"
      "ssl.keystore.location" = "/etc/ssl/certs/kafka.keystore.jks"
    }
  }
  
  neo4j {
    uri = "bolt+s://neo4j-cluster:7687"
    maxConnectionPoolSize = 100
  }
  
  database {
    url = "*******************************************************"
    maximumPoolSize = 50
  }
  
  validation {
    strictMode = true
    failOnSchemaError = true
  }
  
  monitoring {
    healthChecks = [
      {
        name = "kafka_connectivity"
        intervalMs = 15000
        timeoutMs = 3000
      },
      {
        name = "neo4j_connectivity"
        intervalMs = 15000
        timeoutMs = 3000
      },
      {
        name = "database_connectivity"
        intervalMs = 15000
        timeoutMs = 3000
      },
      {
        name = "memory_usage"
        intervalMs = 30000
        maxMemoryUsagePercent = 85
      },
      {
        name = "disk_usage"
        intervalMs = 60000
        maxDiskUsagePercent = 80
      }
    ]
  }
}
