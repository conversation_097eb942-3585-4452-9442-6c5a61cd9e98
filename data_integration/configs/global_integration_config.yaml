# ASI Global Data Integration Configuration
# =====================================

# Global settings
global:
  environment: production
  log_level: INFO
  metrics_enabled: true
  tracing_enabled: true
  security_enabled: true

# Scala Spark Pipeline Configuration
spark:
  master: "spark://spark-master:7077"
  app_name: "ASI-Global-Data-Integration"
  executor_memory: "4g"
  executor_cores: 2
  driver_memory: "2g"
  max_result_size: "1g"
  sql:
    adaptive:
      enabled: true
      coalescePartitions:
        enabled: true
  streaming:
    checkpointLocation: "/app/checkpoints"
    triggerInterval: "10 seconds"
    maxFilesPerTrigger: 100
  
  # Data sources configuration
  sources:
    kafka:
      bootstrap_servers: "kafka:9092"
      topics:
        - "asi-raw-data"
        - "asi-sensor-data"
        - "asi-api-data"
        - "asi-file-data"
      consumer_group: "asi-spark-consumer"
      auto_offset_reset: "latest"
    
    databases:
      postgres:
        url: "****************************************"
        user: "asi_user"
        password: "asi_password"
        driver: "org.postgresql.Driver"
        connection_pool_size: 10
      
      neo4j:
        uri: "bolt://neo4j:7687"
        username: "neo4j"
        password: "password"
        database: "neo4j"
    
    apis:
      - name: "weather_api"
        url: "https://api.openweathermap.org/data/2.5"
        auth_type: "api_key"
        polling_interval: "5 minutes"
        rate_limit: 60
      
      - name: "financial_api"
        url: "https://api.financial-data.com/v1"
        auth_type: "bearer_token"
        polling_interval: "1 minute"
        rate_limit: 1000
  
  # Data sinks configuration
  sinks:
    kafka:
      bootstrap_servers: "kafka:9092"
      topics:
        learning_engine: "asi-learning-engine"
        decision_engine: "asi-decision-engine"
        nlp_data: "asi-nlp-data"
        vision_data: "asi-vision-data"
        rl_data: "asi-rl-data"
        abstraction_data: "asi-abstraction-data"
      
    neo4j:
      uri: "bolt://neo4j:7687"
      username: "neo4j"
      password: "password"
      batch_size: 1000
      
    postgres:
      url: "****************************************"
      user: "asi_user"
      password: "asi_password"
      table_prefix: "asi_"
  
  # Data processing configuration
  processing:
    normalization:
      enabled: true
      remove_duplicates: true
      deduplication_keys: ["source_id", "timestamp"]
      null_handling: "impute"
      version: "1.0"
    
    validation:
      enabled: true
      schema_registry_url: "http://schema-registry:8081"
      required_fields: ["id", "source_id", "timestamp", "payload"]
      max_data_age_hours: 24
      anomaly_threshold: 3.0
      validation_rules:
        - name: "timestamp_range"
          field: "timestamp"
          rule_type: "range"
          min_value: 0
          max_value: 9999999999
        - name: "quality_score_range"
          field: "quality_score"
          rule_type: "range"
          min_value: 0.0
          max_value: 1.0

# Java Protocol Service Configuration
java_service:
  server:
    port: 8083
    max_threads: 200
    connection_timeout: 30000
    read_timeout: 60000
  
  protocols:
    rest:
      enabled: true
      max_requests_per_minute: 1000
      enable_authentication: true
      valid_api_keys:
        - "asi-api-key-001"
        - "asi-api-key-002"
      webhook_endpoints:
        weather: "/webhook/weather"
        iot: "/webhook/iot"
        financial: "/webhook/financial"
      external_apis:
        - name: "weather_service"
          url: "https://api.weather.com/v1"
          polling_interval: 300
        - name: "iot_platform"
          url: "https://iot.platform.com/api"
          polling_interval: 60
      output_topic: "asi-raw-data"
    
    graphql:
      enabled: true
      endpoint: "/graphql"
      max_query_depth: 10
      max_query_complexity: 1000
    
    websocket:
      enabled: true
      endpoint: "/ws"
      max_connections: 1000
      heartbeat_interval: 30
    
    grpc:
      enabled: true
      port: 50054
      max_message_size: 4194304  # 4MB
      keepalive_time: 30
      keepalive_timeout: 5
    
    mqtt:
      enabled: true
      broker_url: "tcp://mqtt-broker:1883"
      client_id: "asi-protocol-service"
      topics:
        - "sensors/+/data"
        - "devices/+/status"
        - "alerts/+"
    
    database:
      enabled: true
      connections:
        - name: "primary_postgres"
          type: "postgresql"
          url: "****************************************"
          username: "asi_user"
          password: "asi_password"
          pool_size: 10
        - name: "analytics_db"
          type: "clickhouse"
          url: "*******************************************"
          username: "default"
          password: ""
          pool_size: 5
  
  kafka:
    bootstrap_servers: "kafka:9092"
    producer:
      acks: "all"
      retries: 3
      batch_size: 16384
      linger_ms: 5
      buffer_memory: 33554432
    consumer:
      group_id: "java-protocol-service"
      auto_offset_reset: "latest"
      enable_auto_commit: false
      max_poll_records: 500
  
  redis:
    host: "redis"
    port: 6379
    database: 0
    pool_size: 10
    timeout: 5000
  
  security:
    enable_tls: false
    keystore_path: ""
    keystore_password: ""
    truststore_path: ""
    truststore_password: ""
  
  monitoring:
    metrics_port: 9093
    health_check_interval: 30
    prometheus_enabled: true

# Rust Device Integration Configuration
rust_service:
  server:
    grpc_port: 50055
    metrics_port: 8084
    max_connections: 1000
    keepalive_interval: 30
  
  kafka:
    bootstrap_servers: "kafka:9092"
    topic: "asi-device-data"
    producer:
      acks: "all"
      retries: 3
      batch_size: 16384
      linger_ms: 5
  
  redis:
    url: "redis://redis:6379"
    pool_size: 10
    timeout: 5
  
  devices:
    - id: "sensor_001"
      name: "Temperature Sensor Lab A"
      device_type: "ENVIRONMENTAL"
      protocol: "MODBUS"
      endpoint: "*************:502"
      metadata:
        location: "lab_a"
        model: "TempSense Pro"
        firmware: "v2.1.0"
    
    - id: "camera_001"
      name: "Security Camera Main Entrance"
      device_type: "VISION"
      protocol: "HTTP"
      endpoint: "http://192.168.1.101:8080/stream"
      metadata:
        location: "main_entrance"
        resolution: "1920x1080"
        fps: "30"
    
    - id: "plc_001"
      name: "Production Line PLC"
      device_type: "INDUSTRIAL"
      protocol: "OPC_UA"
      endpoint: "opc.tcp://*************:4840"
      metadata:
        location: "production_floor"
        manufacturer: "Siemens"
        model: "S7-1500"
  
  protocols:
    modbus:
      timeout: 5000
      retry_count: 3
      slave_id: 1
    
    opcua:
      security_policy: "None"
      security_mode: "None"
      session_timeout: 60000
      subscription_interval: 1000
    
    mqtt:
      broker_url: "tcp://mqtt-broker:1883"
      client_id: "asi-device-integration"
      keepalive: 60
      clean_session: true
    
    serial:
      baud_rate: 9600
      data_bits: 8
      stop_bits: 1
      parity: "none"
      timeout: 1000
    
    http:
      timeout: 10000
      retry_count: 3
      user_agent: "ASI-Device-Integration/1.0"
    
    tcp:
      timeout: 5000
      keepalive: true
      nodelay: true
  
  health_check_interval: 60
  max_error_rate: 0.1
  device_timeout: 300

# Neo4j Graph Database Configuration
neo4j:
  uri: "bolt://neo4j:7687"
  username: "neo4j"
  password: "password"
  database: "neo4j"
  max_connection_lifetime: 3600
  max_connection_pool_size: 50
  connection_acquisition_timeout: 60
  
  # Graph schema configuration
  schema:
    nodes:
      - label: "Device"
        properties: ["id", "type", "location", "status"]
        indexes: ["id", "type"]
      - label: "DataSource"
        properties: ["id", "type", "endpoint", "protocol"]
        indexes: ["id", "type"]
      - label: "System"
        properties: ["id", "name", "type", "version"]
        indexes: ["id", "type"]
      - label: "Data"
        properties: ["id", "type", "timestamp", "quality_score"]
        indexes: ["id", "timestamp"]
    
    relationships:
      - type: "FEEDS_DATA_TO"
        properties: ["timestamp", "data_rate", "quality"]
      - type: "PROCESSES"
        properties: ["processing_type", "timestamp"]
      - type: "CONNECTS_TO"
        properties: ["protocol", "established_at"]
      - type: "DEPENDS_ON"
        properties: ["dependency_type", "criticality"]

# Monitoring and Observability
monitoring:
  prometheus:
    enabled: true
    port: 9090
    scrape_interval: "15s"
    retention: "30d"
  
  grafana:
    enabled: true
    port: 3000
    admin_password: "admin"
    dashboards:
      - "data-integration-overview"
      - "kafka-metrics"
      - "spark-performance"
      - "device-health"
      - "api-performance"
  
  logging:
    level: "INFO"
    format: "json"
    outputs:
      - "console"
      - "file"
      - "elasticsearch"
    file_path: "/app/logs/asi-data-integration.log"
    max_file_size: "100MB"
    max_files: 10
  
  tracing:
    enabled: true
    jaeger_endpoint: "http://jaeger:14268/api/traces"
    sampling_rate: 0.1
  
  alerting:
    enabled: true
    webhook_url: "http://alertmanager:9093/api/v1/alerts"
    rules:
      - name: "high_error_rate"
        condition: "error_rate > 0.05"
        severity: "warning"
      - name: "service_down"
        condition: "up == 0"
        severity: "critical"
      - name: "high_latency"
        condition: "response_time_p95 > 5000"
        severity: "warning"

# Security Configuration
security:
  authentication:
    enabled: true
    type: "jwt"
    secret_key: "asi-secret-key-change-in-production"
    token_expiry: 3600
  
  authorization:
    enabled: true
    rbac:
      roles:
        - name: "admin"
          permissions: ["read", "write", "delete", "admin"]
        - name: "operator"
          permissions: ["read", "write"]
        - name: "viewer"
          permissions: ["read"]
  
  encryption:
    enabled: false  # Enable in production
    algorithm: "AES-256-GCM"
    key_rotation_interval: 86400  # 24 hours
  
  network:
    enable_tls: false  # Enable in production
    cert_file: "/certs/server.crt"
    key_file: "/certs/server.key"
    ca_file: "/certs/ca.crt"
