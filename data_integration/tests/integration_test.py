#!/usr/bin/env python3
"""
ASI Global Data Integration Integration Tests

Comprehensive integration tests for the data integration platform.
Tests end-to-end data flow from ingestion through processing to storage.
"""

import asyncio
import json
import time
import uuid
import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from datetime import datetime, timezone

import pytest
import grpc
import requests
from kafka import KafkaProducer, KafkaConsumer
from neo4j import GraphDatabase
import psycopg2
import redis

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Test configuration
KAFKA_BOOTSTRAP_SERVERS = ['localhost:9092']
NEO4J_URI = 'bolt://localhost:7687'
NEO4J_USERNAME = 'neo4j'
NEO4J_PASSWORD = 'password'
POSTGRES_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'database': 'asi_integration',
    'user': 'asi_user',
    'password': 'asi_password'
}
REDIS_CONFIG = {
    'host': 'localhost',
    'port': 6379,
    'db': 0
}

# Service endpoints
JAVA_SERVICE_URL = 'http://localhost:8083'
RUST_SERVICE_URL = 'http://localhost:8084'
SPARK_UI_URL = 'http://localhost:4040'
SPARK_METRICS_URL = 'http://localhost:8085'

@dataclass
class TestDataRecord:
    """Test data record structure"""
    id: str
    source_type: str
    data_format: str
    payload: Dict[str, Any]
    timestamp: datetime
    metadata: Dict[str, Any]

class DataIntegrationTestSuite:
    """Comprehensive test suite for data integration platform"""
    
    def __init__(self):
        self.test_id = str(uuid.uuid4())
        self.kafka_producer = None
        self.kafka_consumer = None
        self.neo4j_driver = None
        self.postgres_conn = None
        self.redis_client = None
        
    async def setup(self):
        """Set up test environment and connections"""
        logger.info(f"Setting up integration tests (ID: {self.test_id})")
        
        # Initialize Kafka producer
        self.kafka_producer = KafkaProducer(
            bootstrap_servers=KAFKA_BOOTSTRAP_SERVERS,
            value_serializer=lambda x: json.dumps(x, default=str).encode('utf-8'),
            key_serializer=lambda x: x.encode('utf-8') if x else None,
            acks='all',
            retries=3
        )
        
        # Initialize Kafka consumer
        self.kafka_consumer = KafkaConsumer(
            'asi-processed-data',
            'asi-learning-engine',
            'asi-decision-engine',
            bootstrap_servers=KAFKA_BOOTSTRAP_SERVERS,
            value_deserializer=lambda x: json.loads(x.decode('utf-8')),
            key_deserializer=lambda x: x.decode('utf-8') if x else None,
            group_id=f'test-group-{self.test_id}',
            auto_offset_reset='latest'
        )
        
        # Initialize Neo4j driver
        self.neo4j_driver = GraphDatabase.driver(
            NEO4J_URI,
            auth=(NEO4J_USERNAME, NEO4J_PASSWORD)
        )
        
        # Initialize PostgreSQL connection
        self.postgres_conn = psycopg2.connect(**POSTGRES_CONFIG)
        
        # Initialize Redis client
        self.redis_client = redis.Redis(**REDIS_CONFIG)
        
        logger.info("Test environment setup completed")
    
    async def teardown(self):
        """Clean up test environment"""
        logger.info("Cleaning up test environment...")
        
        if self.kafka_producer:
            self.kafka_producer.close()
        
        if self.kafka_consumer:
            self.kafka_consumer.close()
        
        if self.neo4j_driver:
            self.neo4j_driver.close()
        
        if self.postgres_conn:
            self.postgres_conn.close()
        
        if self.redis_client:
            self.redis_client.close()
        
        logger.info("Test environment cleaned up")
    
    async def test_service_health_checks(self) -> Dict[str, bool]:
        """Test health of all services"""
        logger.info("Testing service health checks...")
        
        health_results = {}
        
        # Test Java Protocol Service
        try:
            response = requests.get(f'{JAVA_SERVICE_URL}/actuator/health', timeout=5)
            health_results['java_service'] = response.status_code == 200
        except Exception as e:
            logger.error(f"Java service health check failed: {e}")
            health_results['java_service'] = False
        
        # Test Rust Device Service
        try:
            response = requests.get(f'{RUST_SERVICE_URL}/health', timeout=5)
            health_results['rust_service'] = response.status_code == 200
        except Exception as e:
            logger.error(f"Rust service health check failed: {e}")
            health_results['rust_service'] = False
        
        # Test Spark Pipeline
        try:
            response = requests.get(f'{SPARK_METRICS_URL}/health', timeout=5)
            health_results['spark_pipeline'] = response.status_code == 200
        except Exception as e:
            logger.error(f"Spark pipeline health check failed: {e}")
            health_results['spark_pipeline'] = False
        
        # Test Neo4j
        try:
            with self.neo4j_driver.session() as session:
                result = session.run("RETURN 1 as test")
                health_results['neo4j'] = result.single()['test'] == 1
        except Exception as e:
            logger.error(f"Neo4j health check failed: {e}")
            health_results['neo4j'] = False
        
        # Test PostgreSQL
        try:
            with self.postgres_conn.cursor() as cursor:
                cursor.execute("SELECT 1")
                health_results['postgres'] = cursor.fetchone()[0] == 1
        except Exception as e:
            logger.error(f"PostgreSQL health check failed: {e}")
            health_results['postgres'] = False
        
        # Test Redis
        try:
            health_results['redis'] = self.redis_client.ping()
        except Exception as e:
            logger.error(f"Redis health check failed: {e}")
            health_results['redis'] = False
        
        # Test Kafka
        try:
            # Send a test message
            future = self.kafka_producer.send('asi-data-integration', {'test': 'health_check'})
            future.get(timeout=10)
            health_results['kafka'] = True
        except Exception as e:
            logger.error(f"Kafka health check failed: {e}")
            health_results['kafka'] = False
        
        return health_results
    
    async def test_data_ingestion_flow(self) -> bool:
        """Test complete data ingestion and processing flow"""
        logger.info("Testing data ingestion flow...")
        
        try:
            # Create test data records
            test_records = self.create_test_data_records(10)
            
            # Send data through Java Protocol Service
            for record in test_records:
                payload = {
                    'id': record.id,
                    'sourceType': record.source_type,
                    'format': record.data_format,
                    'payload': record.payload,
                    'timestamp': record.timestamp.isoformat(),
                    'metadata': record.metadata
                }
                
                response = requests.post(
                    f'{JAVA_SERVICE_URL}/api/v1/data/ingest',
                    json=payload,
                    headers={'Content-Type': 'application/json'},
                    timeout=10
                )
                
                if response.status_code not in [200, 201, 202]:
                    logger.error(f"Failed to ingest data: {response.status_code} - {response.text}")
                    return False
            
            # Wait for processing
            await asyncio.sleep(10)
            
            # Verify data in Neo4j
            processed_count = self.verify_data_in_neo4j(test_records)
            
            # Verify data in output topics
            output_messages = self.verify_output_messages(test_records)
            
            success = processed_count >= len(test_records) * 0.8  # Allow 20% loss
            logger.info(f"Data ingestion flow test: {processed_count}/{len(test_records)} records processed")
            
            return success
            
        except Exception as e:
            logger.error(f"Data ingestion flow test failed: {e}")
            return False
    
    async def test_device_integration(self) -> bool:
        """Test device data integration via Rust service"""
        logger.info("Testing device integration...")
        
        try:
            # Simulate device data
            device_data = {
                'device_id': f'test-device-{self.test_id}',
                'device_type': 'sensor',
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'readings': [
                    {
                        'sensor_id': 'temp_01',
                        'sensor_type': 'temperature',
                        'value': 23.5,
                        'unit': 'celsius'
                    },
                    {
                        'sensor_id': 'humid_01',
                        'sensor_type': 'humidity',
                        'value': 65.2,
                        'unit': 'percent'
                    }
                ]
            }
            
            # Send device data via gRPC (simplified HTTP for testing)
            response = requests.post(
                f'{RUST_SERVICE_URL}/api/v1/device/data',
                json=device_data,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            if response.status_code not in [200, 201, 202]:
                logger.error(f"Failed to send device data: {response.status_code}")
                return False
            
            # Wait for processing
            await asyncio.sleep(5)
            
            # Verify device data was processed
            # This would check Kafka topics and Neo4j for device data
            return True
            
        except Exception as e:
            logger.error(f"Device integration test failed: {e}")
            return False
    
    async def test_schema_validation(self) -> bool:
        """Test schema validation and evolution"""
        logger.info("Testing schema validation...")
        
        try:
            # Test valid schema
            valid_data = {
                'id': str(uuid.uuid4()),
                'sourceType': 'API',
                'format': 'JSON',
                'payload': {'valid': 'data'},
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'metadata': {'source': 'test'}
            }
            
            response = requests.post(
                f'{JAVA_SERVICE_URL}/api/v1/data/validate',
                json=valid_data,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            if response.status_code != 200:
                logger.error(f"Valid data validation failed: {response.status_code}")
                return False
            
            # Test invalid schema
            invalid_data = {
                'invalid_field': 'should_fail'
            }
            
            response = requests.post(
                f'{JAVA_SERVICE_URL}/api/v1/data/validate',
                json=invalid_data,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            if response.status_code == 200:
                logger.error("Invalid data validation should have failed")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Schema validation test failed: {e}")
            return False
    
    async def test_performance_metrics(self) -> bool:
        """Test performance and metrics collection"""
        logger.info("Testing performance metrics...")
        
        try:
            # Check Java service metrics
            response = requests.get(f'{JAVA_SERVICE_URL}/actuator/prometheus', timeout=5)
            if response.status_code != 200:
                logger.error("Failed to get Java service metrics")
                return False
            
            # Check Rust service metrics
            response = requests.get(f'{RUST_SERVICE_URL}/metrics', timeout=5)
            if response.status_code != 200:
                logger.error("Failed to get Rust service metrics")
                return False
            
            # Check Spark metrics
            response = requests.get(f'{SPARK_METRICS_URL}/metrics', timeout=5)
            if response.status_code != 200:
                logger.error("Failed to get Spark metrics")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Performance metrics test failed: {e}")
            return False
    
    def create_test_data_records(self, count: int) -> List[TestDataRecord]:
        """Create test data records"""
        records = []
        
        for i in range(count):
            record = TestDataRecord(
                id=str(uuid.uuid4()),
                source_type='API',
                data_format='JSON',
                payload={
                    'user_id': f'user_{i}',
                    'action': 'test_action',
                    'value': i * 10,
                    'test_id': self.test_id
                },
                timestamp=datetime.now(timezone.utc),
                metadata={
                    'source': 'integration_test',
                    'batch': self.test_id,
                    'index': i
                }
            )
            records.append(record)
        
        return records
    
    def verify_data_in_neo4j(self, test_records: List[TestDataRecord]) -> int:
        """Verify data was stored in Neo4j"""
        try:
            with self.neo4j_driver.session() as session:
                result = session.run(
                    "MATCH (n) WHERE n.test_id = $test_id RETURN count(n) as count",
                    test_id=self.test_id
                )
                return result.single()['count']
        except Exception as e:
            logger.error(f"Failed to verify data in Neo4j: {e}")
            return 0
    
    def verify_output_messages(self, test_records: List[TestDataRecord]) -> int:
        """Verify output messages in Kafka topics"""
        try:
            message_count = 0
            timeout = time.time() + 30  # 30 second timeout
            
            while time.time() < timeout:
                message_batch = self.kafka_consumer.poll(timeout_ms=1000)
                for topic_partition, messages in message_batch.items():
                    for message in messages:
                        if message.value.get('test_id') == self.test_id:
                            message_count += 1
                
                if message_count >= len(test_records):
                    break
            
            return message_count
            
        except Exception as e:
            logger.error(f"Failed to verify output messages: {e}")
            return 0
    
    async def run_all_tests(self) -> Dict[str, bool]:
        """Run all integration tests"""
        logger.info(f"Starting ASI Data Integration Tests (ID: {self.test_id})")
        logger.info("=" * 60)
        
        await self.setup()
        
        test_results = {}
        
        try:
            # Run individual tests
            health_results = await self.test_service_health_checks()
            test_results.update({f'health_{k}': v for k, v in health_results.items()})
            
            test_results['data_ingestion_flow'] = await self.test_data_ingestion_flow()
            test_results['device_integration'] = await self.test_device_integration()
            test_results['schema_validation'] = await self.test_schema_validation()
            test_results['performance_metrics'] = await self.test_performance_metrics()
            
        finally:
            await self.teardown()
        
        # Print results
        logger.info("\n" + "=" * 60)
        logger.info("TEST RESULTS:")
        logger.info("=" * 60)
        
        passed_tests = 0
        total_tests = len(test_results)
        
        for test_name, result in test_results.items():
            status = "PASS" if result else "FAIL"
            logger.info(f"{test_name:<30} : {status}")
            if result:
                passed_tests += 1
        
        logger.info("=" * 60)
        logger.info(f"SUMMARY: {passed_tests}/{total_tests} tests passed")
        
        if passed_tests == total_tests:
            logger.info("🎉 All integration tests PASSED!")
        else:
            logger.info("❌ Some integration tests FAILED!")
        
        return test_results


async def main():
    """Main test runner"""
    test_suite = DataIntegrationTestSuite()
    results = await test_suite.run_all_tests()
    
    # Exit with appropriate code
    all_passed = all(results.values())
    exit(0 if all_passed else 1)


if __name__ == "__main__":
    asyncio.run(main())
