use anyhow::{Result, Context};
use dashmap::DashMap;
use std::sync::Arc;
use tokio::sync::{mpsc, RwLock};
use tokio::time::{interval, Duration, Instant};
use tracing::{info, warn, error, debug, instrument};
use uuid::Uuid;

use crate::config::DeviceConfig;
use crate::kafka_producer::KafkaProducer;
use crate::metrics::MetricsCollector;
use crate::protocols::{DeviceProtocol, ProtocolHandler};

/// Device data structure
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct DeviceData {
    pub device_id: String,
    pub device_type: String,
    pub protocol: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub data: serde_json::Value,
    pub metadata: std::collections::HashMap<String, String>,
    pub quality_score: f64,
}

/// Device status enumeration
#[derive(Debug, <PERSON>lone, PartialEq, serde::Serialize, serde::Deserialize)]
pub enum DeviceStatus {
    Connected,
    Disconnected,
    Error,
    Maintenance,
}

/// Device information structure
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct DeviceInfo {
    pub id: String,
    pub name: String,
    pub device_type: String,
    pub protocol: DeviceProtocol,
    pub endpoint: String,
    pub status: DeviceStatus,
    pub last_seen: Option<chrono::DateTime<chrono::Utc>>,
    pub data_rate: f64, // messages per second
    pub error_count: u64,
    pub total_messages: u64,
    pub metadata: std::collections::HashMap<String, String>,
}

/// Device manager for handling multiple device connections and data collection
pub struct DeviceManager {
    devices: Arc<DashMap<String, DeviceInfo>>,
    protocol_handlers: Arc<DashMap<DeviceProtocol, Arc<dyn ProtocolHandler>>>,
    kafka_producer: Arc<KafkaProducer>,
    metrics: Arc<MetricsCollector>,
    data_sender: mpsc::UnboundedSender<DeviceData>,
    data_receiver: Arc<RwLock<Option<mpsc::UnboundedReceiver<DeviceData>>>>,
    config: DeviceConfig,
    shutdown_signal: Arc<tokio::sync::Notify>,
}

impl DeviceManager {
    /// Create a new device manager
    pub async fn new(
        config: &DeviceConfig,
        kafka_producer: Arc<KafkaProducer>,
        metrics: Arc<MetricsCollector>,
    ) -> Result<Self> {
        let (data_sender, data_receiver) = mpsc::unbounded_channel();
        
        let manager = Self {
            devices: Arc::new(DashMap::new()),
            protocol_handlers: Arc::new(DashMap::new()),
            kafka_producer,
            metrics,
            data_sender,
            data_receiver: Arc::new(RwLock::new(Some(data_receiver))),
            config: config.clone(),
            shutdown_signal: Arc::new(tokio::sync::Notify::new()),
        };
        
        // Initialize protocol handlers
        manager.initialize_protocol_handlers().await?;
        
        // Register configured devices
        manager.register_configured_devices().await?;
        
        info!("Device manager initialized with {} devices", manager.devices.len());
        
        Ok(manager)
    }
    
    /// Start the device manager
    #[instrument(skip(self))]
    pub async fn start(&self) -> Result<()> {
        info!("Starting device manager");
        
        // Start data processing task
        let data_processor = self.start_data_processor().await?;
        
        // Start device monitoring task
        let device_monitor = self.start_device_monitor();
        
        // Start metrics collection task
        let metrics_collector = self.start_metrics_collection();
        
        // Start all protocol handlers
        self.start_protocol_handlers().await?;
        
        // Wait for shutdown signal
        tokio::select! {
            _ = self.shutdown_signal.notified() => {
                info!("Received shutdown signal");
            }
            result = data_processor => {
                match result {
                    Ok(_) => info!("Data processor completed"),
                    Err(e) => error!("Data processor error: {}", e),
                }
            }
            _ = device_monitor => {
                info!("Device monitor completed");
            }
            _ = metrics_collector => {
                info!("Metrics collector completed");
            }
        }
        
        // Cleanup
        self.stop_all_handlers().await?;
        
        Ok(())
    }
    
    /// Register a new device
    #[instrument(skip(self))]
    pub async fn register_device(&self, device_info: DeviceInfo) -> Result<()> {
        let device_id = device_info.id.clone();
        
        // Validate device configuration
        self.validate_device_config(&device_info)?;
        
        // Get or create protocol handler
        let handler = self.get_or_create_protocol_handler(device_info.protocol).await?;
        
        // Initialize device connection
        handler.connect_device(&device_info).await
            .context(format!("Failed to connect device {}", device_id))?;
        
        // Register device
        self.devices.insert(device_id.clone(), device_info);
        
        // Update metrics
        self.metrics.increment_counter("devices_registered_total", &[("device_id", &device_id)]);
        self.metrics.set_gauge("devices_active_count", self.devices.len() as f64);
        
        info!("Device registered successfully: {}", device_id);
        
        Ok(())
    }
    
    /// Unregister a device
    #[instrument(skip(self))]
    pub async fn unregister_device(&self, device_id: &str) -> Result<()> {
        if let Some((_, device_info)) = self.devices.remove(device_id) {
            // Get protocol handler and disconnect device
            if let Some(handler) = self.protocol_handlers.get(&device_info.protocol) {
                if let Err(e) = handler.disconnect_device(device_id).await {
                    warn!("Failed to disconnect device {}: {}", device_id, e);
                }
            }
            
            // Update metrics
            self.metrics.increment_counter("devices_unregistered_total", &[("device_id", device_id)]);
            self.metrics.set_gauge("devices_active_count", self.devices.len() as f64);
            
            info!("Device unregistered: {}", device_id);
        } else {
            warn!("Attempted to unregister unknown device: {}", device_id);
        }
        
        Ok(())
    }
    
    /// Get device information
    pub fn get_device(&self, device_id: &str) -> Option<DeviceInfo> {
        self.devices.get(device_id).map(|entry| entry.value().clone())
    }
    
    /// List all devices
    pub fn list_devices(&self) -> Vec<DeviceInfo> {
        self.devices.iter().map(|entry| entry.value().clone()).collect()
    }
    
    /// Update device status
    #[instrument(skip(self))]
    pub fn update_device_status(&self, device_id: &str, status: DeviceStatus) {
        if let Some(mut device) = self.devices.get_mut(device_id) {
            device.status = status.clone();
            device.last_seen = Some(chrono::Utc::now());
            
            // Update metrics
            let status_str = match status {
                DeviceStatus::Connected => "connected",
                DeviceStatus::Disconnected => "disconnected",
                DeviceStatus::Error => "error",
                DeviceStatus::Maintenance => "maintenance",
            };
            
            self.metrics.set_gauge(
                "device_status",
                1.0,
                &[("device_id", device_id), ("status", status_str)]
            );
            
            debug!("Device status updated: {} -> {:?}", device_id, status);
        }
    }
    
    /// Process incoming device data
    #[instrument(skip(self, data))]
    pub async fn process_device_data(&self, data: DeviceData) -> Result<()> {
        // Validate data
        self.validate_device_data(&data)?;
        
        // Update device statistics
        self.update_device_statistics(&data.device_id);
        
        // Send data for processing
        self.data_sender.send(data)
            .context("Failed to send device data for processing")?;
        
        Ok(())
    }
    
    /// Get device statistics
    pub fn get_device_statistics(&self, device_id: &str) -> Option<DeviceStatistics> {
        self.devices.get(device_id).map(|device| {
            DeviceStatistics {
                device_id: device.id.clone(),
                total_messages: device.total_messages,
                error_count: device.error_count,
                data_rate: device.data_rate,
                uptime_percentage: self.calculate_uptime_percentage(&device.id),
                last_seen: device.last_seen,
                status: device.status.clone(),
            }
        })
    }
    
    /// Shutdown the device manager
    pub async fn shutdown(&self) {
        info!("Shutting down device manager");
        self.shutdown_signal.notify_waiters();
    }
    
    // Private helper methods
    
    async fn initialize_protocol_handlers(&self) -> Result<()> {
        use crate::protocols::*;
        
        // Initialize supported protocol handlers
        let handlers: Vec<(DeviceProtocol, Arc<dyn ProtocolHandler>)> = vec![
            (DeviceProtocol::Modbus, Arc::new(ModbusHandler::new(&self.config.modbus)?)),
            (DeviceProtocol::OpcUa, Arc::new(OpcUaHandler::new(&self.config.opcua)?)),
            (DeviceProtocol::Mqtt, Arc::new(MqttHandler::new(&self.config.mqtt)?)),
            (DeviceProtocol::Serial, Arc::new(SerialHandler::new(&self.config.serial)?)),
            (DeviceProtocol::Http, Arc::new(HttpHandler::new(&self.config.http)?)),
            (DeviceProtocol::Tcp, Arc::new(TcpHandler::new(&self.config.tcp)?)),
        ];
        
        for (protocol, handler) in handlers {
            self.protocol_handlers.insert(protocol, handler);
        }
        
        info!("Initialized {} protocol handlers", self.protocol_handlers.len());
        Ok(())
    }
    
    async fn register_configured_devices(&self) -> Result<()> {
        for device_config in &self.config.devices {
            let device_info = DeviceInfo {
                id: device_config.id.clone(),
                name: device_config.name.clone(),
                device_type: device_config.device_type.clone(),
                protocol: device_config.protocol,
                endpoint: device_config.endpoint.clone(),
                status: DeviceStatus::Disconnected,
                last_seen: None,
                data_rate: 0.0,
                error_count: 0,
                total_messages: 0,
                metadata: device_config.metadata.clone(),
            };
            
            if let Err(e) = self.register_device(device_info).await {
                warn!("Failed to register configured device {}: {}", device_config.id, e);
            }
        }
        
        Ok(())
    }
    
    async fn start_data_processor(&self) -> Result<tokio::task::JoinHandle<Result<()>>> {
        let mut receiver = self.data_receiver.write().await.take()
            .context("Data receiver already taken")?;
        
        let kafka_producer = self.kafka_producer.clone();
        let metrics = self.metrics.clone();
        let topic = self.config.kafka_topic.clone();
        
        let handle = tokio::spawn(async move {
            while let Some(data) = receiver.recv().await {
                // Process and send data to Kafka
                match Self::process_and_send_data(&kafka_producer, &topic, data, &metrics).await {
                    Ok(_) => {
                        metrics.increment_counter("data_processed_total", &[]);
                    }
                    Err(e) => {
                        error!("Failed to process device data: {}", e);
                        metrics.increment_counter("data_processing_errors_total", &[]);
                    }
                }
            }
            Ok(())
        });
        
        Ok(handle)
    }
    
    fn start_device_monitor(&self) -> tokio::task::JoinHandle<()> {
        let devices = self.devices.clone();
        let metrics = self.metrics.clone();
        let check_interval = Duration::from_secs(self.config.health_check_interval);
        
        tokio::spawn(async move {
            let mut interval = interval(check_interval);
            
            loop {
                interval.tick().await;
                
                // Check device health
                for device in devices.iter() {
                    let device_id = device.key();
                    let device_info = device.value();
                    
                    // Check if device is stale
                    if let Some(last_seen) = device_info.last_seen {
                        let stale_threshold = chrono::Utc::now() - chrono::Duration::seconds(300); // 5 minutes
                        if last_seen < stale_threshold && device_info.status == DeviceStatus::Connected {
                            warn!("Device {} appears to be stale", device_id);
                            // Update status to disconnected
                            // Note: This would need a mutable reference, so we'd need to restructure
                        }
                    }
                    
                    // Update health metrics
                    let health_score = Self::calculate_device_health(&device_info);
                    metrics.set_gauge(
                        "device_health_score",
                        health_score,
                        &[("device_id", device_id)]
                    );
                }
            }
        })
    }
    
    fn start_metrics_collection(&self) -> tokio::task::JoinHandle<()> {
        let devices = self.devices.clone();
        let metrics = self.metrics.clone();
        let collection_interval = Duration::from_secs(60); // 1 minute
        
        tokio::spawn(async move {
            let mut interval = interval(collection_interval);
            
            loop {
                interval.tick().await;
                
                // Collect and report device metrics
                let total_devices = devices.len();
                let connected_devices = devices.iter()
                    .filter(|d| d.value().status == DeviceStatus::Connected)
                    .count();
                
                metrics.set_gauge("devices_total_count", total_devices as f64);
                metrics.set_gauge("devices_connected_count", connected_devices as f64);
                
                // Calculate overall system health
                let system_health = if total_devices > 0 {
                    connected_devices as f64 / total_devices as f64
                } else {
                    0.0
                };
                
                metrics.set_gauge("system_health_score", system_health);
            }
        })
    }
    
    async fn start_protocol_handlers(&self) -> Result<()> {
        for handler in self.protocol_handlers.iter() {
            if let Err(e) = handler.value().start().await {
                error!("Failed to start protocol handler {:?}: {}", handler.key(), e);
            }
        }
        Ok(())
    }
    
    async fn stop_all_handlers(&self) -> Result<()> {
        for handler in self.protocol_handlers.iter() {
            if let Err(e) = handler.value().stop().await {
                warn!("Error stopping protocol handler {:?}: {}", handler.key(), e);
            }
        }
        Ok(())
    }
    
    async fn get_or_create_protocol_handler(
        &self,
        protocol: DeviceProtocol,
    ) -> Result<Arc<dyn ProtocolHandler>> {
        if let Some(handler) = self.protocol_handlers.get(&protocol) {
            Ok(handler.value().clone())
        } else {
            Err(anyhow::anyhow!("Unsupported protocol: {:?}", protocol))
        }
    }
    
    fn validate_device_config(&self, device_info: &DeviceInfo) -> Result<()> {
        if device_info.id.is_empty() {
            return Err(anyhow::anyhow!("Device ID cannot be empty"));
        }
        
        if device_info.endpoint.is_empty() {
            return Err(anyhow::anyhow!("Device endpoint cannot be empty"));
        }
        
        Ok(())
    }
    
    fn validate_device_data(&self, data: &DeviceData) -> Result<()> {
        if data.device_id.is_empty() {
            return Err(anyhow::anyhow!("Device ID cannot be empty"));
        }
        
        if data.quality_score < 0.0 || data.quality_score > 1.0 {
            return Err(anyhow::anyhow!("Quality score must be between 0.0 and 1.0"));
        }
        
        Ok(())
    }
    
    fn update_device_statistics(&self, device_id: &str) {
        if let Some(mut device) = self.devices.get_mut(device_id) {
            device.total_messages += 1;
            device.last_seen = Some(chrono::Utc::now());
            
            // Calculate data rate (simple moving average)
            // This is a simplified calculation - in production, you'd want a more sophisticated approach
            device.data_rate = device.total_messages as f64 / 60.0; // messages per minute
        }
    }
    
    async fn process_and_send_data(
        kafka_producer: &KafkaProducer,
        topic: &str,
        data: DeviceData,
        metrics: &MetricsCollector,
    ) -> Result<()> {
        let start_time = Instant::now();
        
        // Serialize data
        let payload = serde_json::to_string(&data)
            .context("Failed to serialize device data")?;
        
        // Send to Kafka
        kafka_producer.send(topic, &data.device_id, &payload).await
            .context("Failed to send data to Kafka")?;
        
        // Record metrics
        let processing_time = start_time.elapsed().as_millis() as f64;
        metrics.record_histogram("data_processing_duration_ms", processing_time);
        metrics.increment_counter("data_sent_total", &[("device_id", &data.device_id)]);
        
        debug!("Device data processed and sent: {}", data.device_id);
        
        Ok(())
    }
    
    fn calculate_device_health(device_info: &DeviceInfo) -> f64 {
        match device_info.status {
            DeviceStatus::Connected => {
                // Calculate health based on error rate and recency
                let error_rate = if device_info.total_messages > 0 {
                    device_info.error_count as f64 / device_info.total_messages as f64
                } else {
                    0.0
                };
                
                let health_from_errors = 1.0 - error_rate;
                
                // Factor in recency
                let health_from_recency = if let Some(last_seen) = device_info.last_seen {
                    let minutes_since_last_seen = (chrono::Utc::now() - last_seen).num_minutes();
                    if minutes_since_last_seen < 5 {
                        1.0
                    } else if minutes_since_last_seen < 30 {
                        0.5
                    } else {
                        0.1
                    }
                } else {
                    0.0
                };
                
                (health_from_errors + health_from_recency) / 2.0
            }
            DeviceStatus::Disconnected => 0.0,
            DeviceStatus::Error => 0.1,
            DeviceStatus::Maintenance => 0.5,
        }
    }
    
    fn calculate_uptime_percentage(&self, _device_id: &str) -> f64 {
        // Placeholder implementation
        // In a real system, you'd track uptime over time
        95.0
    }
}

/// Device statistics structure
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct DeviceStatistics {
    pub device_id: String,
    pub total_messages: u64,
    pub error_count: u64,
    pub data_rate: f64,
    pub uptime_percentage: f64,
    pub last_seen: Option<chrono::DateTime<chrono::Utc>>,
    pub status: DeviceStatus,
}
