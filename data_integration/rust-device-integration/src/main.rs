use anyhow::Result;
use clap::Parser;
use std::sync::Arc;
use tokio::signal;
use tracing::{info, error, warn};

mod config;
mod device;
mod grpc_server;
mod kafka_producer;
mod metrics;
mod protocols;
mod security;

use config::Config;
use device::DeviceManager;
use grpc_server::DeviceIntegrationServer;
use kafka_producer::KafkaProducer;
use metrics::MetricsCollector;

/// ASI Device Integration Service
/// 
/// Real-time device integration service that handles communication with various
/// IoT devices, sensors, and industrial equipment through multiple protocols.
#[derive(Parser)]
#[command(name = "asi-device-integration")]
#[command(about = "ASI Device Integration Service for real-time data collection")]
struct Args {
    /// Configuration file path
    #[arg(short, long, default_value = "config.toml")]
    config: String,
    
    /// Log level
    #[arg(short, long, default_value = "info")]
    log_level: String,
    
    /// Enable development mode
    #[arg(long)]
    dev_mode: bool,
}

#[tokio::main]
async fn main() -> Result<()> {
    let args = Args::parse();
    
    // Initialize tracing
    init_tracing(&args.log_level)?;
    
    info!("Starting ASI Device Integration Service");
    
    // Load configuration
    let config = Config::load(&args.config)?;
    info!("Configuration loaded from {}", args.config);
    
    // Initialize metrics collector
    let metrics = Arc::new(MetricsCollector::new(&config.metrics)?);
    
    // Initialize Kafka producer
    let kafka_producer = Arc::new(KafkaProducer::new(&config.kafka, metrics.clone()).await?);
    
    // Initialize device manager
    let device_manager = Arc::new(DeviceManager::new(
        &config.devices,
        kafka_producer.clone(),
        metrics.clone(),
    ).await?);
    
    // Initialize gRPC server
    let grpc_server = DeviceIntegrationServer::new(
        device_manager.clone(),
        metrics.clone(),
    );
    
    // Start metrics server
    let metrics_handle = start_metrics_server(config.metrics.port, metrics.clone());
    
    // Start gRPC server
    let grpc_handle = start_grpc_server(config.grpc.port, grpc_server);
    
    // Start device manager
    let device_handle = tokio::spawn(async move {
        if let Err(e) = device_manager.start().await {
            error!("Device manager error: {}", e);
        }
    });
    
    info!("All services started successfully");
    
    // Wait for shutdown signal
    tokio::select! {
        _ = signal::ctrl_c() => {
            info!("Received shutdown signal");
        }
        result = grpc_handle => {
            match result {
                Ok(_) => info!("gRPC server completed"),
                Err(e) => error!("gRPC server error: {}", e),
            }
        }
        result = device_handle => {
            match result {
                Ok(_) => info!("Device manager completed"),
                Err(e) => error!("Device manager error: {}", e),
            }
        }
    }
    
    // Graceful shutdown
    info!("Initiating graceful shutdown...");
    
    // Stop metrics server
    metrics_handle.abort();
    
    // Cleanup
    kafka_producer.close().await?;
    
    info!("ASI Device Integration Service stopped");
    Ok(())
}

fn init_tracing(log_level: &str) -> Result<()> {
    use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};
    
    let env_filter = tracing_subscriber::EnvFilter::try_from_default_env()
        .unwrap_or_else(|_| tracing_subscriber::EnvFilter::new(log_level));
    
    tracing_subscriber::registry()
        .with(env_filter)
        .with(
            tracing_subscriber::fmt::layer()
                .json()
                .with_current_span(false)
                .with_span_list(true)
        )
        .init();
    
    info!("Tracing initialized with level: {}", log_level);
    Ok(())
}

async fn start_metrics_server(
    port: u16,
    metrics: Arc<MetricsCollector>,
) -> tokio::task::JoinHandle<()> {
    tokio::spawn(async move {
        use axum::{
            extract::State,
            http::StatusCode,
            response::Response,
            routing::get,
            Router,
        };
        use axum_prometheus::PrometheusMetricLayer;
        
        let (prometheus_layer, metric_handle) = PrometheusMetricLayer::pair();
        
        let app = Router::new()
            .route("/health", get(health_check))
            .route("/metrics", get(move || async move {
                let encoder = prometheus::TextEncoder::new();
                let metric_families = metrics.gather();
                match encoder.encode_to_string(&metric_families) {
                    Ok(output) => Response::builder()
                        .status(StatusCode::OK)
                        .header("content-type", "text/plain; version=0.0.4")
                        .body(output)
                        .unwrap(),
                    Err(_) => Response::builder()
                        .status(StatusCode::INTERNAL_SERVER_ERROR)
                        .body("Failed to encode metrics".to_string())
                        .unwrap(),
                }
            }))
            .route("/metrics/prometheus", get(|| async move { metric_handle.render() }))
            .layer(prometheus_layer)
            .with_state(metrics);
        
        let listener = tokio::net::TcpListener::bind(format!("0.0.0.0:{}", port))
            .await
            .expect("Failed to bind metrics server");
        
        info!("Metrics server listening on port {}", port);
        
        if let Err(e) = axum::serve(listener, app).await {
            error!("Metrics server error: {}", e);
        }
    })
}

async fn health_check() -> StatusCode {
    StatusCode::OK
}

async fn start_grpc_server(
    port: u16,
    server: DeviceIntegrationServer,
) -> tokio::task::JoinHandle<Result<()>> {
    tokio::spawn(async move {
        use tonic::transport::Server;
        use crate::grpc_server::device_integration_service_server::DeviceIntegrationServiceServer;
        
        let addr = format!("0.0.0.0:{}", port).parse()?;
        
        info!("Starting gRPC server on {}", addr);
        
        Server::builder()
            .add_service(DeviceIntegrationServiceServer::new(server))
            .serve(addr)
            .await?;
        
        Ok(())
    })
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio_test;
    
    #[tokio::test]
    async fn test_service_initialization() {
        // Test that the service can be initialized with default config
        let config = Config::default();
        assert!(config.grpc.port > 0);
        assert!(config.metrics.port > 0);
    }
    
    #[test]
    fn test_args_parsing() {
        let args = Args::parse_from(&["test", "--config", "test.toml", "--log-level", "debug"]);
        assert_eq!(args.config, "test.toml");
        assert_eq!(args.log_level, "debug");
    }
}
