use anyhow::Result;
use std::sync::Arc;
use tonic::{Request, Response, Status, Code};
use tracing::{info, warn, error, instrument};

use crate::device::{DeviceManager, DeviceData, DeviceInfo, DeviceStatus};
use crate::metrics::MetricsCollector;

// Include generated protobuf code
pub mod device_integration {
    tonic::include_proto!("asi.device_integration");
}

use device_integration::*;

/// gRPC server implementation for device integration
pub struct DeviceIntegrationServer {
    device_manager: Arc<DeviceManager>,
    metrics: Arc<MetricsCollector>,
}

impl DeviceIntegrationServer {
    pub fn new(
        device_manager: Arc<DeviceManager>,
        metrics: Arc<MetricsCollector>,
    ) -> Self {
        Self {
            device_manager,
            metrics,
        }
    }
}

#[tonic::async_trait]
impl device_integration_service_server::DeviceIntegrationService for DeviceIntegrationServer {
    /// Register a new device
    #[instrument(skip(self))]
    async fn register_device(
        &self,
        request: Request<RegisterDeviceRequest>,
    ) -> Result<Response<RegisterDeviceResponse>, Status> {
        let req = request.into_inner();
        
        info!("Registering device: {}", req.device_id);
        
        // Validate request
        if req.device_id.is_empty() {
            return Err(Status::new(Code::InvalidArgument, "Device ID cannot be empty"));
        }
        
        if req.endpoint.is_empty() {
            return Err(Status::new(Code::InvalidArgument, "Device endpoint cannot be empty"));
        }
        
        // Convert protocol
        let protocol = match device_protocol::Protocol::from_i32(req.protocol) {
            Some(p) => convert_protocol(p),
            None => return Err(Status::new(Code::InvalidArgument, "Invalid protocol")),
        };
        
        // Create device info
        let device_info = DeviceInfo {
            id: req.device_id.clone(),
            name: req.name,
            device_type: req.device_type,
            protocol,
            endpoint: req.endpoint,
            status: DeviceStatus::Disconnected,
            last_seen: None,
            data_rate: 0.0,
            error_count: 0,
            total_messages: 0,
            metadata: req.metadata,
        };
        
        // Register device
        match self.device_manager.register_device(device_info).await {
            Ok(_) => {
                self.metrics.increment_counter("grpc_register_device_success", &[]);
                
                let response = RegisterDeviceResponse {
                    success: true,
                    message: format!("Device {} registered successfully", req.device_id),
                    device_id: req.device_id,
                };
                
                Ok(Response::new(response))
            }
            Err(e) => {
                error!("Failed to register device {}: {}", req.device_id, e);
                self.metrics.increment_counter("grpc_register_device_error", &[]);
                
                Err(Status::new(
                    Code::Internal,
                    format!("Failed to register device: {}", e),
                ))
            }
        }
    }
    
    /// Unregister a device
    #[instrument(skip(self))]
    async fn unregister_device(
        &self,
        request: Request<UnregisterDeviceRequest>,
    ) -> Result<Response<UnregisterDeviceResponse>, Status> {
        let req = request.into_inner();
        
        info!("Unregistering device: {}", req.device_id);
        
        if req.device_id.is_empty() {
            return Err(Status::new(Code::InvalidArgument, "Device ID cannot be empty"));
        }
        
        match self.device_manager.unregister_device(&req.device_id).await {
            Ok(_) => {
                self.metrics.increment_counter("grpc_unregister_device_success", &[]);
                
                let response = UnregisterDeviceResponse {
                    success: true,
                    message: format!("Device {} unregistered successfully", req.device_id),
                };
                
                Ok(Response::new(response))
            }
            Err(e) => {
                error!("Failed to unregister device {}: {}", req.device_id, e);
                self.metrics.increment_counter("grpc_unregister_device_error", &[]);
                
                Err(Status::new(
                    Code::Internal,
                    format!("Failed to unregister device: {}", e),
                ))
            }
        }
    }
    
    /// Send device data
    #[instrument(skip(self))]
    async fn send_device_data(
        &self,
        request: Request<SendDeviceDataRequest>,
    ) -> Result<Response<SendDeviceDataResponse>, Status> {
        let req = request.into_inner();
        
        // Validate request
        if req.device_id.is_empty() {
            return Err(Status::new(Code::InvalidArgument, "Device ID cannot be empty"));
        }
        
        // Convert timestamp
        let timestamp = req.timestamp
            .map(|ts| chrono::DateTime::from_timestamp(ts.seconds, ts.nanos as u32))
            .flatten()
            .unwrap_or_else(chrono::Utc::now);
        
        // Parse data payload
        let data: serde_json::Value = match serde_json::from_str(&req.data_payload) {
            Ok(data) => data,
            Err(e) => {
                return Err(Status::new(
                    Code::InvalidArgument,
                    format!("Invalid JSON payload: {}", e),
                ));
            }
        };
        
        // Create device data
        let device_data = DeviceData {
            device_id: req.device_id.clone(),
            device_type: req.device_type,
            protocol: req.protocol,
            timestamp,
            data,
            metadata: req.metadata,
            quality_score: req.quality_score.clamp(0.0, 1.0),
        };
        
        // Process device data
        match self.device_manager.process_device_data(device_data).await {
            Ok(_) => {
                self.metrics.increment_counter("grpc_send_data_success", &[("device_id", &req.device_id)]);
                
                let response = SendDeviceDataResponse {
                    success: true,
                    message: "Data processed successfully".to_string(),
                    data_id: uuid::Uuid::new_v4().to_string(),
                };
                
                Ok(Response::new(response))
            }
            Err(e) => {
                error!("Failed to process device data from {}: {}", req.device_id, e);
                self.metrics.increment_counter("grpc_send_data_error", &[("device_id", &req.device_id)]);
                
                Err(Status::new(
                    Code::Internal,
                    format!("Failed to process device data: {}", e),
                ))
            }
        }
    }
    
    /// Stream device data (bidirectional streaming)
    async fn stream_device_data(
        &self,
        request: Request<tonic::Streaming<StreamDeviceDataRequest>>,
    ) -> Result<Response<Self::StreamDeviceDataStream>, Status> {
        let mut stream = request.into_inner();
        let device_manager = self.device_manager.clone();
        let metrics = self.metrics.clone();
        
        let (tx, rx) = tokio::sync::mpsc::channel(100);
        
        // Spawn task to handle incoming stream
        tokio::spawn(async move {
            while let Some(result) = stream.message().await.transpose() {
                match result {
                    Ok(req) => {
                        // Process streaming data
                        let timestamp = req.timestamp
                            .map(|ts| chrono::DateTime::from_timestamp(ts.seconds, ts.nanos as u32))
                            .flatten()
                            .unwrap_or_else(chrono::Utc::now);
                        
                        if let Ok(data) = serde_json::from_str(&req.data_payload) {
                            let device_data = DeviceData {
                                device_id: req.device_id.clone(),
                                device_type: req.device_type,
                                protocol: req.protocol,
                                timestamp,
                                data,
                                metadata: req.metadata,
                                quality_score: req.quality_score.clamp(0.0, 1.0),
                            };
                            
                            match device_manager.process_device_data(device_data).await {
                                Ok(_) => {
                                    metrics.increment_counter("grpc_stream_data_success", &[("device_id", &req.device_id)]);
                                    
                                    let response = StreamDeviceDataResponse {
                                        success: true,
                                        message: "Data processed successfully".to_string(),
                                        data_id: uuid::Uuid::new_v4().to_string(),
                                        timestamp: Some(prost_types::Timestamp {
                                            seconds: chrono::Utc::now().timestamp(),
                                            nanos: 0,
                                        }),
                                    };
                                    
                                    if tx.send(Ok(response)).await.is_err() {
                                        break;
                                    }
                                }
                                Err(e) => {
                                    error!("Failed to process streaming data from {}: {}", req.device_id, e);
                                    metrics.increment_counter("grpc_stream_data_error", &[("device_id", &req.device_id)]);
                                    
                                    let response = StreamDeviceDataResponse {
                                        success: false,
                                        message: format!("Failed to process data: {}", e),
                                        data_id: String::new(),
                                        timestamp: Some(prost_types::Timestamp {
                                            seconds: chrono::Utc::now().timestamp(),
                                            nanos: 0,
                                        }),
                                    };
                                    
                                    if tx.send(Ok(response)).await.is_err() {
                                        break;
                                    }
                                }
                            }
                        } else {
                            warn!("Invalid JSON payload in streaming data from {}", req.device_id);
                            
                            let response = StreamDeviceDataResponse {
                                success: false,
                                message: "Invalid JSON payload".to_string(),
                                data_id: String::new(),
                                timestamp: Some(prost_types::Timestamp {
                                    seconds: chrono::Utc::now().timestamp(),
                                    nanos: 0,
                                }),
                            };
                            
                            if tx.send(Ok(response)).await.is_err() {
                                break;
                            }
                        }
                    }
                    Err(e) => {
                        error!("Stream error: {}", e);
                        break;
                    }
                }
            }
        });
        
        let output_stream = tokio_stream::wrappers::ReceiverStream::new(rx);
        Ok(Response::new(Box::pin(output_stream) as Self::StreamDeviceDataStream))
    }
    
    /// Get device information
    #[instrument(skip(self))]
    async fn get_device_info(
        &self,
        request: Request<GetDeviceInfoRequest>,
    ) -> Result<Response<GetDeviceInfoResponse>, Status> {
        let req = request.into_inner();
        
        if req.device_id.is_empty() {
            return Err(Status::new(Code::InvalidArgument, "Device ID cannot be empty"));
        }
        
        match self.device_manager.get_device(&req.device_id) {
            Some(device_info) => {
                let response = GetDeviceInfoResponse {
                    success: true,
                    device_info: Some(convert_device_info_to_proto(device_info)),
                    message: String::new(),
                };
                
                Ok(Response::new(response))
            }
            None => {
                Err(Status::new(
                    Code::NotFound,
                    format!("Device {} not found", req.device_id),
                ))
            }
        }
    }
    
    /// List all devices
    #[instrument(skip(self))]
    async fn list_devices(
        &self,
        _request: Request<ListDevicesRequest>,
    ) -> Result<Response<ListDevicesResponse>, Status> {
        let devices = self.device_manager.list_devices();
        
        let device_infos: Vec<DeviceInfo> = devices
            .into_iter()
            .map(convert_device_info_to_proto)
            .collect();
        
        let response = ListDevicesResponse {
            devices: device_infos,
            total_count: device_infos.len() as u32,
        };
        
        Ok(Response::new(response))
    }
    
    /// Get device statistics
    #[instrument(skip(self))]
    async fn get_device_statistics(
        &self,
        request: Request<GetDeviceStatisticsRequest>,
    ) -> Result<Response<GetDeviceStatisticsResponse>, Status> {
        let req = request.into_inner();
        
        if req.device_id.is_empty() {
            return Err(Status::new(Code::InvalidArgument, "Device ID cannot be empty"));
        }
        
        match self.device_manager.get_device_statistics(&req.device_id) {
            Some(stats) => {
                let response = GetDeviceStatisticsResponse {
                    success: true,
                    statistics: Some(DeviceStatistics {
                        device_id: stats.device_id,
                        total_messages: stats.total_messages,
                        error_count: stats.error_count,
                        data_rate: stats.data_rate,
                        uptime_percentage: stats.uptime_percentage,
                        last_seen: stats.last_seen.map(|dt| prost_types::Timestamp {
                            seconds: dt.timestamp(),
                            nanos: 0,
                        }),
                        status: convert_status_to_proto(stats.status),
                    }),
                    message: String::new(),
                };
                
                Ok(Response::new(response))
            }
            None => {
                Err(Status::new(
                    Code::NotFound,
                    format!("Device {} not found", req.device_id),
                ))
            }
        }
    }
    
    /// Health check
    async fn health_check(
        &self,
        _request: Request<HealthCheckRequest>,
    ) -> Result<Response<HealthCheckResponse>, Status> {
        let response = HealthCheckResponse {
            status: "healthy".to_string(),
            timestamp: Some(prost_types::Timestamp {
                seconds: chrono::Utc::now().timestamp(),
                nanos: 0,
            }),
            component: "DeviceIntegrationServer".to_string(),
            details: std::collections::HashMap::new(),
        };
        
        Ok(Response::new(response))
    }
    
    type StreamDeviceDataStream = std::pin::Pin<
        Box<dyn futures::Stream<Item = Result<StreamDeviceDataResponse, Status>> + Send>
    >;
}

// Helper functions for protocol buffer conversions

fn convert_protocol(proto: device_protocol::Protocol) -> crate::protocols::DeviceProtocol {
    match proto {
        device_protocol::Protocol::Modbus => crate::protocols::DeviceProtocol::Modbus,
        device_protocol::Protocol::OpcUa => crate::protocols::DeviceProtocol::OpcUa,
        device_protocol::Protocol::Mqtt => crate::protocols::DeviceProtocol::Mqtt,
        device_protocol::Protocol::Serial => crate::protocols::DeviceProtocol::Serial,
        device_protocol::Protocol::Http => crate::protocols::DeviceProtocol::Http,
        device_protocol::Protocol::Tcp => crate::protocols::DeviceProtocol::Tcp,
    }
}

fn convert_device_info_to_proto(device_info: crate::device::DeviceInfo) -> DeviceInfo {
    DeviceInfo {
        id: device_info.id,
        name: device_info.name,
        device_type: device_info.device_type,
        protocol: convert_protocol_to_proto(device_info.protocol) as i32,
        endpoint: device_info.endpoint,
        status: convert_status_to_proto(device_info.status) as i32,
        last_seen: device_info.last_seen.map(|dt| prost_types::Timestamp {
            seconds: dt.timestamp(),
            nanos: 0,
        }),
        data_rate: device_info.data_rate,
        error_count: device_info.error_count,
        total_messages: device_info.total_messages,
        metadata: device_info.metadata,
    }
}

fn convert_protocol_to_proto(protocol: crate::protocols::DeviceProtocol) -> device_protocol::Protocol {
    match protocol {
        crate::protocols::DeviceProtocol::Modbus => device_protocol::Protocol::Modbus,
        crate::protocols::DeviceProtocol::OpcUa => device_protocol::Protocol::OpcUa,
        crate::protocols::DeviceProtocol::Mqtt => device_protocol::Protocol::Mqtt,
        crate::protocols::DeviceProtocol::Serial => device_protocol::Protocol::Serial,
        crate::protocols::DeviceProtocol::Http => device_protocol::Protocol::Http,
        crate::protocols::DeviceProtocol::Tcp => device_protocol::Protocol::Tcp,
    }
}

fn convert_status_to_proto(status: crate::device::DeviceStatus) -> device_status::Status {
    match status {
        crate::device::DeviceStatus::Connected => device_status::Status::Connected,
        crate::device::DeviceStatus::Disconnected => device_status::Status::Disconnected,
        crate::device::DeviceStatus::Error => device_status::Status::Error,
        crate::device::DeviceStatus::Maintenance => device_status::Status::Maintenance,
    }
}
