[package]
name = "asi-device-integration"
version = "1.0.0"
edition = "2021"
authors = ["ASI System Team"]
description = "Real-time device integration service for ASI data integration"
license = "MIT"
repository = "https://github.com/asi-system/data-integration"

[dependencies]
# Async runtime
tokio = { version = "1.0", features = ["full"] }
tokio-stream = "0.1"
tokio-util = { version = "0.7", features = ["codec"] }

# gRPC and protobuf
tonic = "0.10"
prost = "0.12"
prost-types = "0.12"

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Logging and tracing
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }
tracing-opentelemetry = "0.21"
opentelemetry = "0.21"
opentelemetry-jaeger = "0.20"

# Metrics
prometheus = "0.13"
metrics = "0.22"
metrics-prometheus = "0.6"

# Configuration
config = "0.13"
clap = { version = "4.0", features = ["derive"] }

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Utilities
uuid = { version = "1.0", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }
bytes = "1.0"
futures = "0.3"

# Networking
hyper = { version = "0.14", features = ["full"] }
tower = "0.4"
tower-http = { version = "0.4", features = ["cors", "trace"] }

# Device communication protocols
rumqttc = "0.23"  # MQTT client
modbus = "1.0"    # Modbus protocol
serialport = "4.0" # Serial communication

# Concurrency
dashmap = "5.0"
arc-swap = "1.0"
parking_lot = "0.12"

# Kafka integration
rdkafka = { version = "0.36", features = ["cmake-build", "ssl", "sasl"] }

# Database
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "postgres", "chrono", "uuid"] }

# Security
ring = "0.17"
rustls = "0.21"
webpki-roots = "0.25"

# Health checks and monitoring
axum = "0.7"
axum-prometheus = "0.4"

[build-dependencies]
tonic-build = "0.10"

[dev-dependencies]
tokio-test = "0.4"
mockall = "0.11"
testcontainers = "0.15"

[[bin]]
name = "device-integration"
path = "src/main.rs"

[profile.release]
lto = true
codegen-units = 1
panic = "abort"
strip = true

[profile.dev]
debug = true
opt-level = 0

[profile.test]
debug = true
