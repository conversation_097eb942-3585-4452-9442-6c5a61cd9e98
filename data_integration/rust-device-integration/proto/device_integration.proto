syntax = "proto3";

package asi.device_integration;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/asi/device_integration/proto";
option java_package = "com.asi.device_integration.proto";
option java_outer_classname = "DeviceIntegrationProto";

// Device Integration Service for ASI System
service DeviceIntegrationService {
  // Register a new device
  rpc RegisterDevice(RegisterDeviceRequest) returns (RegisterDeviceResponse);
  
  // Unregister a device
  rpc UnregisterDevice(UnregisterDeviceRequest) returns (UnregisterDeviceResponse);
  
  // Send device data (single message)
  rpc SendDeviceData(SendDeviceDataRequest) returns (SendDeviceDataResponse);
  
  // Stream device data (bidirectional streaming)
  rpc StreamDeviceData(stream StreamDeviceDataRequest) returns (stream StreamDeviceDataResponse);
  
  // Get device information
  rpc GetDeviceInfo(GetDeviceInfoRequest) returns (GetDeviceInfoResponse);
  
  // List all devices
  rpc ListDevices(ListDevicesRequest) returns (ListDevicesResponse);
  
  // Get device statistics
  rpc GetDeviceStatistics(GetDeviceStatisticsRequest) returns (GetDeviceStatisticsResponse);
  
  // Health check
  rpc HealthCheck(HealthCheckRequest) returns (HealthCheckResponse);
}

// Device protocols supported
message DeviceProtocol {
  enum Protocol {
    MODBUS = 0;
    OPC_UA = 1;
    MQTT = 2;
    SERIAL = 3;
    HTTP = 4;
    TCP = 5;
  }
}

// Device status enumeration
message DeviceStatus {
  enum Status {
    CONNECTED = 0;
    DISCONNECTED = 1;
    ERROR = 2;
    MAINTENANCE = 3;
  }
}

// Register device request
message RegisterDeviceRequest {
  string device_id = 1;
  string name = 2;
  string device_type = 3;
  int32 protocol = 4; // DeviceProtocol.Protocol
  string endpoint = 5;
  map<string, string> metadata = 6;
}

// Register device response
message RegisterDeviceResponse {
  bool success = 1;
  string message = 2;
  string device_id = 3;
}

// Unregister device request
message UnregisterDeviceRequest {
  string device_id = 1;
}

// Unregister device response
message UnregisterDeviceResponse {
  bool success = 1;
  string message = 2;
}

// Send device data request
message SendDeviceDataRequest {
  string device_id = 1;
  string device_type = 2;
  string protocol = 3;
  google.protobuf.Timestamp timestamp = 4;
  string data_payload = 5; // JSON string
  map<string, string> metadata = 6;
  double quality_score = 7;
}

// Send device data response
message SendDeviceDataResponse {
  bool success = 1;
  string message = 2;
  string data_id = 3;
}

// Stream device data request
message StreamDeviceDataRequest {
  string device_id = 1;
  string device_type = 2;
  string protocol = 3;
  google.protobuf.Timestamp timestamp = 4;
  string data_payload = 5; // JSON string
  map<string, string> metadata = 6;
  double quality_score = 7;
}

// Stream device data response
message StreamDeviceDataResponse {
  bool success = 1;
  string message = 2;
  string data_id = 3;
  google.protobuf.Timestamp timestamp = 4;
}

// Get device info request
message GetDeviceInfoRequest {
  string device_id = 1;
}

// Get device info response
message GetDeviceInfoResponse {
  bool success = 1;
  DeviceInfo device_info = 2;
  string message = 3;
}

// Device information
message DeviceInfo {
  string id = 1;
  string name = 2;
  string device_type = 3;
  int32 protocol = 4; // DeviceProtocol.Protocol
  string endpoint = 5;
  int32 status = 6; // DeviceStatus.Status
  google.protobuf.Timestamp last_seen = 7;
  double data_rate = 8;
  uint64 error_count = 9;
  uint64 total_messages = 10;
  map<string, string> metadata = 11;
}

// List devices request
message ListDevicesRequest {
  // Optional filters can be added here
  string device_type_filter = 1;
  int32 protocol_filter = 2;
  int32 status_filter = 3;
}

// List devices response
message ListDevicesResponse {
  repeated DeviceInfo devices = 1;
  uint32 total_count = 2;
}

// Get device statistics request
message GetDeviceStatisticsRequest {
  string device_id = 1;
}

// Get device statistics response
message GetDeviceStatisticsResponse {
  bool success = 1;
  DeviceStatistics statistics = 2;
  string message = 3;
}

// Device statistics
message DeviceStatistics {
  string device_id = 1;
  uint64 total_messages = 2;
  uint64 error_count = 3;
  double data_rate = 4;
  double uptime_percentage = 5;
  google.protobuf.Timestamp last_seen = 6;
  int32 status = 7; // DeviceStatus.Status
}

// Health check request
message HealthCheckRequest {
  string service = 1;
}

// Health check response
message HealthCheckResponse {
  string status = 1;
  google.protobuf.Timestamp timestamp = 2;
  string component = 3;
  map<string, string> details = 4;
}

// Batch operations for efficiency
message BatchRegisterDevicesRequest {
  repeated RegisterDeviceRequest devices = 1;
}

message BatchRegisterDevicesResponse {
  repeated RegisterDeviceResponse responses = 1;
  uint32 successful_count = 2;
  uint32 failed_count = 3;
}

message BatchSendDeviceDataRequest {
  repeated SendDeviceDataRequest data_requests = 1;
}

message BatchSendDeviceDataResponse {
  repeated SendDeviceDataResponse responses = 1;
  uint32 successful_count = 2;
  uint32 failed_count = 3;
}

// Device configuration for dynamic updates
message DeviceConfiguration {
  string device_id = 1;
  map<string, string> configuration = 2;
  google.protobuf.Timestamp updated_at = 3;
}

message UpdateDeviceConfigurationRequest {
  DeviceConfiguration configuration = 1;
}

message UpdateDeviceConfigurationResponse {
  bool success = 1;
  string message = 2;
}

// Device alerts and notifications
message DeviceAlert {
  enum AlertLevel {
    INFO = 0;
    WARNING = 1;
    ERROR = 2;
    CRITICAL = 3;
  }
  
  string device_id = 1;
  AlertLevel level = 2;
  string message = 3;
  google.protobuf.Timestamp timestamp = 4;
  map<string, string> context = 5;
}

message SubscribeDeviceAlertsRequest {
  repeated string device_ids = 1; // Empty means all devices
  repeated DeviceAlert.AlertLevel levels = 2; // Empty means all levels
}

message SubscribeDeviceAlertsResponse {
  DeviceAlert alert = 1;
}

// Device metrics for monitoring
message DeviceMetrics {
  string device_id = 1;
  google.protobuf.Timestamp timestamp = 2;
  map<string, double> metrics = 3; // metric_name -> value
}

message GetDeviceMetricsRequest {
  string device_id = 1;
  google.protobuf.Timestamp start_time = 2;
  google.protobuf.Timestamp end_time = 3;
  repeated string metric_names = 4; // Empty means all metrics
}

message GetDeviceMetricsResponse {
  repeated DeviceMetrics metrics = 1;
}

// Device commands for remote control
message DeviceCommand {
  string device_id = 1;
  string command_type = 2;
  map<string, string> parameters = 3;
  google.protobuf.Timestamp timestamp = 4;
  string command_id = 5;
}

message SendDeviceCommandRequest {
  DeviceCommand command = 1;
}

message SendDeviceCommandResponse {
  bool success = 1;
  string message = 2;
  string command_id = 3;
  google.protobuf.Timestamp executed_at = 4;
}

// Extended service definition with additional operations
service ExtendedDeviceIntegrationService {
  // Batch operations
  rpc BatchRegisterDevices(BatchRegisterDevicesRequest) returns (BatchRegisterDevicesResponse);
  rpc BatchSendDeviceData(BatchSendDeviceDataRequest) returns (BatchSendDeviceDataResponse);
  
  // Configuration management
  rpc UpdateDeviceConfiguration(UpdateDeviceConfigurationRequest) returns (UpdateDeviceConfigurationResponse);
  
  // Alerts and notifications
  rpc SubscribeDeviceAlerts(SubscribeDeviceAlertsRequest) returns (stream SubscribeDeviceAlertsResponse);
  
  // Metrics and monitoring
  rpc GetDeviceMetrics(GetDeviceMetricsRequest) returns (GetDeviceMetricsResponse);
  
  // Remote device control
  rpc SendDeviceCommand(SendDeviceCommandRequest) returns (SendDeviceCommandResponse);
}
