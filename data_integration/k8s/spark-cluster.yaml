apiVersion: v1
kind: ConfigMap
metadata:
  name: spark-config
  namespace: asi-data-integration
data:
  spark-defaults.conf: |
    spark.master                     spark://spark-master:7077
    spark.sql.adaptive.enabled      true
    spark.sql.adaptive.coalescePartitions.enabled true
    spark.sql.adaptive.skewJoin.enabled true
    spark.serializer                 org.apache.spark.serializer.KryoSerializer
    spark.sql.streaming.checkpointLocation /app/checkpoints
    spark.sql.extensions            org.neo4j.spark.Neo4jSparkExtensions
    spark.executor.memory            4g
    spark.executor.cores             2
    spark.executor.instances         3
    spark.driver.memory              2g
    spark.driver.cores               1
    spark.sql.streaming.stateStore.providerClass org.apache.spark.sql.execution.streaming.state.HDFSBackedStateStoreProvider
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: spark-master
  namespace: asi-data-integration
  labels:
    app: spark-master
    component: spark
spec:
  replicas: 1
  selector:
    matchLabels:
      app: spark-master
  template:
    metadata:
      labels:
        app: spark-master
        component: spark
    spec:
      containers:
      - name: spark-master
        image: bitnami/spark:3.5.0
        ports:
        - containerPort: 8080
          name: web-ui
        - containerPort: 7077
          name: spark-port
        env:
        - name: SPARK_MODE
          value: "master"
        - name: SPARK_RPC_AUTHENTICATION_ENABLED
          value: "no"
        - name: SPARK_RPC_ENCRYPTION_ENABLED
          value: "no"
        - name: SPARK_LOCAL_STORAGE_ENCRYPTION_ENABLED
          value: "no"
        - name: SPARK_SSL_ENABLED
          value: "no"
        - name: SPARK_MASTER_OPTS
          value: "-Dspark.deploy.defaultCores=2"
        volumeMounts:
        - name: spark-config
          mountPath: /opt/bitnami/spark/conf/spark-defaults.conf
          subPath: spark-defaults.conf
        - name: spark-data
          mountPath: /opt/bitnami/spark/data
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
      volumes:
      - name: spark-config
        configMap:
          name: spark-config
      - name: spark-data
        persistentVolumeClaim:
          claimName: spark-master-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: spark-master
  namespace: asi-data-integration
  labels:
    app: spark-master
spec:
  selector:
    app: spark-master
  ports:
  - name: web-ui
    port: 8080
    targetPort: 8080
  - name: spark-port
    port: 7077
    targetPort: 7077
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: spark-worker
  namespace: asi-data-integration
  labels:
    app: spark-worker
    component: spark
spec:
  replicas: 3
  selector:
    matchLabels:
      app: spark-worker
  template:
    metadata:
      labels:
        app: spark-worker
        component: spark
    spec:
      containers:
      - name: spark-worker
        image: bitnami/spark:3.5.0
        ports:
        - containerPort: 8081
          name: web-ui
        env:
        - name: SPARK_MODE
          value: "worker"
        - name: SPARK_MASTER_URL
          value: "spark://spark-master:7077"
        - name: SPARK_WORKER_MEMORY
          value: "4g"
        - name: SPARK_WORKER_CORES
          value: "2"
        - name: SPARK_RPC_AUTHENTICATION_ENABLED
          value: "no"
        - name: SPARK_RPC_ENCRYPTION_ENABLED
          value: "no"
        - name: SPARK_LOCAL_STORAGE_ENCRYPTION_ENABLED
          value: "no"
        - name: SPARK_SSL_ENABLED
          value: "no"
        volumeMounts:
        - name: spark-config
          mountPath: /opt/bitnami/spark/conf/spark-defaults.conf
          subPath: spark-defaults.conf
        - name: spark-data
          mountPath: /opt/bitnami/spark/data
        resources:
          requests:
            memory: "4Gi"
            cpu: "2000m"
          limits:
            memory: "6Gi"
            cpu: "3000m"
        livenessProbe:
          httpGet:
            path: /
            port: 8081
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /
            port: 8081
          initialDelaySeconds: 30
          periodSeconds: 10
      volumes:
      - name: spark-config
        configMap:
          name: spark-config
      - name: spark-data
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: spark-worker
  namespace: asi-data-integration
  labels:
    app: spark-worker
spec:
  selector:
    app: spark-worker
  ports:
  - name: web-ui
    port: 8081
    targetPort: 8081
  type: ClusterIP
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: spark-master-pvc
  namespace: asi-data-integration
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: fast-ssd
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: spark-pipeline
  namespace: asi-data-integration
  labels:
    app: spark-pipeline
    component: data-processing
spec:
  replicas: 1
  selector:
    matchLabels:
      app: spark-pipeline
  template:
    metadata:
      labels:
        app: spark-pipeline
        component: data-processing
    spec:
      containers:
      - name: spark-pipeline
        image: asi/spark-pipeline:1.0.0
        ports:
        - containerPort: 4040
          name: spark-ui
        - containerPort: 8085
          name: metrics
        env:
        - name: SPARK_MASTER_URL
          value: "spark://spark-master:7077"
        - name: KAFKA_BOOTSTRAP_SERVERS
          value: "kafka:9092"
        - name: NEO4J_URI
          value: "bolt://neo4j:7687"
        - name: NEO4J_USERNAME
          valueFrom:
            secretKeyRef:
              name: neo4j-credentials
              key: username
        - name: NEO4J_PASSWORD
          valueFrom:
            secretKeyRef:
              name: neo4j-credentials
              key: password
        - name: SPARK_EXECUTOR_MEMORY
          value: "4g"
        - name: SPARK_EXECUTOR_CORES
          value: "2"
        volumeMounts:
        - name: spark-checkpoints
          mountPath: /app/checkpoints
        - name: spark-config
          mountPath: /app/conf
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8085
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health
            port: 8085
          initialDelaySeconds: 30
          periodSeconds: 10
      volumes:
      - name: spark-checkpoints
        persistentVolumeClaim:
          claimName: spark-checkpoints-pvc
      - name: spark-config
        configMap:
          name: spark-config
---
apiVersion: v1
kind: Service
metadata:
  name: spark-pipeline
  namespace: asi-data-integration
  labels:
    app: spark-pipeline
spec:
  selector:
    app: spark-pipeline
  ports:
  - name: spark-ui
    port: 4040
    targetPort: 4040
  - name: metrics
    port: 8085
    targetPort: 8085
  type: ClusterIP
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: spark-checkpoints-pvc
  namespace: asi-data-integration
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
  storageClassName: fast-ssd
