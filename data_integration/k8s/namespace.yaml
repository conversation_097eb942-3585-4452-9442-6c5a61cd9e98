apiVersion: v1
kind: Namespace
metadata:
  name: asi-data-integration
  labels:
    name: asi-data-integration
    component: data-integration
    system: asi
    environment: production
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: asi-data-integration-quota
  namespace: asi-data-integration
spec:
  hard:
    requests.cpu: "20"
    requests.memory: 40Gi
    limits.cpu: "40"
    limits.memory: 80Gi
    persistentvolumeclaims: "20"
    services: "20"
    secrets: "20"
    configmaps: "20"
    pods: "50"
---
apiVersion: v1
kind: LimitRange
metadata:
  name: asi-data-integration-limits
  namespace: asi-data-integration
spec:
  limits:
  - default:
      cpu: "2000m"
      memory: "4Gi"
    defaultRequest:
      cpu: "500m"
      memory: "1Gi"
    type: Container
  - max:
      cpu: "8000m"
      memory: "16Gi"
    min:
      cpu: "100m"
      memory: "128Mi"
    type: Container
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: asi-data-integration-network-policy
  namespace: asi-data-integration
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: asi-ingestion
    - namespaceSelector:
        matchLabels:
          name: asi-learning-engine
    - namespaceSelector:
        matchLabels:
          name: asi-decision-engine
    - namespaceSelector:
        matchLabels:
          name: monitoring
  egress:
  - to: []
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53
  - to:
    - namespaceSelector:
        matchLabels:
          name: asi-ingestion
  - to:
    - namespaceSelector:
        matchLabels:
          name: asi-learning-engine
  - to:
    - namespaceSelector:
        matchLabels:
          name: asi-decision-engine
