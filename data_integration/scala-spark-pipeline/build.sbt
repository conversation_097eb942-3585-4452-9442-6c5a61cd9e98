ThisBuild / version := "1.0.0"
ThisBuild / scalaVersion := "2.12.17"

lazy val sparkVersion = "3.5.0"
lazy val kafkaVersion = "3.6.0"
lazy val neo4jVersion = "5.13.0"

lazy val root = (project in file("."))
  .settings(
    name := "asi-spark-pipeline",
    organization := "com.asi.data_integration",
    
    libraryDependencies ++= Seq(
      // Spark Core
      "org.apache.spark" %% "spark-core" % sparkVersion % "provided",
      "org.apache.spark" %% "spark-sql" % sparkVersion % "provided",
      "org.apache.spark" %% "spark-streaming" % sparkVersion % "provided",
      "org.apache.spark" %% "spark-sql-kafka-0-10" % sparkVersion,
      "org.apache.spark" %% "spark-avro" % sparkVersion,
      
      // Kafka
      "org.apache.kafka" % "kafka-clients" % kafkaVersion,
      "org.apache.kafka" %% "kafka" % kafkaVersion,
      "io.confluent" % "kafka-avro-serializer" % "7.5.0",
      "io.confluent" % "kafka-schema-registry-client" % "7.5.0",
      
      // Neo4j
      "org.neo4j.driver" % "neo4j-java-driver" % neo4jVersion,
      "org.neo4j" % "neo4j-connector-apache-spark_2.12" % "5.2.0_for_spark_3",
      
      // Data Processing
      "com.typesafe" % "config" % "1.4.3",
      "com.fasterxml.jackson.core" % "jackson-databind" % "2.15.3",
      "com.fasterxml.jackson.module" %% "jackson-module-scala" % "2.15.3",
      
      // Schema Registry and Avro
      "org.apache.avro" % "avro" % "1.11.3",
      "com.sksamuel.avro4s" %% "avro4s-core" % "4.1.1",
      
      // Logging
      "ch.qos.logback" % "logback-classic" % "1.4.11",
      "com.typesafe.scala-logging" %% "scala-logging" % "3.9.5",
      
      // Metrics and Monitoring
      "io.dropwizard.metrics" % "metrics-core" % "4.2.21",
      "io.prometheus" % "simpleclient" % "0.16.0",
      "io.prometheus" % "simpleclient_hotspot" % "0.16.0",
      "io.prometheus" % "simpleclient_httpserver" % "0.16.0",
      
      // Testing
      "org.scalatest" %% "scalatest" % "3.2.17" % Test,
      "org.scalatestplus" %% "mockito-4-11" % "3.2.17.0" % Test,
      "com.holdenkarau" %% "spark-testing-base" % "3.5.0_1.4.7" % Test,
      
      // Utilities
      "org.typelevel" %% "cats-core" % "2.10.0",
      "com.github.pureconfig" %% "pureconfig" % "0.17.4",
      "com.github.scopt" %% "scopt" % "4.1.0"
    ),
    
    // Resolver for Confluent packages
    resolvers ++= Seq(
      "Confluent" at "https://packages.confluent.io/maven/",
      "Maven Central" at "https://repo1.maven.org/maven2/"
    ),
    
    // Assembly settings for fat JAR
    assembly / assemblyMergeStrategy := {
      case PathList("META-INF", xs @ _*) => MergeStrategy.discard
      case PathList("reference.conf") => MergeStrategy.concat
      case PathList("application.conf") => MergeStrategy.concat
      case _ => MergeStrategy.first
    },
    
    // Compiler options
    scalacOptions ++= Seq(
      "-deprecation",
      "-feature",
      "-unchecked",
      "-Xlint",
      "-Ywarn-dead-code",
      "-Ywarn-numeric-widen",
      "-Ywarn-value-discard"
    ),
    
    // Test settings
    Test / parallelExecution := false,
    Test / fork := true,
    Test / javaOptions ++= Seq("-Xmx2g"),
    
    // Runtime settings
    run / javaOptions ++= Seq(
      "-Xmx4g",
      "-XX:+UseG1GC",
      "-XX:+UseStringDeduplication"
    )
  )

// Assembly plugin
addSbtPlugin("com.eed3si9n" % "sbt-assembly" % "2.1.3")

// Docker plugin
enablePlugins(DockerPlugin)
docker / dockerfile := {
  val artifact: File = assembly.value
  val artifactTargetPath = s"/app/${artifact.name}"

  new Dockerfile {
    from("openjdk:11-jre-slim")
    
    // Install dependencies
    runRaw("apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*")
    
    // Create app directory
    runRaw("mkdir -p /app")
    
    // Copy JAR
    add(artifact, artifactTargetPath)
    
    // Copy configuration
    copy(baseDirectory.value / "src" / "main" / "resources", "/app/conf/")
    
    // Set working directory
    workDir("/app")
    
    // Expose ports
    expose(4040, 8085) // Spark UI and metrics
    
    // Health check
    healthCheck("CMD curl -f http://localhost:8085/health || exit 1")
    
    // Entry point
    entryPoint("java", "-jar", artifactTargetPath)
  }
}
