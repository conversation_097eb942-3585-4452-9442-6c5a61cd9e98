package com.asi.data_integration.monitoring

import com.asi.data_integration.config.MonitoringConfig
import com.typesafe.scalalogging.LazyLogging
import io.prometheus.client._
import io.prometheus.client.exporter.HTTPServer
import io.prometheus.client.hotspot.DefaultExports
import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.streaming.StreamingQuery

import scala.collection.mutable
import scala.util.{Failure, Success, Try}

/**
 * Comprehensive metrics collection for the ASI Data Integration pipeline.
 * 
 * Provides:
 * - Prometheus metrics export
 * - Spark streaming metrics
 * - Custom business metrics
 * - Health checks and alerting
 * - Performance monitoring
 */
class MetricsCollector(config: MonitoringConfig)(implicit spark: SparkSession) extends LazyLogging {
  
  // Prometheus metrics
  private val recordsProcessed = Counter.build()
    .name("asi_data_integration_records_processed_total")
    .help("Total number of records processed")
    .labelNames("source", "stage", "status")
    .register()
  
  private val processingDuration = Histogram.build()
    .name("asi_data_integration_processing_duration_seconds")
    .help("Time spent processing data")
    .labelNames("source", "stage")
    .buckets(0.1, 0.5, 1.0, 5.0, 10.0, 30.0, 60.0, 300.0)
    .register()
  
  private val dataQualityScore = Histogram.build()
    .name("asi_data_integration_quality_score")
    .help("Data quality score distribution")
    .labelNames("source", "data_type")
    .buckets(0.0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0)
    .register()
  
  private val errorCount = Counter.build()
    .name("asi_data_integration_errors_total")
    .help("Total number of errors")
    .labelNames("source", "stage", "error_type")
    .register()
  
  private val streamingLag = Gauge.build()
    .name("asi_data_integration_streaming_lag_seconds")
    .help("Streaming processing lag in seconds")
    .labelNames("source", "query_name")
    .register()
  
  private val memoryUsage = Gauge.build()
    .name("asi_data_integration_memory_usage_bytes")
    .help("Memory usage in bytes")
    .labelNames("component")
    .register()
  
  private val activeStreams = Gauge.build()
    .name("asi_data_integration_active_streams")
    .help("Number of active streaming queries")
    .register()
  
  private val throughput = Gauge.build()
    .name("asi_data_integration_throughput_records_per_second")
    .help("Processing throughput in records per second")
    .labelNames("source", "stage")
    .register()
  
  // HTTP server for metrics export
  private var metricsServer: Option[HTTPServer] = None
  
  // Active streaming queries tracking
  private val activeQueries = mutable.Map[String, StreamingQuery]()
  
  /**
   * Initialize metrics collection
   */
  def initialize(): Unit = {
    if (config.enabled) {
      try {
        // Register default JVM metrics
        DefaultExports.initialize()
        
        // Start metrics HTTP server
        if (config.prometheus.enabled) {
          metricsServer = Some(new HTTPServer(config.prometheus.port))
          logger.info(s"Metrics server started on port ${config.prometheus.port}")
        }
        
        // Start background metrics collection
        startBackgroundCollection()
        
        logger.info("Metrics collection initialized successfully")
      } catch {
        case ex: Exception =>
          logger.error("Failed to initialize metrics collection", ex)
      }
    }
  }
  
  /**
   * Record processed records
   */
  def recordProcessed(source: String, stage: String, count: Long, status: String = "success"): Unit = {
    recordsProcessed.labels(source, stage, status).inc(count.toDouble)
  }
  
  /**
   * Record processing duration
   */
  def recordDuration(source: String, stage: String, durationMs: Long): Unit = {
    processingDuration.labels(source, stage).observe(durationMs / 1000.0)
  }
  
  /**
   * Record data quality score
   */
  def recordQualityScore(source: String, dataType: String, score: Double): Unit = {
    dataQualityScore.labels(source, dataType).observe(score)
  }
  
  /**
   * Record error
   */
  def recordError(source: String, stage: String, errorType: String): Unit = {
    errorCount.labels(source, stage, errorType).inc()
  }
  
  /**
   * Update streaming lag
   */
  def updateStreamingLag(source: String, queryName: String, lagSeconds: Double): Unit = {
    streamingLag.labels(source, queryName).set(lagSeconds)
  }
  
  /**
   * Update memory usage
   */
  def updateMemoryUsage(component: String, bytes: Long): Unit = {
    memoryUsage.labels(component).set(bytes.toDouble)
  }
  
  /**
   * Update throughput
   */
  def updateThroughput(source: String, stage: String, recordsPerSecond: Double): Unit = {
    throughput.labels(source, stage).set(recordsPerSecond)
  }
  
  /**
   * Register streaming query for monitoring
   */
  def registerStreamingQuery(name: String, query: StreamingQuery): Unit = {
    activeQueries.put(name, query)
    activeStreams.set(activeQueries.size.toDouble)
    logger.info(s"Registered streaming query: $name")
  }
  
  /**
   * Unregister streaming query
   */
  def unregisterStreamingQuery(name: String): Unit = {
    activeQueries.remove(name)
    activeStreams.set(activeQueries.size.toDouble)
    logger.info(s"Unregistered streaming query: $name")
  }
  
  /**
   * Get current metrics summary
   */
  def getMetricsSummary(): Map[String, Any] = {
    Map(
      "records_processed" -> recordsProcessed.get(),
      "active_streams" -> activeQueries.size,
      "memory_usage_mb" -> (Runtime.getRuntime.totalMemory() - Runtime.getRuntime.freeMemory()) / (1024 * 1024),
      "uptime_seconds" -> (System.currentTimeMillis() - spark.sparkContext.startTime) / 1000,
      "spark_version" -> spark.version,
      "streaming_queries" -> activeQueries.keys.toList
    )
  }
  
  /**
   * Check system health
   */
  def checkHealth(): Map[String, Any] = {
    val healthChecks = mutable.Map[String, Any]()
    
    // Check Spark context
    healthChecks("spark_context") = Try {
      spark.sparkContext.statusTracker.getExecutorInfos.length > 0
    } match {
      case Success(value) => Map("status" -> "healthy", "executors" -> value)
      case Failure(ex) => Map("status" -> "unhealthy", "error" -> ex.getMessage)
    }
    
    // Check streaming queries
    healthChecks("streaming_queries") = activeQueries.map { case (name, query) =>
      name -> Map(
        "status" -> (if (query.isActive) "active" else "inactive"),
        "last_progress" -> query.lastProgress.timestamp
      )
    }.toMap
    
    // Check memory usage
    val runtime = Runtime.getRuntime
    val maxMemory = runtime.maxMemory()
    val totalMemory = runtime.totalMemory()
    val freeMemory = runtime.freeMemory()
    val usedMemory = totalMemory - freeMemory
    val memoryUsagePercent = (usedMemory.toDouble / maxMemory) * 100
    
    healthChecks("memory") = Map(
      "status" -> (if (memoryUsagePercent < 90) "healthy" else "warning"),
      "usage_percent" -> memoryUsagePercent,
      "used_mb" -> usedMemory / (1024 * 1024),
      "max_mb" -> maxMemory / (1024 * 1024)
    )
    
    // Overall health status
    val overallStatus = if (healthChecks.values.forall {
      case map: Map[String, Any] => map.get("status").contains("healthy")
      case _ => true
    }) "healthy" else "unhealthy"
    
    healthChecks("overall_status") = overallStatus
    healthChecks("timestamp") = System.currentTimeMillis()
    
    healthChecks.toMap
  }
  
  /**
   * Start background metrics collection
   */
  private def startBackgroundCollection(): Unit = {
    val metricsThread = new Thread(() => {
      while (!Thread.currentThread().isInterrupted) {
        try {
          collectSystemMetrics()
          collectSparkMetrics()
          collectStreamingMetrics()
          
          Thread.sleep(30000) // Collect every 30 seconds
        } catch {
          case _: InterruptedException =>
            Thread.currentThread().interrupt()
          case ex: Exception =>
            logger.warn("Error in background metrics collection", ex)
        }
      }
    })
    
    metricsThread.setDaemon(true)
    metricsThread.setName("asi-metrics-collector")
    metricsThread.start()
    
    logger.info("Background metrics collection started")
  }
  
  /**
   * Collect system metrics
   */
  private def collectSystemMetrics(): Unit = {
    val runtime = Runtime.getRuntime
    updateMemoryUsage("jvm", runtime.totalMemory() - runtime.freeMemory())
  }
  
  /**
   * Collect Spark metrics
   */
  private def collectSparkMetrics(): Unit = {
    try {
      val statusTracker = spark.sparkContext.statusTracker
      val executorInfos = statusTracker.getExecutorInfos
      
      // Update executor metrics
      updateMemoryUsage("spark_executors", executorInfos.map(_.maxMemory).sum)
      
    } catch {
      case ex: Exception =>
        logger.debug("Failed to collect Spark metrics", ex)
    }
  }
  
  /**
   * Collect streaming metrics
   */
  private def collectStreamingMetrics(): Unit = {
    activeQueries.foreach { case (name, query) =>
      try {
        if (query.isActive) {
          val progress = query.lastProgress
          if (progress != null) {
            // Calculate lag
            val currentTime = System.currentTimeMillis()
            val progressTime = progress.timestamp.toLong
            val lagSeconds = (currentTime - progressTime) / 1000.0
            
            updateStreamingLag("streaming", name, lagSeconds)
            
            // Update throughput
            val inputRowsPerSecond = progress.inputRowsPerSecond
            if (inputRowsPerSecond > 0) {
              updateThroughput("streaming", name, inputRowsPerSecond)
            }
          }
        }
      } catch {
        case ex: Exception =>
          logger.debug(s"Failed to collect metrics for query $name", ex)
      }
    }
  }
  
  /**
   * Shutdown metrics collection
   */
  def shutdown(): Unit = {
    try {
      metricsServer.foreach(_.stop())
      logger.info("Metrics collection shutdown completed")
    } catch {
      case ex: Exception =>
        logger.error("Error during metrics collection shutdown", ex)
    }
  }
}
