package com.asi.data_integration.processors

import com.asi.data_integration.config.NormalizationConfig
import com.asi.data_integration.utils.StructuredLogger
import org.apache.spark.sql.{DataFrame, SparkSession}
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types._
import com.typesafe.scalalogging.LazyLogging
import io.circe.syntax._
import io.circe.generic.auto._

/**
 * Data normalization processor for the ASI Global Data Integration pipeline.
 * 
 * Handles:
 * - Schema standardization across different data sources
 * - Data type conversions and casting
 * - Field mapping and transformation
 * - Null value handling and imputation
 * - Data format standardization (dates, numbers, strings)
 * - Duplicate detection and removal
 */
class DataNormalizer(config: NormalizationConfig)(implicit spark: SparkSession) extends LazyLogging {
  
  import spark.implicits._
  
  private val structuredLogger = new StructuredLogger("DataNormalizer")
  
  /**
   * Normalize a DataFrame according to the configured rules
   */
  def normalize(df: DataFrame, sourceType: String): DataFrame = {
    val startTime = System.currentTimeMillis()
    
    try {
      structuredLogger.info(
        "Starting data normalization",
        Map(
          "sourceType" -> sourceType,
          "inputRows" -> df.count().toString,
          "inputSchema" -> df.schema.treeString
        )
      )
      
      val normalizedDf = df
        .transform(standardizeSchema)
        .transform(normalizeDataTypes)
        .transform(handleNullValues)
        .transform(standardizeFormats)
        .transform(removeDuplicates)
        .transform(addMetadata(sourceType))
      
      val endTime = System.currentTimeMillis()
      val processingTime = endTime - startTime
      
      structuredLogger.info(
        "Data normalization completed",
        Map(
          "sourceType" -> sourceType,
          "outputRows" -> normalizedDf.count().toString,
          "processingTimeMs" -> processingTime.toString,
          "outputSchema" -> normalizedDf.schema.treeString
        )
      )
      
      normalizedDf
      
    } catch {
      case ex: Exception =>
        structuredLogger.error(
          "Data normalization failed",
          Map(
            "sourceType" -> sourceType,
            "error" -> ex.getMessage,
            "stackTrace" -> ex.getStackTrace.mkString("\n")
          )
        )
        throw ex
    }
  }
  
  /**
   * Standardize schema to unified format
   */
  private def standardizeSchema(df: DataFrame): DataFrame = {
    val standardColumns = Seq(
      "id",
      "source_id",
      "source_type",
      "data_type",
      "timestamp",
      "ingested_at",
      "payload",
      "metadata",
      "quality_score",
      "tags"
    )
    
    // Add missing columns with default values
    val dfWithStandardColumns = standardColumns.foldLeft(df) { (accDf, colName) =>
      if (!accDf.columns.contains(colName)) {
        colName match {
          case "id" => accDf.withColumn("id", monotonically_increasing_id().cast(StringType))
          case "timestamp" => accDf.withColumn("timestamp", current_timestamp())
          case "ingested_at" => accDf.withColumn("ingested_at", current_timestamp())
          case "quality_score" => accDf.withColumn("quality_score", lit(1.0))
          case "metadata" => accDf.withColumn("metadata", map())
          case "tags" => accDf.withColumn("tags", array())
          case _ => accDf.withColumn(colName, lit(null).cast(StringType))
        }
      } else {
        accDf
      }
    }
    
    // Reorder columns to standard order
    dfWithStandardColumns.select(standardColumns.map(col): _*)
  }
  
  /**
   * Normalize data types according to schema
   */
  private def normalizeDataTypes(df: DataFrame): DataFrame = {
    df
      .withColumn("id", col("id").cast(StringType))
      .withColumn("source_id", col("source_id").cast(StringType))
      .withColumn("source_type", col("source_type").cast(StringType))
      .withColumn("data_type", col("data_type").cast(StringType))
      .withColumn("timestamp", col("timestamp").cast(TimestampType))
      .withColumn("ingested_at", col("ingested_at").cast(TimestampType))
      .withColumn("quality_score", col("quality_score").cast(DoubleType))
  }
  
  /**
   * Handle null values according to configuration
   */
  private def handleNullValues(df: DataFrame): DataFrame = {
    config.nullHandling match {
      case "drop" => df.na.drop()
      case "fill" => 
        df.na.fill(Map(
          "source_id" -> "unknown",
          "source_type" -> "unknown",
          "data_type" -> "unknown",
          "quality_score" -> 0.0
        ))
      case "impute" => imputeNullValues(df)
      case _ => df
    }
  }
  
  /**
   * Impute null values using statistical methods
   */
  private def imputeNullValues(df: DataFrame): DataFrame = {
    // For numeric columns, use mean imputation
    val numericColumns = df.schema.fields
      .filter(field => field.dataType.isInstanceOf[NumericType])
      .map(_.name)
    
    val imputedDf = numericColumns.foldLeft(df) { (accDf, colName) =>
      val meanValue = accDf.select(mean(col(colName))).collect()(0)(0)
      if (meanValue != null) {
        accDf.na.fill(Map(colName -> meanValue))
      } else {
        accDf
      }
    }
    
    // For string columns, use mode imputation
    val stringColumns = df.schema.fields
      .filter(_.dataType == StringType)
      .map(_.name)
    
    stringColumns.foldLeft(imputedDf) { (accDf, colName) =>
      val modeValue = accDf
        .groupBy(col(colName))
        .count()
        .orderBy(desc("count"))
        .first()
        .getString(0)
      
      if (modeValue != null) {
        accDf.na.fill(Map(colName -> modeValue))
      } else {
        accDf
      }
    }
  }
  
  /**
   * Standardize data formats (dates, numbers, strings)
   */
  private def standardizeFormats(df: DataFrame): DataFrame = {
    df
      .withColumn("timestamp", 
        when(col("timestamp").isNull, current_timestamp())
        .otherwise(col("timestamp"))
      )
      .withColumn("ingested_at",
        when(col("ingested_at").isNull, current_timestamp())
        .otherwise(col("ingested_at"))
      )
      .withColumn("source_type", upper(trim(col("source_type"))))
      .withColumn("data_type", upper(trim(col("data_type"))))
  }
  
  /**
   * Remove duplicate records based on key fields
   */
  private def removeDuplicates(df: DataFrame): DataFrame = {
    if (config.removeDuplicates) {
      val keyColumns = config.deduplicationKeys.getOrElse(Seq("source_id", "timestamp"))
      df.dropDuplicates(keyColumns)
    } else {
      df
    }
  }
  
  /**
   * Add metadata and processing information
   */
  private def addMetadata(sourceType: String)(df: DataFrame): DataFrame = {
    df
      .withColumn("processing_timestamp", current_timestamp())
      .withColumn("normalization_version", lit(config.version))
      .withColumn("source_system", lit(sourceType))
      .withColumn("data_lineage", 
        array(
          struct(
            lit("stage").as("stage"),
            lit("normalization").as("operation"),
            current_timestamp().as("timestamp"),
            lit(sourceType).as("source")
          )
        )
      )
  }
  
  /**
   * Validate normalized data quality
   */
  def validateNormalizedData(df: DataFrame): DataFrame = {
    df
      .withColumn("quality_checks",
        struct(
          col("id").isNotNull.as("has_id"),
          col("source_id").isNotNull.as("has_source_id"),
          col("timestamp").isNotNull.as("has_timestamp"),
          col("payload").isNotNull.as("has_payload")
        )
      )
      .withColumn("quality_score",
        when(
          col("quality_checks.has_id") && 
          col("quality_checks.has_source_id") && 
          col("quality_checks.has_timestamp") && 
          col("quality_checks.has_payload"), 1.0
        ).when(
          col("quality_checks.has_id") && 
          col("quality_checks.has_source_id") && 
          col("quality_checks.has_timestamp"), 0.8
        ).when(
          col("quality_checks.has_id") && 
          col("quality_checks.has_source_id"), 0.6
        ).otherwise(0.3)
      )
  }
  
  /**
   * Get normalization statistics
   */
  def getNormalizationStats(originalDf: DataFrame, normalizedDf: DataFrame): Map[String, Any] = {
    Map(
      "original_count" -> originalDf.count(),
      "normalized_count" -> normalizedDf.count(),
      "duplicate_removal_count" -> (originalDf.count() - normalizedDf.count()),
      "null_percentage" -> calculateNullPercentage(normalizedDf),
      "quality_score_avg" -> normalizedDf.select(avg("quality_score")).collect()(0)(0)
    )
  }
  
  private def calculateNullPercentage(df: DataFrame): Double = {
    val totalCells = df.count() * df.columns.length
    val nullCells = df.columns.map { colName =>
      df.filter(col(colName).isNull).count()
    }.sum
    
    if (totalCells > 0) (nullCells.toDouble / totalCells) * 100 else 0.0
  }
}
