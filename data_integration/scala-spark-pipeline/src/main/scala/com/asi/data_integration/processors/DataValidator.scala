package com.asi.data_integration.processors

import com.asi.data_integration.config.ValidationConfig
import com.asi.data_integration.utils.{StructuredLogger, SchemaRegistry}
import org.apache.spark.sql.{DataFrame, SparkSession}
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types._
import com.typesafe.scalalogging.LazyLogging
import io.circe.syntax._
import io.circe.generic.auto._
import scala.util.{Try, Success, Failure}

/**
 * Data validation processor for the ASI Global Data Integration pipeline.
 * 
 * Handles:
 * - Schema validation against registered schemas
 * - Data quality checks and scoring
 * - Business rule validation
 * - Anomaly detection
 * - Data completeness validation
 * - Format validation (emails, URLs, phone numbers, etc.)
 */
class DataValidator(config: ValidationConfig)(implicit spark: SparkSession) extends LazyLogging {
  
  import spark.implicits._
  
  private val structuredLogger = new StructuredLogger("DataValidator")
  private val schemaRegistry = new SchemaRegistry(config.schemaRegistryUrl)
  
  /**
   * Validate DataFrame against schema and business rules
   */
  def validate(df: DataFrame, schemaName: String): DataFrame = {
    val startTime = System.currentTimeMillis()
    
    try {
      structuredLogger.info(
        "Starting data validation",
        Map(
          "schemaName" -> schemaName,
          "inputRows" -> df.count().toString,
          "validationRules" -> config.validationRules.size.toString
        )
      )
      
      val validatedDf = df
        .transform(validateSchema(schemaName))
        .transform(validateDataQuality)
        .transform(validateBusinessRules)
        .transform(detectAnomalies)
        .transform(validateCompleteness)
        .transform(validateFormats)
        .transform(addValidationMetadata)
      
      val endTime = System.currentTimeMillis()
      val processingTime = endTime - startTime
      
      val validationStats = getValidationStats(validatedDf)
      
      structuredLogger.info(
        "Data validation completed",
        Map(
          "schemaName" -> schemaName,
          "outputRows" -> validatedDf.count().toString,
          "processingTimeMs" -> processingTime.toString,
          "validRecords" -> validationStats("valid_records").toString,
          "invalidRecords" -> validationStats("invalid_records").toString,
          "avgQualityScore" -> validationStats("avg_quality_score").toString
        )
      )
      
      validatedDf
      
    } catch {
      case ex: Exception =>
        structuredLogger.error(
          "Data validation failed",
          Map(
            "schemaName" -> schemaName,
            "error" -> ex.getMessage,
            "stackTrace" -> ex.getStackTrace.mkString("\n")
          )
        )
        throw ex
    }
  }
  
  /**
   * Validate DataFrame schema against registered schema
   */
  private def validateSchema(schemaName: String)(df: DataFrame): DataFrame = {
    Try {
      val expectedSchema = schemaRegistry.getSchema(schemaName)
      val actualSchema = df.schema
      
      val schemaValidation = validateSchemaCompatibility(expectedSchema, actualSchema)
      
      df.withColumn("schema_validation",
        struct(
          lit(schemaValidation.isValid).as("is_valid"),
          lit(schemaValidation.errors.mkString("; ")).as("errors"),
          lit(schemaName).as("expected_schema"),
          lit(actualSchema.treeString).as("actual_schema")
        )
      )
    } match {
      case Success(validatedDf) => validatedDf
      case Failure(ex) =>
        logger.warn(s"Schema validation failed for $schemaName: ${ex.getMessage}")
        df.withColumn("schema_validation",
          struct(
            lit(false).as("is_valid"),
            lit(s"Schema validation error: ${ex.getMessage}").as("errors"),
            lit(schemaName).as("expected_schema"),
            lit(df.schema.treeString).as("actual_schema")
          )
        )
    }
  }
  
  /**
   * Validate data quality metrics
   */
  private def validateDataQuality(df: DataFrame): DataFrame = {
    df
      .withColumn("data_quality",
        struct(
          // Completeness checks
          calculateCompleteness(df).as("completeness_score"),
          
          // Uniqueness checks
          calculateUniqueness(df).as("uniqueness_score"),
          
          // Consistency checks
          calculateConsistency(df).as("consistency_score"),
          
          // Accuracy checks
          calculateAccuracy(df).as("accuracy_score"),
          
          // Timeliness checks
          calculateTimeliness(df).as("timeliness_score")
        )
      )
      .withColumn("overall_quality_score",
        (col("data_quality.completeness_score") * 0.3 +
         col("data_quality.uniqueness_score") * 0.2 +
         col("data_quality.consistency_score") * 0.2 +
         col("data_quality.accuracy_score") * 0.2 +
         col("data_quality.timeliness_score") * 0.1)
      )
  }
  
  /**
   * Validate business rules
   */
  private def validateBusinessRules(df: DataFrame): DataFrame = {
    val businessRuleValidations = config.validationRules.map { rule =>
      validateBusinessRule(df, rule)
    }
    
    val businessRulesStruct = struct(businessRuleValidations: _*)
    
    df.withColumn("business_rules_validation", businessRulesStruct)
      .withColumn("business_rules_passed",
        businessRuleValidations.map(col).reduce(_ && _)
      )
  }
  
  /**
   * Detect anomalies in the data
   */
  private def detectAnomalies(df: DataFrame): DataFrame = {
    // Statistical anomaly detection for numeric columns
    val numericColumns = df.schema.fields
      .filter(field => field.dataType.isInstanceOf[NumericType])
      .map(_.name)
    
    val anomalyDetections = numericColumns.map { colName =>
      detectStatisticalAnomalies(df, colName)
    }
    
    df.withColumn("anomaly_detection",
      struct(anomalyDetections: _*)
    ).withColumn("has_anomalies",
      anomalyDetections.map(col).reduce(_ || _)
    )
  }
  
  /**
   * Validate data completeness
   */
  private def validateCompleteness(df: DataFrame): DataFrame = {
    val requiredFields = config.requiredFields.getOrElse(Seq("id", "source_id", "timestamp"))
    
    val completenessChecks = requiredFields.map { fieldName =>
      col(fieldName).isNotNull.as(s"${fieldName}_present")
    }
    
    df.withColumn("completeness_validation",
      struct(completenessChecks: _*)
    ).withColumn("is_complete",
      completenessChecks.map(col).reduce(_ && _)
    )
  }
  
  /**
   * Validate data formats
   */
  private def validateFormats(df: DataFrame): DataFrame = {
    df
      .withColumn("format_validation",
        struct(
          validateEmailFormat(df).as("email_format_valid"),
          validateUrlFormat(df).as("url_format_valid"),
          validatePhoneFormat(df).as("phone_format_valid"),
          validateDateFormat(df).as("date_format_valid"),
          validateJsonFormat(df).as("json_format_valid")
        )
      )
  }
  
  /**
   * Add validation metadata
   */
  private def addValidationMetadata(df: DataFrame): DataFrame = {
    df
      .withColumn("validation_timestamp", current_timestamp())
      .withColumn("validation_version", lit(config.version))
      .withColumn("validation_rules_applied", lit(config.validationRules.size))
      .withColumn("is_valid",
        col("schema_validation.is_valid") &&
        col("business_rules_passed") &&
        col("is_complete") &&
        !col("has_anomalies")
      )
  }
  
  // Helper methods for quality calculations
  private def calculateCompleteness(df: DataFrame): Column = {
    val totalFields = df.columns.length
    val nonNullCounts = df.columns.map { colName =>
      when(col(colName).isNotNull, 1).otherwise(0)
    }.reduce(_ + _)
    
    nonNullCounts.cast(DoubleType) / lit(totalFields)
  }
  
  private def calculateUniqueness(df: DataFrame): Column = {
    // For simplicity, check uniqueness of ID field
    val totalCount = count("*")
    val distinctCount = countDistinct("id")
    distinctCount.cast(DoubleType) / totalCount.cast(DoubleType)
  }
  
  private def calculateConsistency(df: DataFrame): Column = {
    // Check data type consistency
    lit(1.0) // Placeholder - implement specific consistency checks
  }
  
  private def calculateAccuracy(df: DataFrame): Column = {
    // Check against reference data or business rules
    lit(1.0) // Placeholder - implement specific accuracy checks
  }
  
  private def calculateTimeliness(df: DataFrame): Column = {
    // Check if data is recent enough
    val hoursSinceIngestion = (unix_timestamp(current_timestamp()) - unix_timestamp(col("ingested_at"))) / 3600
    when(hoursSinceIngestion <= config.maxDataAgeHours.getOrElse(24), 1.0)
      .when(hoursSinceIngestion <= config.maxDataAgeHours.getOrElse(24) * 2, 0.5)
      .otherwise(0.0)
  }
  
  private def validateBusinessRule(df: DataFrame, rule: ValidationRule): Column = {
    // Parse and apply business rule
    // This is a simplified implementation
    rule.ruleType match {
      case "range" => 
        col(rule.field) >= lit(rule.minValue.getOrElse(Double.MinValue)) &&
        col(rule.field) <= lit(rule.maxValue.getOrElse(Double.MaxValue))
      case "regex" =>
        col(rule.field).rlike(rule.pattern.getOrElse(".*"))
      case "not_null" =>
        col(rule.field).isNotNull
      case _ =>
        lit(true)
    }
  }
  
  private def detectStatisticalAnomalies(df: DataFrame, colName: String): Column = {
    // Z-score based anomaly detection
    val meanVal = df.select(mean(col(colName))).collect()(0)(0).asInstanceOf[Double]
    val stdVal = df.select(stddev(col(colName))).collect()(0)(0).asInstanceOf[Double]
    
    val zScore = abs((col(colName) - lit(meanVal)) / lit(stdVal))
    (zScore > lit(config.anomalyThreshold.getOrElse(3.0))).as(s"${colName}_anomaly")
  }
  
  private def validateEmailFormat(df: DataFrame): Column = {
    if (df.columns.contains("email")) {
      col("email").rlike("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$")
    } else {
      lit(true)
    }
  }
  
  private def validateUrlFormat(df: DataFrame): Column = {
    if (df.columns.contains("url")) {
      col("url").rlike("^https?://[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}.*$")
    } else {
      lit(true)
    }
  }
  
  private def validatePhoneFormat(df: DataFrame): Column = {
    if (df.columns.contains("phone")) {
      col("phone").rlike("^\\+?[1-9]\\d{1,14}$")
    } else {
      lit(true)
    }
  }
  
  private def validateDateFormat(df: DataFrame): Column = {
    if (df.columns.contains("date")) {
      col("date").isNotNull && col("date").cast(DateType).isNotNull
    } else {
      lit(true)
    }
  }
  
  private def validateJsonFormat(df: DataFrame): Column = {
    if (df.columns.contains("json_data")) {
      // Try to parse JSON - if it fails, it's invalid
      try {
        from_json(col("json_data"), MapType(StringType, StringType)).isNotNull
      } catch {
        case _: Exception => lit(false)
      }
    } else {
      lit(true)
    }
  }
  
  private def validateSchemaCompatibility(expected: StructType, actual: StructType): SchemaValidationResult = {
    val errors = scala.collection.mutable.ListBuffer[String]()
    
    // Check if all required fields are present
    expected.fields.foreach { expectedField =>
      actual.fields.find(_.name == expectedField.name) match {
        case Some(actualField) =>
          if (actualField.dataType != expectedField.dataType) {
            errors += s"Field ${expectedField.name}: expected ${expectedField.dataType}, got ${actualField.dataType}"
          }
        case None =>
          if (!expectedField.nullable) {
            errors += s"Required field ${expectedField.name} is missing"
          }
      }
    }
    
    SchemaValidationResult(errors.isEmpty, errors.toList)
  }
  
  /**
   * Get validation statistics
   */
  def getValidationStats(df: DataFrame): Map[String, Any] = {
    val validCount = df.filter(col("is_valid")).count()
    val totalCount = df.count()
    val avgQualityScore = df.select(avg("overall_quality_score")).collect()(0)(0)
    
    Map(
      "total_records" -> totalCount,
      "valid_records" -> validCount,
      "invalid_records" -> (totalCount - validCount),
      "validation_rate" -> (validCount.toDouble / totalCount * 100),
      "avg_quality_score" -> avgQualityScore
    )
  }
}

case class ValidationRule(
  name: String,
  field: String,
  ruleType: String,
  pattern: Option[String] = None,
  minValue: Option[Double] = None,
  maxValue: Option[Double] = None,
  required: Boolean = false
)

case class SchemaValidationResult(
  isValid: Boolean,
  errors: List[String]
)
