package com.asi.data_integration.sinks

import com.asi.data_integration.config.KafkaConfig
import com.asi.data_integration.utils.StructuredLogger
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.{DataFrame, SparkSession}
import org.apache.spark.sql.functions._
import org.apache.spark.sql.streaming.{StreamingQuery, Trigger}
import org.apache.spark.sql.types._

/**
 * Kafka sink for the ASI Global Data Integration pipeline.
 * 
 * Handles:
 * - Writing processed data to Kafka topics
 * - Topic routing based on data type and quality
 * - Message serialization (JSON, Avro, Protobuf)
 * - Error handling and dead letter queues
 * - Streaming and batch writes
 */
class KafkaSink(config: KafkaConfig)(implicit spark: SparkSession) extends LazyLogging {
  
  import spark.implicits._
  
  private val structuredLogger = new StructuredLogger("KafkaSink")
  
  /**
   * Write processed data to appropriate Kafka topics
   */
  def writeProcessedData(df: DataFrame): Unit = {
    val startTime = System.currentTimeMillis()
    
    try {
      structuredLogger.info(
        "Starting Kafka processed data write",
        Map(
          "inputRows" -> df.count().toString,
          "topic" -> config.outputTopics.processed
        )
      )
      
      // Prepare data for Kafka
      val kafkaDF = prepareForKafka(df, "processed")
      
      // Write to Kafka
      writeToKafka(kafkaDF, config.outputTopics.processed)
      
      val endTime = System.currentTimeMillis()
      val processingTime = endTime - startTime
      
      structuredLogger.logMetrics(
        "kafka_processed_write",
        df.count(),
        kafkaDF.count(),
        processingTime
      )
      
    } catch {
      case ex: Exception =>
        structuredLogger.error(
          "Kafka processed data write failed",
          Map(
            "topic" -> config.outputTopics.processed,
            "error" -> ex.getMessage
          )
        )
        throw ex
    }
  }
  
  /**
   * Write enriched data to Kafka
   */
  def writeEnrichedData(df: DataFrame): Unit = {
    val startTime = System.currentTimeMillis()
    
    try {
      structuredLogger.info(
        "Starting Kafka enriched data write",
        Map(
          "inputRows" -> df.count().toString,
          "topic" -> config.outputTopics.enriched
        )
      )
      
      // Prepare data for Kafka
      val kafkaDF = prepareForKafka(df, "enriched")
      
      // Write to Kafka
      writeToKafka(kafkaDF, config.outputTopics.enriched)
      
      val endTime = System.currentTimeMillis()
      val processingTime = endTime - startTime
      
      structuredLogger.logMetrics(
        "kafka_enriched_write",
        df.count(),
        kafkaDF.count(),
        processingTime
      )
      
    } catch {
      case ex: Exception =>
        structuredLogger.error(
          "Kafka enriched data write failed",
          Map(
            "topic" -> config.outputTopics.enriched,
            "error" -> ex.getMessage
          )
        )
        throw ex
    }
  }
  
  /**
   * Write data to Learning Engine topics
   */
  def writeToLearningEngine(df: DataFrame): Unit = {
    val startTime = System.currentTimeMillis()
    
    try {
      structuredLogger.info(
        "Starting Learning Engine data write",
        Map(
          "inputRows" -> df.count().toString,
          "topic" -> config.outputTopics.learningEngine
        )
      )
      
      // Filter and route data based on type
      val nlpData = df.filter(col("data_type").isin("text", "document", "news"))
      val visionData = df.filter(col("data_type").isin("image", "video"))
      val sensorData = df.filter(col("data_type") === "sensor")
      val generalData = df.except(nlpData).except(visionData).except(sensorData)
      
      // Write to different Learning Engine topics
      if (nlpData.count() > 0) {
        val nlpKafkaDF = prepareForKafka(nlpData, "nlp")
        writeToKafka(nlpKafkaDF, s"${config.outputTopics.learningEngine}-nlp")
      }
      
      if (visionData.count() > 0) {
        val visionKafkaDF = prepareForKafka(visionData, "vision")
        writeToKafka(visionKafkaDF, s"${config.outputTopics.learningEngine}-vision")
      }
      
      if (sensorData.count() > 0) {
        val sensorKafkaDF = prepareForKafka(sensorData, "sensor")
        writeToKafka(sensorKafkaDF, s"${config.outputTopics.learningEngine}-sensor")
      }
      
      if (generalData.count() > 0) {
        val generalKafkaDF = prepareForKafka(generalData, "general")
        writeToKafka(generalKafkaDF, config.outputTopics.learningEngine)
      }
      
      val endTime = System.currentTimeMillis()
      val processingTime = endTime - startTime
      
      structuredLogger.logMetrics(
        "kafka_learning_engine_write",
        df.count(),
        df.count(),
        processingTime
      )
      
    } catch {
      case ex: Exception =>
        structuredLogger.error(
          "Learning Engine data write failed",
          Map(
            "topic" -> config.outputTopics.learningEngine,
            "error" -> ex.getMessage
          )
        )
        throw ex
    }
  }
  
  /**
   * Write error data to dead letter queue
   */
  def writeErrors(df: DataFrame): Unit = {
    val startTime = System.currentTimeMillis()
    
    try {
      structuredLogger.info(
        "Starting error data write",
        Map(
          "inputRows" -> df.count().toString,
          "topic" -> config.outputTopics.errors
        )
      )
      
      // Prepare error data for Kafka
      val errorKafkaDF = prepareErrorsForKafka(df)
      
      // Write to error topic
      writeToKafka(errorKafkaDF, config.outputTopics.errors)
      
      val endTime = System.currentTimeMillis()
      val processingTime = endTime - startTime
      
      structuredLogger.logMetrics(
        "kafka_errors_write",
        df.count(),
        errorKafkaDF.count(),
        processingTime
      )
      
    } catch {
      case ex: Exception =>
        structuredLogger.error(
          "Error data write failed",
          Map(
            "topic" -> config.outputTopics.errors,
            "error" -> ex.getMessage
          )
        )
        // Don't throw here to avoid infinite error loops
    }
  }
  
  /**
   * Start streaming write to Kafka
   */
  def startStreamingWrite(df: DataFrame, outputType: String): StreamingQuery = {
    val topic = outputType match {
      case "processed" => config.outputTopics.processed
      case "enriched" => config.outputTopics.enriched
      case "learning" => config.outputTopics.learningEngine
      case "errors" => config.outputTopics.errors
      case _ => config.outputTopics.processed
    }
    
    val kafkaDF = if (outputType == "errors") {
      prepareErrorsForKafka(df)
    } else {
      prepareForKafka(df, outputType)
    }
    
    kafkaDF.writeStream
      .format("kafka")
      .option("kafka.bootstrap.servers", config.bootstrapServers)
      .option("topic", topic)
      .option("checkpointLocation", s"/tmp/kafka-sink-checkpoint-$outputType")
      .trigger(Trigger.ProcessingTime("30 seconds"))
      .start()
  }
  
  /**
   * Prepare DataFrame for Kafka write
   */
  private def prepareForKafka(df: DataFrame, messageType: String): DataFrame = {
    // Create Kafka message structure
    val messageDF = df
      .withColumn("message_type", lit(messageType))
      .withColumn("output_timestamp", current_timestamp())
      .withColumn("message_payload", 
        to_json(struct(
          col("id"),
          col("source"),
          col("data_type"),
          col("event_timestamp"),
          col("payload"),
          col("metadata"),
          col("quality_score"),
          col("schema_version"),
          col("message_type"),
          col("output_timestamp")
        ))
      )
    
    // Prepare key and value for Kafka
    val kafkaDF = messageDF
      .select(
        col("id").cast(StringType).as("key"),
        col("message_payload").as("value")
      )
    
    kafkaDF
  }
  
  /**
   * Prepare error DataFrame for Kafka write
   */
  private def prepareErrorsForKafka(df: DataFrame): DataFrame = {
    // Create error message structure
    val errorDF = df
      .withColumn("error_timestamp", current_timestamp())
      .withColumn("error_payload",
        to_json(struct(
          col("id"),
          col("source"),
          col("data_type"),
          col("error_type"),
          col("error_message"),
          col("original_payload"),
          col("error_timestamp"),
          col("pipeline_stage")
        ))
      )
    
    // Prepare key and value for Kafka
    val kafkaDF = errorDF
      .select(
        coalesce(col("id"), lit("unknown")).cast(StringType).as("key"),
        col("error_payload").as("value")
      )
    
    kafkaDF
  }
  
  /**
   * Write DataFrame to specific Kafka topic
   */
  private def writeToKafka(df: DataFrame, topic: String): Unit = {
    df.write
      .format("kafka")
      .option("kafka.bootstrap.servers", config.bootstrapServers)
      .option("topic", topic)
      .mode("append")
      .save()
    
    structuredLogger.info(
      "Data written to Kafka topic",
      Map(
        "topic" -> topic,
        "records" -> df.count().toString
      )
    )
  }
  
  /**
   * Validate Kafka connectivity
   */
  def validateConnection(): Boolean = {
    try {
      // Create a test DataFrame
      val testData = Seq(("test_key", "test_value")).toDF("key", "value")
      
      // Try to write to a test topic (this will validate connection)
      testData.write
        .format("kafka")
        .option("kafka.bootstrap.servers", config.bootstrapServers)
        .option("topic", "test-topic")
        .mode("append")
        .save()
      
      structuredLogger.info(
        "Kafka connection validated successfully",
        Map("bootstrapServers" -> config.bootstrapServers)
      )
      
      true
    } catch {
      case ex: Exception =>
        structuredLogger.error(
          "Kafka connection validation failed",
          Map(
            "bootstrapServers" -> config.bootstrapServers,
            "error" -> ex.getMessage
          )
        )
        false
    }
  }
}
