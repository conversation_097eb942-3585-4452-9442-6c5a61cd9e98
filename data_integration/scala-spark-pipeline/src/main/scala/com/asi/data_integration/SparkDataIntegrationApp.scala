package com.asi.data_integration

import com.asi.data_integration.config.AppConfig
import com.asi.data_integration.processors.{DataNormalizer, DataValidator, SchemaEvolutionManager}
import com.asi.data_integration.sinks.{Neo4jSink, KafkaSink, LearningEngineSink}
import com.asi.data_integration.sources.{KafkaSource, DatabaseSource, APISource}
import com.asi.data_integration.monitoring.{MetricsCollector, HealthCheckServer}
import com.asi.data_integration.utils.{StructuredLogger, SchemaRegistry}
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.{SparkSession, DataFrame}
import org.apache.spark.sql.streaming.{StreamingQuery, Trigger}
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types._
import pureconfig.ConfigSource
import pureconfig.generic.auto._
import io.circe.syntax._
import io.circe.generic.auto._

import scala.concurrent.duration._
import scala.util.{Try, Success, Failure}

/**
 * Main Spark application for ASI Data Integration Pipeline
 *
 * This application processes data from multiple sources, normalizes it,
 * validates schemas, and outputs to various sinks including Neo4j and Kafka.
 */
object SparkDataIntegrationApp extends LazyLogging {

  def main(args: Array[String]): Unit = {
    logger.info("Starting ASI Data Integration Spark Pipeline")

    // Parse command line arguments
    val config = parseArgs(args) match {
      case Some(cfg) => cfg
      case None =>
        logger.error("Failed to parse configuration")
        sys.exit(1)
    }

    // Initialize Spark session
    implicit val spark: SparkSession = createSparkSession(config)

    try {
      // Initialize components
      val metricsCollector = new MetricsCollector(config.monitoring)
      val healthCheckServer = new HealthCheckServer(config.monitoring.healthPort)

      // Start health check server
      healthCheckServer.start()

      // Initialize data processors
      val dataValidator = new DataValidator(config.validation)
      val dataNormalizer = new DataNormalizer(config.normalization)
      val schemaManager = new SchemaEvolutionManager(config.schemaRegistry)

      // Initialize sources
      val kafkaSource = new KafkaSource(config.kafka)
      val databaseSource = new DatabaseSource(config.database)

      // Initialize sinks
      val neo4jSink = new Neo4jSink(config.neo4j)
      val kafkaSink = new KafkaSink(config.kafka)

      // Create data integration pipeline
      val pipeline = new DataIntegrationPipeline(
        dataValidator,
        dataNormalizer,
        schemaManager,
        neo4jSink,
        kafkaSink,
        metricsCollector
      )

      // Start streaming queries
      val queries = startStreamingQueries(pipeline, kafkaSource, databaseSource, config)

      logger.info(s"Started ${queries.length} streaming queries")

      // Wait for termination
      queries.foreach(_.awaitTermination())

    } catch {
      case ex: Exception =>
        logger.error("Error in Spark application", ex)
        sys.exit(1)
    } finally {
      spark.stop()
      logger.info("Spark application stopped")
    }
  }

  private def createSparkSession(config: AppConfig): SparkSession = {
    val builder = SparkSession.builder()
      .appName("ASI-Data-Integration-Pipeline")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .config("spark.sql.streaming.checkpointLocation", config.spark.checkpointLocation)
      .config("spark.sql.streaming.stateStore.providerClass", "org.apache.spark.sql.execution.streaming.state.HDFSBackedStateStoreProvider")
      .config("spark.serializer", "org.apache.spark.serializer.KryoSerializer")
      .config("spark.sql.extensions", "org.neo4j.spark.Neo4jSparkExtensions")

    // Add Kafka configurations
    config.kafka.properties.foreach { case (key, value) =>
      builder.config(s"spark.kafka.$key", value)
    }

    // Add Neo4j configurations
    builder.config("neo4j.url", config.neo4j.uri)
      .config("neo4j.authentication.basic.username", config.neo4j.username)
      .config("neo4j.authentication.basic.password", config.neo4j.password)

    val spark = builder.getOrCreate()

    // Set log level
    spark.sparkContext.setLogLevel(config.spark.logLevel)

    logger.info(s"Created Spark session with ${spark.sparkContext.defaultParallelism} partitions")
    spark
  }

  private def startStreamingQueries(
    pipeline: DataIntegrationPipeline,
    kafkaSource: KafkaSource,
    databaseSource: DatabaseSource,
    config: AppConfig
  )(implicit spark: SparkSession): List[StreamingQuery] = {

    import spark.implicits._

    val queries = scala.collection.mutable.ListBuffer[StreamingQuery]()

    // Kafka streaming query for real-time data
    val kafkaStream = kafkaSource.createStream(config.kafka.inputTopics)
    val processedKafkaStream = pipeline.processStream(kafkaStream)

    val kafkaQuery = processedKafkaStream.writeStream
      .outputMode("append")
      .trigger(Trigger.ProcessingTime(config.spark.triggerInterval))
      .option("checkpointLocation", s"${config.spark.checkpointLocation}/kafka")
      .foreachBatch { (batchDF: DataFrame, batchId: Long) =>
        logger.info(s"Processing Kafka batch $batchId with ${batchDF.count()} records")
        pipeline.processBatch(batchDF, batchId)
      }
      .start()

    queries += kafkaQuery

    // Database streaming query for batch data
    if (config.database.enabled) {
      val databaseStream = databaseSource.createStream()
      val processedDatabaseStream = pipeline.processStream(databaseStream)

      val databaseQuery = processedDatabaseStream.writeStream
        .outputMode("append")
        .trigger(Trigger.ProcessingTime(config.database.triggerInterval))
        .option("checkpointLocation", s"${config.spark.checkpointLocation}/database")
        .foreachBatch { (batchDF: DataFrame, batchId: Long) =>
          logger.info(s"Processing database batch $batchId with ${batchDF.count()} records")
          pipeline.processBatch(batchDF, batchId)
        }
        .start()

      queries += databaseQuery
    }

    queries.toList
  }

  private def parseArgs(args: Array[String]): Option[AppConfig] = {
    val parser = new scopt.OptionParser[Map[String, String]]("asi-data-integration") {
      head("ASI Data Integration Pipeline", "1.0.0")

      opt[String]('c', "config")
        .action((x, c) => c + ("config" -> x))
        .text("Configuration file path")

      opt[String]('e', "env")
        .action((x, c) => c + ("env" -> x))
        .text("Environment (development, staging, production)")
        .validate(x => if (Set("development", "staging", "production").contains(x)) success else failure("Invalid environment"))

      help("help").text("Show this help message")
    }

    parser.parse(args, Map.empty[String, String]) match {
      case Some(options) =>
        val configPath = options.getOrElse("config", "application.conf")
        val env = options.getOrElse("env", "development")

        Try {
          ConfigSource.file(configPath).at(env).load[AppConfig]
        } match {
          case Success(config) => Some(config)
          case Failure(ex) =>
            logger.error(s"Failed to load configuration from $configPath", ex)
            None
        }
      case None => None
    }
  }
}

/**
 * Main data integration pipeline that orchestrates all processing steps
 */
class DataIntegrationPipeline(
  dataValidator: DataValidator,
  dataNormalizer: DataNormalizer,
  schemaManager: SchemaEvolutionManager,
  neo4jSink: Neo4jSink,
  kafkaSink: KafkaSink,
  metricsCollector: MetricsCollector
)(implicit spark: SparkSession) extends LazyLogging {

  import spark.implicits._

  def processStream(inputDF: DataFrame): DataFrame = {
    logger.debug("Processing stream with schema: " + inputDF.schema.treeString)

    inputDF
      .withColumn("processing_timestamp", current_timestamp())
      .withColumn("batch_id", monotonically_increasing_id())
  }

  def processBatch(batchDF: DataFrame, batchId: Long): Unit = {
    val startTime = System.currentTimeMillis()

    try {
      logger.info(s"Starting batch processing for batch $batchId")

      // Step 1: Schema validation and evolution
      val validatedDF = dataValidator.validate(batchDF)
      val evolvedDF = schemaManager.evolveSchema(validatedDF)

      // Step 2: Data normalization
      val normalizedDF = dataNormalizer.normalize(evolvedDF)

      // Step 3: Data quality checks
      val qualityCheckedDF = performQualityChecks(normalizedDF)

      // Step 4: Extract relationships for graph database
      val relationshipsDF = extractRelationships(qualityCheckedDF)

      // Step 5: Write to sinks
      writeBatchToSinks(qualityCheckedDF, relationshipsDF, batchId)

      // Update metrics
      val processingTime = System.currentTimeMillis() - startTime
      metricsCollector.recordBatchProcessed(batchId, batchDF.count(), processingTime)

      logger.info(s"Completed batch processing for batch $batchId in ${processingTime}ms")

    } catch {
      case ex: Exception =>
        logger.error(s"Error processing batch $batchId", ex)
        metricsCollector.recordBatchFailed(batchId)
        throw ex
    }
  }

  private def performQualityChecks(df: DataFrame): DataFrame = {
    logger.debug("Performing data quality checks")

    df.filter($"data".isNotNull)
      .filter(length($"data") > 0)
      .withColumn("quality_score",
        when($"validation_errors".isNull, 1.0)
        .otherwise(0.5)
      )
  }

  private def extractRelationships(df: DataFrame): DataFrame = {
    logger.debug("Extracting relationships for graph database")

    // This is a simplified relationship extraction
    // In practice, this would use more sophisticated algorithms
    df.select(
      $"id".as("source_id"),
      $"metadata.sourceType".as("source_type"),
      explode_outer($"relationships").as("relationship")
    ).select(
      $"source_id",
      $"source_type",
      $"relationship.targetId".as("target_id"),
      $"relationship.relationshipType".as("relationship_type"),
      $"relationship.confidenceScore".as("confidence_score")
    ).filter($"target_id".isNotNull)
  }

  private def writeBatchToSinks(dataDF: DataFrame, relationshipsDF: DataFrame, batchId: Long): Unit = {
    logger.debug(s"Writing batch $batchId to sinks")

    // Write data to Neo4j
    neo4jSink.writeData(dataDF)
    neo4jSink.writeRelationships(relationshipsDF)

    // Write processed data to Kafka for downstream consumption
    kafkaSink.writeProcessedData(dataDF)

    logger.debug(s"Successfully wrote batch $batchId to all sinks")
  }
}
