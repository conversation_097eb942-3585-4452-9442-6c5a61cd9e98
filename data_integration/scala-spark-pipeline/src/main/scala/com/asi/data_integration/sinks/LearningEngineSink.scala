package com.asi.data_integration.sinks

import com.asi.data_integration.config.LearningEngineConfig
import com.asi.data_integration.utils.StructuredLogger
import org.apache.spark.sql.{DataFrame, SparkSession}
import org.apache.spark.sql.functions._
import org.apache.spark.sql.streaming.{DataStreamWriter, OutputMode, Trigger}
import org.apache.spark.sql.types._
import com.typesafe.scalalogging.LazyLogging
import io.circe.syntax._
import io.circe.generic.auto._
import scala.util.{Try, Success, Failure}

/**
 * Learning Engine sink for the ASI Global Data Integration pipeline.
 * 
 * Handles:
 * - Data routing to appropriate learning engine topics based on data type
 * - Data format transformation for ML/DL consumption
 * - Feature extraction and preprocessing
 * - Batch and streaming data delivery
 * - Quality filtering for training data
 */
class LearningEngineSink(config: LearningEngineConfig)(implicit spark: SparkSession) extends LazyLogging {
  
  import spark.implicits._
  
  private val structuredLogger = new StructuredLogger("LearningEngineSink")
  
  /**
   * Write DataFrame to Learning Engine topics
   */
  def write(df: DataFrame, outputMode: OutputMode = OutputMode.Append()): Unit = {
    val startTime = System.currentTimeMillis()
    
    try {
      structuredLogger.info(
        "Starting Learning Engine data write",
        Map(
          "inputRows" -> df.count().toString,
          "outputMode" -> outputMode.toString,
          "targetTopics" -> config.kafkaTopics.mkString(", ")
        )
      )
      
      // Filter high-quality data for training
      val qualityFilteredDf = filterHighQualityData(df)
      
      // Route data by type to appropriate topics
      routeDataByType(qualityFilteredDf)
      
      val endTime = System.currentTimeMillis()
      val processingTime = endTime - startTime
      
      structuredLogger.logMetrics(
        "learning_engine_write",
        df.count(),
        qualityFilteredDf.count(),
        processingTime
      )
      
    } catch {
      case ex: Exception =>
        structuredLogger.error(
          "Learning Engine data write failed",
          Map(
            "error" -> ex.getMessage,
            "stackTrace" -> ex.getStackTrace.mkString("\n")
          )
        )
        throw ex
    }
  }
  
  /**
   * Write streaming DataFrame to Learning Engine
   */
  def writeStream(df: DataFrame): DataStreamWriter[org.apache.spark.sql.Row] = {
    structuredLogger.info(
      "Setting up Learning Engine streaming write",
      Map(
        "checkpointLocation" -> config.checkpointLocation,
        "triggerInterval" -> config.triggerInterval
      )
    )
    
    val qualityFilteredDf = filterHighQualityData(df)
    val routedDf = prepareForStreaming(qualityFilteredDf)
    
    routedDf.writeStream
      .outputMode(OutputMode.Append())
      .format("kafka")
      .option("kafka.bootstrap.servers", config.kafkaBootstrapServers)
      .option("checkpointLocation", config.checkpointLocation)
      .trigger(Trigger.ProcessingTime(config.triggerInterval))
      .foreachBatch { (batchDf: DataFrame, batchId: Long) =>
        processBatch(batchDf, batchId)
      }
  }
  
  /**
   * Filter high-quality data suitable for training
   */
  private def filterHighQualityData(df: DataFrame): DataFrame = {
    val minQualityScore = config.minQualityScore.getOrElse(0.7)
    
    val filteredDf = df
      .filter(col("is_valid") === true)
      .filter(col("overall_quality_score") >= minQualityScore)
      .filter(col("payload").isNotNull)
      .filter(col("data_type").isNotNull)
    
    structuredLogger.info(
      "Applied quality filtering",
      Map(
        "originalRows" -> df.count().toString,
        "filteredRows" -> filteredDf.count().toString,
        "minQualityScore" -> minQualityScore.toString,
        "filterRate" -> (filteredDf.count().toDouble / df.count() * 100).toString
      )
    )
    
    filteredDf
  }
  
  /**
   * Route data to appropriate Learning Engine topics based on data type
   */
  private def routeDataByType(df: DataFrame): Unit = {
    val dataTypes = df.select("data_type").distinct().collect().map(_.getString(0))
    
    dataTypes.foreach { dataType =>
      val typedData = df.filter(col("data_type") === dataType)
      val targetTopic = getTargetTopic(dataType)
      
      if (targetTopic.nonEmpty) {
        writeToKafkaTopic(typedData, targetTopic)
      } else {
        structuredLogger.warn(
          "No target topic found for data type",
          Map("dataType" -> dataType)
        )
      }
    }
  }
  
  /**
   * Get target Kafka topic based on data type
   */
  private def getTargetTopic(dataType: String): String = {
    dataType.toUpperCase match {
      case "TEXT" | "NLP" | "DOCUMENT" => config.nlpTopic
      case "IMAGE" | "VIDEO" | "VISION" => config.visionTopic
      case "SENSOR" | "TIMESERIES" | "NUMERIC" => config.rlTopic
      case "GRAPH" | "RELATIONSHIP" | "SEMANTIC" => config.abstractionTopic
      case _ => config.defaultTopic
    }
  }
  
  /**
   * Write data to specific Kafka topic
   */
  private def writeToKafkaTopic(df: DataFrame, topic: String): Unit = {
    val transformedDf = transformForLearningEngine(df, topic)
    
    transformedDf
      .select(
        lit(topic).as("topic"),
        col("id").as("key"),
        to_json(struct(col("*"))).as("value")
      )
      .write
      .format("kafka")
      .option("kafka.bootstrap.servers", config.kafkaBootstrapServers)
      .save()
    
    structuredLogger.info(
      "Data written to Learning Engine topic",
      Map(
        "topic" -> topic,
        "records" -> transformedDf.count().toString
      )
    )
  }
  
  /**
   * Transform data for Learning Engine consumption
   */
  private def transformForLearningEngine(df: DataFrame, topic: String): DataFrame = {
    val baseDf = df
      .withColumn("learning_engine_metadata",
        struct(
          lit(topic).as("target_topic"),
          current_timestamp().as("sent_at"),
          col("data_type").as("original_data_type"),
          col("source_type").as("source_system"),
          col("overall_quality_score").as("quality_score")
        )
      )
    
    // Apply topic-specific transformations
    topic match {
      case t if t.contains("nlp") => transformForNLP(baseDf)
      case t if t.contains("vision") => transformForVision(baseDf)
      case t if t.contains("rl") => transformForRL(baseDf)
      case t if t.contains("abstraction") => transformForAbstraction(baseDf)
      case _ => baseDf
    }
  }
  
  /**
   * Transform data for NLP training
   */
  private def transformForNLP(df: DataFrame): DataFrame = {
    df
      .withColumn("nlp_features",
        struct(
          col("payload").as("text"),
          when(col("metadata").isNotNull, col("metadata")).otherwise(map()).as("annotations"),
          length(col("payload")).as("text_length"),
          size(split(col("payload"), " ")).as("word_count")
        )
      )
      .withColumn("training_data",
        struct(
          col("nlp_features.text").as("input_text"),
          col("nlp_features.annotations").as("labels"),
          col("learning_engine_metadata").as("metadata")
        )
      )
  }
  
  /**
   * Transform data for Vision training
   */
  private def transformForVision(df: DataFrame): DataFrame = {
    df
      .withColumn("vision_features",
        struct(
          col("payload").as("image_data"),
          when(col("metadata.image_format").isNotNull, col("metadata.image_format")).otherwise(lit("unknown")).as("format"),
          when(col("metadata.width").isNotNull, col("metadata.width")).otherwise(lit(0)).as("width"),
          when(col("metadata.height").isNotNull, col("metadata.height")).otherwise(lit(0)).as("height")
        )
      )
      .withColumn("training_data",
        struct(
          col("vision_features.image_data").as("image"),
          col("vision_features").as("image_metadata"),
          col("learning_engine_metadata").as("metadata")
        )
      )
  }
  
  /**
   * Transform data for RL training
   */
  private def transformForRL(df: DataFrame): DataFrame = {
    df
      .withColumn("rl_features",
        struct(
          col("payload").as("observation"),
          when(col("metadata.action").isNotNull, col("metadata.action")).otherwise(lit(null)).as("action"),
          when(col("metadata.reward").isNotNull, col("metadata.reward")).otherwise(lit(0.0)).as("reward"),
          when(col("metadata.done").isNotNull, col("metadata.done")).otherwise(lit(false)).as("done")
        )
      )
      .withColumn("training_data",
        struct(
          col("rl_features.observation").as("state"),
          col("rl_features.action").as("action"),
          col("rl_features.reward").as("reward"),
          col("rl_features.done").as("terminal"),
          col("learning_engine_metadata").as("metadata")
        )
      )
  }
  
  /**
   * Transform data for Abstraction training
   */
  private def transformForAbstraction(df: DataFrame): DataFrame = {
    df
      .withColumn("abstraction_features",
        struct(
          col("payload").as("context"),
          when(col("metadata.relationships").isNotNull, col("metadata.relationships")).otherwise(array()).as("relationships"),
          when(col("metadata.entities").isNotNull, col("metadata.entities")).otherwise(array()).as("entities")
        )
      )
      .withColumn("training_data",
        struct(
          col("abstraction_features.context").as("input_context"),
          col("abstraction_features.relationships").as("relationships"),
          col("abstraction_features.entities").as("entities"),
          col("learning_engine_metadata").as("metadata")
        )
      )
  }
  
  /**
   * Prepare DataFrame for streaming
   */
  private def prepareForStreaming(df: DataFrame): DataFrame = {
    df
      .withColumn("kafka_key", col("id"))
      .withColumn("kafka_value", to_json(struct(col("*"))))
      .withColumn("kafka_topic", 
        when(col("data_type") === "TEXT", lit(config.nlpTopic))
        .when(col("data_type") === "IMAGE", lit(config.visionTopic))
        .when(col("data_type") === "SENSOR", lit(config.rlTopic))
        .otherwise(lit(config.defaultTopic))
      )
  }
  
  /**
   * Process streaming batch
   */
  private def processBatch(batchDf: DataFrame, batchId: Long): Unit = {
    val correlationId = structuredLogger.createCorrelationId()
    val loggerWithCorrelation = structuredLogger.withCorrelationId(correlationId)
    
    loggerWithCorrelation.info(
      "Processing Learning Engine batch",
      Map(
        "batchId" -> batchId.toString,
        "batchSize" -> batchDf.count().toString
      )
    )
    
    try {
      // Group by topic and write
      val topicGroups = batchDf.groupBy("kafka_topic").agg(collect_list(struct(col("*"))).as("records"))
      
      topicGroups.collect().foreach { row =>
        val topic = row.getString(0)
        val records = row.getSeq[org.apache.spark.sql.Row](1)
        
        // Write to Kafka topic
        val topicDf = spark.createDataFrame(
          spark.sparkContext.parallelize(records),
          batchDf.schema
        )
        
        writeToKafkaTopic(topicDf, topic)
        
        loggerWithCorrelation.info(
          "Batch written to topic",
          Map(
            "topic" -> topic,
            "records" -> records.length.toString
          )
        )
      }
      
    } catch {
      case ex: Exception =>
        loggerWithCorrelation.error(
          "Batch processing failed",
          Map(
            "batchId" -> batchId.toString,
            "error" -> ex.getMessage
          )
        )
        throw ex
    }
  }
  
  /**
   * Get sink statistics
   */
  def getStatistics(): Map[String, Any] = {
    Map(
      "target_topics" -> config.kafkaTopics,
      "min_quality_score" -> config.minQualityScore.getOrElse(0.7),
      "kafka_servers" -> config.kafkaBootstrapServers,
      "checkpoint_location" -> config.checkpointLocation
    )
  }
}
