package com.asi.data_integration.sinks

import com.asi.data_integration.config.Neo4jConfig
import com.asi.data_integration.utils.StructuredLogger
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.{DataFrame, SparkSession}
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types._

/**
 * Neo4j sink for the ASI Global Data Integration pipeline.
 * 
 * Handles:
 * - Writing nodes and relationships to Neo4j graph database
 * - Schema mapping and transformation
 * - Batch and streaming writes
 * - Relationship extraction and modeling
 * - Graph analytics and indexing
 */
class Neo4jSink(config: Neo4jConfig)(implicit spark: SparkSession) extends LazyLogging {
  
  import spark.implicits._
  
  private val structuredLogger = new StructuredLogger("Neo4jSink")
  
  /**
   * Write data as nodes to Neo4j
   */
  def writeData(df: DataFrame): Unit = {
    val startTime = System.currentTimeMillis()
    
    try {
      structuredLogger.info(
        "Starting Neo4j data write",
        Map(
          "inputRows" -> df.count().toString,
          "database" -> config.database
        )
      )
      
      // Transform data for Neo4j nodes
      val nodesDF = transformToNodes(df)
      
      // Write nodes to Neo4j
      writeNodes(nodesDF)
      
      val endTime = System.currentTimeMillis()
      val processingTime = endTime - startTime
      
      structuredLogger.logMetrics(
        "neo4j_data_write",
        df.count(),
        nodesDF.count(),
        processingTime
      )
      
    } catch {
      case ex: Exception =>
        structuredLogger.error(
          "Neo4j data write failed",
          Map(
            "database" -> config.database,
            "error" -> ex.getMessage
          )
        )
        throw ex
    }
  }
  
  /**
   * Write relationships to Neo4j
   */
  def writeRelationships(df: DataFrame): Unit = {
    val startTime = System.currentTimeMillis()
    
    try {
      structuredLogger.info(
        "Starting Neo4j relationships write",
        Map(
          "inputRows" -> df.count().toString,
          "database" -> config.database
        )
      )
      
      // Transform data for Neo4j relationships
      val relationshipsDF = transformToRelationships(df)
      
      // Write relationships to Neo4j
      writeRelationshipsToNeo4j(relationshipsDF)
      
      val endTime = System.currentTimeMillis()
      val processingTime = endTime - startTime
      
      structuredLogger.logMetrics(
        "neo4j_relationships_write",
        df.count(),
        relationshipsDF.count(),
        processingTime
      )
      
    } catch {
      case ex: Exception =>
        structuredLogger.error(
          "Neo4j relationships write failed",
          Map(
            "database" -> config.database,
            "error" -> ex.getMessage
          )
        )
        throw ex
    }
  }
  
  /**
   * Transform DataFrame to Neo4j nodes format
   */
  private def transformToNodes(df: DataFrame): DataFrame = {
    // Determine node label based on data type
    val nodesDF = df
      .withColumn("node_id", col("id"))
      .withColumn("node_label", 
        when(col("data_type") === "sensor", lit(config.nodeLabels.sensor))
        .when(col("data_type") === "document", lit(config.nodeLabels.document))
        .when(col("data_type") === "device", lit(config.nodeLabels.device))
        .when(col("data_type") === "user", lit(config.nodeLabels.user))
        .otherwise(lit(config.nodeLabels.entity))
      )
      .withColumn("properties", 
        map(
          lit("id"), col("id"),
          lit("source"), col("source"),
          lit("data_type"), col("data_type"),
          lit("timestamp"), col("event_timestamp").cast(StringType),
          lit("ingestion_timestamp"), col("ingestion_timestamp").cast(StringType),
          lit("payload"), col("payload"),
          lit("schema_version"), col("schema_version")
        )
      )
    
    // Add metadata properties
    val enrichedNodesDF = nodesDF
      .withColumn("properties", 
        map_concat(
          col("properties"),
          when(col("metadata").isNotNull, col("metadata"))
          .otherwise(map())
        )
      )
    
    enrichedNodesDF.select("node_id", "node_label", "properties")
  }
  
  /**
   * Transform DataFrame to Neo4j relationships format
   */
  private def transformToRelationships(df: DataFrame): DataFrame = {
    // Extract relationships based on data content and metadata
    val relationshipsDF = df
      .filter(col("metadata").isNotNull)
      .withColumn("source_id", col("id"))
      .withColumn("target_id", 
        when(col("metadata").getItem("related_to").isNotNull, 
          col("metadata").getItem("related_to"))
        .when(col("metadata").getItem("parent_id").isNotNull,
          col("metadata").getItem("parent_id"))
        .otherwise(null)
      )
      .filter(col("target_id").isNotNull)
      .withColumn("relationship_type",
        when(col("metadata").getItem("relationship_type").isNotNull,
          col("metadata").getItem("relationship_type"))
        .when(col("data_type") === "sensor", lit(config.relationshipTypes.generatedBy))
        .when(col("metadata").getItem("parent_id").isNotNull, lit(config.relationshipTypes.belongsTo))
        .otherwise(lit(config.relationshipTypes.relatedTo))
      )
      .withColumn("properties",
        map(
          lit("created_at"), col("ingestion_timestamp").cast(StringType),
          lit("source_type"), col("data_type"),
          lit("confidence"), lit("1.0")
        )
      )
    
    relationshipsDF.select("source_id", "target_id", "relationship_type", "properties")
  }
  
  /**
   * Write nodes to Neo4j using Neo4j Spark connector
   */
  private def writeNodes(df: DataFrame): Unit = {
    df.write
      .format("org.neo4j.spark.DataSource")
      .mode("append")
      .option("url", config.uri)
      .option("authentication.basic.username", config.username)
      .option("authentication.basic.password", config.password)
      .option("database", config.database)
      .option("labels", ":Entity") // Default label, will be overridden by node_label
      .option("node.keys", "node_id")
      .option("batch.size", config.batchSize.toString)
      .save()
    
    structuredLogger.info(
      "Nodes written to Neo4j",
      Map(
        "records" -> df.count().toString,
        "database" -> config.database
      )
    )
  }
  
  /**
   * Write relationships to Neo4j using Neo4j Spark connector
   */
  private def writeRelationshipsToNeo4j(df: DataFrame): Unit = {
    df.write
      .format("org.neo4j.spark.DataSource")
      .mode("append")
      .option("url", config.uri)
      .option("authentication.basic.username", config.username)
      .option("authentication.basic.password", config.password)
      .option("database", config.database)
      .option("relationship", "RELATED_TO") // Default relationship type
      .option("relationship.source.labels", ":Entity")
      .option("relationship.source.node.keys", "source_id")
      .option("relationship.target.labels", ":Entity")
      .option("relationship.target.node.keys", "target_id")
      .option("batch.size", config.batchSize.toString)
      .save()
    
    structuredLogger.info(
      "Relationships written to Neo4j",
      Map(
        "records" -> df.count().toString,
        "database" -> config.database
      )
    )
  }
  
  /**
   * Execute custom Cypher query
   */
  def executeCypher(query: String): DataFrame = {
    spark.read
      .format("org.neo4j.spark.DataSource")
      .option("url", config.uri)
      .option("authentication.basic.username", config.username)
      .option("authentication.basic.password", config.password)
      .option("database", config.database)
      .option("query", query)
      .load()
  }
  
  /**
   * Create indexes for better performance
   */
  def createIndexes(): Unit = {
    val indexes = List(
      "CREATE INDEX entity_id_index IF NOT EXISTS FOR (e:Entity) ON (e.id)",
      "CREATE INDEX sensor_timestamp_index IF NOT EXISTS FOR (s:Sensor) ON (s.timestamp)",
      "CREATE INDEX document_source_index IF NOT EXISTS FOR (d:Document) ON (d.source)",
      "CREATE INDEX device_type_index IF NOT EXISTS FOR (d:Device) ON (d.data_type)"
    )
    
    indexes.foreach { indexQuery =>
      try {
        executeCypher(indexQuery)
        structuredLogger.info(
          "Index created successfully",
          Map("query" -> indexQuery)
        )
      } catch {
        case ex: Exception =>
          structuredLogger.warn(
            "Failed to create index",
            Map(
              "query" -> indexQuery,
              "error" -> ex.getMessage
            )
          )
      }
    }
  }
  
  /**
   * Validate Neo4j connectivity
   */
  def validateConnection(): Boolean = {
    try {
      val testDF = executeCypher("RETURN 1 as test")
      testDF.count()
      
      structuredLogger.info(
        "Neo4j connection validated successfully",
        Map("uri" -> config.uri)
      )
      
      true
    } catch {
      case ex: Exception =>
        structuredLogger.error(
          "Neo4j connection validation failed",
          Map(
            "uri" -> config.uri,
            "error" -> ex.getMessage
          )
        )
        false
    }
  }
}
