package com.asi.data_integration.utils

import com.typesafe.scalalogging.LazyLogging
import io.circe.syntax._
import io.circe.generic.auto._
import io.circe.Json
import java.time.Instant
import java.util.UUID

/**
 * Structured logging utility for the ASI Global Data Integration pipeline.
 * 
 * Provides JSON-structured logging with consistent format across all components.
 * Includes correlation IDs, timestamps, and contextual metadata.
 */
class StructuredLogger(component: String) extends LazyLogging {
  
  private val hostname = java.net.InetAddress.getLocalHost.getHostName
  private val applicationName = "asi-data-integration"
  private val version = "1.0.0"
  
  /**
   * Log info message with structured data
   */
  def info(message: String, context: Map[String, String] = Map.empty, correlationId: Option[String] = None): Unit = {
    val logEntry = createLogEntry("INFO", message, context, correlationId)
    logger.info(logEntry.asJson.noSpaces)
  }
  
  /**
   * Log warning message with structured data
   */
  def warn(message: String, context: Map[String, String] = Map.empty, correlationId: Option[String] = None): Unit = {
    val logEntry = createLogEntry("WARN", message, context, correlationId)
    logger.warn(logEntry.asJson.noSpaces)
  }
  
  /**
   * Log error message with structured data
   */
  def error(message: String, context: Map[String, String] = Map.empty, correlationId: Option[String] = None): Unit = {
    val logEntry = createLogEntry("ERROR", message, context, correlationId)
    logger.error(logEntry.asJson.noSpaces)
  }
  
  /**
   * Log debug message with structured data
   */
  def debug(message: String, context: Map[String, String] = Map.empty, correlationId: Option[String] = None): Unit = {
    val logEntry = createLogEntry("DEBUG", message, context, correlationId)
    logger.debug(logEntry.asJson.noSpaces)
  }
  
  /**
   * Log data processing metrics
   */
  def logMetrics(
    operation: String,
    inputRecords: Long,
    outputRecords: Long,
    processingTimeMs: Long,
    additionalMetrics: Map[String, Any] = Map.empty,
    correlationId: Option[String] = None
  ): Unit = {
    val metricsContext = Map(
      "operation" -> operation,
      "input_records" -> inputRecords.toString,
      "output_records" -> outputRecords.toString,
      "processing_time_ms" -> processingTimeMs.toString,
      "throughput_records_per_second" -> (if (processingTimeMs > 0) (inputRecords * 1000 / processingTimeMs).toString else "0"),
      "data_loss_percentage" -> (if (inputRecords > 0) ((inputRecords - outputRecords).toDouble / inputRecords * 100).toString else "0")
    ) ++ additionalMetrics.map { case (k, v) => k -> v.toString }
    
    info(s"Data processing metrics for $operation", metricsContext, correlationId)
  }
  
  /**
   * Log data quality metrics
   */
  def logDataQuality(
    source: String,
    qualityScore: Double,
    validRecords: Long,
    invalidRecords: Long,
    qualityChecks: Map[String, Boolean],
    correlationId: Option[String] = None
  ): Unit = {
    val qualityContext = Map(
      "source" -> source,
      "quality_score" -> qualityScore.toString,
      "valid_records" -> validRecords.toString,
      "invalid_records" -> invalidRecords.toString,
      "total_records" -> (validRecords + invalidRecords).toString,
      "validation_rate" -> (if (validRecords + invalidRecords > 0) (validRecords.toDouble / (validRecords + invalidRecords) * 100).toString else "0")
    ) ++ qualityChecks.map { case (k, v) => s"check_$k" -> v.toString }
    
    info(s"Data quality metrics for $source", qualityContext, correlationId)
  }
  
  /**
   * Log schema validation results
   */
  def logSchemaValidation(
    schemaName: String,
    isValid: Boolean,
    errors: List[String],
    recordCount: Long,
    correlationId: Option[String] = None
  ): Unit = {
    val schemaContext = Map(
      "schema_name" -> schemaName,
      "is_valid" -> isValid.toString,
      "error_count" -> errors.length.toString,
      "record_count" -> recordCount.toString,
      "errors" -> errors.mkString("; ")
    )
    
    if (isValid) {
      info(s"Schema validation passed for $schemaName", schemaContext, correlationId)
    } else {
      error(s"Schema validation failed for $schemaName", schemaContext, correlationId)
    }
  }
  
  /**
   * Log performance metrics
   */
  def logPerformance(
    operation: String,
    duration: Long,
    memoryUsed: Long,
    cpuUsage: Double,
    additionalMetrics: Map[String, String] = Map.empty,
    correlationId: Option[String] = None
  ): Unit = {
    val performanceContext = Map(
      "operation" -> operation,
      "duration_ms" -> duration.toString,
      "memory_used_mb" -> (memoryUsed / 1024 / 1024).toString,
      "cpu_usage_percent" -> cpuUsage.toString
    ) ++ additionalMetrics
    
    info(s"Performance metrics for $operation", performanceContext, correlationId)
  }
  
  /**
   * Log data lineage information
   */
  def logDataLineage(
    dataId: String,
    sourceSystem: String,
    transformations: List[String],
    destination: String,
    correlationId: Option[String] = None
  ): Unit = {
    val lineageContext = Map(
      "data_id" -> dataId,
      "source_system" -> sourceSystem,
      "transformations" -> transformations.mkString(" -> "),
      "destination" -> destination,
      "transformation_count" -> transformations.length.toString
    )
    
    info(s"Data lineage tracked for $dataId", lineageContext, correlationId)
  }
  
  /**
   * Log security events
   */
  def logSecurityEvent(
    eventType: String,
    userId: Option[String],
    resource: String,
    action: String,
    success: Boolean,
    additionalContext: Map[String, String] = Map.empty,
    correlationId: Option[String] = None
  ): Unit = {
    val securityContext = Map(
      "event_type" -> eventType,
      "user_id" -> userId.getOrElse("anonymous"),
      "resource" -> resource,
      "action" -> action,
      "success" -> success.toString,
      "security_level" -> "HIGH"
    ) ++ additionalContext
    
    if (success) {
      info(s"Security event: $eventType", securityContext, correlationId)
    } else {
      warn(s"Security event failed: $eventType", securityContext, correlationId)
    }
  }
  
  /**
   * Create structured log entry
   */
  private def createLogEntry(
    level: String,
    message: String,
    context: Map[String, String],
    correlationId: Option[String]
  ): LogEntry = {
    LogEntry(
      timestamp = Instant.now().toString,
      level = level,
      component = component,
      application = applicationName,
      version = version,
      hostname = hostname,
      message = message,
      correlationId = correlationId.getOrElse(UUID.randomUUID().toString),
      context = context,
      thread = Thread.currentThread().getName
    )
  }
  
  /**
   * Create correlation ID for request tracking
   */
  def createCorrelationId(): String = {
    UUID.randomUUID().toString
  }
  
  /**
   * Log with correlation ID from context
   */
  def withCorrelationId(correlationId: String): StructuredLoggerWithCorrelation = {
    new StructuredLoggerWithCorrelation(this, correlationId)
  }
}

/**
 * Structured logger with pre-set correlation ID
 */
class StructuredLoggerWithCorrelation(logger: StructuredLogger, correlationId: String) {
  
  def info(message: String, context: Map[String, String] = Map.empty): Unit = {
    logger.info(message, context, Some(correlationId))
  }
  
  def warn(message: String, context: Map[String, String] = Map.empty): Unit = {
    logger.warn(message, context, Some(correlationId))
  }
  
  def error(message: String, context: Map[String, String] = Map.empty): Unit = {
    logger.error(message, context, Some(correlationId))
  }
  
  def debug(message: String, context: Map[String, String] = Map.empty): Unit = {
    logger.debug(message, context, Some(correlationId))
  }
  
  def logMetrics(
    operation: String,
    inputRecords: Long,
    outputRecords: Long,
    processingTimeMs: Long,
    additionalMetrics: Map[String, Any] = Map.empty
  ): Unit = {
    logger.logMetrics(operation, inputRecords, outputRecords, processingTimeMs, additionalMetrics, Some(correlationId))
  }
}

/**
 * Log entry case class for JSON serialization
 */
case class LogEntry(
  timestamp: String,
  level: String,
  component: String,
  application: String,
  version: String,
  hostname: String,
  message: String,
  correlationId: String,
  context: Map[String, String],
  thread: String
)
