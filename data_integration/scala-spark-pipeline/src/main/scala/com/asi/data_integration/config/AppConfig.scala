package com.asi.data_integration.config

import scala.concurrent.duration.FiniteDuration

/**
 * Application configuration case classes for ASI Data Integration Pipeline
 */

case class AppConfig(
  spark: SparkConfig,
  kafka: KafkaConfig,
  neo4j: Neo4jConfig,
  database: DatabaseConfig,
  monitoring: MonitoringConfig,
  validation: ValidationConfig,
  normalization: NormalizationConfig,
  schemaRegistry: SchemaRegistryConfig
)

case class SparkConfig(
  master: String,
  appName: String,
  logLevel: String,
  checkpointLocation: String,
  triggerInterval: String,
  sql: SparkSqlConfig
)

case class SparkSqlConfig(
  adaptive: SparkAdaptiveConfig,
  streaming: SparkStreamingConfig
)

case class SparkAdaptiveConfig(
  enabled: Boolean,
  coalescePartitions: SparkCoalesceConfig
)

case class SparkCoalesceConfig(
  enabled: Boolean
)

case class SparkStreamingConfig(
  stateStore: SparkStateStoreConfig
)

case class SparkStateStoreConfig(
  providerClass: String
)

case class KafkaConfig(
  bootstrapServers: String,
  inputTopics: List[String],
  outputTopics: KafkaOutputTopics,
  properties: Map[String, String],
  consumer: KafkaConsumerConfig,
  producer: KafkaProducerConfig
)

case class KafkaOutputTopics(
  processed: String,
  enriched: String,
  errors: String,
  learningEngine: String
)

case class KafkaConsumerConfig(
  groupId: String,
  autoOffsetReset: String,
  enableAutoCommit: Boolean,
  maxPollRecords: Int
)

case class KafkaProducerConfig(
  acks: String,
  retries: Int,
  batchSize: Int,
  lingerMs: Int,
  bufferMemory: Long,
  compressionType: String
)

case class Neo4jConfig(
  uri: String,
  username: String,
  password: String,
  database: String,
  maxConnectionLifetime: Int,
  maxConnectionPoolSize: Int,
  connectionAcquisitionTimeout: Int,
  batchSize: Int,
  nodeLabels: Neo4jNodeLabels,
  relationshipTypes: Neo4jRelationshipTypes
)

case class Neo4jNodeLabels(
  entity: String,
  document: String,
  sensor: String,
  device: String,
  user: String
)

case class Neo4jRelationshipTypes(
  relatedTo: String,
  contains: String,
  generatedBy: String,
  belongsTo: String
)

case class DatabaseConfig(
  enabled: Boolean,
  driver: String,
  url: String,
  username: String,
  password: String,
  triggerInterval: String,
  batchSize: Int,
  tables: List[DatabaseTable]
)

case class DatabaseTable(
  name: String,
  timestampColumn: String,
  incrementalColumn: Option[String]
)

case class MonitoringConfig(
  enabled: Boolean,
  port: Int,
  healthPort: Int,
  metricsPath: String,
  healthPath: String,
  prometheus: PrometheusConfig
)

case class PrometheusConfig(
  enabled: Boolean,
  port: Int,
  path: String
)

case class ValidationConfig(
  enabled: Boolean,
  strictMode: Boolean,
  maxErrors: Int,
  schemas: Map[String, String],
  rules: ValidationRules
)

case class ValidationRules(
  required: List[String],
  dataTypes: Map[String, String],
  ranges: Map[String, ValidationRange],
  patterns: Map[String, String]
)

case class ValidationRange(
  min: Option[Double],
  max: Option[Double]
)

case class NormalizationConfig(
  enabled: Boolean,
  rules: NormalizationRules,
  transformations: Map[String, String]
)

case class NormalizationRules(
  dateFormats: List[String],
  timeZone: String,
  encoding: String,
  nullValues: List[String],
  booleanValues: Map[String, Boolean]
)

case class SchemaRegistryConfig(
  enabled: Boolean,
  url: String,
  auth: SchemaRegistryAuth,
  cacheSize: Int,
  cacheTtl: Int
)

case class SchemaRegistryAuth(
  username: Option[String],
  password: Option[String]
)

case class LearningEngineConfig(
  kafkaBootstrapServers: String,
  kafkaTopics: List[String],
  checkpointLocation: String,
  triggerInterval: String,
  nlpTopic: String,
  visionTopic: String,
  rlTopic: String,
  defaultTopic: String,
  qualityThreshold: Double,
  batchSize: Int
)
