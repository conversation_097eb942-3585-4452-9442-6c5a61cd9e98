package com.asi.data_integration.sources

import com.asi.data_integration.config.{DatabaseConfig, DatabaseTable}
import com.asi.data_integration.utils.StructuredLogger
import com.typesafe.scalalogging.LazyLogging
import org.apache.spark.sql.{DataFrame, SparkSession}
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types._

/**
 * Database source for the ASI Global Data Integration pipeline.
 * 
 * Handles:
 * - Incremental data loading from databases
 * - Change data capture (CDC) simulation
 * - Multiple database support (PostgreSQL, MySQL, Oracle, etc.)
 * - Schema inference and validation
 * - Watermarking for streaming
 */
class DatabaseSource(config: DatabaseConfig)(implicit spark: SparkSession) extends LazyLogging {
  
  import spark.implicits._
  
  private val structuredLogger = new StructuredLogger("DatabaseSource")
  
  /**
   * Create streaming DataFrame from database tables
   */
  def createStream(): DataFrame = {
    if (!config.enabled) {
      throw new IllegalStateException("Database source is not enabled")
    }
    
    structuredLogger.info(
      "Creating database stream",
      Map(
        "url" -> config.url,
        "tables" -> config.tables.map(_.name).mkString(", ")
      )
    )
    
    // For streaming, we'll simulate CDC by polling tables at intervals
    val unionedDF = config.tables.map(createTableStream).reduce(_.union(_))
    
    // Add common metadata
    val enrichedDF = unionedDF
      .withColumn("data_source", lit("database"))
      .withColumn("ingestion_timestamp", current_timestamp())
      .withColumn("pipeline_version", lit("1.0.0"))
    
    structuredLogger.info(
      "Database stream created successfully",
      Map(
        "schema" -> enrichedDF.schema.treeString,
        "tables" -> config.tables.map(_.name).mkString(", ")
      )
    )
    
    enrichedDF
  }
  
  /**
   * Create batch DataFrame from database tables
   */
  def createBatch(tables: Option[List[String]] = None): DataFrame = {
    if (!config.enabled) {
      throw new IllegalStateException("Database source is not enabled")
    }
    
    val tablesToProcess = tables.getOrElse(config.tables.map(_.name))
    
    structuredLogger.info(
      "Creating database batch",
      Map(
        "url" -> config.url,
        "tables" -> tablesToProcess.mkString(", ")
      )
    )
    
    val tableDataFrames = config.tables
      .filter(table => tablesToProcess.contains(table.name))
      .map(createTableBatch)
    
    val unionedDF = if (tableDataFrames.nonEmpty) {
      tableDataFrames.reduce(_.union(_))
    } else {
      spark.emptyDataFrame
    }
    
    // Add metadata
    val enrichedDF = unionedDF
      .withColumn("data_source", lit("database"))
      .withColumn("ingestion_timestamp", current_timestamp())
      .withColumn("pipeline_version", lit("1.0.0"))
    
    structuredLogger.info(
      "Database batch created successfully",
      Map(
        "records" -> enrichedDF.count().toString,
        "tables" -> tablesToProcess.mkString(", ")
      )
    )
    
    enrichedDF
  }
  
  /**
   * Create streaming DataFrame for a single table
   */
  private def createTableStream(table: DatabaseTable): DataFrame = {
    // For streaming, we use rate source to simulate periodic polling
    val rateDF = spark.readStream
      .format("rate")
      .option("rowsPerSecond", "1")
      .option("rampUpTime", "0s")
      .load()
    
    // Transform rate stream to trigger table reads
    val tableDF = rateDF
      .withColumn("table_name", lit(table.name))
      .withColumn("read_timestamp", col("timestamp"))
      .select("table_name", "read_timestamp")
    
    // In a real implementation, this would trigger incremental reads
    // For now, we'll create a mock structure
    createMockTableData(table)
  }
  
  /**
   * Create batch DataFrame for a single table
   */
  private def createTableBatch(table: DatabaseTable): DataFrame = {
    try {
      val tableDF = spark.read
        .format("jdbc")
        .option("url", config.url)
        .option("dbtable", table.name)
        .option("user", config.username)
        .option("password", config.password)
        .option("driver", config.driver)
        .option("fetchsize", config.batchSize.toString)
        .load()
      
      // Add table metadata
      val enrichedDF = tableDF
        .withColumn("source_table", lit(table.name))
        .withColumn("extraction_timestamp", current_timestamp())
        .withColumn("data_type", lit("structured"))
      
      // Handle incremental loading if configured
      table.incrementalColumn match {
        case Some(column) =>
          val lastValue = getLastIncrementalValue(table.name, column)
          enrichedDF.filter(col(column) > lastValue)
        case None =>
          enrichedDF
      }
      
    } catch {
      case ex: Exception =>
        structuredLogger.error(
          "Failed to read table",
          Map(
            "table" -> table.name,
            "error" -> ex.getMessage
          )
        )
        
        // Return empty DataFrame with expected schema
        createMockTableData(table).limit(0)
    }
  }
  
  /**
   * Create mock table data for development/testing
   */
  private def createMockTableData(table: DatabaseTable): DataFrame = {
    // Create mock data based on table configuration
    val mockData = Seq(
      ("mock_id_1", table.name, "sample_data_1", java.sql.Timestamp.valueOf("2023-01-01 10:00:00")),
      ("mock_id_2", table.name, "sample_data_2", java.sql.Timestamp.valueOf("2023-01-01 11:00:00")),
      ("mock_id_3", table.name, "sample_data_3", java.sql.Timestamp.valueOf("2023-01-01 12:00:00"))
    )
    
    val mockDF = spark.createDataFrame(mockData).toDF("id", "source_table", "payload", "event_timestamp")
    
    mockDF
      .withColumn("data_type", lit("structured"))
      .withColumn("metadata", map(
        lit("table"), col("source_table"),
        lit("mock"), lit("true")
      ))
      .withColumn("schema_version", lit("1.0"))
  }
  
  /**
   * Get last incremental value for a table column
   */
  private def getLastIncrementalValue(tableName: String, column: String): Any = {
    try {
      val lastValueDF = spark.read
        .format("jdbc")
        .option("url", config.url)
        .option("dbtable", s"(SELECT MAX($column) as max_value FROM $tableName) as t")
        .option("user", config.username)
        .option("password", config.password)
        .option("driver", config.driver)
        .load()
      
      val result = lastValueDF.collect().headOption
      result.map(_.get(0)).getOrElse(0)
      
    } catch {
      case ex: Exception =>
        structuredLogger.warn(
          "Failed to get last incremental value, using default",
          Map(
            "table" -> tableName,
            "column" -> column,
            "error" -> ex.getMessage
          )
        )
        0
    }
  }
  
  /**
   * Validate database connectivity
   */
  def validateConnection(): Boolean = {
    if (!config.enabled) {
      return true // Skip validation if disabled
    }
    
    try {
      val testDF = spark.read
        .format("jdbc")
        .option("url", config.url)
        .option("dbtable", "(SELECT 1 as test_column) as test_table")
        .option("user", config.username)
        .option("password", config.password)
        .option("driver", config.driver)
        .load()
      
      testDF.count() // This will trigger the connection
      
      structuredLogger.info(
        "Database connection validated successfully",
        Map("url" -> config.url)
      )
      
      true
    } catch {
      case ex: Exception =>
        structuredLogger.error(
          "Database connection validation failed",
          Map(
            "url" -> config.url,
            "error" -> ex.getMessage
          )
        )
        false
    }
  }
  
  /**
   * Get table schemas
   */
  def getTableSchemas(): Map[String, StructType] = {
    config.tables.map { table =>
      try {
        val tableDF = spark.read
          .format("jdbc")
          .option("url", config.url)
          .option("dbtable", table.name)
          .option("user", config.username)
          .option("password", config.password)
          .option("driver", config.driver)
          .load()
        
        table.name -> tableDF.schema
      } catch {
        case ex: Exception =>
          structuredLogger.warn(
            "Failed to get schema for table",
            Map(
              "table" -> table.name,
              "error" -> ex.getMessage
            )
          )
          table.name -> StructType(Seq.empty)
      }
    }.toMap
  }
}
