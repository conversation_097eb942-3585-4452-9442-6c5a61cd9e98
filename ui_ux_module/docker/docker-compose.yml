version: '3.8'

services:
  # React Dashboard
  react-dashboard:
    build:
      context: ../react-dashboard
      dockerfile: Dockerfile
      target: production
    container_name: asi-react-dashboard
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - REACT_APP_API_URL=http://localhost:4000/api
      - REACT_APP_WEBSOCKET_URL=ws://localhost:8080
      - REACT_APP_STREAMLIT_URL=http://localhost:8501
    volumes:
      - ../configs:/app/configs:ro
      - react-logs:/app/logs
    networks:
      - asi-ui-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    depends_on:
      - websocket-server
      - api-gateway

  # Streamlit Inspector
  streamlit-inspector:
    build:
      context: ../streamlit-inspector
      dockerfile: Dockerfile
    container_name: asi-streamlit-inspector
    ports:
      - "8501:8501"
    environment:
      - PYTHONPATH=/app/src
      - STREAMLIT_SERVER_PORT=8501
      - STREAMLIT_SERVER_ADDRESS=0.0.0.0
      - STREAMLIT_BROWSER_GATHER_USAGE_STATS=false
      - STREAMLIT_SERVER_ENABLE_CORS=true
      - STREAMLIT_SERVER_ENABLE_XSRF_PROTECTION=false
    volumes:
      - ../configs:/app/configs:ro
      - streamlit-logs:/app/logs
      - streamlit-cache:/app/.streamlit
    networks:
      - asi-ui-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8501/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # WebSocket Server
  websocket-server:
    build:
      context: ../websocket-server
      dockerfile: Dockerfile
    container_name: asi-websocket-server
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=production
      - PORT=8080
      - HOST=0.0.0.0
      - LOG_LEVEL=info
      - DECISION_ENGINE_ENDPOINT=decision-engine:50070
      - LEARNING_ENGINE_ENDPOINT=learning-engine:50060
      - SELF_IMPROVEMENT_ENDPOINT=self-improvement:50080
      - DATA_INTEGRATION_ENDPOINT=data-integration:8085
    volumes:
      - ../configs:/app/configs:ro
      - websocket-logs:/app/logs
    networks:
      - asi-ui-network
      - asi-system-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # API Gateway
  api-gateway:
    build:
      context: ../api-gateway
      dockerfile: Dockerfile
    container_name: asi-api-gateway
    ports:
      - "4000:4000"
    environment:
      - NODE_ENV=production
      - PORT=4000
      - HOST=0.0.0.0
      - LOG_LEVEL=info
      - CORS_ORIGINS=http://localhost:3000,http://localhost:8501
      - RATE_LIMIT_WINDOW_MS=60000
      - RATE_LIMIT_MAX_REQUESTS=1000
    volumes:
      - ../configs:/app/configs:ro
      - api-gateway-logs:/app/logs
    networks:
      - asi-ui-network
      - asi-system-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Redis for caching and session storage
  redis:
    image: redis:7-alpine
    container_name: asi-ui-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass asi_redis_password
    volumes:
      - redis-data:/data
    networks:
      - asi-ui-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx reverse proxy and load balancer
  nginx:
    image: nginx:alpine
    container_name: asi-ui-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx-logs:/var/log/nginx
    networks:
      - asi-ui-network
    restart: unless-stopped
    depends_on:
      - react-dashboard
      - streamlit-inspector
      - websocket-server
      - api-gateway
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus for metrics collection
  prometheus:
    image: prom/prometheus:latest
    container_name: asi-ui-prometheus
    ports:
      - "9090:9090"
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    networks:
      - asi-ui-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9090/-/healthy"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    container_name: asi-ui-grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana-data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - asi-ui-network
    restart: unless-stopped
    depends_on:
      - prometheus
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Jaeger for distributed tracing
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: asi-ui-jaeger
    ports:
      - "16686:16686"
      - "14268:14268"
      - "14250:14250"
    environment:
      - COLLECTOR_OTLP_ENABLED=true
      - SPAN_STORAGE_TYPE=memory
    networks:
      - asi-ui-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:14269/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Log aggregation with Fluentd
  fluentd:
    build:
      context: ./fluentd
      dockerfile: Dockerfile
    container_name: asi-ui-fluentd
    ports:
      - "24224:24224"
      - "24224:24224/udp"
    volumes:
      - ./fluentd/conf:/fluentd/etc:ro
      - fluentd-logs:/fluentd/log
    networks:
      - asi-ui-network
    restart: unless-stopped

# Networks
networks:
  asi-ui-network:
    driver: bridge
    name: asi-ui-network
  asi-system-network:
    external: true
    name: asi-system-network

# Volumes
volumes:
  react-logs:
    driver: local
  streamlit-logs:
    driver: local
  streamlit-cache:
    driver: local
  websocket-logs:
    driver: local
  api-gateway-logs:
    driver: local
  redis-data:
    driver: local
  nginx-logs:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
  fluentd-logs:
    driver: local
