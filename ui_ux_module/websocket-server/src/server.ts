import express from 'express';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import { config } from 'dotenv';
import { StatusCodes } from 'http-status-codes';

// Internal modules
import { Logger } from './utils/logger';
import { ConfigManager } from './utils/config';
import { MetricsCollector } from './utils/metrics';
import { RateLimiter } from './utils/rateLimiter';
import { ASIConnector } from './clients/asiConnector';
import { WebSocketHandler } from './handlers/websocketHandler';
import { HealthCheckHandler } from './handlers/healthCheckHandler';

// Load environment variables
config();

class ASIWebSocketServer {
  private app: express.Application;
  private server: any;
  private io: SocketIOServer;
  private logger: Logger;
  private config: ConfigManager;
  private metrics: MetricsCollector;
  private rateLimiter: RateLimiter;
  private asiConnector: ASIConnector;
  private wsHandler: WebSocketHandler;
  private healthHandler: HealthCheckHandler;

  constructor() {
    this.logger = new Logger('ASIWebSocketServer');
    this.config = new ConfigManager();
    this.metrics = new MetricsCollector();
    this.rateLimiter = new RateLimiter();
    
    this.initializeExpress();
    this.initializeSocketIO();
    this.initializeConnectors();
    this.initializeHandlers();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  private initializeExpress(): void {
    this.app = express();
    this.server = createServer(this.app);

    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: false, // Disable for WebSocket connections
    }));

    // CORS configuration
    this.app.use(cors({
      origin: this.config.get('cors.origins', ['http://localhost:3000']),
      methods: this.config.get('cors.methods', ['GET', 'POST']),
      credentials: true,
    }));

    // Compression and logging
    this.app.use(compression());
    this.app.use(morgan('combined', {
      stream: { write: (message) => this.logger.info(message.trim()) }
    }));

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Rate limiting
    this.app.use(this.rateLimiter.middleware());

    this.logger.info('Express application initialized');
  }

  private initializeSocketIO(): void {
    this.io = new SocketIOServer(this.server, {
      cors: {
        origin: this.config.get('cors.origins', ['http://localhost:3000']),
        methods: ['GET', 'POST'],
        credentials: true,
      },
      transports: ['websocket', 'polling'],
      pingTimeout: this.config.get('websocket.pingTimeout', 60000),
      pingInterval: this.config.get('websocket.pingInterval', 25000),
      maxHttpBufferSize: this.config.get('websocket.maxBufferSize', 1e6),
      allowEIO3: true,
    });

    this.logger.info('Socket.IO server initialized');
  }

  private initializeConnectors(): void {
    this.asiConnector = new ASIConnector(this.config, this.logger);
    this.logger.info('ASI connectors initialized');
  }

  private initializeHandlers(): void {
    this.wsHandler = new WebSocketHandler(
      this.io,
      this.asiConnector,
      this.metrics,
      this.logger
    );
    
    this.healthHandler = new HealthCheckHandler(
      this.asiConnector,
      this.metrics,
      this.logger
    );

    this.logger.info('Handlers initialized');
  }

  private setupRoutes(): void {
    // Health check endpoint
    this.app.get('/health', (req, res) => {
      this.healthHandler.handleHealthCheck(req, res);
    });

    // Metrics endpoint
    this.app.get('/metrics', (req, res) => {
      res.set('Content-Type', 'text/plain');
      res.send(this.metrics.getPrometheusMetrics());
    });

    // WebSocket connection info
    this.app.get('/ws/info', (req, res) => {
      res.json({
        connected_clients: this.io.engine.clientsCount,
        uptime: process.uptime(),
        memory_usage: process.memoryUsage(),
        version: process.env.npm_package_version || '1.0.0',
      });
    });

    // API status endpoint
    this.app.get('/api/status', async (req, res) => {
      try {
        const status = await this.asiConnector.getSystemStatus();
        res.json({
          success: true,
          data: status,
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        this.logger.error('Error getting system status:', error);
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
          success: false,
          error: 'Failed to get system status',
          timestamp: new Date().toISOString(),
        });
      }
    });

    // 404 handler
    this.app.use('*', (req, res) => {
      res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        error: 'Endpoint not found',
        path: req.originalUrl,
        timestamp: new Date().toISOString(),
      });
    });

    this.logger.info('Routes configured');
  }

  private setupErrorHandling(): void {
    // Express error handler
    this.app.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
      this.logger.error('Express error:', error);
      
      if (res.headersSent) {
        return next(error);
      }

      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        success: false,
        error: 'Internal server error',
        timestamp: new Date().toISOString(),
      });
    });

    // Process error handlers
    process.on('uncaughtException', (error) => {
      this.logger.error('Uncaught exception:', error);
      this.gracefulShutdown('UNCAUGHT_EXCEPTION');
    });

    process.on('unhandledRejection', (reason, promise) => {
      this.logger.error('Unhandled rejection at:', promise, 'reason:', reason);
      this.gracefulShutdown('UNHANDLED_REJECTION');
    });

    // Graceful shutdown signals
    process.on('SIGTERM', () => {
      this.logger.info('SIGTERM received');
      this.gracefulShutdown('SIGTERM');
    });

    process.on('SIGINT', () => {
      this.logger.info('SIGINT received');
      this.gracefulShutdown('SIGINT');
    });

    this.logger.info('Error handling configured');
  }

  public async start(): Promise<void> {
    try {
      // Connect to ASI services
      await this.asiConnector.connect();

      // Start WebSocket handler
      await this.wsHandler.start();

      // Start the server
      const port = this.config.get('server.port', 8080);
      const host = this.config.get('server.host', '0.0.0.0');

      this.server.listen(port, host, () => {
        this.logger.info(`ASI WebSocket Server started on ${host}:${port}`);
        this.logger.info(`Health check: http://${host}:${port}/health`);
        this.logger.info(`Metrics: http://${host}:${port}/metrics`);
        this.logger.info(`WebSocket: ws://${host}:${port}`);
      });

      // Start metrics collection
      this.metrics.startCollection();

      this.logger.info('ASI WebSocket Server fully initialized');

    } catch (error) {
      this.logger.error('Failed to start server:', error);
      process.exit(1);
    }
  }

  private async gracefulShutdown(signal: string): Promise<void> {
    this.logger.info(`Graceful shutdown initiated by ${signal}`);

    try {
      // Stop accepting new connections
      this.server.close(() => {
        this.logger.info('HTTP server closed');
      });

      // Close WebSocket connections
      this.io.close(() => {
        this.logger.info('WebSocket server closed');
      });

      // Stop WebSocket handler
      await this.wsHandler.stop();

      // Disconnect from ASI services
      await this.asiConnector.disconnect();

      // Stop metrics collection
      this.metrics.stopCollection();

      this.logger.info('Graceful shutdown completed');
      process.exit(0);

    } catch (error) {
      this.logger.error('Error during graceful shutdown:', error);
      process.exit(1);
    }
  }

  public getApp(): express.Application {
    return this.app;
  }

  public getIO(): SocketIOServer {
    return this.io;
  }
}

// Start the server if this file is run directly
if (require.main === module) {
  const server = new ASIWebSocketServer();
  server.start().catch((error) => {
    console.error('Failed to start server:', error);
    process.exit(1);
  });
}

export { ASIWebSocketServer };
export default ASIWebSocketServer;
