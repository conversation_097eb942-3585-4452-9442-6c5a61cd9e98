{"name": "asi-websocket-server", "version": "1.0.0", "description": "Real-time WebSocket server for ASI System UI/UX module", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "ts-node-dev --respawn --transpile-only src/server.ts", "build": "tsc", "build:watch": "tsc --watch", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "format": "prettier --write src/**/*.ts", "clean": "<PERSON><PERSON><PERSON> dist", "docker:build": "docker build -t asi-websocket-server .", "docker:run": "docker run -p 8080:8080 asi-websocket-server"}, "dependencies": {"socket.io": "^4.7.2", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "compression": "^1.7.4", "morgan": "^1.10.0", "winston": "^3.10.0", "dotenv": "^16.3.1", "yaml": "^2.3.2", "lodash": "^4.17.21", "moment": "^2.29.4", "uuid": "^9.0.0", "joi": "^17.9.2", "rate-limiter-flexible": "^2.4.2", "@grpc/grpc-js": "^1.9.0", "@grpc/proto-loader": "^0.7.8", "kafkajs": "^2.2.4", "redis": "^4.6.7", "prometheus-client": "^14.2.0", "http-status-codes": "^2.2.0"}, "devDependencies": {"@types/node": "^20.4.5", "@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/compression": "^1.7.2", "@types/morgan": "^1.9.4", "@types/lodash": "^4.14.195", "@types/uuid": "^9.0.2", "@types/jest": "^29.5.3", "typescript": "^5.1.6", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "jest": "^29.6.1", "ts-jest": "^29.1.1", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "eslint": "^8.45.0", "prettier": "^3.0.0", "rimraf": "^5.0.1", "supertest": "^6.3.3", "@types/supertest": "^2.0.12"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/server.ts"]}, "eslintConfig": {"extends": ["eslint:recommended", "@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2020, "sourceType": "module"}, "rules": {"@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/explicit-function-return-type": "off"}}, "prettier": {"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 80, "tabWidth": 2}, "keywords": ["asi", "websocket", "real-time", "socket.io", "typescript", "monitoring"], "author": "ASI System Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/asi-system/ui-ux-module"}, "bugs": {"url": "https://github.com/asi-system/ui-ux-module/issues"}, "homepage": "https://github.com/asi-system/ui-ux-module#readme"}