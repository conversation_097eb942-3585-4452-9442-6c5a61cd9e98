# ASI UI/UX Module Configuration
# ===============================

# General settings
global:
  environment: "development"
  log_level: "INFO"
  debug: true
  enable_metrics: true
  enable_tracing: true

# React Dashboard Configuration
react_dashboard:
  port: 3000
  host: "0.0.0.0"
  title: "ASI System Dashboard"
  description: "Real-time monitoring and visualization for ASI System"
  
  # Build configuration
  build:
    output_dir: "dist"
    public_path: "/"
    source_maps: true
    minify: true
  
  # Theme configuration
  theme:
    default_theme: "dark"
    enable_theme_switching: true
    primary_color: "#1976d2"
    secondary_color: "#dc004e"
    accent_color: "#00bcd4"
  
  # Feature flags
  features:
    enable_real_time_updates: true
    enable_notifications: true
    enable_export: true
    enable_fullscreen: true
    enable_mobile_view: true
    auto_refresh_interval: 5000  # milliseconds

# Streamlit Inspector Configuration
streamlit_inspector:
  port: 8501
  host: "0.0.0.0"
  title: "ASI Model Inspector"
  
  # App configuration
  apps:
    model_inspector:
      enabled: true
      path: "/model"
      title: "Model Analysis"
    data_explorer:
      enabled: true
      path: "/data"
      title: "Data Explorer"
    performance_monitor:
      enabled: true
      path: "/performance"
      title: "Performance Monitor"
    decision_analyzer:
      enabled: true
      path: "/decisions"
      title: "Decision Analyzer"
  
  # UI configuration
  ui:
    wide_mode: true
    sidebar_state: "expanded"
    initial_sidebar_state: "expanded"
    layout: "wide"
    page_icon: "🤖"

# WebSocket Server Configuration
websocket_server:
  port: 8080
  host: "0.0.0.0"
  
  # Connection settings
  max_connections: 1000
  heartbeat_interval: 30000  # milliseconds
  connection_timeout: 60000  # milliseconds
  
  # Message settings
  max_message_size: 1048576  # 1MB
  compression: true
  
  # Real-time data streams
  streams:
    decision_engine:
      enabled: true
      topic: "decisions"
      buffer_size: 100
    learning_engine:
      enabled: true
      topic: "training"
      buffer_size: 50
    self_improvement:
      enabled: true
      topic: "improvements"
      buffer_size: 25
    system_metrics:
      enabled: true
      topic: "metrics"
      buffer_size: 200

# API Gateway Configuration
api_gateway:
  port: 4000
  host: "0.0.0.0"
  
  # CORS configuration
  cors:
    enabled: true
    origins:
      - "http://localhost:3000"
      - "http://localhost:8501"
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    headers: ["Content-Type", "Authorization", "X-Requested-With"]
  
  # Rate limiting
  rate_limiting:
    enabled: true
    window_ms: 60000  # 1 minute
    max_requests: 1000
    
  # Authentication
  auth:
    enabled: false  # Disabled for development
    jwt_secret: "your-jwt-secret-key"
    token_expiry: 3600  # 1 hour

# Integration with ASI Modules
integration:
  # Decision Engine integration
  decision_engine:
    grpc_endpoint: "localhost:50070"
    rest_endpoint: "http://localhost:8070"
    websocket_topic: "decisions"
    health_check_interval: 30
    
  # Learning Engine integration
  learning_engine:
    grpc_endpoint: "localhost:50060"
    rest_endpoint: "http://localhost:8060"
    websocket_topic: "training"
    health_check_interval: 30
    
  # Self-Improvement Engine integration
  self_improvement_engine:
    grpc_endpoint: "localhost:50080"
    rest_endpoint: "http://localhost:8080"
    websocket_topic: "improvements"
    health_check_interval: 30
    
  # Data Integration module
  data_integration:
    rest_endpoint: "http://localhost:8085"
    kafka_endpoint: "localhost:9092"
    websocket_topic: "data_flow"
    health_check_interval: 30

# Monitoring and Observability
monitoring:
  # Prometheus metrics
  prometheus:
    enabled: true
    port: 9090
    path: "/metrics"
    scrape_interval: 15
    
  # Health checks
  health_checks:
    enabled: true
    interval: 30
    timeout: 5
    endpoints:
      - name: "decision_engine"
        url: "http://localhost:8070/health"
      - name: "learning_engine"
        url: "http://localhost:8060/health"
      - name: "self_improvement"
        url: "http://localhost:8080/health"
      - name: "data_integration"
        url: "http://localhost:8085/health"
  
  # Logging
  logging:
    level: "INFO"
    format: "json"
    outputs: ["console", "file"]
    file_path: "/app/logs/ui-module.log"
    max_file_size: "100MB"
    max_files: 10
  
  # Tracing
  tracing:
    enabled: true
    jaeger_endpoint: "http://localhost:14268/api/traces"
    service_name: "asi-ui-module"
    sampling_rate: 0.1

# Dashboard Layout Configuration
dashboard:
  # Grid layout
  grid:
    columns: 12
    row_height: 60
    margin: [10, 10]
    container_padding: [20, 20]
  
  # Default widgets
  default_widgets:
    - id: "system_overview"
      type: "metrics_card"
      position: { x: 0, y: 0, w: 6, h: 4 }
      title: "System Overview"
    - id: "decision_flow"
      type: "flow_chart"
      position: { x: 6, y: 0, w: 6, h: 4 }
      title: "Decision Flow"
    - id: "training_status"
      type: "progress_chart"
      position: { x: 0, y: 4, w: 4, h: 3 }
      title: "Training Status"
    - id: "performance_metrics"
      type: "line_chart"
      position: { x: 4, y: 4, w: 8, h: 3 }
      title: "Performance Metrics"
    - id: "system_logs"
      type: "log_viewer"
      position: { x: 0, y: 7, w: 12, h: 4 }
      title: "System Logs"

# Data Visualization Configuration
visualization:
  # Chart defaults
  charts:
    default_theme: "dark"
    animation_duration: 300
    responsive: true
    maintain_aspect_ratio: false
  
  # D3.js configuration
  d3:
    force_simulation:
      alpha: 0.3
      alpha_decay: 0.0228
      velocity_decay: 0.4
    
  # Chart.js configuration
  chartjs:
    responsive: true
    plugins:
      legend:
        display: true
        position: "top"
      tooltip:
        enabled: true
        mode: "index"

# Security Configuration
security:
  # Content Security Policy
  csp:
    enabled: true
    directives:
      default_src: ["'self'"]
      script_src: ["'self'", "'unsafe-inline'"]
      style_src: ["'self'", "'unsafe-inline'"]
      img_src: ["'self'", "data:", "https:"]
  
  # HTTPS configuration
  https:
    enabled: false  # Disabled for development
    cert_file: "/certs/server.crt"
    key_file: "/certs/server.key"
  
  # Session configuration
  session:
    secret: "your-session-secret"
    max_age: 86400000  # 24 hours
    secure: false  # Set to true in production with HTTPS

# Performance Configuration
performance:
  # Caching
  cache:
    enabled: true
    ttl: 300  # 5 minutes
    max_size: 1000
  
  # Compression
  compression:
    enabled: true
    level: 6
    threshold: 1024  # bytes
  
  # Bundle optimization
  optimization:
    code_splitting: true
    tree_shaking: true
    minification: true
    source_maps: true

# Development Configuration
development:
  # Hot reloading
  hot_reload: true
  
  # Development server
  dev_server:
    port: 3001
    host: "localhost"
    open: true
    overlay: true
  
  # Mock data
  mock_data:
    enabled: true
    decision_engine: true
    learning_engine: true
    self_improvement: true
