#!/usr/bin/env python3
"""
ASI UI/UX Module Integration Test
================================

Comprehensive integration test suite for the ASI UI/UX module.
Tests connectivity and functionality with all ASI system components.
"""

import asyncio
import json
import time
import requests
import websocket
import pytest
from typing import Dict, Any, List
from concurrent.futures import ThreadPoolExecutor
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ASIUIIntegrationTest:
    """Integration test suite for ASI UI/UX module."""
    
    def __init__(self):
        self.base_urls = {
            'react_dashboard': 'http://localhost:3000',
            'streamlit_inspector': 'http://localhost:8501',
            'websocket_server': 'http://localhost:8080',
            'api_gateway': 'http://localhost:4000',
            'decision_engine': 'http://localhost:8070',
            'learning_engine': 'http://localhost:8060',
            'self_improvement': 'http://localhost:8080',
            'data_integration': 'http://localhost:8085',
        }
        
        self.websocket_url = 'ws://localhost:8080'
        self.test_results = {}
        
    def test_service_health(self) -> Dict[str, bool]:
        """Test health endpoints of all services."""
        logger.info("Testing service health endpoints...")
        
        health_results = {}
        
        for service, base_url in self.base_urls.items():
            try:
                response = requests.get(f"{base_url}/health", timeout=10)
                health_results[service] = response.status_code == 200
                
                if health_results[service]:
                    logger.info(f"✅ {service} is healthy")
                else:
                    logger.error(f"❌ {service} health check failed: {response.status_code}")
                    
            except Exception as e:
                health_results[service] = False
                logger.error(f"❌ {service} health check failed: {str(e)}")
        
        return health_results
    
    def test_react_dashboard(self) -> bool:
        """Test React dashboard functionality."""
        logger.info("Testing React dashboard...")
        
        try:
            # Test main page
            response = requests.get(self.base_urls['react_dashboard'], timeout=10)
            if response.status_code != 200:
                logger.error(f"React dashboard main page failed: {response.status_code}")
                return False
            
            # Test API endpoints
            api_endpoints = [
                '/api/dashboard/layouts/default',
                '/api/system/health',
                '/api/models',
                '/api/decisions',
            ]
            
            for endpoint in api_endpoints:
                try:
                    response = requests.get(
                        f"{self.base_urls['api_gateway']}{endpoint}",
                        timeout=5
                    )
                    if response.status_code not in [200, 404]:  # 404 is acceptable for some endpoints
                        logger.warning(f"API endpoint {endpoint} returned {response.status_code}")
                except Exception as e:
                    logger.warning(f"API endpoint {endpoint} failed: {str(e)}")
            
            logger.info("✅ React dashboard tests passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ React dashboard test failed: {str(e)}")
            return False
    
    def test_streamlit_inspector(self) -> bool:
        """Test Streamlit inspector functionality."""
        logger.info("Testing Streamlit inspector...")
        
        try:
            # Test main Streamlit app
            response = requests.get(self.base_urls['streamlit_inspector'], timeout=10)
            if response.status_code != 200:
                logger.error(f"Streamlit inspector failed: {response.status_code}")
                return False
            
            # Test Streamlit health endpoint
            response = requests.get(f"{self.base_urls['streamlit_inspector']}/health", timeout=5)
            if response.status_code != 200:
                logger.warning("Streamlit health endpoint not available")
            
            logger.info("✅ Streamlit inspector tests passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Streamlit inspector test failed: {str(e)}")
            return False
    
    def test_websocket_connection(self) -> bool:
        """Test WebSocket server connectivity and messaging."""
        logger.info("Testing WebSocket connection...")
        
        try:
            # Test WebSocket connection
            ws = websocket.create_connection(self.websocket_url, timeout=10)
            
            # Send test message
            test_message = {
                'type': 'test',
                'data': {'message': 'integration_test'},
                'timestamp': time.time()
            }
            
            ws.send(json.dumps(test_message))
            
            # Wait for response (with timeout)
            ws.settimeout(5)
            try:
                response = ws.recv()
                logger.info(f"WebSocket response received: {response[:100]}...")
            except websocket.WebSocketTimeoutException:
                logger.warning("WebSocket response timeout (this may be expected)")
            
            ws.close()
            
            logger.info("✅ WebSocket connection tests passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ WebSocket connection test failed: {str(e)}")
            return False
    
    def test_api_gateway(self) -> bool:
        """Test API gateway functionality."""
        logger.info("Testing API gateway...")
        
        try:
            # Test gateway health
            response = requests.get(f"{self.base_urls['api_gateway']}/health", timeout=10)
            if response.status_code != 200:
                logger.error(f"API gateway health failed: {response.status_code}")
                return False
            
            # Test CORS headers
            response = requests.options(
                f"{self.base_urls['api_gateway']}/api/test",
                headers={'Origin': 'http://localhost:3000'},
                timeout=5
            )
            
            if 'Access-Control-Allow-Origin' not in response.headers:
                logger.warning("CORS headers not found in API gateway response")
            
            # Test rate limiting (make multiple requests)
            for i in range(5):
                response = requests.get(f"{self.base_urls['api_gateway']}/health", timeout=2)
                if response.status_code == 429:
                    logger.info("Rate limiting is working")
                    break
            
            logger.info("✅ API gateway tests passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ API gateway test failed: {str(e)}")
            return False
    
    def test_asi_module_integration(self) -> Dict[str, bool]:
        """Test integration with ASI modules."""
        logger.info("Testing ASI module integration...")
        
        integration_results = {}
        
        # Test Decision Engine integration
        try:
            response = requests.get(f"{self.base_urls['decision_engine']}/api/decisions", timeout=5)
            integration_results['decision_engine'] = response.status_code in [200, 404]
        except Exception as e:
            integration_results['decision_engine'] = False
            logger.warning(f"Decision Engine integration test failed: {str(e)}")
        
        # Test Learning Engine integration
        try:
            response = requests.get(f"{self.base_urls['learning_engine']}/api/models", timeout=5)
            integration_results['learning_engine'] = response.status_code in [200, 404]
        except Exception as e:
            integration_results['learning_engine'] = False
            logger.warning(f"Learning Engine integration test failed: {str(e)}")
        
        # Test Self-Improvement integration
        try:
            response = requests.get(f"{self.base_urls['self_improvement']}/api/improvements", timeout=5)
            integration_results['self_improvement'] = response.status_code in [200, 404]
        except Exception as e:
            integration_results['self_improvement'] = False
            logger.warning(f"Self-Improvement integration test failed: {str(e)}")
        
        # Test Data Integration
        try:
            response = requests.get(f"{self.base_urls['data_integration']}/api/sources", timeout=5)
            integration_results['data_integration'] = response.status_code in [200, 404]
        except Exception as e:
            integration_results['data_integration'] = False
            logger.warning(f"Data Integration test failed: {str(e)}")
        
        return integration_results
    
    def test_real_time_data_flow(self) -> bool:
        """Test real-time data flow through WebSocket."""
        logger.info("Testing real-time data flow...")
        
        try:
            # Connect to WebSocket
            ws = websocket.create_connection(self.websocket_url, timeout=10)
            
            # Subscribe to real-time updates
            subscribe_message = {
                'type': 'subscribe',
                'topics': ['decisions', 'training', 'improvements', 'metrics']
            }
            
            ws.send(json.dumps(subscribe_message))
            
            # Wait for real-time messages
            messages_received = 0
            ws.settimeout(10)
            
            for _ in range(5):  # Try to receive 5 messages
                try:
                    message = ws.recv()
                    data = json.loads(message)
                    logger.info(f"Received real-time message: {data.get('type', 'unknown')}")
                    messages_received += 1
                except websocket.WebSocketTimeoutException:
                    break
                except json.JSONDecodeError:
                    logger.warning("Received non-JSON message")
            
            ws.close()
            
            if messages_received > 0:
                logger.info(f"✅ Real-time data flow test passed ({messages_received} messages)")
                return True
            else:
                logger.warning("⚠️ No real-time messages received (this may be expected)")
                return True  # Not necessarily a failure
                
        except Exception as e:
            logger.error(f"❌ Real-time data flow test failed: {str(e)}")
            return False
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all integration tests."""
        logger.info("Starting ASI UI/UX Module Integration Tests...")
        logger.info("=" * 50)
        
        # Run tests
        self.test_results = {
            'service_health': self.test_service_health(),
            'react_dashboard': self.test_react_dashboard(),
            'streamlit_inspector': self.test_streamlit_inspector(),
            'websocket_connection': self.test_websocket_connection(),
            'api_gateway': self.test_api_gateway(),
            'asi_module_integration': self.test_asi_module_integration(),
            'real_time_data_flow': self.test_real_time_data_flow(),
        }
        
        # Calculate overall results
        total_tests = 0
        passed_tests = 0
        
        for test_name, result in self.test_results.items():
            if isinstance(result, dict):
                for sub_test, sub_result in result.items():
                    total_tests += 1
                    if sub_result:
                        passed_tests += 1
            else:
                total_tests += 1
                if result:
                    passed_tests += 1
        
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        
        # Print summary
        logger.info("=" * 50)
        logger.info("INTEGRATION TEST SUMMARY")
        logger.info("=" * 50)
        logger.info(f"Total Tests: {total_tests}")
        logger.info(f"Passed: {passed_tests}")
        logger.info(f"Failed: {total_tests - passed_tests}")
        logger.info(f"Success Rate: {success_rate:.1f}%")
        
        if success_rate >= 80:
            logger.info("🎉 Integration tests PASSED!")
        else:
            logger.error("💥 Integration tests FAILED!")
        
        return {
            'results': self.test_results,
            'summary': {
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'failed_tests': total_tests - passed_tests,
                'success_rate': success_rate,
                'overall_status': 'PASSED' if success_rate >= 80 else 'FAILED'
            }
        }

def main():
    """Main test runner."""
    test_suite = ASIUIIntegrationTest()
    results = test_suite.run_all_tests()
    
    # Save results to file
    with open('integration_test_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    # Exit with appropriate code
    exit_code = 0 if results['summary']['overall_status'] == 'PASSED' else 1
    exit(exit_code)

if __name__ == "__main__":
    main()
