import React, { useState, useEffect } from 'react';
import {
  Grid,
  Paper,
  Typography,
  Box,
  Card,
  CardContent,
  CardHeader,
  IconButton,
  Tooltip,
  Chip,
  LinearProgress,
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Settings as SettingsIcon,
  Fullscreen as FullscreenIcon,
  Dashboard as DashboardIcon,
} from '@mui/icons-material';
import { Responsive, WidthProvider, Layout } from 'react-grid-layout';
import { useQuery } from '@tanstack/react-query';

// Components
import SystemOverview from './SystemOverview';
import DecisionFlowChart from './DecisionFlowChart';
import TrainingStatusWidget from './TrainingStatusWidget';
import PerformanceMetricsChart from './PerformanceMetricsChart';
import SystemLogsViewer from './SystemLogsViewer';
import RealTimeMetrics from './RealTimeMetrics';

// Hooks
import { useRealTimeData } from '@/hooks/useWebSocketConnection';

// Services
import { uiGatewayApi } from '@/services/api/ApiService';

// Types
import { DashboardLayout, DashboardWidget, SystemHealth } from '@/types';

// Styles
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';

const ResponsiveGridLayout = WidthProvider(Responsive);

interface DashboardProps {
  layoutId?: string;
}

const Dashboard: React.FC<DashboardProps> = ({ layoutId = 'default' }) => {
  const [currentLayout, setCurrentLayout] = useState<Layout[]>([]);
  const [isEditing, setIsEditing] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(5000);

  // Real-time data from WebSocket
  const { data: realTimeMetrics, isConnected } = useRealTimeData('data_integration', 'metrics');

  // Fetch dashboard layout
  const { data: dashboardLayout, isLoading: layoutLoading, refetch: refetchLayout } = useQuery({
    queryKey: ['dashboard-layout', layoutId],
    queryFn: async () => {
      const response = await uiGatewayApi.get<DashboardLayout>(`/dashboard/layouts/${layoutId}`);
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Fetch system health
  const { data: systemHealth, isLoading: healthLoading, refetch: refetchHealth } = useQuery({
    queryKey: ['system-health'],
    queryFn: async () => {
      const response = await uiGatewayApi.get<SystemHealth>('/system/health');
      return response.data;
    },
    refetchInterval: autoRefresh ? refreshInterval : false,
    staleTime: 30 * 1000, // 30 seconds
  });

  // Default layout configuration
  const defaultLayout: Layout[] = [
    { i: 'system_overview', x: 0, y: 0, w: 6, h: 4, minW: 4, minH: 3 },
    { i: 'decision_flow', x: 6, y: 0, w: 6, h: 4, minW: 4, minH: 3 },
    { i: 'training_status', x: 0, y: 4, w: 4, h: 3, minW: 3, minH: 2 },
    { i: 'performance_metrics', x: 4, y: 4, w: 8, h: 3, minW: 4, minH: 2 },
    { i: 'real_time_metrics', x: 0, y: 7, w: 6, h: 3, minW: 4, minH: 2 },
    { i: 'system_logs', x: 6, y: 7, w: 6, h: 4, minW: 4, minH: 3 },
  ];

  // Initialize layout
  useEffect(() => {
    if (dashboardLayout?.widgets) {
      const layout = dashboardLayout.widgets.map(widget => ({
        i: widget.id,
        x: widget.position.x,
        y: widget.position.y,
        w: widget.position.w,
        h: widget.position.h,
        minW: 2,
        minH: 2,
      }));
      setCurrentLayout(layout);
    } else {
      setCurrentLayout(defaultLayout);
    }
  }, [dashboardLayout]);

  // Handle layout change
  const handleLayoutChange = (layout: Layout[]) => {
    setCurrentLayout(layout);
    
    if (isEditing) {
      // Save layout changes
      const updatedWidgets = layout.map(item => {
        const existingWidget = dashboardLayout?.widgets.find(w => w.id === item.i);
        return {
          ...existingWidget,
          id: item.i,
          position: {
            x: item.x,
            y: item.y,
            w: item.w,
            h: item.h,
          },
        } as DashboardWidget;
      });

      // TODO: Save to backend
      console.log('Saving layout changes:', updatedWidgets);
    }
  };

  // Refresh all data
  const handleRefresh = () => {
    refetchLayout();
    refetchHealth();
  };

  // Render widget content
  const renderWidget = (widgetId: string) => {
    switch (widgetId) {
      case 'system_overview':
        return (
          <SystemOverview
            systemHealth={systemHealth}
            isLoading={healthLoading}
            isConnected={isConnected}
          />
        );
      
      case 'decision_flow':
        return <DecisionFlowChart />;
      
      case 'training_status':
        return <TrainingStatusWidget />;
      
      case 'performance_metrics':
        return <PerformanceMetricsChart />;
      
      case 'real_time_metrics':
        return (
          <RealTimeMetrics
            data={realTimeMetrics}
            isConnected={isConnected}
          />
        );
      
      case 'system_logs':
        return <SystemLogsViewer />;
      
      default:
        return (
          <Box
            display="flex"
            alignItems="center"
            justifyContent="center"
            height="100%"
            color="text.secondary"
          >
            <Typography variant="body2">
              Widget not found: {widgetId}
            </Typography>
          </Box>
        );
    }
  };

  if (layoutLoading) {
    return (
      <Box display="flex" alignItems="center" justifyContent="center" height="100vh">
        <LinearProgress sx={{ width: '50%' }} />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 2, height: '100vh', overflow: 'auto' }}>
      {/* Dashboard Header */}
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        mb={2}
      >
        <Box display="flex" alignItems="center" gap={2}>
          <DashboardIcon color="primary" />
          <Typography variant="h4" component="h1">
            ASI System Dashboard
          </Typography>
          <Chip
            label={isConnected ? 'Connected' : 'Disconnected'}
            color={isConnected ? 'success' : 'error'}
            size="small"
          />
        </Box>

        <Box display="flex" alignItems="center" gap={1}>
          <Tooltip title="Refresh Dashboard">
            <IconButton onClick={handleRefresh} color="primary">
              <RefreshIcon />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Edit Layout">
            <IconButton
              onClick={() => setIsEditing(!isEditing)}
              color={isEditing ? 'secondary' : 'default'}
            >
              <SettingsIcon />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Fullscreen">
            <IconButton
              onClick={() => {
                if (document.fullscreenElement) {
                  document.exitFullscreen();
                } else {
                  document.documentElement.requestFullscreen();
                }
              }}
            >
              <FullscreenIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* System Status Bar */}
      {systemHealth && (
        <Card sx={{ mb: 2 }}>
          <CardContent sx={{ py: 1 }}>
            <Box display="flex" alignItems="center" gap={2}>
              <Typography variant="body2" color="text.secondary">
                System Status:
              </Typography>
              <Chip
                label={systemHealth.status.toUpperCase()}
                color={
                  systemHealth.status === 'healthy' ? 'success' :
                  systemHealth.status === 'degraded' ? 'warning' : 'error'
                }
                size="small"
              />
              <Typography variant="body2" color="text.secondary">
                Uptime: {Math.floor(systemHealth.uptime / 3600)}h {Math.floor((systemHealth.uptime % 3600) / 60)}m
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Version: {systemHealth.version}
              </Typography>
            </Box>
          </CardContent>
        </Card>
      )}

      {/* Dashboard Grid */}
      <ResponsiveGridLayout
        className="layout"
        layouts={{ lg: currentLayout }}
        breakpoints={{ lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 }}
        cols={{ lg: 12, md: 10, sm: 6, xs: 4, xxs: 2 }}
        rowHeight={60}
        margin={[16, 16]}
        containerPadding={[0, 0]}
        isDraggable={isEditing}
        isResizable={isEditing}
        onLayoutChange={handleLayoutChange}
        useCSSTransforms={true}
        preventCollision={false}
        compactType="vertical"
      >
        {currentLayout.map((item) => (
          <Paper
            key={item.i}
            elevation={2}
            sx={{
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              overflow: 'hidden',
              border: isEditing ? '2px dashed' : 'none',
              borderColor: 'primary.main',
            }}
          >
            {renderWidget(item.i)}
          </Paper>
        ))}
      </ResponsiveGridLayout>
    </Box>
  );
};

export default Dashboard;
