/**
 * Real-Time Metrics Component for ASI Dashboard
 * 
 * Displays live system metrics with animated charts and alerts.
 */

import React, { useState, useEffect, useMemo } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Card,
  CardContent,
  LinearProgress,
  Chip,
  Alert,
  IconButton,
  Tooltip,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  Warning,
  Error,
  CheckCircle,
  Refresh,
  Pause,
  PlayArrow
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Pie,
  Cell
} from 'recharts';

// Types
interface MetricValue {
  timestamp: number;
  value: number;
  status?: 'normal' | 'warning' | 'critical';
}

interface SystemMetric {
  id: string;
  name: string;
  unit: string;
  current: number;
  target?: number;
  threshold?: {
    warning: number;
    critical: number;
  };
  history: MetricValue[];
  trend: 'up' | 'down' | 'stable';
  change: number; // Percentage change
}

interface RealTimeMetricsProps {
  data: {
    system: SystemMetric[];
    learning: SystemMetric[];
    decision: SystemMetric[];
    improvement: SystemMetric[];
  };
  isConnected: boolean;
  refreshInterval?: number;
}

const RealTimeMetrics: React.FC<RealTimeMetricsProps> = ({
  data,
  isConnected,
  refreshInterval = 5000
}) => {
  const [isPaused, setIsPaused] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('system');
  const [showAlerts, setShowAlerts] = useState(true);
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Calculate alerts
  const alerts = useMemo(() => {
    const allMetrics = [
      ...data.system,
      ...data.learning,
      ...data.decision,
      ...data.improvement
    ];

    return allMetrics
      .filter(metric => {
        if (!metric.threshold) return false;
        return metric.current >= metric.threshold.warning;
      })
      .map(metric => ({
        id: metric.id,
        name: metric.name,
        value: metric.current,
        severity: metric.current >= (metric.threshold?.critical || Infinity) ? 'critical' : 'warning',
        message: `${metric.name} is ${metric.current}${metric.unit}`
      }));
  }, [data]);

  // Format metric value
  const formatValue = (value: number, unit: string): string => {
    if (unit === '%') return `${value.toFixed(1)}%`;
    if (unit === 'ms') return `${value.toFixed(0)}ms`;
    if (unit === 'MB') return `${(value / 1024).toFixed(1)}GB`;
    if (unit === '/s') return `${value.toFixed(0)}/s`;
    return `${value.toFixed(2)}${unit}`;
  };

  // Get status color
  const getStatusColor = (metric: SystemMetric): string => {
    if (!metric.threshold) return 'success';
    if (metric.current >= metric.threshold.critical) return 'error';
    if (metric.current >= metric.threshold.warning) return 'warning';
    return 'success';
  };

  // Get trend icon
  const getTrendIcon = (trend: string, change: number) => {
    if (trend === 'up') {
      return <TrendingUp color={change > 0 ? 'success' : 'error'} />;
    } else if (trend === 'down') {
      return <TrendingDown color={change < 0 ? 'success' : 'error'} />;
    }
    return <CheckCircle color="success" />;
  };

  // Metric card component
  const MetricCard: React.FC<{ metric: SystemMetric }> = ({ metric }) => {
    const statusColor = getStatusColor(metric);
    const progress = metric.target ? (metric.current / metric.target) * 100 : 0;

    return (
      <Card elevation={2} sx={{ height: '100%' }}>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={1}>
            <Typography variant="h6" component="div" noWrap>
              {metric.name}
            </Typography>
            <Box display="flex" alignItems="center" gap={0.5}>
              {getTrendIcon(metric.trend, metric.change)}
              <Typography
                variant="caption"
                color={metric.change >= 0 ? 'success.main' : 'error.main'}
              >
                {metric.change >= 0 ? '+' : ''}{metric.change.toFixed(1)}%
              </Typography>
            </Box>
          </Box>

          <Typography variant="h4" component="div" color={`${statusColor}.main`}>
            {formatValue(metric.current, metric.unit)}
          </Typography>

          {metric.target && (
            <Box mt={1}>
              <Box display="flex" justifyContent="space-between" mb={0.5}>
                <Typography variant="caption">
                  Target: {formatValue(metric.target, metric.unit)}
                </Typography>
                <Typography variant="caption">
                  {progress.toFixed(1)}%
                </Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={Math.min(progress, 100)}
                color={statusColor as any}
                sx={{ height: 6, borderRadius: 3 }}
              />
            </Box>
          )}

          {metric.threshold && (
            <Box mt={1}>
              <Box display="flex" gap={1}>
                <Chip
                  label={`Warn: ${formatValue(metric.threshold.warning, metric.unit)}`}
                  size="small"
                  color="warning"
                  variant="outlined"
                />
                <Chip
                  label={`Crit: ${formatValue(metric.threshold.critical, metric.unit)}`}
                  size="small"
                  color="error"
                  variant="outlined"
                />
              </Box>
            </Box>
          )}

          {/* Mini chart */}
          <Box mt={2} height={60}>
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={metric.history.slice(-20)}>
                <Area
                  type="monotone"
                  dataKey="value"
                  stroke={statusColor === 'error' ? '#f44336' : statusColor === 'warning' ? '#ff9800' : '#4caf50'}
                  fill={statusColor === 'error' ? '#f44336' : statusColor === 'warning' ? '#ff9800' : '#4caf50'}
                  fillOpacity={0.3}
                />
                <XAxis hide />
                <YAxis hide />
              </AreaChart>
            </ResponsiveContainer>
          </Box>
        </CardContent>
      </Card>
    );
  };

  // Chart component for detailed view
  const DetailedChart: React.FC<{ metrics: SystemMetric[] }> = ({ metrics }) => {
    const chartData = useMemo(() => {
      if (!metrics.length) return [];
      
      const maxLength = Math.max(...metrics.map(m => m.history.length));
      const result = [];
      
      for (let i = 0; i < Math.min(maxLength, 50); i++) {
        const point: any = { timestamp: Date.now() - (maxLength - i) * 5000 };
        
        metrics.forEach(metric => {
          if (metric.history[i]) {
            point[metric.id] = metric.history[i].value;
          }
        });
        
        result.push(point);
      }
      
      return result;
    }, [metrics]);

    const colors = ['#2196F3', '#4CAF50', '#FF9800', '#F44336', '#9C27B0'];

    return (
      <Box height={300}>
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={chartData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis
              dataKey="timestamp"
              type="number"
              scale="time"
              domain={['dataMin', 'dataMax']}
              tickFormatter={(value) => new Date(value).toLocaleTimeString()}
            />
            <YAxis />
            <RechartsTooltip
              labelFormatter={(value) => new Date(value).toLocaleString()}
              formatter={(value: number, name: string) => {
                const metric = metrics.find(m => m.id === name);
                return [formatValue(value, metric?.unit || ''), metric?.name || name];
              }}
            />
            {metrics.map((metric, index) => (
              <Line
                key={metric.id}
                type="monotone"
                dataKey={metric.id}
                stroke={colors[index % colors.length]}
                strokeWidth={2}
                dot={false}
                connectNulls={false}
              />
            ))}
          </LineChart>
        </ResponsiveContainer>
      </Box>
    );
  };

  return (
    <Box sx={{ p: 2 }}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Typography variant="h5" component="h2">
          Real-Time Metrics
        </Typography>
        
        <Box display="flex" alignItems="center" gap={2}>
          <Chip
            label={isConnected ? 'Connected' : 'Disconnected'}
            color={isConnected ? 'success' : 'error'}
            size="small"
          />
          
          <FormControlLabel
            control={
              <Switch
                checked={showAlerts}
                onChange={(e) => setShowAlerts(e.target.checked)}
                size="small"
              />
            }
            label="Alerts"
          />
          
          <FormControlLabel
            control={
              <Switch
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh(e.target.checked)}
                size="small"
              />
            }
            label="Auto Refresh"
          />
          
          <Tooltip title={isPaused ? 'Resume' : 'Pause'}>
            <IconButton onClick={() => setIsPaused(!isPaused)}>
              {isPaused ? <PlayArrow /> : <Pause />}
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Alerts */}
      {showAlerts && alerts.length > 0 && (
        <Box mb={2}>
          {alerts.map((alert) => (
            <Alert
              key={alert.id}
              severity={alert.severity as any}
              sx={{ mb: 1 }}
              icon={alert.severity === 'critical' ? <Error /> : <Warning />}
            >
              <strong>{alert.name}:</strong> {alert.message}
            </Alert>
          ))}
        </Box>
      )}

      {/* Category Tabs */}
      <Box mb={2}>
        <Box display="flex" gap={1} flexWrap="wrap">
          {Object.keys(data).map((category) => (
            <Chip
              key={category}
              label={category.charAt(0).toUpperCase() + category.slice(1)}
              onClick={() => setSelectedCategory(category)}
              color={selectedCategory === category ? 'primary' : 'default'}
              variant={selectedCategory === category ? 'filled' : 'outlined'}
            />
          ))}
        </Box>
      </Box>

      {/* Metrics Grid */}
      <Grid container spacing={2} mb={3}>
        {data[selectedCategory as keyof typeof data]?.map((metric) => (
          <Grid item xs={12} sm={6} md={4} lg={3} key={metric.id}>
            <MetricCard metric={metric} />
          </Grid>
        ))}
      </Grid>

      {/* Detailed Chart */}
      <Paper elevation={2} sx={{ p: 2 }}>
        <Typography variant="h6" gutterBottom>
          {selectedCategory.charAt(0).toUpperCase() + selectedCategory.slice(1)} Metrics Trend
        </Typography>
        <DetailedChart metrics={data[selectedCategory as keyof typeof data] || []} />
      </Paper>

      {/* System Overview */}
      <Grid container spacing={2} mt={2}>
        <Grid item xs={12} md={6}>
          <Paper elevation={2} sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              System Health Overview
            </Typography>
            <Box height={200}>
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={[
                      { name: 'Healthy', value: data.system.filter(m => getStatusColor(m) === 'success').length, fill: '#4CAF50' },
                      { name: 'Warning', value: data.system.filter(m => getStatusColor(m) === 'warning').length, fill: '#FF9800' },
                      { name: 'Critical', value: data.system.filter(m => getStatusColor(m) === 'error').length, fill: '#F44336' }
                    ]}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    dataKey="value"
                    label={({ name, value }) => `${name}: ${value}`}
                  >
                    {[
                      { fill: '#4CAF50' },
                      { fill: '#FF9800' },
                      { fill: '#F44336' }
                    ].map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.fill} />
                    ))}
                  </Pie>
                  <RechartsTooltip />
                </PieChart>
              </ResponsiveContainer>
            </Box>
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper elevation={2} sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Performance Summary
            </Typography>
            <Box>
              {Object.entries(data).map(([category, metrics]) => {
                const avgPerformance = metrics.reduce((acc, m) => acc + m.current, 0) / metrics.length;
                const healthyCount = metrics.filter(m => getStatusColor(m) === 'success').length;
                const healthPercentage = (healthyCount / metrics.length) * 100;
                
                return (
                  <Box key={category} mb={2}>
                    <Box display="flex" justifyContent="space-between" mb={0.5}>
                      <Typography variant="body2">
                        {category.charAt(0).toUpperCase() + category.slice(1)}
                      </Typography>
                      <Typography variant="body2">
                        {healthPercentage.toFixed(0)}% Healthy
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={healthPercentage}
                      color={healthPercentage >= 80 ? 'success' : healthPercentage >= 60 ? 'warning' : 'error'}
                      sx={{ height: 8, borderRadius: 4 }}
                    />
                  </Box>
                );
              })}
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default RealTimeMetrics;
