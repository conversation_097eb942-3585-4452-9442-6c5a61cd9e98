/**
 * D3.js Network Graph Component for ASI System Visualization
 * 
 * Provides interactive network visualization for system components,
 * data flow, decision trees, and model relationships.
 */

import React, { useEffect, useRef, useState, useCallback } from 'react';
import * as d3 from 'd3';
import { Box, Paper, Typography, IconButton, Tooltip, Chip } from '@mui/material';
import { ZoomIn, ZoomOut, CenterFocusStrong, Settings } from '@mui/icons-material';

// Types
interface Node {
  id: string;
  name: string;
  type: 'module' | 'service' | 'model' | 'data' | 'decision';
  status: 'active' | 'inactive' | 'error' | 'warning';
  metrics?: {
    cpu?: number;
    memory?: number;
    latency?: number;
    throughput?: number;
  };
  x?: number;
  y?: number;
  fx?: number | null;
  fy?: number | null;
}

interface Link {
  source: string | Node;
  target: string | Node;
  type: 'data' | 'control' | 'feedback';
  strength: number;
  latency?: number;
  throughput?: number;
}

interface NetworkData {
  nodes: Node[];
  links: Link[];
}

interface D3NetworkGraphProps {
  data: NetworkData;
  width?: number;
  height?: number;
  onNodeClick?: (node: Node) => void;
  onLinkClick?: (link: Link) => void;
  showMetrics?: boolean;
  interactive?: boolean;
  theme?: 'light' | 'dark';
}

const D3NetworkGraph: React.FC<D3NetworkGraphProps> = ({
  data,
  width = 800,
  height = 600,
  onNodeClick,
  onLinkClick,
  showMetrics = true,
  interactive = true,
  theme = 'dark'
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const [zoom, setZoom] = useState(1);
  const [selectedNode, setSelectedNode] = useState<Node | null>(null);
  const [simulation, setSimulation] = useState<d3.Simulation<Node, Link> | null>(null);

  // Color schemes
  const colors = {
    light: {
      background: '#ffffff',
      text: '#333333',
      nodes: {
        module: '#2196F3',
        service: '#4CAF50',
        model: '#FF9800',
        data: '#9C27B0',
        decision: '#F44336'
      },
      links: {
        data: '#666666',
        control: '#2196F3',
        feedback: '#FF9800'
      },
      status: {
        active: '#4CAF50',
        inactive: '#9E9E9E',
        error: '#F44336',
        warning: '#FF9800'
      }
    },
    dark: {
      background: '#1a1a1a',
      text: '#ffffff',
      nodes: {
        module: '#64B5F6',
        service: '#81C784',
        model: '#FFB74D',
        data: '#BA68C8',
        decision: '#E57373'
      },
      links: {
        data: '#999999',
        control: '#64B5F6',
        feedback: '#FFB74D'
      },
      status: {
        active: '#81C784',
        inactive: '#616161',
        error: '#E57373',
        warning: '#FFB74D'
      }
    }
  };

  const currentColors = colors[theme];

  // Initialize D3 simulation
  useEffect(() => {
    if (!svgRef.current || !data.nodes.length) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    // Create main group for zooming
    const g = svg.append('g').attr('class', 'main-group');

    // Create simulation
    const sim = d3.forceSimulation<Node>(data.nodes)
      .force('link', d3.forceLink<Node, Link>(data.links)
        .id(d => d.id)
        .distance(d => 100 + (d.strength * 50))
        .strength(d => d.strength * 0.5)
      )
      .force('charge', d3.forceManyBody().strength(-300))
      .force('center', d3.forceCenter(width / 2, height / 2))
      .force('collision', d3.forceCollide().radius(30));

    // Create arrow markers for directed links
    const defs = svg.append('defs');
    
    Object.keys(currentColors.links).forEach(linkType => {
      defs.append('marker')
        .attr('id', `arrow-${linkType}`)
        .attr('viewBox', '0 -5 10 10')
        .attr('refX', 25)
        .attr('refY', 0)
        .attr('markerWidth', 6)
        .attr('markerHeight', 6)
        .attr('orient', 'auto')
        .append('path')
        .attr('d', 'M0,-5L10,0L0,5')
        .attr('fill', currentColors.links[linkType as keyof typeof currentColors.links]);
    });

    // Create links
    const link = g.append('g')
      .attr('class', 'links')
      .selectAll('line')
      .data(data.links)
      .enter().append('line')
      .attr('stroke', d => currentColors.links[d.type as keyof typeof currentColors.links])
      .attr('stroke-width', d => Math.max(1, d.strength * 3))
      .attr('stroke-opacity', 0.6)
      .attr('marker-end', d => `url(#arrow-${d.type})`)
      .style('cursor', interactive ? 'pointer' : 'default');

    // Create nodes
    const node = g.append('g')
      .attr('class', 'nodes')
      .selectAll('g')
      .data(data.nodes)
      .enter().append('g')
      .attr('class', 'node')
      .style('cursor', interactive ? 'pointer' : 'default');

    // Node circles
    node.append('circle')
      .attr('r', d => {
        const baseRadius = 15;
        const metricBonus = d.metrics ? 5 : 0;
        return baseRadius + metricBonus;
      })
      .attr('fill', d => currentColors.nodes[d.type as keyof typeof currentColors.nodes])
      .attr('stroke', d => currentColors.status[d.status as keyof typeof currentColors.status])
      .attr('stroke-width', 3);

    // Node labels
    node.append('text')
      .text(d => d.name)
      .attr('x', 0)
      .attr('y', -25)
      .attr('text-anchor', 'middle')
      .attr('fill', currentColors.text)
      .attr('font-size', '12px')
      .attr('font-weight', 'bold');

    // Metrics display
    if (showMetrics) {
      node.filter(d => d.metrics)
        .append('text')
        .text(d => {
          if (d.metrics?.cpu) return `CPU: ${d.metrics.cpu.toFixed(1)}%`;
          if (d.metrics?.latency) return `${d.metrics.latency.toFixed(0)}ms`;
          return '';
        })
        .attr('x', 0)
        .attr('y', 35)
        .attr('text-anchor', 'middle')
        .attr('fill', currentColors.text)
        .attr('font-size', '10px');
    }

    // Event handlers
    if (interactive) {
      node.on('click', (event, d) => {
        setSelectedNode(d);
        onNodeClick?.(d);
      });

      link.on('click', (event, d) => {
        onLinkClick?.(d);
      });

      // Drag behavior
      const drag = d3.drag<SVGGElement, Node>()
        .on('start', (event, d) => {
          if (!event.active) sim.alphaTarget(0.3).restart();
          d.fx = d.x;
          d.fy = d.y;
        })
        .on('drag', (event, d) => {
          d.fx = event.x;
          d.fy = event.y;
        })
        .on('end', (event, d) => {
          if (!event.active) sim.alphaTarget(0);
          d.fx = null;
          d.fy = null;
        });

      node.call(drag);
    }

    // Zoom behavior
    const zoomBehavior = d3.zoom<SVGSVGElement, unknown>()
      .scaleExtent([0.1, 4])
      .on('zoom', (event) => {
        g.attr('transform', event.transform);
        setZoom(event.transform.k);
      });

    svg.call(zoomBehavior);

    // Simulation tick
    sim.on('tick', () => {
      link
        .attr('x1', d => (d.source as Node).x!)
        .attr('y1', d => (d.source as Node).y!)
        .attr('x2', d => (d.target as Node).x!)
        .attr('y2', d => (d.target as Node).y!);

      node.attr('transform', d => `translate(${d.x},${d.y})`);
    });

    setSimulation(sim);

    return () => {
      sim.stop();
    };
  }, [data, width, height, theme, showMetrics, interactive]);

  // Zoom controls
  const handleZoomIn = useCallback(() => {
    if (svgRef.current) {
      const svg = d3.select(svgRef.current);
      svg.transition().call(
        d3.zoom<SVGSVGElement, unknown>().scaleBy as any,
        1.5
      );
    }
  }, []);

  const handleZoomOut = useCallback(() => {
    if (svgRef.current) {
      const svg = d3.select(svgRef.current);
      svg.transition().call(
        d3.zoom<SVGSVGElement, unknown>().scaleBy as any,
        1 / 1.5
      );
    }
  }, []);

  const handleResetZoom = useCallback(() => {
    if (svgRef.current) {
      const svg = d3.select(svgRef.current);
      svg.transition().call(
        d3.zoom<SVGSVGElement, unknown>().transform as any,
        d3.zoomIdentity
      );
    }
  }, []);

  const handleRestartSimulation = useCallback(() => {
    if (simulation) {
      simulation.alpha(1).restart();
    }
  }, [simulation]);

  return (
    <Paper
      elevation={2}
      sx={{
        position: 'relative',
        width: '100%',
        height: '100%',
        overflow: 'hidden',
        backgroundColor: currentColors.background
      }}
    >
      {/* Header */}
      <Box
        sx={{
          position: 'absolute',
          top: 8,
          left: 8,
          right: 8,
          zIndex: 10,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}
      >
        <Typography variant="h6" sx={{ color: currentColors.text }}>
          ASI System Network
        </Typography>
        
        <Box display="flex" gap={1}>
          <Chip
            label={`Nodes: ${data.nodes.length}`}
            size="small"
            variant="outlined"
          />
          <Chip
            label={`Links: ${data.links.length}`}
            size="small"
            variant="outlined"
          />
          <Chip
            label={`Zoom: ${(zoom * 100).toFixed(0)}%`}
            size="small"
            variant="outlined"
          />
        </Box>
      </Box>

      {/* Controls */}
      <Box
        sx={{
          position: 'absolute',
          top: 8,
          right: 8,
          zIndex: 10,
          display: 'flex',
          flexDirection: 'column',
          gap: 1
        }}
      >
        <Tooltip title="Zoom In">
          <IconButton size="small" onClick={handleZoomIn}>
            <ZoomIn />
          </IconButton>
        </Tooltip>
        <Tooltip title="Zoom Out">
          <IconButton size="small" onClick={handleZoomOut}>
            <ZoomOut />
          </IconButton>
        </Tooltip>
        <Tooltip title="Reset View">
          <IconButton size="small" onClick={handleResetZoom}>
            <CenterFocusStrong />
          </IconButton>
        </Tooltip>
        <Tooltip title="Restart Simulation">
          <IconButton size="small" onClick={handleRestartSimulation}>
            <Settings />
          </IconButton>
        </Tooltip>
      </Box>

      {/* SVG Container */}
      <svg
        ref={svgRef}
        width={width}
        height={height}
        style={{ width: '100%', height: '100%' }}
      />

      {/* Node Details Panel */}
      {selectedNode && (
        <Paper
          elevation={4}
          sx={{
            position: 'absolute',
            bottom: 16,
            left: 16,
            p: 2,
            minWidth: 200,
            maxWidth: 300,
            zIndex: 10
          }}
        >
          <Typography variant="h6" gutterBottom>
            {selectedNode.name}
          </Typography>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            Type: {selectedNode.type}
          </Typography>
          <Chip
            label={selectedNode.status}
            size="small"
            color={
              selectedNode.status === 'active' ? 'success' :
              selectedNode.status === 'error' ? 'error' :
              selectedNode.status === 'warning' ? 'warning' : 'default'
            }
            sx={{ mb: 1 }}
          />
          
          {selectedNode.metrics && (
            <Box mt={1}>
              <Typography variant="subtitle2" gutterBottom>
                Metrics:
              </Typography>
              {selectedNode.metrics.cpu && (
                <Typography variant="body2">
                  CPU: {selectedNode.metrics.cpu.toFixed(1)}%
                </Typography>
              )}
              {selectedNode.metrics.memory && (
                <Typography variant="body2">
                  Memory: {selectedNode.metrics.memory.toFixed(1)}%
                </Typography>
              )}
              {selectedNode.metrics.latency && (
                <Typography variant="body2">
                  Latency: {selectedNode.metrics.latency.toFixed(0)}ms
                </Typography>
              )}
              {selectedNode.metrics.throughput && (
                <Typography variant="body2">
                  Throughput: {selectedNode.metrics.throughput.toFixed(0)}/s
                </Typography>
              )}
            </Box>
          )}
        </Paper>
      )}

      {/* Legend */}
      <Paper
        elevation={2}
        sx={{
          position: 'absolute',
          top: 60,
          left: 8,
          p: 1,
          zIndex: 10
        }}
      >
        <Typography variant="caption" gutterBottom>
          Node Types:
        </Typography>
        {Object.entries(currentColors.nodes).map(([type, color]) => (
          <Box key={type} display="flex" alignItems="center" gap={1} mb={0.5}>
            <Box
              sx={{
                width: 12,
                height: 12,
                borderRadius: '50%',
                backgroundColor: color
              }}
            />
            <Typography variant="caption">
              {type.charAt(0).toUpperCase() + type.slice(1)}
            </Typography>
          </Box>
        ))}
      </Paper>
    </Paper>
  );
};

export default D3NetworkGraph;
