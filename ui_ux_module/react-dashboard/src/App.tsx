import React, { Suspense, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ThemeProvider, createTheme, CssBaseline } from '@mui/material';
import { HelmetProvider } from 'react-helmet-async';
import { Toaster } from 'react-hot-toast';

// Components
import Layout from '@/components/Layout/Layout';
import Dashboard from '@/components/Dashboard/Dashboard';
import ModelInspector from '@/components/Models/ModelInspector';
import SystemMonitoring from '@/components/Monitoring/SystemMonitoring';
import DecisionFlow from '@/components/Dashboard/DecisionFlow';
import PerformanceAnalytics from '@/components/Analytics/PerformanceAnalytics';
import LoadingSpinner from '@/components/Common/LoadingSpinner';
import ErrorBoundary from '@/components/Common/ErrorBoundary';

// Hooks
import { useThemeStore } from '@/hooks/useThemeStore';
import { useWebSocketConnection } from '@/hooks/useWebSocketConnection';

// Services
import { initializeServices } from '@/services/api/serviceInitializer';

// Styles
import '@/styles/global.css';

// Create React Query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      refetchOnWindowFocus: false,
      refetchOnReconnect: true,
    },
    mutations: {
      retry: 1,
    },
  },
});

const App: React.FC = () => {
  const { theme, isDarkMode } = useThemeStore();
  
  // Initialize WebSocket connections
  useWebSocketConnection();

  // Create MUI theme
  const muiTheme = createTheme({
    palette: {
      mode: isDarkMode ? 'dark' : 'light',
      primary: {
        main: theme.primaryColor,
      },
      secondary: {
        main: theme.secondaryColor,
      },
      background: {
        default: isDarkMode ? '#121212' : '#f5f5f5',
        paper: isDarkMode ? '#1e1e1e' : '#ffffff',
      },
    },
    typography: {
      fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
      h1: {
        fontSize: '2.5rem',
        fontWeight: 600,
      },
      h2: {
        fontSize: '2rem',
        fontWeight: 600,
      },
      h3: {
        fontSize: '1.75rem',
        fontWeight: 500,
      },
      h4: {
        fontSize: '1.5rem',
        fontWeight: 500,
      },
      h5: {
        fontSize: '1.25rem',
        fontWeight: 500,
      },
      h6: {
        fontSize: '1rem',
        fontWeight: 500,
      },
    },
    components: {
      MuiCssBaseline: {
        styleOverrides: {
          body: {
            scrollbarColor: isDarkMode ? '#6b6b6b #2b2b2b' : '#c1c1c1 #f1f1f1',
            '&::-webkit-scrollbar, & *::-webkit-scrollbar': {
              backgroundColor: isDarkMode ? '#2b2b2b' : '#f1f1f1',
              width: '8px',
            },
            '&::-webkit-scrollbar-thumb, & *::-webkit-scrollbar-thumb': {
              borderRadius: 8,
              backgroundColor: isDarkMode ? '#6b6b6b' : '#c1c1c1',
              minHeight: 24,
            },
            '&::-webkit-scrollbar-thumb:focus, & *::-webkit-scrollbar-thumb:focus': {
              backgroundColor: isDarkMode ? '#959595' : '#a8a8a8',
            },
            '&::-webkit-scrollbar-thumb:active, & *::-webkit-scrollbar-thumb:active': {
              backgroundColor: isDarkMode ? '#959595' : '#a8a8a8',
            },
            '&::-webkit-scrollbar-thumb:hover, & *::-webkit-scrollbar-thumb:hover': {
              backgroundColor: isDarkMode ? '#959595' : '#a8a8a8',
            },
          },
        },
      },
    },
  });

  // Initialize services on app start
  useEffect(() => {
    initializeServices().catch((error) => {
      console.error('Failed to initialize services:', error);
    });
  }, []);

  return (
    <ErrorBoundary>
      <HelmetProvider>
        <QueryClientProvider client={queryClient}>
          <ThemeProvider theme={muiTheme}>
            <CssBaseline />
            <Router>
              <Layout>
                <Suspense fallback={<LoadingSpinner />}>
                  <Routes>
                    {/* Main Dashboard */}
                    <Route path="/" element={<Dashboard />} />
                    
                    {/* System Monitoring */}
                    <Route path="/monitoring" element={<SystemMonitoring />} />
                    
                    {/* Model Inspector */}
                    <Route path="/models" element={<ModelInspector />} />
                    <Route path="/models/:modelId" element={<ModelInspector />} />
                    
                    {/* Decision Flow */}
                    <Route path="/decisions" element={<DecisionFlow />} />
                    
                    {/* Performance Analytics */}
                    <Route path="/analytics" element={<PerformanceAnalytics />} />
                    
                    {/* Catch-all route */}
                    <Route path="*" element={<Dashboard />} />
                  </Routes>
                </Suspense>
              </Layout>
            </Router>
            
            {/* Global Toast Notifications */}
            <Toaster
              position="top-right"
              toastOptions={{
                duration: 4000,
                style: {
                  background: isDarkMode ? '#333' : '#fff',
                  color: isDarkMode ? '#fff' : '#333',
                },
                success: {
                  iconTheme: {
                    primary: theme.primaryColor,
                    secondary: '#fff',
                  },
                },
                error: {
                  iconTheme: {
                    primary: '#f44336',
                    secondary: '#fff',
                  },
                },
              }}
            />
          </ThemeProvider>
        </QueryClientProvider>
      </HelmetProvider>
    </ErrorBoundary>
  );
};

export default App;
