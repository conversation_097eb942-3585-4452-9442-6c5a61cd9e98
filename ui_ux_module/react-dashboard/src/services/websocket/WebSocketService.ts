import { io, Socket } from 'socket.io-client';
import { WebSocketMessage, WebSocketConnection } from '@/types';

export type WebSocketEventHandler = (message: WebSocketMessage) => void;

export interface WebSocketServiceConfig {
  url: string;
  reconnectAttempts?: number;
  reconnectDelay?: number;
  timeout?: number;
}

export class WebSocketService {
  private socket: Socket | null = null;
  private config: WebSocketServiceConfig;
  private eventHandlers: Map<string, Set<WebSocketEventHandler>> = new Map();
  private connectionStatus: WebSocketConnection['status'] = 'disconnected';
  private reconnectAttempts = 0;
  private reconnectTimer: NodeJS.Timeout | null = null;

  constructor(config: WebSocketServiceConfig) {
    this.config = {
      reconnectAttempts: 5,
      reconnectDelay: 1000,
      timeout: 10000,
      ...config,
    };
  }

  /**
   * Connect to WebSocket server
   */
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.socket?.connected) {
        resolve();
        return;
      }

      this.connectionStatus = 'connecting';
      
      this.socket = io(this.config.url, {
        timeout: this.config.timeout,
        transports: ['websocket', 'polling'],
        upgrade: true,
        rememberUpgrade: true,
      });

      // Connection successful
      this.socket.on('connect', () => {
        console.log('WebSocket connected to:', this.config.url);
        this.connectionStatus = 'connected';
        this.reconnectAttempts = 0;
        this.clearReconnectTimer();
        resolve();
      });

      // Connection error
      this.socket.on('connect_error', (error) => {
        console.error('WebSocket connection error:', error);
        this.connectionStatus = 'error';
        this.handleReconnect();
        reject(error);
      });

      // Disconnection
      this.socket.on('disconnect', (reason) => {
        console.log('WebSocket disconnected:', reason);
        this.connectionStatus = 'disconnected';
        
        // Attempt reconnection for unexpected disconnections
        if (reason === 'io server disconnect') {
          // Server initiated disconnect, don't reconnect
          return;
        }
        
        this.handleReconnect();
      });

      // Handle incoming messages
      this.socket.onAny((eventName: string, data: any) => {
        const message: WebSocketMessage = {
          type: eventName,
          payload: data,
          timestamp: new Date().toISOString(),
          source: 'websocket',
        };

        this.handleMessage(message);
      });

      // Set connection timeout
      setTimeout(() => {
        if (this.connectionStatus === 'connecting') {
          this.connectionStatus = 'error';
          reject(new Error('Connection timeout'));
        }
      }, this.config.timeout);
    });
  }

  /**
   * Disconnect from WebSocket server
   */
  disconnect(): void {
    this.clearReconnectTimer();
    
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    
    this.connectionStatus = 'disconnected';
    this.eventHandlers.clear();
  }

  /**
   * Subscribe to specific event types
   */
  subscribe(eventType: string, handler: WebSocketEventHandler): () => void {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, new Set());
    }
    
    this.eventHandlers.get(eventType)!.add(handler);

    // Return unsubscribe function
    return () => {
      const handlers = this.eventHandlers.get(eventType);
      if (handlers) {
        handlers.delete(handler);
        if (handlers.size === 0) {
          this.eventHandlers.delete(eventType);
        }
      }
    };
  }

  /**
   * Send message to server
   */
  send(eventType: string, data: any): void {
    if (!this.socket?.connected) {
      console.warn('Cannot send message: WebSocket not connected');
      return;
    }

    this.socket.emit(eventType, data);
  }

  /**
   * Get current connection status
   */
  getStatus(): WebSocketConnection['status'] {
    return this.connectionStatus;
  }

  /**
   * Check if connected
   */
  isConnected(): boolean {
    return this.socket?.connected ?? false;
  }

  /**
   * Handle incoming messages
   */
  private handleMessage(message: WebSocketMessage): void {
    const handlers = this.eventHandlers.get(message.type);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(message);
        } catch (error) {
          console.error('Error in WebSocket message handler:', error);
        }
      });
    }

    // Also trigger handlers for 'all' events
    const allHandlers = this.eventHandlers.get('*');
    if (allHandlers) {
      allHandlers.forEach(handler => {
        try {
          handler(message);
        } catch (error) {
          console.error('Error in WebSocket message handler:', error);
        }
      });
    }
  }

  /**
   * Handle reconnection logic
   */
  private handleReconnect(): void {
    if (this.reconnectAttempts >= (this.config.reconnectAttempts || 5)) {
      console.error('Max reconnection attempts reached');
      this.connectionStatus = 'error';
      return;
    }

    this.reconnectAttempts++;
    const delay = this.config.reconnectDelay! * Math.pow(2, this.reconnectAttempts - 1);
    
    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`);
    
    this.reconnectTimer = setTimeout(() => {
      this.connect().catch(error => {
        console.error('Reconnection failed:', error);
      });
    }, delay);
  }

  /**
   * Clear reconnection timer
   */
  private clearReconnectTimer(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }
}

// Singleton instance for the main WebSocket connection
let webSocketService: WebSocketService | null = null;

/**
 * Get or create the main WebSocket service instance
 */
export function getWebSocketService(config?: WebSocketServiceConfig): WebSocketService {
  if (!webSocketService && config) {
    webSocketService = new WebSocketService(config);
  }
  
  if (!webSocketService) {
    throw new Error('WebSocket service not initialized. Provide config on first call.');
  }
  
  return webSocketService;
}

/**
 * Initialize WebSocket service with default configuration
 */
export function initializeWebSocketService(): WebSocketService {
  const config: WebSocketServiceConfig = {
    url: process.env.REACT_APP_WEBSOCKET_URL || 'ws://localhost:8080',
    reconnectAttempts: 5,
    reconnectDelay: 1000,
    timeout: 10000,
  };

  return getWebSocketService(config);
}
