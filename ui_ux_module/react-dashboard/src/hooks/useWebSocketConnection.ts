import { useEffect, useRef, useState, useCallback } from 'react';
import { WebSocketService, initializeWebSocketService } from '@/services/websocket/WebSocketService';
import { WebSocketMessage, WebSocketConnection } from '@/types';

export interface UseWebSocketOptions {
  autoConnect?: boolean;
  reconnectOnMount?: boolean;
  subscriptions?: string[];
}

export interface UseWebSocketReturn {
  connectionStatus: WebSocketConnection['status'];
  lastMessage: WebSocketMessage | null;
  sendMessage: (type: string, data: any) => void;
  subscribe: (eventType: string, handler: (message: WebSocketMessage) => void) => () => void;
  connect: () => Promise<void>;
  disconnect: () => void;
  isConnected: boolean;
}

/**
 * Hook for managing WebSocket connections
 */
export function useWebSocketConnection(options: UseWebSocketOptions = {}): UseWebSocketReturn {
  const {
    autoConnect = true,
    reconnectOnMount = true,
    subscriptions = [],
  } = options;

  const [connectionStatus, setConnectionStatus] = useState<WebSocketConnection['status']>('disconnected');
  const [lastMessage, setLastMessage] = useState<WebSocketMessage | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  
  const webSocketService = useRef<WebSocketService | null>(null);
  const subscriptionRefs = useRef<Map<string, () => void>>(new Map());

  // Initialize WebSocket service
  useEffect(() => {
    if (!webSocketService.current) {
      webSocketService.current = initializeWebSocketService();
    }
  }, []);

  // Monitor connection status
  useEffect(() => {
    if (!webSocketService.current) return;

    const checkStatus = () => {
      const status = webSocketService.current!.getStatus();
      const connected = webSocketService.current!.isConnected();
      
      setConnectionStatus(status);
      setIsConnected(connected);
    };

    // Check status immediately
    checkStatus();

    // Set up periodic status checks
    const statusInterval = setInterval(checkStatus, 1000);

    return () => {
      clearInterval(statusInterval);
    };
  }, []);

  // Auto-connect on mount
  useEffect(() => {
    if (autoConnect && webSocketService.current && connectionStatus === 'disconnected') {
      connect();
    }
  }, [autoConnect, connectionStatus]);

  // Set up subscriptions
  useEffect(() => {
    if (!webSocketService.current || !isConnected) return;

    // Subscribe to specified event types
    subscriptions.forEach(eventType => {
      if (!subscriptionRefs.current.has(eventType)) {
        const unsubscribe = webSocketService.current!.subscribe(eventType, (message) => {
          setLastMessage(message);
        });
        subscriptionRefs.current.set(eventType, unsubscribe);
      }
    });

    // Subscribe to all messages for debugging
    if (!subscriptionRefs.current.has('*')) {
      const unsubscribe = webSocketService.current.subscribe('*', (message) => {
        console.log('WebSocket message received:', message);
        setLastMessage(message);
      });
      subscriptionRefs.current.set('*', unsubscribe);
    }

    return () => {
      // Clean up subscriptions
      subscriptionRefs.current.forEach(unsubscribe => unsubscribe());
      subscriptionRefs.current.clear();
    };
  }, [isConnected, subscriptions]);

  // Connect function
  const connect = useCallback(async (): Promise<void> => {
    if (!webSocketService.current) {
      throw new Error('WebSocket service not initialized');
    }

    try {
      await webSocketService.current.connect();
    } catch (error) {
      console.error('Failed to connect to WebSocket:', error);
      throw error;
    }
  }, []);

  // Disconnect function
  const disconnect = useCallback((): void => {
    if (webSocketService.current) {
      webSocketService.current.disconnect();
    }
  }, []);

  // Send message function
  const sendMessage = useCallback((type: string, data: any): void => {
    if (!webSocketService.current) {
      console.warn('Cannot send message: WebSocket service not initialized');
      return;
    }

    webSocketService.current.send(type, data);
  }, []);

  // Subscribe function
  const subscribe = useCallback((
    eventType: string,
    handler: (message: WebSocketMessage) => void
  ): (() => void) => {
    if (!webSocketService.current) {
      console.warn('Cannot subscribe: WebSocket service not initialized');
      return () => {};
    }

    return webSocketService.current.subscribe(eventType, handler);
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (webSocketService.current) {
        webSocketService.current.disconnect();
      }
    };
  }, []);

  return {
    connectionStatus,
    lastMessage,
    sendMessage,
    subscribe,
    connect,
    disconnect,
    isConnected,
  };
}

/**
 * Hook for subscribing to specific WebSocket events
 */
export function useWebSocketSubscription(
  eventType: string,
  handler: (message: WebSocketMessage) => void,
  dependencies: any[] = []
): void {
  const { subscribe, isConnected } = useWebSocketConnection({ autoConnect: true });

  useEffect(() => {
    if (!isConnected) return;

    const unsubscribe = subscribe(eventType, handler);
    return unsubscribe;
  }, [eventType, isConnected, subscribe, ...dependencies]);
}

/**
 * Hook for real-time data from specific ASI modules
 */
export function useRealTimeData<T = any>(
  source: 'decision_engine' | 'learning_engine' | 'self_improvement' | 'data_integration',
  dataType?: string
): {
  data: T | null;
  lastUpdate: string | null;
  isConnected: boolean;
} {
  const [data, setData] = useState<T | null>(null);
  const [lastUpdate, setLastUpdate] = useState<string | null>(null);
  
  const { isConnected } = useWebSocketConnection({ autoConnect: true });

  useWebSocketSubscription(
    source,
    useCallback((message: WebSocketMessage) => {
      if (!dataType || message.payload.type === dataType) {
        setData(message.payload.data);
        setLastUpdate(message.timestamp);
      }
    }, [dataType]),
    [source, dataType]
  );

  return {
    data,
    lastUpdate,
    isConnected,
  };
}

export default useWebSocketConnection;
