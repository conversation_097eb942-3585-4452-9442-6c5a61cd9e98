import React from 'react';
import { createRoot } from 'react-dom/client';
import App from './App';

// Performance monitoring
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

// Service Worker registration
import { registerSW } from './utils/serviceWorker';

// Global error handling
window.addEventListener('error', (event) => {
  console.error('Global error:', event.error);
  // Send to monitoring service in production
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason);
  // Send to monitoring service in production
});

// Web Vitals reporting
function sendToAnalytics(metric: any) {
  // In production, send to your analytics service
  console.log('Web Vital:', metric);
}

getCLS(sendToAnalytics);
getFID(sendToAnalytics);
getFCP(sendToAnalytics);
getLCP(sendToAnalytics);
getTTFB(sendToAnalytics);

// Render the app
const container = document.getElementById('root');
if (!container) {
  throw new Error('Root element not found');
}

const root = createRoot(container);

root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);

// Register service worker for offline support
if ('serviceWorker' in navigator && process.env.NODE_ENV === 'production') {
  registerSW();
}
