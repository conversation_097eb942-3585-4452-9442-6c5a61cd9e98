// ASI System Type Definitions
// ===========================

// Base Types
export interface BaseEntity {
  id: string;
  createdAt: string;
  updatedAt: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// System Status Types
export interface SystemHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  uptime: number;
  version: string;
  environment: string;
  services: ServiceHealth[];
  lastCheck: string;
}

export interface ServiceHealth {
  name: string;
  status: 'up' | 'down' | 'degraded';
  endpoint: string;
  responseTime: number;
  lastCheck: string;
  error?: string;
}

// Decision Engine Types
export interface Decision {
  id: string;
  type: 'rule_based' | 'neural' | 'hybrid';
  input: Record<string, any>;
  output: Record<string, any>;
  confidence: number;
  reasoning: string[];
  executionTime: number;
  timestamp: string;
  metadata: Record<string, any>;
}

export interface DecisionFlow {
  id: string;
  decisions: Decision[];
  startTime: string;
  endTime: string;
  totalExecutionTime: number;
  success: boolean;
  error?: string;
}

export interface DecisionMetrics {
  totalDecisions: number;
  averageExecutionTime: number;
  successRate: number;
  confidenceDistribution: Record<string, number>;
  typeDistribution: Record<string, number>;
  recentDecisions: Decision[];
}

// Learning Engine Types
export interface Model {
  id: string;
  name: string;
  type: 'nlp' | 'vision' | 'rl' | 'abstraction' | 'multimodal';
  version: string;
  status: 'training' | 'deployed' | 'archived' | 'failed';
  accuracy?: number;
  loss?: number;
  trainingProgress?: number;
  createdAt: string;
  updatedAt: string;
  metadata: ModelMetadata;
}

export interface ModelMetadata {
  architecture: string;
  parameters: number;
  datasetSize: number;
  epochs: number;
  batchSize: number;
  learningRate: number;
  optimizer: string;
  framework: string;
  hardware: string;
  tags: string[];
}

export interface TrainingJob {
  id: string;
  modelId: string;
  status: 'queued' | 'running' | 'completed' | 'failed' | 'cancelled';
  progress: number;
  currentEpoch: number;
  totalEpochs: number;
  currentLoss: number;
  bestLoss: number;
  accuracy: number;
  startTime: string;
  endTime?: string;
  estimatedTimeRemaining?: number;
  logs: TrainingLog[];
}

export interface TrainingLog {
  timestamp: string;
  level: 'info' | 'warning' | 'error';
  message: string;
  metrics?: Record<string, number>;
}

export interface ModelPerformance {
  modelId: string;
  metrics: {
    accuracy: number;
    precision: number;
    recall: number;
    f1Score: number;
    loss: number;
  };
  inferenceTime: number;
  throughput: number;
  memoryUsage: number;
  timestamp: string;
}

// Self-Improvement Types
export interface ImprovementTask {
  id: string;
  type: 'genetic_programming' | 'rlhf' | 'evolutionary' | 'symbolic_refactor';
  target: 'model' | 'code' | 'rules' | 'architecture';
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  startTime: string;
  endTime?: string;
  improvements: Improvement[];
  metrics: ImprovementMetrics;
}

export interface Improvement {
  id: string;
  description: string;
  type: string;
  impact: 'low' | 'medium' | 'high';
  confidence: number;
  beforeMetrics: Record<string, number>;
  afterMetrics: Record<string, number>;
  appliedAt: string;
  rollbackAvailable: boolean;
}

export interface ImprovementMetrics {
  totalImprovements: number;
  successRate: number;
  averageImpact: number;
  performanceGain: number;
  recentImprovements: Improvement[];
}

// Data Integration Types
export interface DataSource {
  id: string;
  name: string;
  type: 'kafka' | 'database' | 'api' | 'file' | 'stream';
  status: 'connected' | 'disconnected' | 'error';
  endpoint: string;
  lastSync: string;
  recordsProcessed: number;
  errorCount: number;
  metadata: Record<string, any>;
}

export interface DataFlow {
  id: string;
  source: string;
  destination: string;
  status: 'active' | 'paused' | 'error';
  throughput: number;
  latency: number;
  errorRate: number;
  lastProcessed: string;
}

// UI/Dashboard Types
export interface DashboardWidget {
  id: string;
  type: 'chart' | 'metric' | 'table' | 'log' | 'status' | 'flow';
  title: string;
  position: {
    x: number;
    y: number;
    w: number;
    h: number;
  };
  config: Record<string, any>;
  dataSource: string;
  refreshInterval?: number;
  visible: boolean;
}

export interface DashboardLayout {
  id: string;
  name: string;
  widgets: DashboardWidget[];
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ChartData {
  labels: string[];
  datasets: ChartDataset[];
}

export interface ChartDataset {
  label: string;
  data: number[];
  backgroundColor?: string | string[];
  borderColor?: string | string[];
  borderWidth?: number;
  fill?: boolean;
}

// WebSocket Types
export interface WebSocketMessage {
  type: string;
  payload: any;
  timestamp: string;
  source: string;
}

export interface WebSocketConnection {
  url: string;
  status: 'connecting' | 'connected' | 'disconnected' | 'error';
  lastMessage?: WebSocketMessage;
  reconnectAttempts: number;
  maxReconnectAttempts: number;
}

// Theme Types
export interface Theme {
  mode: 'light' | 'dark';
  primaryColor: string;
  secondaryColor: string;
  accentColor: string;
  backgroundColor: string;
  surfaceColor: string;
  textColor: string;
  borderColor: string;
}

export interface ThemeConfig {
  defaultTheme: 'light' | 'dark';
  enableThemeSwitching: boolean;
  themes: {
    light: Theme;
    dark: Theme;
  };
}

// User Preferences
export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  language: string;
  timezone: string;
  dashboardLayout: string;
  notifications: {
    enabled: boolean;
    types: string[];
  };
  autoRefresh: boolean;
  refreshInterval: number;
}

// Error Types
export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
  source: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

// Notification Types
export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  actions?: NotificationAction[];
}

export interface NotificationAction {
  label: string;
  action: string;
  primary?: boolean;
}

// Metrics Types
export interface SystemMetrics {
  cpu: number;
  memory: number;
  disk: number;
  network: {
    inbound: number;
    outbound: number;
  };
  timestamp: string;
}

export interface PerformanceMetrics {
  responseTime: number;
  throughput: number;
  errorRate: number;
  availability: number;
  timestamp: string;
}

// Export all types
export type {
  BaseEntity,
  ApiResponse,
  PaginatedResponse,
  SystemHealth,
  ServiceHealth,
  Decision,
  DecisionFlow,
  DecisionMetrics,
  Model,
  ModelMetadata,
  TrainingJob,
  TrainingLog,
  ModelPerformance,
  ImprovementTask,
  Improvement,
  ImprovementMetrics,
  DataSource,
  DataFlow,
  DashboardWidget,
  DashboardLayout,
  ChartData,
  ChartDataset,
  WebSocketMessage,
  WebSocketConnection,
  Theme,
  ThemeConfig,
  UserPreferences,
  AppError,
  Notification,
  NotificationAction,
  SystemMetrics,
  PerformanceMetrics,
};
