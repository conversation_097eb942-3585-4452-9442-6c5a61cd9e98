{"compilerOptions": {"target": "ES2020", "lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "removeComments": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "baseUrl": "src", "paths": {"@/*": ["*"], "@/components/*": ["components/*"], "@/services/*": ["services/*"], "@/hooks/*": ["hooks/*"], "@/utils/*": ["utils/*"], "@/types/*": ["types/*"], "@/styles/*": ["styles/*"], "@/assets/*": ["assets/*"]}}, "include": ["src/**/*", "src/**/*.tsx", "src/**/*.ts"], "exclude": ["node_modules", "dist", "build"]}