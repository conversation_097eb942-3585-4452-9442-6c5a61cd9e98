{"name": "asi-react-dashboard", "version": "1.0.0", "description": "Real-time dashboard for ASI System monitoring and visualization", "main": "src/index.tsx", "scripts": {"start": "webpack serve --mode development", "build": "webpack --mode production", "build:dev": "webpack --mode development", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit", "format": "prettier --write src/**/*.{ts,tsx,css,json}", "analyze": "webpack-bundle-analyzer dist/static/js/*.js", "serve": "serve -s dist -l 3000"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "react-query": "^3.39.3", "@tanstack/react-query": "^4.24.4", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@mui/material": "^5.11.8", "@mui/icons-material": "^5.11.0", "@mui/x-charts": "^6.0.0", "@mui/x-data-grid": "^6.0.0", "d3": "^7.8.2", "d3-selection": "^3.0.0", "d3-scale": "^4.0.2", "d3-axis": "^3.0.0", "d3-shape": "^3.2.0", "d3-force": "^3.0.0", "chart.js": "^4.2.1", "react-chartjs-2": "^5.2.0", "recharts": "^2.5.0", "react-grid-layout": "^1.3.4", "react-resizable": "^3.0.4", "socket.io-client": "^4.6.1", "axios": "^1.3.4", "@grpc/grpc-js": "^1.8.13", "@grpc/proto-loader": "^0.7.5", "protobufjs": "^7.2.2", "lodash": "^4.17.21", "moment": "^2.29.4", "date-fns": "^2.29.3", "react-virtualized": "^9.22.3", "react-window": "^1.8.8", "react-intersection-observer": "^9.4.3", "react-helmet-async": "^1.3.0", "react-hot-toast": "^2.4.0", "react-use": "^17.4.0", "zustand": "^4.3.6", "immer": "^9.0.19", "framer-motion": "^10.0.1", "react-spring": "^9.6.1"}, "devDependencies": {"@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@types/react-router-dom": "^5.3.3", "@types/d3": "^7.4.0", "@types/lodash": "^4.14.191", "@types/node": "^18.14.6", "@types/jest": "^29.4.0", "typescript": "^4.9.5", "webpack": "^5.75.0", "webpack-cli": "^5.0.1", "webpack-dev-server": "^4.8.1", "webpack-bundle-analyzer": "^4.8.0", "html-webpack-plugin": "^5.5.0", "css-loader": "^6.7.3", "style-loader": "^3.3.2", "sass-loader": "^13.2.0", "sass": "^1.58.3", "postcss": "^8.4.21", "postcss-loader": "^7.0.2", "autoprefixer": "^10.4.13", "ts-loader": "^9.4.2", "babel-loader": "^9.1.2", "@babel/core": "^7.21.0", "@babel/preset-env": "^7.20.2", "@babel/preset-react": "^7.18.6", "@babel/preset-typescript": "^7.21.0", "eslint": "^8.35.0", "@typescript-eslint/eslint-plugin": "^5.54.0", "@typescript-eslint/parser": "^5.54.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^2.8.4", "jest": "^29.4.3", "jest-environment-jsdom": "^29.4.3", "@testing-library/react": "^14.0.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/user-event": "^14.4.3", "ts-jest": "^29.0.5", "serve": "^14.2.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/src/setupTests.ts"], "moduleNameMapping": {"\\.(css|less|scss|sass)$": "identity-obj-proxy"}, "transform": {"^.+\\.(ts|tsx)$": "ts-jest"}, "collectCoverageFrom": ["src/**/*.{ts,tsx}", "!src/**/*.d.ts", "!src/index.tsx", "!src/setupTests.ts"]}, "eslintConfig": {"extends": ["eslint:recommended", "@typescript-eslint/recommended", "plugin:react/recommended", "plugin:react-hooks/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2020, "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "rules": {"react/react-in-jsx-scope": "off", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/no-explicit-any": "warn"}, "settings": {"react": {"version": "detect"}}}, "prettier": {"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 80, "tabWidth": 2}, "keywords": ["asi", "dashboard", "react", "typescript", "real-time", "visualization", "monitoring"], "author": "ASI System Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/asi-system/ui-ux-module"}, "bugs": {"url": "https://github.com/asi-system/ui-ux-module/issues"}, "homepage": "https://github.com/asi-system/ui-ux-module#readme"}