# ASI UI/UX Module Makefile
# =========================

# Variables
SHELL := /bin/bash
.DEFAULT_GOAL := help

# Directories
REACT_DIR := react-dashboard
STREAMLIT_DIR := streamlit-inspector
WEBSOCKET_DIR := websocket-server
API_GATEWAY_DIR := api-gateway
DOCKER_DIR := docker
K8S_DIR := k8s

# Docker
DOCKER_COMPOSE := $(DOCKER_DIR)/docker-compose.yml
DOCKER_REGISTRY := asi-system
IMAGE_TAG := latest

# Colors for output
RED := \033[0;31m
GREEN := \033[0;32m
YELLOW := \033[0;33m
BLUE := \033[0;34m
PURPLE := \033[0;35m
CYAN := \033[0;36m
WHITE := \033[0;37m
RESET := \033[0m

# Help target
.PHONY: help
help: ## Show this help message
	@echo "$(CYAN)ASI UI/UX Module - Available Commands$(RESET)"
	@echo "======================================"
	@awk 'BEGIN {FS = ":.*##"} /^[a-zA-Z_-]+:.*?##/ { printf "$(GREEN)%-20s$(RESET) %s\n", $$1, $$2 }' $(MAKEFILE_LIST)

# Dependency checks
.PHONY: check-deps
check-deps: ## Check if all dependencies are installed
	@echo "$(BLUE)Checking dependencies...$(RESET)"
	@command -v node >/dev/null 2>&1 || { echo "$(RED)Node.js is required but not installed$(RESET)"; exit 1; }
	@command -v npm >/dev/null 2>&1 || { echo "$(RED)npm is required but not installed$(RESET)"; exit 1; }
	@command -v python3 >/dev/null 2>&1 || { echo "$(RED)Python 3 is required but not installed$(RESET)"; exit 1; }
	@command -v pip3 >/dev/null 2>&1 || { echo "$(RED)pip3 is required but not installed$(RESET)"; exit 1; }
	@command -v docker >/dev/null 2>&1 || { echo "$(RED)Docker is required but not installed$(RESET)"; exit 1; }
	@command -v docker-compose >/dev/null 2>&1 || { echo "$(RED)Docker Compose is required but not installed$(RESET)"; exit 1; }
	@echo "$(GREEN)All dependencies are installed$(RESET)"

# Installation targets
.PHONY: install
install: check-deps install-react install-streamlit install-websocket install-api-gateway ## Install all dependencies
	@echo "$(GREEN)All dependencies installed successfully$(RESET)"

.PHONY: install-react
install-react: ## Install React dashboard dependencies
	@echo "$(BLUE)Installing React dashboard dependencies...$(RESET)"
	cd $(REACT_DIR) && npm install
	@echo "$(GREEN)React dependencies installed$(RESET)"

.PHONY: install-streamlit
install-streamlit: ## Install Streamlit inspector dependencies
	@echo "$(BLUE)Installing Streamlit inspector dependencies...$(RESET)"
	cd $(STREAMLIT_DIR) && pip3 install -r requirements.txt
	@echo "$(GREEN)Streamlit dependencies installed$(RESET)"

.PHONY: install-websocket
install-websocket: ## Install WebSocket server dependencies
	@echo "$(BLUE)Installing WebSocket server dependencies...$(RESET)"
	cd $(WEBSOCKET_DIR) && npm install
	@echo "$(GREEN)WebSocket dependencies installed$(RESET)"

.PHONY: install-api-gateway
install-api-gateway: ## Install API gateway dependencies
	@echo "$(BLUE)Installing API gateway dependencies...$(RESET)"
	cd $(API_GATEWAY_DIR) && npm install
	@echo "$(GREEN)API gateway dependencies installed$(RESET)"

# Build targets
.PHONY: build
build: install build-react build-websocket build-api-gateway ## Build all components
	@echo "$(GREEN)All components built successfully$(RESET)"

.PHONY: build-react
build-react: ## Build React dashboard
	@echo "$(BLUE)Building React dashboard...$(RESET)"
	cd $(REACT_DIR) && npm run build
	@echo "$(GREEN)React dashboard built$(RESET)"

.PHONY: build-websocket
build-websocket: ## Build WebSocket server
	@echo "$(BLUE)Building WebSocket server...$(RESET)"
	cd $(WEBSOCKET_DIR) && npm run build
	@echo "$(GREEN)WebSocket server built$(RESET)"

.PHONY: build-api-gateway
build-api-gateway: ## Build API gateway
	@echo "$(BLUE)Building API gateway...$(RESET)"
	cd $(API_GATEWAY_DIR) && npm run build
	@echo "$(GREEN)API gateway built$(RESET)"

# Development targets
.PHONY: dev
dev: ## Start all services in development mode
	@echo "$(BLUE)Starting all services in development mode...$(RESET)"
	@trap 'kill 0' SIGINT; \
	make dev-react & \
	make dev-streamlit & \
	make dev-websocket & \
	make dev-api-gateway & \
	wait

.PHONY: dev-react
dev-react: ## Start React dashboard in development mode
	@echo "$(BLUE)Starting React dashboard in development mode...$(RESET)"
	cd $(REACT_DIR) && npm start

.PHONY: dev-streamlit
dev-streamlit: ## Start Streamlit inspector in development mode
	@echo "$(BLUE)Starting Streamlit inspector in development mode...$(RESET)"
	cd $(STREAMLIT_DIR) && streamlit run src/main.py --server.port 8501 --server.address 0.0.0.0

.PHONY: dev-websocket
dev-websocket: ## Start WebSocket server in development mode
	@echo "$(BLUE)Starting WebSocket server in development mode...$(RESET)"
	cd $(WEBSOCKET_DIR) && npm run dev

.PHONY: dev-api-gateway
dev-api-gateway: ## Start API gateway in development mode
	@echo "$(BLUE)Starting API gateway in development mode...$(RESET)"
	cd $(API_GATEWAY_DIR) && npm run dev

# Production targets
.PHONY: start
start: build ## Start all services in production mode
	@echo "$(BLUE)Starting all services in production mode...$(RESET)"
	docker-compose -f $(DOCKER_COMPOSE) up -d
	@echo "$(GREEN)All services started$(RESET)"

.PHONY: stop
stop: ## Stop all services
	@echo "$(BLUE)Stopping all services...$(RESET)"
	docker-compose -f $(DOCKER_COMPOSE) down
	@echo "$(GREEN)All services stopped$(RESET)"

.PHONY: restart
restart: stop start ## Restart all services

# Docker targets
.PHONY: build-docker
build-docker: ## Build Docker images
	@echo "$(BLUE)Building Docker images...$(RESET)"
	docker-compose -f $(DOCKER_COMPOSE) build
	@echo "$(GREEN)Docker images built$(RESET)"

.PHONY: push-docker
push-docker: build-docker ## Push Docker images to registry
	@echo "$(BLUE)Pushing Docker images to registry...$(RESET)"
	docker-compose -f $(DOCKER_COMPOSE) push
	@echo "$(GREEN)Docker images pushed$(RESET)"

# Testing targets
.PHONY: test
test: test-react test-streamlit test-websocket test-api-gateway ## Run all tests
	@echo "$(GREEN)All tests completed$(RESET)"

.PHONY: test-react
test-react: ## Run React dashboard tests
	@echo "$(BLUE)Running React dashboard tests...$(RESET)"
	cd $(REACT_DIR) && npm test -- --coverage --watchAll=false
	@echo "$(GREEN)React tests completed$(RESET)"

.PHONY: test-streamlit
test-streamlit: ## Run Streamlit inspector tests
	@echo "$(BLUE)Running Streamlit inspector tests...$(RESET)"
	cd $(STREAMLIT_DIR) && python3 -m pytest tests/ -v --cov=src
	@echo "$(GREEN)Streamlit tests completed$(RESET)"

.PHONY: test-websocket
test-websocket: ## Run WebSocket server tests
	@echo "$(BLUE)Running WebSocket server tests...$(RESET)"
	cd $(WEBSOCKET_DIR) && npm test
	@echo "$(GREEN)WebSocket tests completed$(RESET)"

.PHONY: test-api-gateway
test-api-gateway: ## Run API gateway tests
	@echo "$(BLUE)Running API gateway tests...$(RESET)"
	cd $(API_GATEWAY_DIR) && npm test
	@echo "$(GREEN)API gateway tests completed$(RESET)"

.PHONY: test-e2e
test-e2e: ## Run end-to-end tests
	@echo "$(BLUE)Running end-to-end tests...$(RESET)"
	cd tests/e2e && npm test
	@echo "$(GREEN)E2E tests completed$(RESET)"

# Linting and formatting
.PHONY: lint
lint: lint-react lint-websocket lint-api-gateway lint-streamlit ## Run all linters
	@echo "$(GREEN)All linting completed$(RESET)"

.PHONY: lint-react
lint-react: ## Lint React dashboard code
	@echo "$(BLUE)Linting React dashboard...$(RESET)"
	cd $(REACT_DIR) && npm run lint

.PHONY: lint-websocket
lint-websocket: ## Lint WebSocket server code
	@echo "$(BLUE)Linting WebSocket server...$(RESET)"
	cd $(WEBSOCKET_DIR) && npm run lint

.PHONY: lint-api-gateway
lint-api-gateway: ## Lint API gateway code
	@echo "$(BLUE)Linting API gateway...$(RESET)"
	cd $(API_GATEWAY_DIR) && npm run lint

.PHONY: lint-streamlit
lint-streamlit: ## Lint Streamlit inspector code
	@echo "$(BLUE)Linting Streamlit inspector...$(RESET)"
	cd $(STREAMLIT_DIR) && python3 -m flake8 src/ tests/

.PHONY: format
format: format-react format-websocket format-api-gateway format-streamlit ## Format all code
	@echo "$(GREEN)All code formatted$(RESET)"

.PHONY: format-react
format-react: ## Format React dashboard code
	@echo "$(BLUE)Formatting React dashboard...$(RESET)"
	cd $(REACT_DIR) && npm run format

.PHONY: format-websocket
format-websocket: ## Format WebSocket server code
	@echo "$(BLUE)Formatting WebSocket server...$(RESET)"
	cd $(WEBSOCKET_DIR) && npm run format

.PHONY: format-api-gateway
format-api-gateway: ## Format API gateway code
	@echo "$(BLUE)Formatting API gateway...$(RESET)"
	cd $(API_GATEWAY_DIR) && npm run format

.PHONY: format-streamlit
format-streamlit: ## Format Streamlit inspector code
	@echo "$(BLUE)Formatting Streamlit inspector...$(RESET)"
	cd $(STREAMLIT_DIR) && python3 -m black src/ tests/

# Kubernetes targets
.PHONY: deploy-k8s
deploy-k8s: ## Deploy to Kubernetes
	@echo "$(BLUE)Deploying to Kubernetes...$(RESET)"
	kubectl apply -f $(K8S_DIR)/
	@echo "$(GREEN)Deployed to Kubernetes$(RESET)"

.PHONY: undeploy-k8s
undeploy-k8s: ## Remove from Kubernetes
	@echo "$(BLUE)Removing from Kubernetes...$(RESET)"
	kubectl delete -f $(K8S_DIR)/
	@echo "$(GREEN)Removed from Kubernetes$(RESET)"

# Monitoring and logs
.PHONY: logs
logs: ## Show logs from all services
	@echo "$(BLUE)Showing logs from all services...$(RESET)"
	docker-compose -f $(DOCKER_COMPOSE) logs -f

.PHONY: logs-react
logs-react: ## Show React dashboard logs
	@echo "$(BLUE)Showing React dashboard logs...$(RESET)"
	docker-compose -f $(DOCKER_COMPOSE) logs -f react-dashboard

.PHONY: logs-streamlit
logs-streamlit: ## Show Streamlit inspector logs
	@echo "$(BLUE)Showing Streamlit inspector logs...$(RESET)"
	docker-compose -f $(DOCKER_COMPOSE) logs -f streamlit-inspector

.PHONY: logs-websocket
logs-websocket: ## Show WebSocket server logs
	@echo "$(BLUE)Showing WebSocket server logs...$(RESET)"
	docker-compose -f $(DOCKER_COMPOSE) logs -f websocket-server

.PHONY: status
status: ## Show status of all services
	@echo "$(BLUE)Checking service status...$(RESET)"
	docker-compose -f $(DOCKER_COMPOSE) ps

# Cleanup targets
.PHONY: clean
clean: clean-react clean-websocket clean-api-gateway clean-streamlit ## Clean all build artifacts
	@echo "$(GREEN)All build artifacts cleaned$(RESET)"

.PHONY: clean-react
clean-react: ## Clean React dashboard build artifacts
	@echo "$(BLUE)Cleaning React dashboard...$(RESET)"
	cd $(REACT_DIR) && rm -rf dist/ node_modules/.cache/

.PHONY: clean-websocket
clean-websocket: ## Clean WebSocket server build artifacts
	@echo "$(BLUE)Cleaning WebSocket server...$(RESET)"
	cd $(WEBSOCKET_DIR) && rm -rf dist/

.PHONY: clean-api-gateway
clean-api-gateway: ## Clean API gateway build artifacts
	@echo "$(BLUE)Cleaning API gateway...$(RESET)"
	cd $(API_GATEWAY_DIR) && rm -rf dist/

.PHONY: clean-streamlit
clean-streamlit: ## Clean Streamlit inspector cache
	@echo "$(BLUE)Cleaning Streamlit inspector...$(RESET)"
	cd $(STREAMLIT_DIR) && rm -rf __pycache__/ .pytest_cache/ .coverage

.PHONY: clean-docker
clean-docker: ## Clean Docker images and containers
	@echo "$(BLUE)Cleaning Docker images and containers...$(RESET)"
	docker-compose -f $(DOCKER_COMPOSE) down --rmi all --volumes --remove-orphans
	@echo "$(GREEN)Docker cleanup completed$(RESET)"

# Health checks
.PHONY: health
health: ## Check health of all services
	@echo "$(BLUE)Checking service health...$(RESET)"
	@curl -f http://localhost:3000/health || echo "$(RED)React dashboard unhealthy$(RESET)"
	@curl -f http://localhost:8501/health || echo "$(RED)Streamlit inspector unhealthy$(RESET)"
	@curl -f http://localhost:8080/health || echo "$(RED)WebSocket server unhealthy$(RESET)"
	@curl -f http://localhost:4000/health || echo "$(RED)API gateway unhealthy$(RESET)"
	@echo "$(GREEN)Health check completed$(RESET)"
