# Streamlit and core dependencies
streamlit>=1.28.0
streamlit-option-menu>=0.3.6
streamlit-aggrid>=0.3.4
streamlit-plotly>=0.1.0

# Data manipulation and analysis
pandas>=2.0.0
numpy>=1.24.0
scipy>=1.10.0

# Visualization
plotly>=5.15.0
matplotlib>=3.7.0
seaborn>=0.12.0
altair>=5.0.0

# Machine Learning
scikit-learn>=1.3.0
torch>=2.0.0
torchvision>=0.15.0
transformers>=4.30.0
datasets>=2.12.0

# Model inspection and interpretation
shap>=0.42.0
lime>=0.2.0
captum>=0.6.0
interpret>=0.4.0

# API and networking
requests>=2.31.0
httpx>=0.24.0
grpcio>=1.56.0
grpcio-tools>=1.56.0
protobuf>=4.23.0

# Data processing
Pillow>=10.0.0
opencv-python>=4.8.0
librosa>=0.10.0
soundfile>=0.12.0

# Utilities
pyyaml>=6.0
python-dotenv>=1.0.0
tqdm>=4.65.0
rich>=13.4.0

# Database connectivity
sqlalchemy>=2.0.0
psycopg2-binary>=2.9.0
pymongo>=4.4.0

# Caching and performance
redis>=4.6.0
joblib>=1.3.0

# Development and testing
pytest>=7.4.0
pytest-cov>=4.1.0
black>=23.7.0
flake8>=6.0.0

# Async support
asyncio>=3.4.3
aiohttp>=3.8.0
aiofiles>=23.1.0

# Time series analysis
statsmodels>=0.14.0
prophet>=1.1.0

# Graph analysis
networkx>=3.1.0
igraph>=0.10.0

# Text processing
spacy>=3.6.0
nltk>=3.8.0
textblob>=0.17.0

# Image processing
imageio>=2.31.0
scikit-image>=0.21.0

# Configuration management
hydra-core>=1.3.0
omegaconf>=2.3.0

# Monitoring and logging
prometheus-client>=0.17.0
structlog>=23.1.0

# Security
cryptography>=41.0.0
bcrypt>=4.0.0
