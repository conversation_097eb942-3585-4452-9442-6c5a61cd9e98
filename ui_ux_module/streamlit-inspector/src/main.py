"""
ASI Model Inspector - Main Streamlit Application
===============================================

Interactive model analysis and debugging tool for the ASI System.
Provides comprehensive model inspection, data exploration, performance monitoring,
and decision analysis capabilities.
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from streamlit_option_menu import option_menu
import yaml
import os
from pathlib import Path
from typing import Dict, Any, Optional

# Import custom modules
from apps.model_inspector import ModelInspectorApp
from apps.data_explorer import DataExplorerApp
from apps.performance_monitor import PerformanceMonitorApp
from apps.decision_analyzer import DecisionAnalyzerApp
from utils.config import load_config
from utils.api_client import ASIApiClient
from utils.cache_manager import CacheManager

# Page configuration
st.set_page_config(
    page_title="ASI Model Inspector",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded",
    menu_items={
        'Get Help': 'https://github.com/asi-system/ui-ux-module',
        'Report a bug': 'https://github.com/asi-system/ui-ux-module/issues',
        'About': """
        # ASI Model Inspector
        
        Interactive model analysis and debugging tool for the Artificial Super Intelligence (ASI) System.
        
        **Features:**
        - Model performance analysis
        - Data exploration and visualization
        - Real-time performance monitoring
        - Decision flow analysis
        - Model interpretability tools
        
        **Version:** 1.0.0
        """
    }
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: 600;
        color: #1976d2;
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .metric-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin: 0.5rem 0;
    }
    
    .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
    }
    
    .status-healthy { background-color: #4caf50; }
    .status-warning { background-color: #ff9800; }
    .status-error { background-color: #f44336; }
    
    .sidebar-section {
        margin: 1rem 0;
        padding: 1rem;
        background-color: #f8f9fa;
        border-radius: 8px;
        border-left: 4px solid #1976d2;
    }
</style>
""", unsafe_allow_html=True)

class ASIModelInspector:
    """Main application class for ASI Model Inspector."""
    
    def __init__(self):
        self.config = load_config()
        self.api_client = ASIApiClient(self.config)
        self.cache_manager = CacheManager()
        
        # Initialize session state
        if 'initialized' not in st.session_state:
            st.session_state.initialized = True
            st.session_state.selected_model = None
            st.session_state.connection_status = {}
    
    def render_header(self):
        """Render the main application header."""
        st.markdown('<h1 class="main-header">🤖 ASI Model Inspector</h1>', unsafe_allow_html=True)
        
        # Connection status
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            status = self.check_service_status('decision_engine')
            st.markdown(f"""
            <div class="metric-card">
                <span class="status-indicator status-{status}"></span>
                <strong>Decision Engine</strong><br>
                {status.title()}
            </div>
            """, unsafe_allow_html=True)
        
        with col2:
            status = self.check_service_status('learning_engine')
            st.markdown(f"""
            <div class="metric-card">
                <span class="status-indicator status-{status}"></span>
                <strong>Learning Engine</strong><br>
                {status.title()}
            </div>
            """, unsafe_allow_html=True)
        
        with col3:
            status = self.check_service_status('self_improvement')
            st.markdown(f"""
            <div class="metric-card">
                <span class="status-indicator status-{status}"></span>
                <strong>Self-Improvement</strong><br>
                {status.title()}
            </div>
            """, unsafe_allow_html=True)
        
        with col4:
            status = self.check_service_status('data_integration')
            st.markdown(f"""
            <div class="metric-card">
                <span class="status-indicator status-{status}"></span>
                <strong>Data Integration</strong><br>
                {status.title()}
            </div>
            """, unsafe_allow_html=True)
    
    def render_sidebar(self):
        """Render the sidebar with navigation and controls."""
        with st.sidebar:
            st.image("https://via.placeholder.com/200x80/1976d2/ffffff?text=ASI+System", width=200)
            
            # Navigation menu
            selected = option_menu(
                menu_title="Navigation",
                options=["Model Inspector", "Data Explorer", "Performance Monitor", "Decision Analyzer"],
                icons=["cpu", "database", "speedometer2", "diagram-3"],
                menu_icon="list",
                default_index=0,
                styles={
                    "container": {"padding": "0!important", "background-color": "#fafafa"},
                    "icon": {"color": "#1976d2", "font-size": "18px"},
                    "nav-link": {
                        "font-size": "16px",
                        "text-align": "left",
                        "margin": "0px",
                        "--hover-color": "#eee"
                    },
                    "nav-link-selected": {"background-color": "#1976d2"},
                }
            )
            
            st.markdown("---")
            
            # System Information
            st.markdown('<div class="sidebar-section">', unsafe_allow_html=True)
            st.subheader("System Info")
            
            system_info = self.get_system_info()
            st.metric("Active Models", system_info.get('active_models', 0))
            st.metric("Training Jobs", system_info.get('training_jobs', 0))
            st.metric("System Uptime", system_info.get('uptime', 'Unknown'))
            st.markdown('</div>', unsafe_allow_html=True)
            
            # Quick Actions
            st.markdown('<div class="sidebar-section">', unsafe_allow_html=True)
            st.subheader("Quick Actions")
            
            if st.button("🔄 Refresh Data", use_container_width=True):
                self.cache_manager.clear_cache()
                st.rerun()
            
            if st.button("📊 Export Report", use_container_width=True):
                self.export_report()
            
            if st.button("⚙️ Settings", use_container_width=True):
                self.show_settings()
            
            st.markdown('</div>', unsafe_allow_html=True)
            
            return selected
    
    def check_service_status(self, service: str) -> str:
        """Check the status of an ASI service."""
        try:
            # Use cached status if available
            cache_key = f"service_status_{service}"
            cached_status = self.cache_manager.get(cache_key)
            
            if cached_status is not None:
                return cached_status
            
            # Check service health
            health = self.api_client.check_service_health(service)
            status = "healthy" if health else "error"
            
            # Cache for 30 seconds
            self.cache_manager.set(cache_key, status, ttl=30)
            
            return status
        except Exception as e:
            st.error(f"Error checking {service} status: {str(e)}")
            return "error"
    
    def get_system_info(self) -> Dict[str, Any]:
        """Get system information."""
        try:
            cache_key = "system_info"
            cached_info = self.cache_manager.get(cache_key)
            
            if cached_info is not None:
                return cached_info
            
            # Fetch system information
            info = self.api_client.get_system_info()
            
            # Cache for 60 seconds
            self.cache_manager.set(cache_key, info, ttl=60)
            
            return info
        except Exception as e:
            st.error(f"Error fetching system info: {str(e)}")
            return {}
    
    def export_report(self):
        """Export system report."""
        try:
            # Generate report data
            report_data = {
                'timestamp': pd.Timestamp.now().isoformat(),
                'system_info': self.get_system_info(),
                'service_status': {
                    service: self.check_service_status(service)
                    for service in ['decision_engine', 'learning_engine', 'self_improvement', 'data_integration']
                }
            }
            
            # Convert to JSON and offer download
            import json
            report_json = json.dumps(report_data, indent=2)
            
            st.download_button(
                label="Download Report",
                data=report_json,
                file_name=f"asi_system_report_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.json",
                mime="application/json"
            )
            
            st.success("Report generated successfully!")
            
        except Exception as e:
            st.error(f"Error generating report: {str(e)}")
    
    def show_settings(self):
        """Show application settings."""
        with st.expander("Application Settings", expanded=True):
            st.subheader("Display Settings")
            
            # Theme selection
            theme = st.selectbox(
                "Theme",
                options=["Auto", "Light", "Dark"],
                index=0
            )
            
            # Auto-refresh settings
            auto_refresh = st.checkbox("Auto-refresh data", value=True)
            
            if auto_refresh:
                refresh_interval = st.slider(
                    "Refresh interval (seconds)",
                    min_value=5,
                    max_value=300,
                    value=30,
                    step=5
                )
            
            # Data settings
            st.subheader("Data Settings")
            
            max_records = st.number_input(
                "Maximum records to display",
                min_value=100,
                max_value=10000,
                value=1000,
                step=100
            )
            
            cache_ttl = st.number_input(
                "Cache TTL (seconds)",
                min_value=10,
                max_value=3600,
                value=300,
                step=10
            )
            
            if st.button("Save Settings"):
                # Save settings to session state
                st.session_state.settings = {
                    'theme': theme,
                    'auto_refresh': auto_refresh,
                    'refresh_interval': refresh_interval if auto_refresh else None,
                    'max_records': max_records,
                    'cache_ttl': cache_ttl
                }
                st.success("Settings saved!")
    
    def run(self):
        """Run the main application."""
        # Render header
        self.render_header()
        
        # Render sidebar and get selected page
        selected_page = self.render_sidebar()
        
        # Render selected page
        if selected_page == "Model Inspector":
            app = ModelInspectorApp(self.api_client, self.cache_manager)
            app.render()
        
        elif selected_page == "Data Explorer":
            app = DataExplorerApp(self.api_client, self.cache_manager)
            app.render()
        
        elif selected_page == "Performance Monitor":
            app = PerformanceMonitorApp(self.api_client, self.cache_manager)
            app.render()
        
        elif selected_page == "Decision Analyzer":
            app = DecisionAnalyzerApp(self.api_client, self.cache_manager)
            app.render()

def main():
    """Main entry point."""
    try:
        app = ASIModelInspector()
        app.run()
    except Exception as e:
        st.error(f"Application error: {str(e)}")
        st.exception(e)

if __name__ == "__main__":
    main()
