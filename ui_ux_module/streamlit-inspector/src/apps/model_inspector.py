"""
ASI Model Inspector - Streamlit Application
==========================================

Interactive model analysis and debugging tool for the ASI System.
Provides comprehensive model inspection, performance analysis, and debugging capabilities.
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import torch
import torch.nn as nn
from typing import Dict, Any, List, Optional, Tuple
import json
import pickle
from pathlib import Path
import time
from datetime import datetime, timedelta
import seaborn as sns
import matplotlib.pyplot as plt
from sklearn.metrics import confusion_matrix, classification_report
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
import requests
import io
import base64

class ModelInspectorApp:
    """Main Model Inspector Application."""
    
    def __init__(self):
        self.api_client = self._init_api_client()
        self.models_cache = {}
        self.analysis_cache = {}
        
    def _init_api_client(self):
        """Initialize API client for ASI services."""
        try:
            # Mock API client for demo
            return {
                'learning_engine_url': 'http://localhost:8000',
                'decision_engine_url': 'http://localhost:8001'
            }
        except Exception as e:
            st.error(f"Failed to initialize API client: {e}")
            return None
    
    def run(self):
        """Main application entry point."""
        st.set_page_config(
            page_title="ASI Model Inspector",
            page_icon="🔍",
            layout="wide",
            initial_sidebar_state="expanded"
        )
        
        st.title("🔍 ASI Model Inspector")
        st.markdown("Interactive model analysis and debugging tool for the ASI System")
        
        # Sidebar
        self._render_sidebar()
        
        # Main content
        tab1, tab2, tab3, tab4, tab5 = st.tabs([
            "📊 Model Overview",
            "🏗️ Architecture",
            "📈 Performance",
            "🔬 Layer Analysis",
            "🧪 Experiments"
        ])
        
        with tab1:
            self._render_model_overview()
        
        with tab2:
            self._render_architecture_analysis()
        
        with tab3:
            self._render_performance_analysis()
        
        with tab4:
            self._render_layer_analysis()
        
        with tab5:
            self._render_experiments()
    
    def _render_sidebar(self):
        """Render the sidebar with model selection and controls."""
        st.sidebar.header("🎛️ Controls")
        
        # Model selection
        st.sidebar.subheader("Model Selection")
        
        # Get available models
        available_models = self._get_available_models()
        
        selected_model = st.sidebar.selectbox(
            "Select Model",
            options=list(available_models.keys()),
            help="Choose a model to inspect"
        )
        
        if selected_model:
            st.session_state.selected_model = selected_model
            model_info = available_models[selected_model]
            
            # Model info
            st.sidebar.info(f"""
            **Model Type**: {model_info.get('type', 'Unknown')}
            **Framework**: {model_info.get('framework', 'Unknown')}
            **Version**: {model_info.get('version', 'Unknown')}
            **Status**: {model_info.get('status', 'Unknown')}
            """)
        
        # Analysis options
        st.sidebar.subheader("Analysis Options")
        
        st.session_state.show_gradients = st.sidebar.checkbox(
            "Show Gradients", value=False
        )
        
        st.session_state.show_activations = st.sidebar.checkbox(
            "Show Activations", value=True
        )
        
        st.session_state.show_weights = st.sidebar.checkbox(
            "Show Weights", value=True
        )
        
        # Refresh controls
        st.sidebar.subheader("Refresh")
        
        if st.sidebar.button("🔄 Refresh Data"):
            self._refresh_data()
        
        auto_refresh = st.sidebar.checkbox("Auto Refresh", value=False)
        if auto_refresh:
            refresh_interval = st.sidebar.slider(
                "Refresh Interval (seconds)", 5, 60, 10
            )
            time.sleep(refresh_interval)
            st.experimental_rerun()
    
    def _get_available_models(self) -> Dict[str, Dict]:
        """Get list of available models from the API."""
        # Mock data for demo
        return {
            "nlp_transformer_v1": {
                "type": "transformer",
                "framework": "pytorch",
                "version": "1.0.0",
                "status": "active",
                "parameters": 125000000,
                "size_mb": 500
            },
            "vision_cnn_v2": {
                "type": "cnn",
                "framework": "pytorch",
                "version": "2.1.0",
                "status": "active",
                "parameters": 25000000,
                "size_mb": 100
            },
            "rl_agent_dqn": {
                "type": "dqn",
                "framework": "pytorch",
                "version": "1.5.0",
                "status": "training",
                "parameters": 1000000,
                "size_mb": 10
            },
            "reward_model_v1": {
                "type": "reward_model",
                "framework": "pytorch",
                "version": "1.0.0",
                "status": "active",
                "parameters": 50000000,
                "size_mb": 200
            }
        }
    
    def _render_model_overview(self):
        """Render model overview tab."""
        if 'selected_model' not in st.session_state:
            st.warning("Please select a model from the sidebar.")
            return
        
        model_name = st.session_state.selected_model
        st.header(f"📊 Model Overview: {model_name}")
        
        # Model metrics
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric(
                "Parameters",
                "125M",
                delta="5M",
                help="Total number of trainable parameters"
            )
        
        with col2:
            st.metric(
                "Accuracy",
                "94.2%",
                delta="2.1%",
                help="Current model accuracy"
            )
        
        with col3:
            st.metric(
                "Inference Time",
                "15ms",
                delta="-3ms",
                help="Average inference latency"
            )
        
        with col4:
            st.metric(
                "Memory Usage",
                "2.1GB",
                delta="0.3GB",
                help="GPU memory consumption"
            )
        
        # Performance charts
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("Training Progress")
            
            # Generate mock training data
            epochs = list(range(1, 51))
            train_loss = [1.0 - 0.8 * (1 - np.exp(-x/10)) + np.random.normal(0, 0.02) for x in epochs]
            val_loss = [1.0 - 0.75 * (1 - np.exp(-x/10)) + np.random.normal(0, 0.03) for x in epochs]
            
            fig = go.Figure()
            fig.add_trace(go.Scatter(x=epochs, y=train_loss, name="Training Loss", line=dict(color='blue')))
            fig.add_trace(go.Scatter(x=epochs, y=val_loss, name="Validation Loss", line=dict(color='red')))
            fig.update_layout(
                title="Loss Over Time",
                xaxis_title="Epoch",
                yaxis_title="Loss",
                height=400
            )
            st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            st.subheader("Performance Metrics")
            
            # Generate mock performance data
            metrics_data = {
                'Metric': ['Accuracy', 'Precision', 'Recall', 'F1-Score'],
                'Value': [0.942, 0.938, 0.945, 0.941],
                'Target': [0.950, 0.940, 0.940, 0.940]
            }
            
            df = pd.DataFrame(metrics_data)
            
            fig = go.Figure()
            fig.add_trace(go.Bar(
                x=df['Metric'],
                y=df['Value'],
                name='Current',
                marker_color='lightblue'
            ))
            fig.add_trace(go.Bar(
                x=df['Metric'],
                y=df['Target'],
                name='Target',
                marker_color='darkblue',
                opacity=0.7
            ))
            fig.update_layout(
                title="Performance vs Target",
                yaxis_title="Score",
                height=400,
                barmode='group'
            )
            st.plotly_chart(fig, use_container_width=True)
        
        # Model details
        st.subheader("Model Details")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.json({
                "model_name": model_name,
                "architecture": "Transformer",
                "layers": 12,
                "attention_heads": 8,
                "hidden_size": 768,
                "vocab_size": 50000,
                "max_sequence_length": 512
            })
        
        with col2:
            # Recent predictions
            st.subheader("Recent Predictions")
            
            predictions_data = {
                'Timestamp': [
                    datetime.now() - timedelta(minutes=i*5) 
                    for i in range(10, 0, -1)
                ],
                'Input': [f"Sample input {i}" for i in range(1, 11)],
                'Prediction': [f"Prediction {i}" for i in range(1, 11)],
                'Confidence': np.random.uniform(0.8, 0.99, 10)
            }
            
            df = pd.DataFrame(predictions_data)
            df['Timestamp'] = df['Timestamp'].dt.strftime('%H:%M:%S')
            
            st.dataframe(
                df,
                use_container_width=True,
                hide_index=True
            )
    
    def _render_architecture_analysis(self):
        """Render architecture analysis tab."""
        st.header("🏗️ Model Architecture Analysis")
        
        if 'selected_model' not in st.session_state:
            st.warning("Please select a model from the sidebar.")
            return
        
        # Architecture visualization
        st.subheader("Network Architecture")
        
        # Create a mock architecture diagram
        layers_data = {
            'Layer': ['Input', 'Embedding', 'Transformer Block 1', 'Transformer Block 2', 
                     'Transformer Block 3', 'Layer Norm', 'Linear', 'Output'],
            'Type': ['Input', 'Embedding', 'Transformer', 'Transformer', 
                    'Transformer', 'Normalization', 'Linear', 'Output'],
            'Parameters': [0, 38400000, 28800000, 28800000, 28800000, 1536, 768000, 50000],
            'Output Shape': ['(batch, 512)', '(batch, 512, 768)', '(batch, 512, 768)', 
                           '(batch, 512, 768)', '(batch, 512, 768)', '(batch, 512, 768)', 
                           '(batch, 512, 768)', '(batch, 512, 50000)']
        }
        
        df = pd.DataFrame(layers_data)
        
        # Layer details table
        st.dataframe(df, use_container_width=True)
        
        # Parameter distribution
        col1, col2 = st.columns(2)
        
        with col1:
            fig = px.pie(
                df[df['Parameters'] > 0], 
                values='Parameters', 
                names='Layer',
                title="Parameter Distribution by Layer"
            )
            st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            fig = px.bar(
                df[df['Parameters'] > 0], 
                x='Layer', 
                y='Parameters',
                title="Parameters per Layer"
            )
            fig.update_xaxis(tickangle=45)
            st.plotly_chart(fig, use_container_width=True)
        
        # Layer-wise analysis
        st.subheader("Layer-wise Analysis")
        
        selected_layer = st.selectbox(
            "Select Layer for Detailed Analysis",
            options=df['Layer'].tolist()
        )
        
        if selected_layer:
            layer_info = df[df['Layer'] == selected_layer].iloc[0]
            
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.metric("Parameters", f"{layer_info['Parameters']:,}")
            
            with col2:
                st.metric("Type", layer_info['Type'])
            
            with col3:
                st.metric("Output Shape", layer_info['Output Shape'])
            
            # Mock weight distribution
            if layer_info['Parameters'] > 0:
                weights = np.random.normal(0, 0.1, 1000)
                
                fig = go.Figure()
                fig.add_trace(go.Histogram(
                    x=weights,
                    nbinsx=50,
                    name="Weight Distribution"
                ))
                fig.update_layout(
                    title=f"Weight Distribution - {selected_layer}",
                    xaxis_title="Weight Value",
                    yaxis_title="Frequency"
                )
                st.plotly_chart(fig, use_container_width=True)
    
    def _render_performance_analysis(self):
        """Render performance analysis tab."""
        st.header("📈 Performance Analysis")
        
        if 'selected_model' not in st.session_state:
            st.warning("Please select a model from the sidebar.")
            return
        
        # Performance metrics over time
        st.subheader("Performance Trends")
        
        # Generate mock time series data
        dates = pd.date_range(start='2024-01-01', end='2024-01-31', freq='D')
        performance_data = {
            'Date': dates,
            'Accuracy': 0.85 + 0.1 * np.random.random(len(dates)) + 0.05 * np.sin(np.arange(len(dates)) * 0.2),
            'Latency': 20 + 10 * np.random.random(len(dates)),
            'Throughput': 100 + 50 * np.random.random(len(dates))
        }
        
        df = pd.DataFrame(performance_data)
        
        metric_choice = st.selectbox(
            "Select Metric",
            options=['Accuracy', 'Latency', 'Throughput']
        )
        
        fig = px.line(
            df, 
            x='Date', 
            y=metric_choice,
            title=f"{metric_choice} Over Time"
        )
        st.plotly_chart(fig, use_container_width=True)
        
        # Confusion matrix
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("Confusion Matrix")
            
            # Mock confusion matrix
            cm = np.array([[85, 3, 2], [4, 88, 1], [1, 2, 89]])
            
            fig = px.imshow(
                cm,
                text_auto=True,
                aspect="auto",
                title="Confusion Matrix",
                labels=dict(x="Predicted", y="Actual")
            )
            st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            st.subheader("Performance by Class")
            
            class_metrics = {
                'Class': ['Class A', 'Class B', 'Class C'],
                'Precision': [0.94, 0.92, 0.96],
                'Recall': [0.91, 0.95, 0.93],
                'F1-Score': [0.925, 0.935, 0.945]
            }
            
            df_class = pd.DataFrame(class_metrics)
            
            fig = go.Figure()
            
            for metric in ['Precision', 'Recall', 'F1-Score']:
                fig.add_trace(go.Bar(
                    x=df_class['Class'],
                    y=df_class[metric],
                    name=metric
                ))
            
            fig.update_layout(
                title="Performance Metrics by Class",
                barmode='group'
            )
            st.plotly_chart(fig, use_container_width=True)
    
    def _render_layer_analysis(self):
        """Render layer analysis tab."""
        st.header("🔬 Layer Analysis")
        
        if 'selected_model' not in st.session_state:
            st.warning("Please select a model from the sidebar.")
            return
        
        st.info("Layer analysis provides detailed insights into individual layer behavior, activations, and gradients.")
        
        # Layer selection
        layers = ['embedding', 'transformer_1', 'transformer_2', 'transformer_3', 'output']
        selected_layer = st.selectbox("Select Layer", layers)
        
        if selected_layer:
            # Activation analysis
            st.subheader(f"Activation Analysis - {selected_layer}")
            
            # Mock activation data
            activations = np.random.normal(0, 1, (100, 768))
            
            col1, col2 = st.columns(2)
            
            with col1:
                # Activation heatmap
                fig = px.imshow(
                    activations[:20, :50],
                    title="Activation Heatmap (Sample)",
                    aspect="auto"
                )
                st.plotly_chart(fig, use_container_width=True)
            
            with col2:
                # Activation distribution
                fig = go.Figure()
                fig.add_trace(go.Histogram(
                    x=activations.flatten(),
                    nbinsx=50,
                    name="Activation Distribution"
                ))
                fig.update_layout(
                    title="Activation Value Distribution",
                    xaxis_title="Activation Value",
                    yaxis_title="Frequency"
                )
                st.plotly_chart(fig, use_container_width=True)
    
    def _render_experiments(self):
        """Render experiments tab."""
        st.header("🧪 Model Experiments")
        
        st.info("Run interactive experiments to test model behavior and performance.")
        
        # Experiment type selection
        experiment_type = st.selectbox(
            "Select Experiment Type",
            ["Input Perturbation", "Adversarial Testing", "Ablation Study", "Hyperparameter Sweep"]
        )
        
        if experiment_type == "Input Perturbation":
            st.subheader("Input Perturbation Analysis")
            
            # Input text area
            input_text = st.text_area(
                "Enter input text:",
                value="The quick brown fox jumps over the lazy dog."
            )
            
            # Perturbation controls
            col1, col2 = st.columns(2)
            
            with col1:
                noise_level = st.slider("Noise Level", 0.0, 1.0, 0.1)
                perturbation_type = st.selectbox(
                    "Perturbation Type",
                    ["Gaussian Noise", "Word Replacement", "Character Substitution"]
                )
            
            with col2:
                num_samples = st.slider("Number of Samples", 1, 100, 10)
                
                if st.button("Run Experiment"):
                    # Mock experiment results
                    results = {
                        'Sample': list(range(1, num_samples + 1)),
                        'Original_Confidence': np.random.uniform(0.8, 0.95, num_samples),
                        'Perturbed_Confidence': np.random.uniform(0.6, 0.9, num_samples)
                    }
                    
                    df_results = pd.DataFrame(results)
                    df_results['Confidence_Drop'] = df_results['Original_Confidence'] - df_results['Perturbed_Confidence']
                    
                    # Results visualization
                    fig = px.scatter(
                        df_results,
                        x='Original_Confidence',
                        y='Perturbed_Confidence',
                        title="Confidence: Original vs Perturbed",
                        hover_data=['Sample', 'Confidence_Drop']
                    )
                    fig.add_shape(
                        type="line",
                        x0=0.5, y0=0.5, x1=1.0, y1=1.0,
                        line=dict(dash="dash", color="red")
                    )
                    st.plotly_chart(fig, use_container_width=True)
                    
                    # Summary statistics
                    st.subheader("Experiment Summary")
                    col1, col2, col3 = st.columns(3)
                    
                    with col1:
                        st.metric(
                            "Average Confidence Drop",
                            f"{df_results['Confidence_Drop'].mean():.3f}"
                        )
                    
                    with col2:
                        st.metric(
                            "Max Confidence Drop",
                            f"{df_results['Confidence_Drop'].max():.3f}"
                        )
                    
                    with col3:
                        robust_samples = (df_results['Confidence_Drop'] < 0.1).sum()
                        st.metric(
                            "Robust Samples",
                            f"{robust_samples}/{num_samples}"
                        )
    
    def _refresh_data(self):
        """Refresh all cached data."""
        self.models_cache.clear()
        self.analysis_cache.clear()
        st.success("Data refreshed successfully!")
        st.experimental_rerun()

# Main application runner
def run_model_inspector():
    """Run the Model Inspector application."""
    app = ModelInspectorApp()
    app.run()

if __name__ == "__main__":
    run_model_inspector()
