# ASI System - AWS Infrastructure Variables
# ========================================

# ============================================================================
# GENERAL CONFIGURATION
# ============================================================================

variable "project_name" {
  description = "Name of the project"
  type        = string
  default     = "asi-system"
}

variable "environment" {
  description = "Environment name (development, staging, production)"
  type        = string
  default     = "development"
  
  validation {
    condition     = contains(["development", "staging", "production"], var.environment)
    error_message = "Environment must be one of: development, staging, production."
  }
}

variable "aws_region" {
  description = "AWS region"
  type        = string
  default     = "us-west-2"
}

variable "cluster_name" {
  description = "Name of the EKS cluster"
  type        = string
  default     = "asi-system-cluster"
}

# ============================================================================
# NETWORKING CONFIGURATION
# ============================================================================

variable "vpc_cidr" {
  description = "CIDR block for VPC"
  type        = string
  default     = "10.0.0.0/16"
}

variable "public_subnet_cidrs" {
  description = "CIDR blocks for public subnets"
  type        = list(string)
  default     = ["********/24", "********/24", "********/24"]
}

variable "private_subnet_cidrs" {
  description = "CIDR blocks for private subnets"
  type        = list(string)
  default     = ["*********/24", "*********/24", "*********/24"]
}

variable "cluster_endpoint_public_access_cidrs" {
  description = "List of CIDR blocks that can access the Amazon EKS public API server endpoint"
  type        = list(string)
  default     = ["0.0.0.0/0"]
}

# ============================================================================
# EKS CONFIGURATION
# ============================================================================

variable "kubernetes_version" {
  description = "Kubernetes version"
  type        = string
  default     = "1.27"
}

# General Node Group Configuration
variable "general_node_instance_types" {
  description = "Instance types for general node group"
  type        = list(string)
  default     = ["t3.large", "t3.xlarge"]
}

variable "general_node_desired_size" {
  description = "Desired number of nodes in general node group"
  type        = number
  default     = 3
}

variable "general_node_max_size" {
  description = "Maximum number of nodes in general node group"
  type        = number
  default     = 10
}

variable "general_node_min_size" {
  description = "Minimum number of nodes in general node group"
  type        = number
  default     = 1
}

# GPU Node Group Configuration
variable "enable_gpu_nodes" {
  description = "Enable GPU node group for ML workloads"
  type        = bool
  default     = true
}

variable "gpu_node_instance_types" {
  description = "Instance types for GPU node group"
  type        = list(string)
  default     = ["g4dn.xlarge", "g4dn.2xlarge"]
}

variable "gpu_node_desired_size" {
  description = "Desired number of nodes in GPU node group"
  type        = number
  default     = 1
}

variable "gpu_node_max_size" {
  description = "Maximum number of nodes in GPU node group"
  type        = number
  default     = 5
}

variable "gpu_node_min_size" {
  description = "Minimum number of nodes in GPU node group"
  type        = number
  default     = 0
}

# ============================================================================
# RDS POSTGRESQL CONFIGURATION
# ============================================================================

variable "enable_rds" {
  description = "Enable RDS PostgreSQL instance"
  type        = bool
  default     = true
}

variable "postgres_version" {
  description = "PostgreSQL version"
  type        = string
  default     = "15.3"
}

variable "postgres_instance_class" {
  description = "RDS instance class"
  type        = string
  default     = "db.t3.medium"
}

variable "postgres_allocated_storage" {
  description = "Initial allocated storage for RDS instance (GB)"
  type        = number
  default     = 100
}

variable "postgres_max_allocated_storage" {
  description = "Maximum allocated storage for RDS instance (GB)"
  type        = number
  default     = 1000
}

variable "postgres_database_name" {
  description = "Name of the PostgreSQL database"
  type        = string
  default     = "asi_system"
}

variable "postgres_username" {
  description = "Username for PostgreSQL"
  type        = string
  default     = "asi_user"
}

variable "postgres_password" {
  description = "Password for PostgreSQL"
  type        = string
  sensitive   = true
  default     = "asi_secure_password_123"
}

# ============================================================================
# ELASTICACHE REDIS CONFIGURATION
# ============================================================================

variable "enable_elasticache" {
  description = "Enable ElastiCache Redis cluster"
  type        = bool
  default     = true
}

variable "redis_node_type" {
  description = "ElastiCache Redis node type"
  type        = string
  default     = "cache.t3.medium"
}

variable "redis_num_cache_nodes" {
  description = "Number of cache nodes in the Redis cluster"
  type        = number
  default     = 2
}

variable "redis_auth_token" {
  description = "Auth token for Redis cluster"
  type        = string
  sensitive   = true
  default     = "asi_redis_auth_token_123"
}

# ============================================================================
# S3 CONFIGURATION
# ============================================================================

variable "enable_s3_buckets" {
  description = "Enable S3 buckets for storage"
  type        = bool
  default     = true
}

variable "s3_bucket_names" {
  description = "Names of S3 buckets to create"
  type        = list(string)
  default     = [
    "models",
    "data",
    "backups",
    "logs",
    "artifacts"
  ]
}

# ============================================================================
# MONITORING CONFIGURATION
# ============================================================================

variable "enable_cloudwatch" {
  description = "Enable CloudWatch monitoring"
  type        = bool
  default     = true
}

variable "cloudwatch_log_retention_days" {
  description = "CloudWatch log retention in days"
  type        = number
  default     = 30
}

# ============================================================================
# SECURITY CONFIGURATION
# ============================================================================

variable "enable_secrets_manager" {
  description = "Enable AWS Secrets Manager"
  type        = bool
  default     = true
}

variable "secrets" {
  description = "Map of secrets to store in AWS Secrets Manager"
  type        = map(string)
  sensitive   = true
  default     = {
    postgres_password = "asi_secure_password_123"
    redis_auth_token  = "asi_redis_auth_token_123"
    jwt_secret        = "your-super-secret-jwt-key-here"
    grafana_password  = "asi_grafana_admin_123"
  }
}

# ============================================================================
# BACKUP CONFIGURATION
# ============================================================================

variable "enable_backup_vault" {
  description = "Enable AWS Backup vault"
  type        = bool
  default     = true
}

variable "backup_retention_days" {
  description = "Backup retention period in days"
  type        = number
  default     = 30
}

# ============================================================================
# COST OPTIMIZATION
# ============================================================================

variable "enable_spot_instances" {
  description = "Enable spot instances for cost optimization"
  type        = bool
  default     = false
}

variable "spot_instance_types" {
  description = "Instance types for spot instances"
  type        = list(string)
  default     = ["t3.large", "t3.xlarge", "m5.large", "m5.xlarge"]
}

# ============================================================================
# TAGS
# ============================================================================

variable "additional_tags" {
  description = "Additional tags to apply to all resources"
  type        = map(string)
  default     = {}
}

# ============================================================================
# FEATURE FLAGS
# ============================================================================

variable "enable_istio" {
  description = "Enable Istio service mesh"
  type        = bool
  default     = false
}

variable "enable_karpenter" {
  description = "Enable Karpenter for node autoscaling"
  type        = bool
  default     = false
}

variable "enable_external_dns" {
  description = "Enable External DNS for automatic DNS management"
  type        = bool
  default     = false
}

variable "enable_cert_manager" {
  description = "Enable cert-manager for automatic TLS certificate management"
  type        = bool
  default     = false
}

variable "domain_name" {
  description = "Domain name for the ASI system (required if external_dns is enabled)"
  type        = string
  default     = ""
}
