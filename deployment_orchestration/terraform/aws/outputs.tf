# ASI System - AWS Infrastructure Outputs
# =======================================

# ============================================================================
# VPC OUTPUTS
# ============================================================================

output "vpc_id" {
  description = "ID of the VPC"
  value       = aws_vpc.asi_vpc.id
}

output "vpc_cidr_block" {
  description = "CIDR block of the VPC"
  value       = aws_vpc.asi_vpc.cidr_block
}

output "public_subnet_ids" {
  description = "IDs of the public subnets"
  value       = aws_subnet.public[*].id
}

output "private_subnet_ids" {
  description = "IDs of the private subnets"
  value       = aws_subnet.private[*].id
}

output "internet_gateway_id" {
  description = "ID of the Internet Gateway"
  value       = aws_internet_gateway.asi_igw.id
}

output "nat_gateway_ids" {
  description = "IDs of the NAT Gateways"
  value       = aws_nat_gateway.asi_nat[*].id
}

# ============================================================================
# EKS CLUSTER OUTPUTS
# ============================================================================

output "cluster_id" {
  description = "Name of the EKS cluster"
  value       = aws_eks_cluster.asi_cluster.name
}

output "cluster_arn" {
  description = "ARN of the EKS cluster"
  value       = aws_eks_cluster.asi_cluster.arn
}

output "cluster_endpoint" {
  description = "Endpoint for EKS control plane"
  value       = aws_eks_cluster.asi_cluster.endpoint
}

output "cluster_security_group_id" {
  description = "Security group ID attached to the EKS cluster"
  value       = aws_eks_cluster.asi_cluster.vpc_config[0].cluster_security_group_id
}

output "cluster_iam_role_name" {
  description = "IAM role name associated with EKS cluster"
  value       = aws_iam_role.eks_cluster.name
}

output "cluster_iam_role_arn" {
  description = "IAM role ARN associated with EKS cluster"
  value       = aws_iam_role.eks_cluster.arn
}

output "cluster_certificate_authority_data" {
  description = "Base64 encoded certificate data required to communicate with the cluster"
  value       = aws_eks_cluster.asi_cluster.certificate_authority[0].data
}

output "cluster_version" {
  description = "The Kubernetes version for the EKS cluster"
  value       = aws_eks_cluster.asi_cluster.version
}

# ============================================================================
# EKS NODE GROUP OUTPUTS
# ============================================================================

output "node_groups" {
  description = "EKS node groups"
  value = {
    general = {
      arn           = aws_eks_node_group.general.arn
      status        = aws_eks_node_group.general.status
      capacity_type = aws_eks_node_group.general.capacity_type
      instance_types = aws_eks_node_group.general.instance_types
      scaling_config = aws_eks_node_group.general.scaling_config
    }
    gpu = var.enable_gpu_nodes ? {
      arn           = aws_eks_node_group.gpu[0].arn
      status        = aws_eks_node_group.gpu[0].status
      capacity_type = aws_eks_node_group.gpu[0].capacity_type
      instance_types = aws_eks_node_group.gpu[0].instance_types
      scaling_config = aws_eks_node_group.gpu[0].scaling_config
    } : null
  }
}

output "node_group_iam_role_name" {
  description = "IAM role name associated with EKS node groups"
  value       = aws_iam_role.eks_nodes.name
}

output "node_group_iam_role_arn" {
  description = "IAM role ARN associated with EKS node groups"
  value       = aws_iam_role.eks_nodes.arn
}

# ============================================================================
# RDS OUTPUTS
# ============================================================================

output "rds_endpoint" {
  description = "RDS instance endpoint"
  value       = var.enable_rds ? aws_db_instance.asi_postgres[0].endpoint : null
  sensitive   = true
}

output "rds_port" {
  description = "RDS instance port"
  value       = var.enable_rds ? aws_db_instance.asi_postgres[0].port : null
}

output "rds_database_name" {
  description = "RDS database name"
  value       = var.enable_rds ? aws_db_instance.asi_postgres[0].db_name : null
}

output "rds_username" {
  description = "RDS database username"
  value       = var.enable_rds ? aws_db_instance.asi_postgres[0].username : null
  sensitive   = true
}

# ============================================================================
# ELASTICACHE OUTPUTS
# ============================================================================

output "redis_endpoint" {
  description = "ElastiCache Redis endpoint"
  value       = var.enable_elasticache ? aws_elasticache_replication_group.asi_redis[0].primary_endpoint_address : null
  sensitive   = true
}

output "redis_port" {
  description = "ElastiCache Redis port"
  value       = var.enable_elasticache ? aws_elasticache_replication_group.asi_redis[0].port : null
}

output "redis_auth_token" {
  description = "ElastiCache Redis auth token"
  value       = var.enable_elasticache ? aws_elasticache_replication_group.asi_redis[0].auth_token : null
  sensitive   = true
}

# ============================================================================
# SECURITY GROUP OUTPUTS
# ============================================================================

output "security_groups" {
  description = "Security group IDs"
  value = {
    eks_cluster   = aws_security_group.eks_cluster.id
    eks_nodes     = aws_security_group.eks_nodes.id
    rds           = aws_security_group.rds.id
    elasticache   = aws_security_group.elasticache.id
  }
}

# ============================================================================
# KMS OUTPUTS
# ============================================================================

output "kms_key_id" {
  description = "KMS key ID for EKS encryption"
  value       = aws_kms_key.eks.key_id
}

output "kms_key_arn" {
  description = "KMS key ARN for EKS encryption"
  value       = aws_kms_key.eks.arn
}

# ============================================================================
# CLOUDWATCH OUTPUTS
# ============================================================================

output "cloudwatch_log_group_name" {
  description = "CloudWatch log group name for EKS cluster"
  value       = aws_cloudwatch_log_group.eks_cluster.name
}

# ============================================================================
# KUBECTL CONFIGURATION
# ============================================================================

output "kubectl_config" {
  description = "kubectl config as generated by the module"
  value = {
    cluster_name                      = aws_eks_cluster.asi_cluster.name
    endpoint                         = aws_eks_cluster.asi_cluster.endpoint
    region                           = var.aws_region
    certificate_authority_data       = aws_eks_cluster.asi_cluster.certificate_authority[0].data
  }
}

# ============================================================================
# CONNECTION INFORMATION
# ============================================================================

output "connection_info" {
  description = "Connection information for ASI system components"
  value = {
    cluster = {
      name     = aws_eks_cluster.asi_cluster.name
      endpoint = aws_eks_cluster.asi_cluster.endpoint
      region   = var.aws_region
    }
    database = var.enable_rds ? {
      endpoint = aws_db_instance.asi_postgres[0].endpoint
      port     = aws_db_instance.asi_postgres[0].port
      database = aws_db_instance.asi_postgres[0].db_name
      username = aws_db_instance.asi_postgres[0].username
    } : null
    cache = var.enable_elasticache ? {
      endpoint = aws_elasticache_replication_group.asi_redis[0].primary_endpoint_address
      port     = aws_elasticache_replication_group.asi_redis[0].port
    } : null
  }
  sensitive = true
}

# ============================================================================
# DEPLOYMENT COMMANDS
# ============================================================================

output "deployment_commands" {
  description = "Commands to deploy ASI system"
  value = {
    update_kubeconfig = "aws eks update-kubeconfig --region ${var.aws_region} --name ${aws_eks_cluster.asi_cluster.name}"
    helm_deploy       = "helm upgrade --install asi-system ./helm/asi-system --namespace asi-system --create-namespace"
    kubectl_apply     = "kubectl apply -f k8s/"
  }
}

# ============================================================================
# MONITORING ENDPOINTS
# ============================================================================

output "monitoring_info" {
  description = "Information for setting up monitoring"
  value = {
    prometheus_config = {
      cluster_name = aws_eks_cluster.asi_cluster.name
      region       = var.aws_region
    }
    grafana_datasources = {
      prometheus_url = "http://prometheus:9090"
      cloudwatch_region = var.aws_region
    }
  }
}

# ============================================================================
# COST INFORMATION
# ============================================================================

output "estimated_monthly_cost" {
  description = "Estimated monthly cost breakdown (approximate)"
  value = {
    eks_cluster = "~$73/month (control plane)"
    general_nodes = "~$${var.general_node_desired_size * 69}/month (t3.large nodes)"
    gpu_nodes = var.enable_gpu_nodes ? "~$${var.gpu_node_desired_size * 526}/month (g4dn.xlarge nodes)" : "Not enabled"
    rds = var.enable_rds ? "~$35/month (db.t3.medium)" : "Not enabled"
    elasticache = var.enable_elasticache ? "~$25/month (cache.t3.medium)" : "Not enabled"
    data_transfer = "Variable based on usage"
    storage = "Variable based on usage"
    note = "Costs are estimates and may vary based on actual usage, region, and AWS pricing changes"
  }
}
