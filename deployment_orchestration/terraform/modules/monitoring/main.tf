# ASI System - Monitoring Infrastructure Module
# ============================================

terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = "~> 2.20"
    }
    helm = {
      source  = "hashicorp/helm"
      version = "~> 2.10"
    }
  }
}

# Variables
variable "cluster_name" {
  description = "Name of the EKS cluster"
  type        = string
}

variable "namespace" {
  description = "Kubernetes namespace for monitoring"
  type        = string
  default     = "monitoring"
}

variable "environment" {
  description = "Environment name"
  type        = string
}

variable "enable_prometheus" {
  description = "Enable Prometheus monitoring"
  type        = bool
  default     = true
}

variable "enable_grafana" {
  description = "Enable Grafana dashboards"
  type        = bool
  default     = true
}

variable "enable_jaeger" {
  description = "Enable Jaeger tracing"
  type        = bool
  default     = true
}

variable "enable_elasticsearch" {
  description = "Enable Elasticsearch for logging"
  type        = bool
  default     = true
}

variable "storage_class" {
  description = "Storage class for persistent volumes"
  type        = string
  default     = "gp2"
}

variable "prometheus_storage_size" {
  description = "Storage size for Prometheus"
  type        = string
  default     = "50Gi"
}

variable "grafana_admin_password" {
  description = "Admin password for Grafana"
  type        = string
  sensitive   = true
}

# Monitoring namespace
resource "kubernetes_namespace" "monitoring" {
  metadata {
    name = var.namespace
    labels = {
      name        = var.namespace
      environment = var.environment
      component   = "monitoring"
    }
  }
}

# Prometheus Operator
resource "helm_release" "prometheus_operator" {
  count = var.enable_prometheus ? 1 : 0

  name       = "prometheus-operator"
  repository = "https://prometheus-community.github.io/helm-charts"
  chart      = "kube-prometheus-stack"
  version    = "51.2.0"
  namespace  = kubernetes_namespace.monitoring.metadata[0].name

  values = [
    yamlencode({
      fullnameOverride = "prometheus"
      
      prometheus = {
        prometheusSpec = {
          storageSpec = {
            volumeClaimTemplate = {
              spec = {
                storageClassName = var.storage_class
                accessModes      = ["ReadWriteOnce"]
                resources = {
                  requests = {
                    storage = var.prometheus_storage_size
                  }
                }
              }
            }
          }
          retention = "30d"
          resources = {
            requests = {
              cpu    = "500m"
              memory = "2Gi"
            }
            limits = {
              cpu    = "2"
              memory = "4Gi"
            }
          }
          additionalScrapeConfigs = [
            {
              job_name = "asi-services"
              kubernetes_sd_configs = [
                {
                  role = "endpoints"
                  namespaces = {
                    names = ["asi-system"]
                  }
                }
              ]
              relabel_configs = [
                {
                  source_labels = ["__meta_kubernetes_service_annotation_prometheus_io_scrape"]
                  action        = "keep"
                  regex         = "true"
                }
              ]
            }
          ]
        }
      }
      
      grafana = {
        enabled = var.enable_grafana
        adminPassword = var.grafana_admin_password
        persistence = {
          enabled = true
          size    = "10Gi"
          storageClassName = var.storage_class
        }
        resources = {
          requests = {
            cpu    = "250m"
            memory = "512Mi"
          }
          limits = {
            cpu    = "1"
            memory = "1Gi"
          }
        }
        dashboardProviders = {
          "dashboardproviders.yaml" = {
            apiVersion = 1
            providers = [
              {
                name    = "asi-dashboards"
                orgId   = 1
                folder  = "ASI System"
                type    = "file"
                options = {
                  path = "/var/lib/grafana/dashboards/asi"
                }
              }
            ]
          }
        }
        dashboards = {
          asi-dashboards = {
            "asi-overview" = {
              gnetId    = 1860
              revision  = 27
              datasource = "Prometheus"
            }
            "asi-learning-engine" = {
              file = "dashboards/learning-engine.json"
            }
            "asi-decision-engine" = {
              file = "dashboards/decision-engine.json"
            }
          }
        }
      }
      
      alertmanager = {
        alertmanagerSpec = {
          storage = {
            volumeClaimTemplate = {
              spec = {
                storageClassName = var.storage_class
                accessModes      = ["ReadWriteOnce"]
                resources = {
                  requests = {
                    storage = "10Gi"
                  }
                }
              }
            }
          }
          resources = {
            requests = {
              cpu    = "100m"
              memory = "256Mi"
            }
            limits = {
              cpu    = "500m"
              memory = "512Mi"
            }
          }
        }
      }
    })
  ]

  depends_on = [kubernetes_namespace.monitoring]
}

# Jaeger for distributed tracing
resource "helm_release" "jaeger" {
  count = var.enable_jaeger ? 1 : 0

  name       = "jaeger"
  repository = "https://jaegertracing.github.io/helm-charts"
  chart      = "jaeger"
  version    = "0.71.2"
  namespace  = kubernetes_namespace.monitoring.metadata[0].name

  values = [
    yamlencode({
      provisionDataStore = {
        cassandra = false
        elasticsearch = var.enable_elasticsearch
      }
      
      storage = {
        type = var.enable_elasticsearch ? "elasticsearch" : "memory"
        elasticsearch = var.enable_elasticsearch ? {
          host = "elasticsearch-master.${var.namespace}.svc.cluster.local"
          port = 9200
        } : null
      }
      
      agent = {
        enabled = true
        daemonset = {
          useHostPort = true
        }
      }
      
      collector = {
        enabled = true
        replicaCount = 2
        resources = {
          requests = {
            cpu    = "200m"
            memory = "512Mi"
          }
          limits = {
            cpu    = "1"
            memory = "1Gi"
          }
        }
      }
      
      query = {
        enabled = true
        replicaCount = 1
        resources = {
          requests = {
            cpu    = "200m"
            memory = "256Mi"
          }
          limits = {
            cpu    = "500m"
            memory = "512Mi"
          }
        }
      }
    })
  ]

  depends_on = [kubernetes_namespace.monitoring]
}

# Elasticsearch for logging
resource "helm_release" "elasticsearch" {
  count = var.enable_elasticsearch ? 1 : 0

  name       = "elasticsearch"
  repository = "https://helm.elastic.co"
  chart      = "elasticsearch"
  version    = "8.5.1"
  namespace  = kubernetes_namespace.monitoring.metadata[0].name

  values = [
    yamlencode({
      clusterName = "asi-elasticsearch"
      nodeGroup   = "master"
      
      replicas = 3
      minimumMasterNodes = 2
      
      esConfig = {
        "elasticsearch.yml" = <<-EOF
          cluster.name: asi-elasticsearch
          network.host: 0.0.0.0
          discovery.seed_hosts: elasticsearch-master-headless
          cluster.initial_master_nodes: elasticsearch-master-0,elasticsearch-master-1,elasticsearch-master-2
          xpack.security.enabled: false
          xpack.monitoring.collection.enabled: true
        EOF
      }
      
      volumeClaimTemplate = {
        accessModes = ["ReadWriteOnce"]
        storageClassName = var.storage_class
        resources = {
          requests = {
            storage = "30Gi"
          }
        }
      }
      
      resources = {
        requests = {
          cpu    = "1"
          memory = "2Gi"
        }
        limits = {
          cpu    = "2"
          memory = "4Gi"
        }
      }
      
      esJavaOpts = "-Xmx2g -Xms2g"
    })
  ]

  depends_on = [kubernetes_namespace.monitoring]
}

# Kibana for log visualization
resource "helm_release" "kibana" {
  count = var.enable_elasticsearch ? 1 : 0

  name       = "kibana"
  repository = "https://helm.elastic.co"
  chart      = "kibana"
  version    = "8.5.1"
  namespace  = kubernetes_namespace.monitoring.metadata[0].name

  values = [
    yamlencode({
      elasticsearchHosts = "http://elasticsearch-master.${var.namespace}.svc.cluster.local:9200"
      
      kibanaConfig = {
        "kibana.yml" = <<-EOF
          server.host: 0.0.0.0
          elasticsearch.hosts: ["http://elasticsearch-master.${var.namespace}.svc.cluster.local:9200"]
          xpack.monitoring.ui.container.elasticsearch.enabled: true
        EOF
      }
      
      resources = {
        requests = {
          cpu    = "500m"
          memory = "1Gi"
        }
        limits = {
          cpu    = "1"
          memory = "2Gi"
        }
      }
      
      service = {
        type = "ClusterIP"
        port = 5601
      }
    })
  ]

  depends_on = [
    kubernetes_namespace.monitoring,
    helm_release.elasticsearch[0]
  ]
}

# Fluent Bit for log collection
resource "helm_release" "fluent_bit" {
  count = var.enable_elasticsearch ? 1 : 0

  name       = "fluent-bit"
  repository = "https://fluent.github.io/helm-charts"
  chart      = "fluent-bit"
  version    = "0.21.7"
  namespace  = kubernetes_namespace.monitoring.metadata[0].name

  values = [
    yamlencode({
      config = {
        outputs = <<-EOF
          [OUTPUT]
              Name es
              Match *
              Host elasticsearch-master.${var.namespace}.svc.cluster.local
              Port 9200
              Index asi-logs
              Type _doc
              Logstash_Format On
              Logstash_Prefix asi
              Retry_Limit False
        EOF
        
        filters = <<-EOF
          [FILTER]
              Name kubernetes
              Match kube.*
              Kube_URL https://kubernetes.default.svc:443
              Kube_CA_File /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
              Kube_Token_File /var/run/secrets/kubernetes.io/serviceaccount/token
              Kube_Tag_Prefix kube.var.log.containers.
              Merge_Log On
              Keep_Log Off
              K8S-Logging.Parser On
              K8S-Logging.Exclude Off
        EOF
      }
      
      resources = {
        requests = {
          cpu    = "100m"
          memory = "128Mi"
        }
        limits = {
          cpu    = "500m"
          memory = "256Mi"
        }
      }
    })
  ]

  depends_on = [
    kubernetes_namespace.monitoring,
    helm_release.elasticsearch[0]
  ]
}

# Outputs
output "monitoring_namespace" {
  description = "Monitoring namespace name"
  value       = kubernetes_namespace.monitoring.metadata[0].name
}

output "prometheus_endpoint" {
  description = "Prometheus endpoint"
  value       = var.enable_prometheus ? "http://prometheus-server.${var.namespace}.svc.cluster.local:9090" : null
}

output "grafana_endpoint" {
  description = "Grafana endpoint"
  value       = var.enable_grafana ? "http://prometheus-grafana.${var.namespace}.svc.cluster.local:80" : null
}

output "jaeger_endpoint" {
  description = "Jaeger endpoint"
  value       = var.enable_jaeger ? "http://jaeger-query.${var.namespace}.svc.cluster.local:16686" : null
}

output "elasticsearch_endpoint" {
  description = "Elasticsearch endpoint"
  value       = var.enable_elasticsearch ? "http://elasticsearch-master.${var.namespace}.svc.cluster.local:9200" : null
}

output "kibana_endpoint" {
  description = "Kibana endpoint"
  value       = var.enable_elasticsearch ? "http://kibana-kibana.${var.namespace}.svc.cluster.local:5601" : null
}
