version: '3.8'

# ASI System - Production Docker Compose Overrides
# ================================================

services:
  # ============================================================================
  # INFRASTRUCTURE SERVICES - PRODUCTION OVERRIDES
  # ============================================================================

  zookeeper:
    environment:
      ZOOKEEPER_SERVER_ID: 1
      ZOOKEEPER_SERVERS: zookeeper:2888:3888
      ZOOKEEPER_HEAP_OPTS: "-Xmx1G -Xms1G"
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2G
        reservations:
          cpus: '1'
          memory: 1G

  kafka:
    environment:
      KAFKA_HEAP_OPTS: "-Xmx2G -Xms2G"
      KAFKA_NUM_PARTITIONS: 12
      KAFKA_DEFAULT_REPLICATION_FACTOR: 3
      KAFKA_MIN_INSYNC_REPLICAS: 2
      KAFKA_LOG_RETENTION_HOURS: 168
      KAFKA_LOG_SEGMENT_BYTES: 1073741824
      KAFKA_LOG_RETENTION_CHECK_INTERVAL_MS: 300000
    deploy:
      resources:
        limits:
          cpus: '4'
          memory: 4G
        reservations:
          cpus: '2'
          memory: 2G

  redis:
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD} --maxmemory 2gb --maxmemory-policy allkeys-lru
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2G
        reservations:
          cpus: '1'
          memory: 1G

  postgres:
    environment:
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_SHARED_PRELOAD_LIBRARIES: pg_stat_statements
      POSTGRES_MAX_CONNECTIONS: 200
      POSTGRES_SHARED_BUFFERS: 256MB
      POSTGRES_EFFECTIVE_CACHE_SIZE: 1GB
    deploy:
      resources:
        limits:
          cpus: '4'
          memory: 4G
        reservations:
          cpus: '2'
          memory: 2G

  neo4j:
    environment:
      NEO4J_AUTH: neo4j/${NEO4J_PASSWORD}
      NEO4J_dbms_memory_heap_initial__size: 2G
      NEO4J_dbms_memory_heap_max__size: 4G
      NEO4J_dbms_memory_pagecache_size: 2G
      NEO4J_dbms_connector_bolt_thread__pool__min__size: 5
      NEO4J_dbms_connector_bolt_thread__pool__max__size: 400
    deploy:
      resources:
        limits:
          cpus: '4'
          memory: 8G
        reservations:
          cpus: '2'
          memory: 4G

  # ============================================================================
  # APPLICATION SERVICES - PRODUCTION OVERRIDES
  # ============================================================================

  go-kafka-producer:
    environment:
      - KAFKA_BROKERS=kafka:29092
      - GRPC_PORT=50051
      - METRICS_PORT=8080
      - LOG_LEVEL=WARN
      - GOMAXPROCS=4
    deploy:
      replicas: 3
      resources:
        limits:
          cpus: '2'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

  rust-kafka-consumer:
    environment:
      - RUST_LOG=warn
      - WORKER_THREADS=8
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '2'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

  python-scrapy:
    environment:
      - LOG_LEVEL=WARN
      - CONCURRENT_REQUESTS=32
      - DOWNLOAD_DELAY=0.5
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '2'
          memory: 2G
        reservations:
          cpus: '1'
          memory: 1G

  spark-master:
    environment:
      - SPARK_MASTER_OPTS=-Dspark.deploy.defaultCores=4 -Dspark.deploy.defaultMemory=4g
    deploy:
      resources:
        limits:
          cpus: '4'
          memory: 8G
        reservations:
          cpus: '2'
          memory: 4G

  spark-worker:
    environment:
      - SPARK_WORKER_MEMORY=4G
      - SPARK_WORKER_CORES=4
    deploy:
      replicas: 3
      resources:
        limits:
          cpus: '4'
          memory: 6G
        reservations:
          cpus: '2'
          memory: 4G

  python-training-engine:
    environment:
      - ASI_LOG_LEVEL=WARN
      - ASI_ENVIRONMENT=production
      - CUDA_LAUNCH_BLOCKING=0
      - TORCH_CUDNN_V8_API_ENABLED=1
    deploy:
      resources:
        limits:
          cpus: '8'
          memory: 16G
        reservations:
          cpus: '4'
          memory: 8G
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  cpp-inference-engine:
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '4'
          memory: 4G
        reservations:
          cpus: '2'
          memory: 2G

  python-rule-engine:
    environment:
      - LOG_LEVEL=WARN
      - WORKER_PROCESSES=4
    deploy:
      replicas: 3
      resources:
        limits:
          cpus: '2'
          memory: 2G
        reservations:
          cpus: '1'
          memory: 1G

  rust-decision-loop:
    environment:
      - RUST_LOG=warn
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '2'
          memory: 1G
        reservations:
          cpus: '1'
          memory: 512M

  react-dashboard:
    environment:
      - NODE_ENV=production
      - REACT_APP_API_URL=${API_URL}
      - REACT_APP_WEBSOCKET_URL=${WEBSOCKET_URL}
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '1'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M

  # ============================================================================
  # MONITORING - PRODUCTION OVERRIDES
  # ============================================================================

  prometheus:
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=90d'
      - '--storage.tsdb.retention.size=50GB'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
      - '--query.max-concurrency=50'
    deploy:
      resources:
        limits:
          cpus: '4'
          memory: 8G
        reservations:
          cpus: '2'
          memory: 4G

  grafana:
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD}
      - GF_SMTP_ENABLED=true
      - GF_SMTP_HOST=${SMTP_HOST}
      - GF_SMTP_USER=${SMTP_USER}
      - GF_SMTP_PASSWORD=${SMTP_PASSWORD}
      - GF_SMTP_FROM_ADDRESS=${SMTP_FROM_ADDRESS}
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2G
        reservations:
          cpus: '1'
          memory: 1G

  elasticsearch:
    environment:
      - "ES_JAVA_OPTS=-Xms4g -Xmx4g"
      - cluster.name=asi-logs
      - node.name=asi-es-node-1
      - bootstrap.memory_lock=true
    ulimits:
      memlock:
        soft: -1
        hard: -1
    deploy:
      resources:
        limits:
          cpus: '4'
          memory: 8G
        reservations:
          cpus: '2'
          memory: 4G

  jaeger:
    environment:
      - SPAN_STORAGE_TYPE=elasticsearch
      - ES_SERVER_URLS=http://elasticsearch:9200
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2G
        reservations:
          cpus: '1'
          memory: 1G

  # ============================================================================
  # PRODUCTION NETWORKING
  # ============================================================================

networks:
  asi-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
    driver_opts:
      com.docker.network.bridge.name: asi-prod-bridge
