# ASI System - Environment Variables Template
# ===========================================
# Copy this file to .env and update the values for your environment

# ============================================================================
# GENERAL CONFIGURATION
# ============================================================================

# Environment (development, staging, production)
ENVIRONMENT=development

# Project configuration
PROJECT_NAME=asi-system
CONTAINER_REGISTRY=your-registry.com
IMAGE_TAG=latest

# Network configuration
NETWORK_SUBNET=172.20.0.0/16

# ============================================================================
# DATABASE PASSWORDS & CREDENTIALS
# ============================================================================

# PostgreSQL
POSTGRES_PASSWORD=asi_secure_password_123
POSTGRES_USER=asi_user
POSTGRES_DB=asi_system

# Redis
REDIS_PASSWORD=asi_redis_password_123

# Neo4j
NEO4J_PASSWORD=asi_neo4j_password_123

# ============================================================================
# APPLICATION URLS & ENDPOINTS
# ============================================================================

# API URLs
API_URL=http://localhost:4000/api
WEBSOCKET_URL=ws://localhost:8080

# External service endpoints
KAFKA_BOOTSTRAP_SERVERS=kafka:29092
REDIS_URL=redis://redis:6379/0

# ============================================================================
# MONITORING & OBSERVABILITY
# ============================================================================

# Grafana
GRAFANA_ADMIN_PASSWORD=asi_grafana_admin_123

# Prometheus
PROMETHEUS_RETENTION_TIME=30d
PROMETHEUS_RETENTION_SIZE=10GB

# Jaeger
JAEGER_COLLECTOR_ENDPOINT=http://jaeger:14268/api/traces

# ============================================================================
# EMAIL/SMTP CONFIGURATION (for alerts)
# ============================================================================

# SMTP settings for Grafana alerts
SMTP_HOST=smtp.gmail.com:587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_FROM_ADDRESS=<EMAIL>

# ============================================================================
# CLOUD PROVIDER CONFIGURATION
# ============================================================================

# AWS Configuration
AWS_REGION=us-west-2
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key

# GCP Configuration
GCP_PROJECT_ID=your-gcp-project
GCP_REGION=us-central1
GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account.json

# Azure Configuration
AZURE_SUBSCRIPTION_ID=your-azure-subscription
AZURE_RESOURCE_GROUP=asi-system-rg
AZURE_LOCATION=eastus

# ============================================================================
# KUBERNETES CONFIGURATION
# ============================================================================

# Kubernetes namespace
NAMESPACE=asi-system

# Kubeconfig path
KUBECONFIG=~/.kube/config

# ============================================================================
# SECURITY CONFIGURATION
# ============================================================================

# JWT Secret for authentication
JWT_SECRET=your-super-secret-jwt-key-here

# Encryption keys
ENCRYPTION_KEY=your-32-character-encryption-key

# SSL/TLS certificates
SSL_CERT_PATH=/path/to/ssl/cert.pem
SSL_KEY_PATH=/path/to/ssl/key.pem

# ============================================================================
# PERFORMANCE TUNING
# ============================================================================

# Java heap sizes
KAFKA_HEAP_OPTS=-Xmx2G -Xms2G
SPARK_DRIVER_MEMORY=2g
SPARK_EXECUTOR_MEMORY=4g

# Python/ML configuration
PYTHONUNBUFFERED=1
CUDA_VISIBLE_DEVICES=0

# Go configuration
GOMAXPROCS=4

# Rust configuration
RUST_LOG=info

# ============================================================================
# DEVELOPMENT CONFIGURATION
# ============================================================================

# Debug settings (development only)
DEBUG=false
LOG_LEVEL=INFO

# Hot reload (development only)
ENABLE_HOT_RELOAD=true

# Test database (development only)
TEST_DATABASE_URL=postgresql://test_user:test_password@localhost:5433/asi_test

# ============================================================================
# BACKUP CONFIGURATION
# ============================================================================

# Backup storage
BACKUP_STORAGE_TYPE=s3  # s3, gcs, azure, local
BACKUP_BUCKET=asi-system-backups
BACKUP_RETENTION_DAYS=30

# Backup schedule (cron format)
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM

# ============================================================================
# SCALING CONFIGURATION
# ============================================================================

# Auto-scaling settings
MIN_REPLICAS=1
MAX_REPLICAS=10
TARGET_CPU_UTILIZATION=70

# Resource limits
DEFAULT_CPU_LIMIT=2
DEFAULT_MEMORY_LIMIT=2Gi
DEFAULT_CPU_REQUEST=500m
DEFAULT_MEMORY_REQUEST=512Mi

# ============================================================================
# FEATURE FLAGS
# ============================================================================

# Enable/disable features
ENABLE_TRACING=true
ENABLE_METRICS=true
ENABLE_LOGGING=true
ENABLE_ALERTS=true

# Experimental features
ENABLE_EXPERIMENTAL_FEATURES=false
ENABLE_BETA_UI=false

# ============================================================================
# EXTERNAL INTEGRATIONS
# ============================================================================

# Slack integration (for alerts)
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

# PagerDuty integration
PAGERDUTY_INTEGRATION_KEY=your-pagerduty-integration-key

# GitHub integration (for CI/CD)
GITHUB_TOKEN=your-github-token
GITHUB_REPO=your-org/asi-system

# ============================================================================
# CUSTOM APPLICATION SETTINGS
# ============================================================================

# ASI-specific configuration
ASI_MAX_CONCURRENT_JOBS=5
ASI_MODEL_CACHE_SIZE=10GB
ASI_TRAINING_BATCH_SIZE=32
ASI_INFERENCE_TIMEOUT=30s

# Data processing settings
MAX_KAFKA_BATCH_SIZE=1000
SPARK_SHUFFLE_PARTITIONS=200
SCRAPY_CONCURRENT_REQUESTS=16

# ML/AI settings
PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
TRANSFORMERS_CACHE=/app/cache/transformers
HUGGINGFACE_HUB_CACHE=/app/cache/huggingface

# ============================================================================
# NOTES
# ============================================================================

# 1. Never commit the actual .env file to version control
# 2. Use strong, unique passwords for all services
# 3. Rotate passwords regularly in production
# 4. Use secrets management systems in production (e.g., Kubernetes secrets, AWS Secrets Manager)
# 5. Validate all environment variables before starting services
# 6. Use different .env files for different environments (dev, staging, prod)
