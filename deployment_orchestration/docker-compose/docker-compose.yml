version: '3.8'

# ASI System - Main Docker Compose Configuration
# ==============================================

services:
  # ============================================================================
  # INFRASTRUCTURE SERVICES
  # ============================================================================

  # Apache Kafka for event streaming
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: asi-zookeeper
    hostname: zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
      ZOOKEEPER_SYNC_LIMIT: 2
    volumes:
      - zookeeper-data:/var/lib/zookeeper/data
      - zookeeper-logs:/var/lib/zookeeper/log
    networks:
      - asi-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "nc", "-z", "localhost", "2181"]
      interval: 30s
      timeout: 10s
      retries: 3

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: asi-kafka
    hostname: kafka
    depends_on:
      zookeeper:
        condition: service_healthy
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_JMX_PORT: 9101
      KAFKA_JMX_HOSTNAME: localhost
    ports:
      - "9092:9092"
      - "9101:9101"
    volumes:
      - kafka-data:/var/lib/kafka/data
    networks:
      - asi-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "kafka-broker-api-versions", "--bootstrap-server", "localhost:9092"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for caching and session storage
  redis:
    image: redis:7-alpine
    container_name: asi-redis
    hostname: redis
    command: redis-server --appendonly yes --requirepass asi_redis_password
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - asi-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL for relational data
  postgres:
    image: postgres:15-alpine
    container_name: asi-postgres
    hostname: postgres
    environment:
      POSTGRES_DB: asi_system
      POSTGRES_USER: asi_user
      POSTGRES_PASSWORD: asi_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - asi-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U asi_user -d asi_system"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Neo4j Graph Database
  neo4j:
    image: neo4j:5.11-community
    container_name: asi-neo4j
    hostname: neo4j
    environment:
      NEO4J_AUTH: neo4j/asi_neo4j_password
      NEO4J_PLUGINS: '["apoc", "graph-data-science"]'
      NEO4J_dbms_security_procedures_unrestricted: apoc.*,gds.*
      NEO4J_dbms_memory_heap_initial__size: 1G
      NEO4J_dbms_memory_heap_max__size: 2G
    ports:
      - "7474:7474"
      - "7687:7687"
    volumes:
      - neo4j-data:/data
      - neo4j-logs:/logs
      - neo4j-import:/var/lib/neo4j/import
      - neo4j-plugins:/plugins
    networks:
      - asi-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "cypher-shell", "-u", "neo4j", "-p", "asi_neo4j_password", "RETURN 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ============================================================================
  # INGESTION MODULE
  # ============================================================================

  # Go Kafka Producer
  go-kafka-producer:
    build:
      context: ../ingestion/go-kafka-producer
      dockerfile: Dockerfile
    container_name: asi-go-producer
    hostname: go-producer
    environment:
      - KAFKA_BROKERS=kafka:29092
      - GRPC_PORT=50051
      - METRICS_PORT=8080
      - LOG_LEVEL=INFO
    ports:
      - "50051:50051"
      - "8080:8080"
    depends_on:
      kafka:
        condition: service_healthy
    volumes:
      - producer-data:/app/data
    networks:
      - asi-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "grpc_health_probe", "-addr=localhost:50051"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Rust Kafka Consumer
  rust-kafka-consumer:
    build:
      context: ../ingestion/rust-kafka-consumer
      dockerfile: Dockerfile
    container_name: asi-rust-consumer
    hostname: rust-consumer
    environment:
      - KAFKA_BROKERS=kafka:29092
      - METRICS_PORT=8081
      - RUST_LOG=info
    ports:
      - "8081:8081"
    depends_on:
      kafka:
        condition: service_healthy
    volumes:
      - consumer-data:/app/data
    networks:
      - asi-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Python Scrapy Service
  python-scrapy:
    build:
      context: ../ingestion/python-scrapy
      dockerfile: Dockerfile
    container_name: asi-scrapy
    hostname: scrapy
    environment:
      - KAFKA_BROKERS=kafka:29092
      - REDIS_URL=redis://redis:6379/0
      - METRICS_PORT=8082
      - LOG_LEVEL=INFO
    ports:
      - "8082:8082"
    depends_on:
      kafka:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - scrapy-data:/app/data
    networks:
      - asi-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8082/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ============================================================================
  # DATA INTEGRATION MODULE
  # ============================================================================

  # Apache Spark Master
  spark-master:
    image: bitnami/spark:3.5.0
    container_name: asi-spark-master
    hostname: spark-master
    environment:
      - SPARK_MODE=master
      - SPARK_RPC_AUTHENTICATION_ENABLED=no
      - SPARK_RPC_ENCRYPTION_ENABLED=no
      - SPARK_LOCAL_STORAGE_ENCRYPTION_ENABLED=no
      - SPARK_SSL_ENABLED=no
    ports:
      - "8080:8080"
      - "7077:7077"
    volumes:
      - spark-master-data:/opt/bitnami/spark/data
    networks:
      - asi-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Spark Worker
  spark-worker:
    image: bitnami/spark:3.5.0
    container_name: asi-spark-worker
    hostname: spark-worker
    environment:
      - SPARK_MODE=worker
      - SPARK_MASTER_URL=spark://spark-master:7077
      - SPARK_WORKER_MEMORY=2G
      - SPARK_WORKER_CORES=2
    depends_on:
      spark-master:
        condition: service_healthy
    volumes:
      - spark-worker-data:/opt/bitnami/spark/data
    networks:
      - asi-network
    restart: unless-stopped

  # Scala Spark Pipeline
  scala-spark-pipeline:
    build:
      context: ../data_integration/scala-spark-pipeline
      dockerfile: Dockerfile
    container_name: asi-spark-pipeline
    hostname: spark-pipeline
    environment:
      - SPARK_MASTER_URL=spark://spark-master:7077
      - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USERNAME=neo4j
      - NEO4J_PASSWORD=asi_neo4j_password
    ports:
      - "4040:4040"
      - "8085:8085"
    depends_on:
      spark-master:
        condition: service_healthy
      kafka:
        condition: service_healthy
      neo4j:
        condition: service_healthy
    volumes:
      - spark-pipeline-data:/app/data
    networks:
      - asi-network
    restart: unless-stopped

  # Java Protocol Service
  java-protocol-service:
    build:
      context: ../data_integration/java-protocol-service
      dockerfile: Dockerfile
    container_name: asi-java-protocol
    hostname: java-protocol
    environment:
      - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
      - POSTGRES_URL=******************************************
      - POSTGRES_USER=asi_user
      - POSTGRES_PASSWORD=asi_password
    ports:
      - "8083:8083"
    depends_on:
      kafka:
        condition: service_healthy
      postgres:
        condition: service_healthy
    volumes:
      - protocol-service-data:/app/data
    networks:
      - asi-network
    restart: unless-stopped

  # Rust Device Integration
  rust-device-integration:
    build:
      context: ../data_integration/rust-device-integration
      dockerfile: Dockerfile
    container_name: asi-rust-device
    hostname: rust-device
    environment:
      - KAFKA_BROKERS=kafka:29092
      - METRICS_PORT=8084
      - RUST_LOG=info
    ports:
      - "8084:8084"
    depends_on:
      kafka:
        condition: service_healthy
    volumes:
      - device-service-data:/app/data
    networks:
      - asi-network
    restart: unless-stopped

networks:
  asi-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  # Infrastructure volumes
  zookeeper-data:
  zookeeper-logs:
  kafka-data:
  redis-data:
  postgres-data:
  neo4j-data:
  neo4j-logs:
  neo4j-import:
  neo4j-plugins:

  # Ingestion volumes
  producer-data:
  consumer-data:
  scrapy-data:

  # ============================================================================
  # LEARNING ENGINE MODULE
  # ============================================================================

  # Python Training Engine
  python-training-engine:
    build:
      context: ../learning_engine/python-training-engine
      dockerfile: Dockerfile
      target: production
    container_name: asi-training-engine
    hostname: training-engine
    environment:
      - CUDA_VISIBLE_DEVICES=0
      - PYTHONPATH=/app/src
      - ASI_KAFKA_BOOTSTRAP_SERVERS=kafka:29092
      - ASI_MONITORING_ENABLED=true
      - ASI_MONITORING_PROMETHEUS_PORT=8090
    ports:
      - "8090:8090"
      - "8093:8093"
    depends_on:
      kafka:
        condition: service_healthy
    volumes:
      - training-models:/app/models
      - training-checkpoints:/app/checkpoints
      - training-logs:/app/logs
    networks:
      - asi-network
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  # C++ Inference Engine
  cpp-inference-engine:
    build:
      context: ../learning_engine/cpp-inference-engine
      dockerfile: Dockerfile
    container_name: asi-inference-engine
    hostname: inference-engine
    environment:
      - METRICS_PORT=8091
      - LOG_LEVEL=INFO
    ports:
      - "8091:8091"
      - "50060:50060"
    volumes:
      - inference-models:/app/models
    networks:
      - asi-network
    restart: unless-stopped

  # gRPC Model Server
  grpc-model-server:
    build:
      context: ../learning_engine/grpc-model-server
      dockerfile: Dockerfile
    container_name: asi-model-server
    hostname: model-server
    environment:
      - GRPC_PORT=50061
      - METRICS_PORT=8092
      - MODEL_PATH=/app/models
    ports:
      - "50061:50061"
      - "8092:8092"
    volumes:
      - model-server-data:/app/models
    networks:
      - asi-network
    restart: unless-stopped

  # ============================================================================
  # DECISION ENGINE MODULE
  # ============================================================================

  # Python Rule Engine
  python-rule-engine:
    build:
      context: ../decision_engine/python-rule-engine
      dockerfile: Dockerfile
    container_name: asi-rule-engine
    hostname: rule-engine
    environment:
      - LOG_LEVEL=INFO
      - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
      - GRPC_SERVER_PORT=50070
      - LEARNING_ENGINE_ENDPOINT=model-server:50061
      - REDIS_URL=redis://redis:6379/0
      - POSTGRES_URL=************************************************/asi_system
    ports:
      - "50070:50070"
      - "8070:8070"
    depends_on:
      kafka:
        condition: service_healthy
      redis:
        condition: service_healthy
      postgres:
        condition: service_healthy
    volumes:
      - rule-engine-data:/app/data
    networks:
      - asi-network
    restart: unless-stopped

  # Rust Decision Loop
  rust-decision-loop:
    build:
      context: ../decision_engine/rust-decision-loop
      dockerfile: Dockerfile
    container_name: asi-decision-loop
    hostname: decision-loop
    environment:
      - RUST_LOG=info
      - KAFKA_BROKERS=kafka:29092
      - GRPC_ENDPOINT=rule-engine:50070
      - METRICS_PORT=8071
    ports:
      - "8071:8071"
    depends_on:
      kafka:
        condition: service_healthy
    volumes:
      - decision-loop-data:/app/data
    networks:
      - asi-network
    restart: unless-stopped

  # C++ Edge Processor
  cpp-edge-processor:
    build:
      context: ../decision_engine/cpp-edge-processor
      dockerfile: Dockerfile
    container_name: asi-edge-processor
    hostname: edge-processor
    environment:
      - METRICS_PORT=8072
      - LOG_LEVEL=INFO
    ports:
      - "8072:8072"
    volumes:
      - edge-processor-data:/app/data
    networks:
      - asi-network
    restart: unless-stopped

  # ============================================================================
  # SELF-IMPROVEMENT ENGINE MODULE
  # ============================================================================

  # Python Model Retraining
  python-model-retraining:
    build:
      context: ../self_improvement_engine/python-model-retraining
      dockerfile: Dockerfile
    container_name: asi-model-retraining
    hostname: model-retraining
    environment:
      - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
      - LEARNING_ENGINE_ENDPOINT=training-engine:8090
      - METRICS_PORT=8080
    ports:
      - "8080:8080"
    depends_on:
      kafka:
        condition: service_healthy
    volumes:
      - retraining-data:/app/data
      - retraining-models:/app/models
    networks:
      - asi-network
    restart: unless-stopped

  # Lisp Symbolic Refactor
  lisp-symbolic-refactor:
    build:
      context: ../self_improvement_engine/lisp-symbolic-refactor
      dockerfile: Dockerfile
    container_name: asi-symbolic-refactor
    hostname: symbolic-refactor
    environment:
      - METRICS_PORT=8081
      - LOG_LEVEL=INFO
    ports:
      - "8081:8081"
    volumes:
      - symbolic-refactor-data:/app/data
    networks:
      - asi-network
    restart: unless-stopped

  # Julia Performance Analytics
  julia-performance-analytics:
    build:
      context: ../self_improvement_engine/julia-performance-analytics
      dockerfile: Dockerfile
    container_name: asi-performance-analytics
    hostname: performance-analytics
    environment:
      - DASHBOARD_PORT=8090
      - METRICS_PORT=8082
    ports:
      - "8090:8090"
      - "8082:8082"
    volumes:
      - analytics-data:/app/data
    networks:
      - asi-network
    restart: unless-stopped

  # ============================================================================
  # UI/UX MODULE
  # ============================================================================

  # React Dashboard
  react-dashboard:
    build:
      context: ../ui_ux_module/react-dashboard
      dockerfile: Dockerfile
      target: production
    container_name: asi-react-dashboard
    hostname: react-dashboard
    environment:
      - NODE_ENV=production
      - REACT_APP_API_URL=http://localhost:4000/api
      - REACT_APP_WEBSOCKET_URL=ws://localhost:8080
    ports:
      - "3001:3000"
    volumes:
      - react-logs:/app/logs
    networks:
      - asi-network
    restart: unless-stopped

  # Streamlit Inspector
  streamlit-inspector:
    build:
      context: ../ui_ux_module/streamlit-inspector
      dockerfile: Dockerfile
    container_name: asi-streamlit-inspector
    hostname: streamlit-inspector
    environment:
      - STREAMLIT_SERVER_PORT=8501
      - STREAMLIT_SERVER_ADDRESS=0.0.0.0
    ports:
      - "8501:8501"
    volumes:
      - streamlit-data:/app/data
    networks:
      - asi-network
    restart: unless-stopped

  # WebSocket Server
  websocket-server:
    build:
      context: ../ui_ux_module/websocket-server
      dockerfile: Dockerfile
    container_name: asi-websocket-server
    hostname: websocket-server
    environment:
      - WS_PORT=8080
      - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
    ports:
      - "8080:8080"
    depends_on:
      kafka:
        condition: service_healthy
    networks:
      - asi-network
    restart: unless-stopped

  # ============================================================================
  # CORE RUNTIME & CONTROL MODULE
  # ============================================================================

  # Rust Control Kernel
  rust-control-kernel:
    build:
      context: ../core_runtime_control/rust-control-kernel
      dockerfile: Dockerfile
    container_name: asi-control-kernel
    hostname: control-kernel
    environment:
      - RUST_LOG=info
      - METRICS_PORT=9091
      - CONTROL_LOOP_FREQUENCY=1000
    ports:
      - "9091:9091"
    volumes:
      - control-kernel-data:/app/data
    networks:
      - asi-network
    restart: unless-stopped
    privileged: true

  # C++ Inference Accelerator
  cpp-inference-accelerator:
    build:
      context: ../core_runtime_control/cpp-inference-accelerator
      dockerfile: Dockerfile
    container_name: asi-inference-accelerator
    hostname: inference-accelerator
    environment:
      - METRICS_PORT=9092
      - LOG_LEVEL=INFO
    ports:
      - "9092:9092"
    volumes:
      - accelerator-models:/app/models
    networks:
      - asi-network
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  # Data integration volumes
  spark-master-data:
  spark-worker-data:
  spark-pipeline-data:
  protocol-service-data:
  device-service-data:

  # Learning engine volumes
  training-models:
  training-checkpoints:
  training-logs:
  inference-models:
  model-server-data:

  # Decision engine volumes
  rule-engine-data:
  decision-loop-data:
  edge-processor-data:

  # Self-improvement volumes
  retraining-data:
  retraining-models:
  symbolic-refactor-data:
  analytics-data:

  # UI/UX volumes
  react-logs:
  streamlit-data:

  # ============================================================================
  # MONITORING & OBSERVABILITY
  # ============================================================================

  # Prometheus for metrics collection
  prometheus:
    image: prom/prometheus:v2.45.0
    container_name: asi-prometheus
    hostname: prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    ports:
      - "9090:9090"
    volumes:
      - ../monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ../monitoring/prometheus/rules:/etc/prometheus/rules:ro
      - prometheus-data:/prometheus
    networks:
      - asi-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9090/-/healthy"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Grafana for visualization
  grafana:
    image: grafana/grafana:10.0.0
    container_name: asi-grafana
    hostname: grafana
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=asi_grafana_password
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource,grafana-piechart-panel
      - GF_FEATURE_TOGGLES_ENABLE=ngalert
    ports:
      - "3000:3000"
    volumes:
      - grafana-data:/var/lib/grafana
      - ../monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ../monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      prometheus:
        condition: service_healthy
    networks:
      - asi-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:3000/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Jaeger for distributed tracing
  jaeger:
    image: jaegertracing/all-in-one:1.47
    container_name: asi-jaeger
    hostname: jaeger
    environment:
      - COLLECTOR_OTLP_ENABLED=true
      - JAEGER_DISABLED=false
    ports:
      - "16686:16686"  # Jaeger UI
      - "14268:14268"  # Jaeger collector HTTP
      - "14250:14250"  # Jaeger collector gRPC
      - "6831:6831/udp"  # Jaeger agent
      - "6832:6832/udp"  # Jaeger agent
    volumes:
      - jaeger-data:/tmp
    networks:
      - asi-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:16686"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Elasticsearch for log storage
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: asi-elasticsearch
    hostname: elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
    networks:
      - asi-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Fluentd for log aggregation
  fluentd:
    image: fluent/fluentd:v1.16-debian-1
    container_name: asi-fluentd
    hostname: fluentd
    ports:
      - "24224:24224"
      - "24224:24224/udp"
    volumes:
      - ../monitoring/fluentd/fluent.conf:/fluentd/etc/fluent.conf:ro
      - fluentd-data:/fluentd/log
    depends_on:
      elasticsearch:
        condition: service_healthy
    networks:
      - asi-network
    restart: unless-stopped

  # AlertManager for alerting
  alertmanager:
    image: prom/alertmanager:v0.25.0
    container_name: asi-alertmanager
    hostname: alertmanager
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
      - '--web.external-url=http://localhost:9093'
    ports:
      - "9093:9093"
    volumes:
      - ../monitoring/alertmanager/alertmanager.yml:/etc/alertmanager/alertmanager.yml:ro
      - alertmanager-data:/alertmanager
    depends_on:
      prometheus:
        condition: service_healthy
    networks:
      - asi-network
    restart: unless-stopped

  # Node Exporter for system metrics
  node-exporter:
    image: prom/node-exporter:v1.6.0
    container_name: asi-node-exporter
    hostname: node-exporter
    command:
      - '--path.rootfs=/host'
    ports:
      - "9100:9100"
    volumes:
      - '/:/host:ro,rslave'
    networks:
      - asi-network
    restart: unless-stopped
    pid: host

  # cAdvisor for container metrics
  cadvisor:
    image: gcr.io/cadvisor/cadvisor:v0.47.0
    container_name: asi-cadvisor
    hostname: cadvisor
    ports:
      - "8080:8080"
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    devices:
      - /dev/kmsg
    networks:
      - asi-network
    restart: unless-stopped
    privileged: true

  # Core runtime volumes
  control-kernel-data:
  accelerator-models:

  # Monitoring volumes
  prometheus-data:
  grafana-data:
  jaeger-data:
  elasticsearch-data:
  fluentd-data:
  alertmanager-data:
