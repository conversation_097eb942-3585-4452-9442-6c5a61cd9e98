#!/bin/bash

# ASI System - Docker Images Build Script
# =======================================

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"
CONTAINER_REGISTRY="${CONTAINER_REGISTRY:-asi-registry.com}"
IMAGE_TAG="${IMAGE_TAG:-latest}"
PUSH_IMAGES="${PUSH_IMAGES:-false}"
PARALLEL_BUILDS="${PARALLEL_BUILDS:-4}"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

# Function to build a single image
build_image() {
    local module_path="$1"
    local image_name="$2"
    local dockerfile_path="$3"
    local build_context="$4"
    
    print_status "Building $image_name..."
    
    if [[ ! -f "$dockerfile_path" ]]; then
        print_warning "Dockerfile not found at $dockerfile_path, skipping $image_name"
        return 1
    fi
    
    local full_image_name="$CONTAINER_REGISTRY/$image_name:$IMAGE_TAG"
    
    # Build the image
    if docker build -t "$full_image_name" -f "$dockerfile_path" "$build_context"; then
        print_success "Successfully built $image_name"
        
        # Tag as latest
        docker tag "$full_image_name" "$CONTAINER_REGISTRY/$image_name:latest"
        
        # Push if requested
        if [[ "$PUSH_IMAGES" == "true" ]]; then
            print_status "Pushing $image_name..."
            if docker push "$full_image_name" && docker push "$CONTAINER_REGISTRY/$image_name:latest"; then
                print_success "Successfully pushed $image_name"
            else
                print_error "Failed to push $image_name"
                return 1
            fi
        fi
        
        return 0
    else
        print_error "Failed to build $image_name"
        return 1
    fi
}

# Function to build images in parallel
build_images_parallel() {
    local -a pids=()
    local -a modules=("$@")
    local max_jobs="$PARALLEL_BUILDS"
    local job_count=0
    
    for module_info in "${modules[@]}"; do
        # Wait if we've reached max parallel jobs
        while [[ $job_count -ge $max_jobs ]]; do
            for i in "${!pids[@]}"; do
                if ! kill -0 "${pids[$i]}" 2>/dev/null; then
                    wait "${pids[$i]}"
                    unset "pids[$i]"
                    ((job_count--))
                fi
            done
            sleep 1
        done
        
        # Parse module info
        IFS='|' read -r module_path image_name dockerfile_path build_context <<< "$module_info"
        
        # Start build in background
        build_image "$module_path" "$image_name" "$dockerfile_path" "$build_context" &
        pids+=($!)
        ((job_count++))
    done
    
    # Wait for all remaining jobs
    for pid in "${pids[@]}"; do
        wait "$pid"
    done
}

# Main function
main() {
    print_header "ASI System - Building Docker Images"
    
    print_status "Container Registry: $CONTAINER_REGISTRY"
    print_status "Image Tag: $IMAGE_TAG"
    print_status "Push Images: $PUSH_IMAGES"
    print_status "Parallel Builds: $PARALLEL_BUILDS"
    
    # Check if Docker is running
    if ! docker info >/dev/null 2>&1; then
        print_error "Docker is not running or not accessible"
        exit 1
    fi
    
    # Define all modules to build
    # Format: module_path|image_name|dockerfile_path|build_context
    local modules=(
        # Ingestion Module
        "ingestion/go-kafka-producer|go-kafka-producer|$PROJECT_ROOT/ingestion/go-kafka-producer/Dockerfile|$PROJECT_ROOT/ingestion/go-kafka-producer"
        "ingestion/rust-kafka-consumer|rust-kafka-consumer|$PROJECT_ROOT/ingestion/rust-kafka-consumer/Dockerfile|$PROJECT_ROOT/ingestion/rust-kafka-consumer"
        "ingestion/python-scrapy|python-scrapy|$PROJECT_ROOT/ingestion/python-scrapy/Dockerfile|$PROJECT_ROOT/ingestion/python-scrapy"
        
        # Data Integration Module
        "data_integration/scala-spark-pipeline|scala-spark-pipeline|$PROJECT_ROOT/data_integration/scala-spark-pipeline/Dockerfile|$PROJECT_ROOT/data_integration/scala-spark-pipeline"
        "data_integration/java-protocol-service|java-protocol-service|$PROJECT_ROOT/data_integration/java-protocol-service/Dockerfile|$PROJECT_ROOT/data_integration/java-protocol-service"
        "data_integration/rust-device-integration|rust-device-integration|$PROJECT_ROOT/data_integration/rust-device-integration/Dockerfile|$PROJECT_ROOT/data_integration/rust-device-integration"
        
        # Learning Engine Module
        "learning_engine/python-training-engine|python-training-engine|$PROJECT_ROOT/learning_engine/python-training-engine/Dockerfile|$PROJECT_ROOT/learning_engine/python-training-engine"
        "learning_engine/cpp-inference-engine|cpp-inference-engine|$PROJECT_ROOT/learning_engine/cpp-inference-engine/Dockerfile|$PROJECT_ROOT/learning_engine/cpp-inference-engine"
        "learning_engine/grpc-model-server|grpc-model-server|$PROJECT_ROOT/learning_engine/grpc-model-server/Dockerfile|$PROJECT_ROOT/learning_engine/grpc-model-server"
        
        # Decision Engine Module
        "decision_engine/python-rule-engine|python-rule-engine|$PROJECT_ROOT/decision_engine/python-rule-engine/Dockerfile|$PROJECT_ROOT/decision_engine/python-rule-engine"
        "decision_engine/rust-decision-loop|rust-decision-loop|$PROJECT_ROOT/decision_engine/rust-decision-loop/Dockerfile|$PROJECT_ROOT/decision_engine/rust-decision-loop"
        "decision_engine/cpp-edge-processor|cpp-edge-processor|$PROJECT_ROOT/decision_engine/cpp-edge-processor/Dockerfile|$PROJECT_ROOT/decision_engine/cpp-edge-processor"
        
        # Self-Improvement Engine Module
        "self_improvement_engine/python-model-retraining|python-model-retraining|$PROJECT_ROOT/self_improvement_engine/python-model-retraining/Dockerfile|$PROJECT_ROOT/self_improvement_engine/python-model-retraining"
        "self_improvement_engine/lisp-symbolic-refactor|lisp-symbolic-refactor|$PROJECT_ROOT/self_improvement_engine/lisp-symbolic-refactor/Dockerfile|$PROJECT_ROOT/self_improvement_engine/lisp-symbolic-refactor"
        "self_improvement_engine/julia-performance-analytics|julia-performance-analytics|$PROJECT_ROOT/self_improvement_engine/julia-performance-analytics/Dockerfile|$PROJECT_ROOT/self_improvement_engine/julia-performance-analytics"
        
        # UI/UX Module
        "ui_ux_module/react-dashboard|react-dashboard|$PROJECT_ROOT/ui_ux_module/react-dashboard/Dockerfile|$PROJECT_ROOT/ui_ux_module/react-dashboard"
        "ui_ux_module/streamlit-inspector|streamlit-inspector|$PROJECT_ROOT/ui_ux_module/streamlit-inspector/Dockerfile|$PROJECT_ROOT/ui_ux_module/streamlit-inspector"
        "ui_ux_module/websocket-server|websocket-server|$PROJECT_ROOT/ui_ux_module/websocket-server/Dockerfile|$PROJECT_ROOT/ui_ux_module/websocket-server"
        
        # Core Runtime & Control Module
        "core_runtime_control/rust-control-kernel|rust-control-kernel|$PROJECT_ROOT/core_runtime_control/rust-control-kernel/Dockerfile|$PROJECT_ROOT/core_runtime_control/rust-control-kernel"
        "core_runtime_control/cpp-inference-accelerator|cpp-inference-accelerator|$PROJECT_ROOT/core_runtime_control/cpp-inference-accelerator/Dockerfile|$PROJECT_ROOT/core_runtime_control/cpp-inference-accelerator"
    )
    
    print_status "Found ${#modules[@]} modules to build"
    
    # Build images
    local start_time=$(date +%s)
    
    if [[ "$PARALLEL_BUILDS" -gt 1 ]]; then
        print_status "Building images in parallel (max $PARALLEL_BUILDS concurrent builds)..."
        build_images_parallel "${modules[@]}"
    else
        print_status "Building images sequentially..."
        for module_info in "${modules[@]}"; do
            IFS='|' read -r module_path image_name dockerfile_path build_context <<< "$module_info"
            build_image "$module_path" "$image_name" "$dockerfile_path" "$build_context"
        done
    fi
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    print_success "All images built successfully in ${duration} seconds"
    
    # Display built images
    print_header "Built Images"
    docker images "$CONTAINER_REGISTRY/*:$IMAGE_TAG" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
    
    # Clean up dangling images
    print_status "Cleaning up dangling images..."
    docker image prune -f >/dev/null 2>&1 || true
    
    print_success "Build process completed successfully!"
}

# Function to show usage
show_usage() {
    cat << EOF
ASI System Docker Images Build Script

Usage: $0 [OPTIONS]

Options:
    --registry REGISTRY     Container registry [default: asi-registry.com]
    --tag TAG              Image tag [default: latest]
    --push                 Push images to registry after building
    --parallel JOBS        Number of parallel builds [default: 4]
    --sequential           Build images sequentially (same as --parallel 1)
    -h, --help             Show this help message

Environment Variables:
    CONTAINER_REGISTRY     Same as --registry
    IMAGE_TAG              Same as --tag
    PUSH_IMAGES           Set to 'true' to push images
    PARALLEL_BUILDS       Same as --parallel

Examples:
    # Build all images with default settings
    $0

    # Build and push images to custom registry
    $0 --registry my-registry.com --tag v1.0.0 --push

    # Build images sequentially
    $0 --sequential

    # Build with custom parallelism
    $0 --parallel 8

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --registry)
            CONTAINER_REGISTRY="$2"
            shift 2
            ;;
        --tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        --push)
            PUSH_IMAGES="true"
            shift
            ;;
        --parallel)
            PARALLEL_BUILDS="$2"
            shift 2
            ;;
        --sequential)
            PARALLEL_BUILDS="1"
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Run main function
main "$@"
