#!/bin/bash

# ASI System - Main Deployment Script
# ===================================

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DEPLOYMENT_ROOT="$PROJECT_ROOT"

# Default values
ENVIRONMENT="${ENVIRONMENT:-development}"
CLOUD_PROVIDER="${CLOUD_PROVIDER:-aws}"
REGION="${REGION:-us-west-2}"
CLUSTER_NAME="${CLUSTER_NAME:-asi-system-cluster}"
NAMESPACE="${NAMESPACE:-asi-system}"
SKIP_INFRA="${SKIP_INFRA:-false}"
SKIP_BUILD="${SKIP_BUILD:-false}"
SKIP_DEPLOY="${SKIP_DEPLOY:-false}"
DRY_RUN="${DRY_RUN:-false}"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${CYAN}================================${NC}"
    echo -e "${CYAN}$1${NC}"
    echo -e "${CYAN}================================${NC}"
}

# Function to check prerequisites
check_prerequisites() {
    print_header "Checking Prerequisites"
    
    local missing_tools=()
    
    # Check required tools
    command -v docker >/dev/null 2>&1 || missing_tools+=("docker")
    command -v kubectl >/dev/null 2>&1 || missing_tools+=("kubectl")
    command -v helm >/dev/null 2>&1 || missing_tools+=("helm")
    command -v terraform >/dev/null 2>&1 || missing_tools+=("terraform")
    
    if [[ "$CLOUD_PROVIDER" == "aws" ]]; then
        command -v aws >/dev/null 2>&1 || missing_tools+=("aws-cli")
    elif [[ "$CLOUD_PROVIDER" == "gcp" ]]; then
        command -v gcloud >/dev/null 2>&1 || missing_tools+=("gcloud")
    fi
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        print_error "Missing required tools: ${missing_tools[*]}"
        print_error "Please install the missing tools and try again."
        exit 1
    fi
    
    print_success "All prerequisites are satisfied"
}

# Function to validate configuration
validate_config() {
    print_header "Validating Configuration"
    
    # Check environment
    if [[ ! "$ENVIRONMENT" =~ ^(development|staging|production)$ ]]; then
        print_error "Invalid environment: $ENVIRONMENT. Must be one of: development, staging, production"
        exit 1
    fi
    
    # Check cloud provider
    if [[ ! "$CLOUD_PROVIDER" =~ ^(aws|gcp|azure)$ ]]; then
        print_error "Invalid cloud provider: $CLOUD_PROVIDER. Must be one of: aws, gcp, azure"
        exit 1
    fi
    
    # Check if required files exist
    local required_files=(
        "$DEPLOYMENT_ROOT/terraform/$CLOUD_PROVIDER/main.tf"
        "$DEPLOYMENT_ROOT/helm/asi-system/Chart.yaml"
        "$DEPLOYMENT_ROOT/k8s/namespaces/asi-system.yaml"
    )
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            print_error "Required file not found: $file"
            exit 1
        fi
    done
    
    print_success "Configuration is valid"
}

# Function to setup infrastructure
setup_infrastructure() {
    if [[ "$SKIP_INFRA" == "true" ]]; then
        print_warning "Skipping infrastructure setup"
        return 0
    fi
    
    print_header "Setting Up Infrastructure"
    
    cd "$DEPLOYMENT_ROOT/terraform/$CLOUD_PROVIDER"
    
    # Initialize Terraform
    print_status "Initializing Terraform..."
    terraform init
    
    # Plan infrastructure changes
    print_status "Planning infrastructure changes..."
    terraform plan -var="environment=$ENVIRONMENT" -var="aws_region=$REGION" -out=tfplan
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_warning "Dry run mode - not applying infrastructure changes"
        return 0
    fi
    
    # Apply infrastructure changes
    print_status "Applying infrastructure changes..."
    terraform apply tfplan
    
    # Update kubeconfig
    if [[ "$CLOUD_PROVIDER" == "aws" ]]; then
        print_status "Updating kubeconfig..."
        aws eks update-kubeconfig --region "$REGION" --name "$CLUSTER_NAME"
    fi
    
    print_success "Infrastructure setup completed"
}

# Function to build Docker images
build_images() {
    if [[ "$SKIP_BUILD" == "true" ]]; then
        print_warning "Skipping image build"
        return 0
    fi
    
    print_header "Building Docker Images"
    
    # Build all images using the build script
    if [[ -f "$DEPLOYMENT_ROOT/scripts/build-images.sh" ]]; then
        print_status "Building all Docker images..."
        bash "$DEPLOYMENT_ROOT/scripts/build-images.sh"
    else
        print_warning "Build script not found, building images individually..."
        
        # Build images for each module
        local modules=(
            "ingestion/go-kafka-producer"
            "ingestion/rust-kafka-consumer"
            "ingestion/python-scrapy"
            "data_integration/scala-spark-pipeline"
            "data_integration/java-protocol-service"
            "data_integration/rust-device-integration"
            "learning_engine/python-training-engine"
            "learning_engine/cpp-inference-engine"
            "learning_engine/grpc-model-server"
            "decision_engine/python-rule-engine"
            "decision_engine/rust-decision-loop"
            "decision_engine/cpp-edge-processor"
            "self_improvement_engine/python-model-retraining"
            "self_improvement_engine/lisp-symbolic-refactor"
            "self_improvement_engine/julia-performance-analytics"
            "ui_ux_module/react-dashboard"
            "ui_ux_module/streamlit-inspector"
            "ui_ux_module/websocket-server"
            "core_runtime_control/rust-control-kernel"
            "core_runtime_control/cpp-inference-accelerator"
        )
        
        for module in "${modules[@]}"; do
            if [[ -f "$PROJECT_ROOT/../$module/Dockerfile" ]]; then
                print_status "Building $module..."
                docker build -t "asi-$(basename "$module"):latest" "$PROJECT_ROOT/../$module"
            else
                print_warning "Dockerfile not found for $module, skipping..."
            fi
        done
    fi
    
    print_success "Image build completed"
}

# Function to deploy to Kubernetes
deploy_to_kubernetes() {
    if [[ "$SKIP_DEPLOY" == "true" ]]; then
        print_warning "Skipping Kubernetes deployment"
        return 0
    fi
    
    print_header "Deploying to Kubernetes"
    
    # Create namespace
    print_status "Creating namespace..."
    kubectl apply -f "$DEPLOYMENT_ROOT/k8s/namespaces/"
    
    # Deploy using Helm
    print_status "Deploying ASI system using Helm..."
    
    local helm_args=(
        "upgrade" "--install" "asi-system"
        "$DEPLOYMENT_ROOT/helm/asi-system"
        "--namespace" "$NAMESPACE"
        "--create-namespace"
        "--values" "$DEPLOYMENT_ROOT/helm/asi-system/values-$ENVIRONMENT.yaml"
        "--set" "global.environment=$ENVIRONMENT"
        "--set" "global.imageTag=latest"
    )
    
    if [[ "$DRY_RUN" == "true" ]]; then
        helm_args+=("--dry-run")
        print_warning "Dry run mode - not actually deploying"
    fi
    
    helm "${helm_args[@]}"
    
    if [[ "$DRY_RUN" != "true" ]]; then
        # Wait for deployment to be ready
        print_status "Waiting for deployment to be ready..."
        kubectl wait --for=condition=available --timeout=600s deployment --all -n "$NAMESPACE"
        
        # Deploy monitoring stack
        print_status "Deploying monitoring stack..."
        kubectl apply -f "$DEPLOYMENT_ROOT/monitoring/prometheus/"
        kubectl apply -f "$DEPLOYMENT_ROOT/monitoring/grafana/"
        
        print_success "Kubernetes deployment completed"
    fi
}

# Function to verify deployment
verify_deployment() {
    if [[ "$DRY_RUN" == "true" ]]; then
        print_warning "Skipping verification in dry run mode"
        return 0
    fi
    
    print_header "Verifying Deployment"
    
    # Check pod status
    print_status "Checking pod status..."
    kubectl get pods -n "$NAMESPACE"
    
    # Check service status
    print_status "Checking service status..."
    kubectl get services -n "$NAMESPACE"
    
    # Check ingress status
    print_status "Checking ingress status..."
    kubectl get ingress -n "$NAMESPACE" || print_warning "No ingress resources found"
    
    # Run health checks
    print_status "Running health checks..."
    local failed_checks=0
    
    # Check if all pods are running
    local not_running_pods
    not_running_pods=$(kubectl get pods -n "$NAMESPACE" --field-selector=status.phase!=Running --no-headers 2>/dev/null | wc -l)
    
    if [[ "$not_running_pods" -gt 0 ]]; then
        print_warning "$not_running_pods pods are not in Running state"
        kubectl get pods -n "$NAMESPACE" --field-selector=status.phase!=Running
        ((failed_checks++))
    fi
    
    # Check service endpoints
    local services_without_endpoints
    services_without_endpoints=$(kubectl get endpoints -n "$NAMESPACE" --no-headers 2>/dev/null | awk '$2 == "<none>" {print $1}' | wc -l)
    
    if [[ "$services_without_endpoints" -gt 0 ]]; then
        print_warning "$services_without_endpoints services have no endpoints"
        kubectl get endpoints -n "$NAMESPACE" | awk '$2 == "<none>" {print $1}'
        ((failed_checks++))
    fi
    
    if [[ "$failed_checks" -eq 0 ]]; then
        print_success "All health checks passed"
    else
        print_warning "$failed_checks health checks failed"
    fi
}

# Function to display access information
display_access_info() {
    if [[ "$DRY_RUN" == "true" ]]; then
        return 0
    fi
    
    print_header "Access Information"
    
    # Get LoadBalancer services
    local lb_services
    lb_services=$(kubectl get services -n "$NAMESPACE" --field-selector=spec.type=LoadBalancer --no-headers 2>/dev/null || true)
    
    if [[ -n "$lb_services" ]]; then
        print_status "LoadBalancer services:"
        echo "$lb_services"
    fi
    
    # Get ingress information
    local ingresses
    ingresses=$(kubectl get ingress -n "$NAMESPACE" --no-headers 2>/dev/null || true)
    
    if [[ -n "$ingresses" ]]; then
        print_status "Ingress resources:"
        echo "$ingresses"
    fi
    
    # Port forwarding commands
    print_status "Port forwarding commands for local access:"
    echo "  Grafana:    kubectl port-forward svc/grafana 3000:3000 -n $NAMESPACE"
    echo "  Prometheus: kubectl port-forward svc/prometheus 9090:9090 -n $NAMESPACE"
    echo "  Dashboard:  kubectl port-forward svc/react-dashboard 3001:3000 -n $NAMESPACE"
    echo "  Inspector:  kubectl port-forward svc/streamlit-inspector 8501:8501 -n $NAMESPACE"
}

# Function to show usage
show_usage() {
    cat << EOF
ASI System Deployment Script

Usage: $0 [OPTIONS]

Options:
    -e, --environment ENV       Environment (development|staging|production) [default: development]
    -c, --cloud-provider CLOUD  Cloud provider (aws|gcp|azure) [default: aws]
    -r, --region REGION         Cloud region [default: us-west-2]
    -n, --namespace NAMESPACE   Kubernetes namespace [default: asi-system]
    --cluster-name NAME         Kubernetes cluster name [default: asi-system-cluster]
    --skip-infra               Skip infrastructure setup
    --skip-build               Skip Docker image build
    --skip-deploy              Skip Kubernetes deployment
    --dry-run                  Perform a dry run without making changes
    -h, --help                 Show this help message

Environment Variables:
    ENVIRONMENT                Same as --environment
    CLOUD_PROVIDER            Same as --cloud-provider
    REGION                    Same as --region
    NAMESPACE                 Same as --namespace
    CLUSTER_NAME              Same as --cluster-name
    SKIP_INFRA                Same as --skip-infra
    SKIP_BUILD                Same as --skip-build
    SKIP_DEPLOY               Same as --skip-deploy
    DRY_RUN                   Same as --dry-run

Examples:
    # Deploy to development environment on AWS
    $0 --environment development --cloud-provider aws

    # Deploy to production with existing infrastructure
    $0 --environment production --skip-infra

    # Dry run for staging environment
    $0 --environment staging --dry-run

    # Deploy only the application (skip infra and build)
    $0 --skip-infra --skip-build

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -c|--cloud-provider)
            CLOUD_PROVIDER="$2"
            shift 2
            ;;
        -r|--region)
            REGION="$2"
            shift 2
            ;;
        -n|--namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        --cluster-name)
            CLUSTER_NAME="$2"
            shift 2
            ;;
        --skip-infra)
            SKIP_INFRA="true"
            shift
            ;;
        --skip-build)
            SKIP_BUILD="true"
            shift
            ;;
        --skip-deploy)
            SKIP_DEPLOY="true"
            shift
            ;;
        --dry-run)
            DRY_RUN="true"
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Main execution
main() {
    print_header "ASI System Deployment"
    print_status "Environment: $ENVIRONMENT"
    print_status "Cloud Provider: $CLOUD_PROVIDER"
    print_status "Region: $REGION"
    print_status "Cluster: $CLUSTER_NAME"
    print_status "Namespace: $NAMESPACE"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_warning "DRY RUN MODE - No changes will be made"
    fi
    
    check_prerequisites
    validate_config
    setup_infrastructure
    build_images
    deploy_to_kubernetes
    verify_deployment
    display_access_info
    
    print_success "ASI System deployment completed successfully!"
}

# Run main function
main "$@"
