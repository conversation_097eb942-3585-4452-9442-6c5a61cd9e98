# ASI System - Default Helm Values
# ================================

# Global configuration
global:
  # Image registry and tag
  imageRegistry: "asi-registry.com"
  imageTag: "1.0.0"
  imagePullPolicy: IfNotPresent

  # Environment
  environment: development

  # Namespace
  namespace: asi-system

  # Storage class
  storageClass: "standard"

  # Security context
  securityContext:
    runAsNonRoot: true
    runAsUser: 1000
    fsGroup: 1000

# ============================================================================
# INFRASTRUCTURE SERVICES
# ============================================================================

# Apache Kafka
kafka:
  enabled: true
  replicaCount: 3
  auth:
    enabled: false
  zookeeper:
    enabled: true
    replicaCount: 3
  persistence:
    enabled: true
    size: 10Gi
  resources:
    requests:
      cpu: 500m
      memory: 1Gi
    limits:
      cpu: 2
      memory: 2Gi

# Redis
redis:
  enabled: true
  auth:
    enabled: true
    password: "asi_redis_password"
  master:
    persistence:
      enabled: true
      size: 5Gi
  resources:
    requests:
      cpu: 250m
      memory: 512Mi
    limits:
      cpu: 1
      memory: 1Gi

# PostgreSQL
postgresql:
  enabled: true
  auth:
    postgresPassword: "asi_postgres_password"
    username: "asi_user"
    password: "asi_password"
    database: "asi_system"
  primary:
    persistence:
      enabled: true
      size: 20Gi
  resources:
    requests:
      cpu: 500m
      memory: 1Gi
    limits:
      cpu: 2
      memory: 2Gi

# Neo4j
neo4j:
  enabled: true
  neo4j:
    password: "asi_neo4j_password"
  volumes:
    data:
      mode: "defaultStorageClass"
      defaultStorageClass:
        requests:
          storage: 10Gi
  resources:
    cpu: "1"
    memory: "2Gi"

# Apache Spark
spark:
  enabled: true
  master:
    replicaCount: 1
  worker:
    replicaCount: 2
    resources:
      requests:
        cpu: 1
        memory: 2Gi
      limits:
        cpu: 2
        memory: 4Gi

# ============================================================================
# INGESTION MODULE
# ============================================================================

ingestion:
  # Go Kafka Producer
  goKafkaProducer:
    enabled: true
    replicaCount: 2
    image:
      repository: go-kafka-producer
      tag: ""
    service:
      type: ClusterIP
      grpcPort: 50051
      metricsPort: 8080
    resources:
      requests:
        cpu: 250m
        memory: 512Mi
      limits:
        cpu: 1
        memory: 1Gi

  # Rust Kafka Consumer
  rustKafkaConsumer:
    enabled: true
    replicaCount: 2
    image:
      repository: rust-kafka-consumer
      tag: ""
    service:
      type: ClusterIP
      metricsPort: 8081
    resources:
      requests:
        cpu: 250m
        memory: 512Mi
      limits:
        cpu: 1
        memory: 1Gi

  # Python Scrapy
  pythonScrapy:
    enabled: true
    replicaCount: 1
    image:
      repository: python-scrapy
      tag: ""
    service:
      type: ClusterIP
      metricsPort: 8082
    resources:
      requests:
        cpu: 500m
        memory: 1Gi
      limits:
        cpu: 2
        memory: 2Gi

# ============================================================================
# DATA INTEGRATION MODULE
# ============================================================================

dataIntegration:
  # Scala Spark Pipeline
  scalaSparkPipeline:
    enabled: true
    replicaCount: 1
    image:
      repository: scala-spark-pipeline
      tag: ""
    service:
      type: ClusterIP
      uiPort: 4040
      metricsPort: 8085
    resources:
      requests:
        cpu: 1
        memory: 2Gi
      limits:
        cpu: 4
        memory: 8Gi

  # Java Protocol Service
  javaProtocolService:
    enabled: true
    replicaCount: 2
    image:
      repository: java-protocol-service
      tag: ""
    service:
      type: ClusterIP
      port: 8083
    resources:
      requests:
        cpu: 500m
        memory: 1Gi
      limits:
        cpu: 2
        memory: 2Gi

  # Rust Device Integration
  rustDeviceIntegration:
    enabled: true
    replicaCount: 1
    image:
      repository: rust-device-integration
      tag: ""
    service:
      type: ClusterIP
      metricsPort: 8084
    resources:
      requests:
        cpu: 250m
        memory: 512Mi
      limits:
        cpu: 1
        memory: 1Gi

# ============================================================================
# LEARNING ENGINE MODULE
# ============================================================================

learningEngine:
  # Python Training Engine
  pythonTrainingEngine:
    enabled: true
    replicaCount: 1
    image:
      repository: python-training-engine
      tag: ""
    service:
      type: ClusterIP
      metricsPort: 8090
      healthPort: 8093
    resources:
      requests:
        cpu: 2
        memory: 4Gi
        nvidia.com/gpu: 0
      limits:
        cpu: 8
        memory: 16Gi
        nvidia.com/gpu: 1
    persistence:
      enabled: true
      size: 50Gi

  # C++ Inference Engine
  cppInferenceEngine:
    enabled: true
    replicaCount: 2
    image:
      repository: cpp-inference-engine
      tag: ""
    service:
      type: ClusterIP
      grpcPort: 50060
      metricsPort: 8091
    resources:
      requests:
        cpu: 1
        memory: 2Gi
      limits:
        cpu: 4
        memory: 4Gi

  # gRPC Model Server
  grpcModelServer:
    enabled: true
    replicaCount: 2
    image:
      repository: grpc-model-server
      tag: ""
    service:
      type: ClusterIP
      grpcPort: 50061
      metricsPort: 8092
    resources:
      requests:
        cpu: 500m
        memory: 1Gi
      limits:
        cpu: 2
        memory: 2Gi

# ============================================================================
# DECISION ENGINE MODULE
# ============================================================================

decisionEngine:
  # Python Rule Engine
  pythonRuleEngine:
    enabled: true
    replicaCount: 3
    image:
      repository: python-rule-engine
      tag: ""
    service:
      type: ClusterIP
      grpcPort: 50070
      metricsPort: 8070
    resources:
      requests:
        cpu: 500m
        memory: 1Gi
      limits:
        cpu: 2
        memory: 2Gi

  # Rust Decision Loop
  rustDecisionLoop:
    enabled: true
    replicaCount: 2
    image:
      repository: rust-decision-loop
      tag: ""
    service:
      type: ClusterIP
      metricsPort: 8071
    resources:
      requests:
        cpu: 500m
        memory: 512Mi
      limits:
        cpu: 2
        memory: 1Gi

  # C++ Edge Processor
  cppEdgeProcessor:
    enabled: true
    replicaCount: 1
    image:
      repository: cpp-edge-processor
      tag: ""
    service:
      type: ClusterIP
      metricsPort: 8072
    resources:
      requests:
        cpu: 1
        memory: 1Gi
      limits:
        cpu: 4
        memory: 2Gi

# ============================================================================
# SELF-IMPROVEMENT ENGINE MODULE
# ============================================================================

selfImprovementEngine:
  # Python Model Retraining
  pythonModelRetraining:
    enabled: true
    replicaCount: 1
    image:
      repository: python-model-retraining
      tag: ""
    service:
      type: ClusterIP
      metricsPort: 8080
    resources:
      requests:
        cpu: 1
        memory: 2Gi
      limits:
        cpu: 4
        memory: 8Gi
    persistence:
      enabled: true
      size: 20Gi

  # Lisp Symbolic Refactor
  lispSymbolicRefactor:
    enabled: true
    replicaCount: 1
    image:
      repository: lisp-symbolic-refactor
      tag: ""
    service:
      type: ClusterIP
      metricsPort: 8081
    resources:
      requests:
        cpu: 500m
        memory: 1Gi
      limits:
        cpu: 2
        memory: 2Gi

  # Julia Performance Analytics
  juliaPerformanceAnalytics:
    enabled: true
    replicaCount: 1
    image:
      repository: julia-performance-analytics
      tag: ""
    service:
      type: ClusterIP
      dashboardPort: 8090
      metricsPort: 8082
    resources:
      requests:
        cpu: 500m
        memory: 1Gi
      limits:
        cpu: 2
        memory: 4Gi

# ============================================================================
# UI/UX MODULE
# ============================================================================

uiUx:
  # React Dashboard
  reactDashboard:
    enabled: true
    replicaCount: 2
    image:
      repository: react-dashboard
      tag: ""
    service:
      type: ClusterIP
      port: 3000
    ingress:
      enabled: true
      className: "nginx"
      annotations:
        nginx.ingress.kubernetes.io/rewrite-target: /
      hosts:
        - host: asi-dashboard.local
          paths:
            - path: /
              pathType: Prefix
    resources:
      requests:
        cpu: 250m
        memory: 512Mi
      limits:
        cpu: 1
        memory: 1Gi

  # Streamlit Inspector
  streamlitInspector:
    enabled: true
    replicaCount: 1
    image:
      repository: streamlit-inspector
      tag: ""
    service:
      type: ClusterIP
      port: 8501
    ingress:
      enabled: true
      className: "nginx"
      hosts:
        - host: asi-inspector.local
          paths:
            - path: /
              pathType: Prefix
    resources:
      requests:
        cpu: 250m
        memory: 512Mi
      limits:
        cpu: 1
        memory: 2Gi

  # WebSocket Server
  websocketServer:
    enabled: true
    replicaCount: 2
    image:
      repository: websocket-server
      tag: ""
    service:
      type: ClusterIP
      port: 8080
    resources:
      requests:
        cpu: 250m
        memory: 512Mi
      limits:
        cpu: 1
        memory: 1Gi

# ============================================================================
# CORE RUNTIME & CONTROL MODULE
# ============================================================================

coreRuntime:
  # Rust Control Kernel
  rustControlKernel:
    enabled: true
    replicaCount: 1
    image:
      repository: rust-control-kernel
      tag: ""
    service:
      type: ClusterIP
      metricsPort: 9091
    resources:
      requests:
        cpu: 1
        memory: 1Gi
      limits:
        cpu: 4
        memory: 2Gi
    securityContext:
      privileged: true

  # C++ Inference Accelerator
  cppInferenceAccelerator:
    enabled: true
    replicaCount: 1
    image:
      repository: cpp-inference-accelerator
      tag: ""
    service:
      type: ClusterIP
      metricsPort: 9092
    resources:
      requests:
        cpu: 2
        memory: 2Gi
        nvidia.com/gpu: 0
      limits:
        cpu: 8
        memory: 8Gi
        nvidia.com/gpu: 1

# ============================================================================
# MONITORING & OBSERVABILITY
# ============================================================================

monitoring:
  # Prometheus
  prometheus:
    enabled: true
    server:
      retention: "30d"
      resources:
        requests:
          cpu: 1
          memory: 2Gi
        limits:
          cpu: 4
          memory: 8Gi
      persistentVolume:
        enabled: true
        size: 50Gi

  # Grafana
  grafana:
    enabled: true
    adminPassword: "asi_grafana_admin"
    persistence:
      enabled: true
      size: 5Gi
    resources:
      requests:
        cpu: 250m
        memory: 512Mi
      limits:
        cpu: 1
        memory: 2Gi
    ingress:
      enabled: true
      className: "nginx"
      hosts:
        - asi-grafana.local

  # Jaeger
  jaeger:
    enabled: true
    storage:
      type: memory
    resources:
      requests:
        cpu: 250m
        memory: 512Mi
      limits:
        cpu: 1
        memory: 2Gi

# ============================================================================
# LOGGING
# ============================================================================

logging:
  # Elasticsearch
  elasticsearch:
    enabled: true
    replicas: 1
    minimumMasterNodes: 1
    resources:
      requests:
        cpu: 1
        memory: 2Gi
      limits:
        cpu: 2
        memory: 4Gi
    volumeClaimTemplate:
      resources:
        requests:
          storage: 30Gi

# ============================================================================
# AUTOSCALING
# ============================================================================

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 10
  targetCPUUtilizationPercentage: 70
  targetMemoryUtilizationPercentage: 80

# ============================================================================
# INGRESS
# ============================================================================

ingress:
  enabled: true
  className: "nginx"
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/use-regex: "true"
  tls:
    enabled: false
    secretName: asi-system-tls

# ============================================================================
# SERVICE MESH (ISTIO)
# ============================================================================

serviceMesh:
  enabled: false
  istio:
    injection: enabled
    gateway:
      enabled: true
    virtualService:
      enabled: true
