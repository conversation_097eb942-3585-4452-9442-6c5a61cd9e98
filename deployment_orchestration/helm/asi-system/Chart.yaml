apiVersion: v2
name: asi-system
description: A Helm chart for ASI (Artificial Super Intelligence) System
type: application
version: 1.0.0
appVersion: "1.0.0"

keywords:
  - artificial-intelligence
  - machine-learning
  - microservices
  - kubernetes
  - asi

home: https://github.com/asi-system/deployment-orchestration
sources:
  - https://github.com/asi-system/deployment-orchestration

maintainers:
  - name: ASI System Team
    email: <EMAIL>
    url: https://asi-system.com

annotations:
  category: AI/ML
  licenses: MIT
  images: |
    - name: go-kafka-producer
      image: asi-registry.com/go-kafka-producer:1.0.0
    - name: rust-kafka-consumer
      image: asi-registry.com/rust-kafka-consumer:1.0.0
    - name: python-scrapy
      image: asi-registry.com/python-scrapy:1.0.0
    - name: scala-spark-pipeline
      image: asi-registry.com/scala-spark-pipeline:1.0.0
    - name: java-protocol-service
      image: asi-registry.com/java-protocol-service:1.0.0
    - name: rust-device-integration
      image: asi-registry.com/rust-device-integration:1.0.0
    - name: python-training-engine
      image: asi-registry.com/python-training-engine:1.0.0
    - name: cpp-inference-engine
      image: asi-registry.com/cpp-inference-engine:1.0.0
    - name: grpc-model-server
      image: asi-registry.com/grpc-model-server:1.0.0
    - name: python-rule-engine
      image: asi-registry.com/python-rule-engine:1.0.0
    - name: rust-decision-loop
      image: asi-registry.com/rust-decision-loop:1.0.0
    - name: cpp-edge-processor
      image: asi-registry.com/cpp-edge-processor:1.0.0
    - name: python-model-retraining
      image: asi-registry.com/python-model-retraining:1.0.0
    - name: lisp-symbolic-refactor
      image: asi-registry.com/lisp-symbolic-refactor:1.0.0
    - name: julia-performance-analytics
      image: asi-registry.com/julia-performance-analytics:1.0.0
    - name: react-dashboard
      image: asi-registry.com/react-dashboard:1.0.0
    - name: streamlit-inspector
      image: asi-registry.com/streamlit-inspector:1.0.0
    - name: websocket-server
      image: asi-registry.com/websocket-server:1.0.0
    - name: rust-control-kernel
      image: asi-registry.com/rust-control-kernel:1.0.0
    - name: cpp-inference-accelerator
      image: asi-registry.com/cpp-inference-accelerator:1.0.0

dependencies:
  - name: kafka
    version: "22.1.5"
    repository: "https://charts.bitnami.com/bitnami"
    condition: kafka.enabled
  
  - name: redis
    version: "17.11.3"
    repository: "https://charts.bitnami.com/bitnami"
    condition: redis.enabled
  
  - name: postgresql
    version: "12.6.6"
    repository: "https://charts.bitnami.com/bitnami"
    condition: postgresql.enabled
  
  - name: neo4j
    version: "5.8.0"
    repository: "https://helm.neo4j.com/neo4j"
    condition: neo4j.enabled
  
  - name: prometheus
    version: "23.1.0"
    repository: "https://prometheus-community.github.io/helm-charts"
    condition: monitoring.prometheus.enabled
  
  - name: grafana
    version: "6.57.4"
    repository: "https://grafana.github.io/helm-charts"
    condition: monitoring.grafana.enabled
  
  - name: jaeger
    version: "0.71.2"
    repository: "https://jaegertracing.github.io/helm-charts"
    condition: monitoring.jaeger.enabled
  
  - name: elasticsearch
    version: "8.5.1"
    repository: "https://helm.elastic.co"
    condition: logging.elasticsearch.enabled
  
  - name: spark
    version: "6.2.8"
    repository: "https://charts.bitnami.com/bitnami"
    condition: spark.enabled
