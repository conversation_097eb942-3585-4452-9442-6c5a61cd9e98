# ASI System - Istio Service Mesh Configuration
# =============================================

apiVersion: v1
kind: Namespace
metadata:
  name: istio-system
  labels:
    istio-injection: disabled
    name: istio-system

---
# Istio Control Plane Configuration
apiVersion: install.istio.io/v1alpha1
kind: IstioOperator
metadata:
  name: asi-control-plane
  namespace: istio-system
spec:
  values:
    global:
      meshID: asi-mesh
      multiCluster:
        clusterName: asi-cluster
      network: asi-network
    pilot:
      env:
        EXTERNAL_ISTIOD: false
  components:
    pilot:
      k8s:
        resources:
          requests:
            cpu: 500m
            memory: 2048Mi
          limits:
            cpu: 1000m
            memory: 4096Mi
        hpaSpec:
          maxReplicas: 5
          minReplicas: 2
          scaleTargetRef:
            apiVersion: apps/v1
            kind: Deployment
            name: istiod
          metrics:
          - type: Resource
            resource:
              name: cpu
              target:
                type: Utilization
                averageUtilization: 80
    ingressGateways:
    - name: istio-ingressgateway
      enabled: true
      k8s:
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 2000m
            memory: 1024Mi
        hpaSpec:
          maxReplicas: 5
          minReplicas: 2
          scaleTargetRef:
            apiVersion: apps/v1
            kind: Deployment
            name: istio-ingressgateway
          metrics:
          - type: Resource
            resource:
              name: cpu
              target:
                type: Utilization
                averageUtilization: 80
        service:
          type: LoadBalancer
          ports:
          - port: 15021
            targetPort: 15021
            name: status-port
          - port: 80
            targetPort: 8080
            name: http2
          - port: 443
            targetPort: 8443
            name: https
          - port: 15443
            targetPort: 15443
            name: tls
    egressGateways:
    - name: istio-egressgateway
      enabled: true
      k8s:
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 2000m
            memory: 1024Mi

---
# Gateway for ASI System
apiVersion: networking.istio.io/v1beta1
kind: Gateway
metadata:
  name: asi-gateway
  namespace: asi-system
spec:
  selector:
    istio: ingressgateway
  servers:
  - port:
      number: 80
      name: http
      protocol: HTTP
    hosts:
    - "asi.local"
    - "*.asi.local"
    tls:
      httpsRedirect: true
  - port:
      number: 443
      name: https
      protocol: HTTPS
    tls:
      mode: SIMPLE
      credentialName: asi-tls-secret
    hosts:
    - "asi.local"
    - "*.asi.local"

---
# Virtual Service for ASI System
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: asi-virtual-service
  namespace: asi-system
spec:
  hosts:
  - "asi.local"
  - "*.asi.local"
  gateways:
  - asi-gateway
  http:
  # Dashboard routing
  - match:
    - uri:
        prefix: /dashboard
    route:
    - destination:
        host: react-dashboard
        port:
          number: 3000
    timeout: 30s
    retries:
      attempts: 3
      perTryTimeout: 10s
  
  # Model Inspector routing
  - match:
    - uri:
        prefix: /inspector
    route:
    - destination:
        host: streamlit-inspector
        port:
          number: 8501
    timeout: 60s
  
  # Learning Engine API
  - match:
    - uri:
        prefix: /api/learning
    route:
    - destination:
        host: grpc-model-server
        port:
          number: 50051
    timeout: 30s
    retries:
      attempts: 3
      perTryTimeout: 10s
  
  # Decision Engine API
  - match:
    - uri:
        prefix: /api/decision
    route:
    - destination:
        host: python-rule-engine
        port:
          number: 8000
    timeout: 5s
    retries:
      attempts: 3
      perTryTimeout: 2s
  
  # WebSocket connections
  - match:
    - uri:
        prefix: /ws
    route:
    - destination:
        host: websocket-server
        port:
          number: 8080
    timeout: 0s  # No timeout for WebSocket connections

---
# Destination Rules for ASI Services
apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: asi-destination-rules
  namespace: asi-system
spec:
  host: "*.asi-system.svc.cluster.local"
  trafficPolicy:
    tls:
      mode: ISTIO_MUTUAL
    connectionPool:
      tcp:
        maxConnections: 100
      http:
        http1MaxPendingRequests: 50
        http2MaxRequests: 100
        maxRequestsPerConnection: 10
        maxRetries: 3
        consecutiveGatewayErrors: 5
        interval: 30s
        baseEjectionTime: 30s
    loadBalancer:
      simple: LEAST_CONN
    outlierDetection:
      consecutiveGatewayErrors: 5
      interval: 30s
      baseEjectionTime: 30s
      maxEjectionPercent: 50
      minHealthPercent: 30

---
# Specific Destination Rules for High-Performance Services
apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: learning-engine-dr
  namespace: asi-system
spec:
  host: grpc-model-server
  trafficPolicy:
    connectionPool:
      tcp:
        maxConnections: 200
      http:
        http2MaxRequests: 1000
        maxRequestsPerConnection: 100
    loadBalancer:
      simple: ROUND_ROBIN

---
apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: decision-engine-dr
  namespace: asi-system
spec:
  host: rust-decision-loop
  trafficPolicy:
    connectionPool:
      tcp:
        maxConnections: 500
      http:
        http1MaxPendingRequests: 100
        http2MaxRequests: 1000
        maxRequestsPerConnection: 50
    loadBalancer:
      simple: LEAST_CONN

---
# Service Entry for External Dependencies
apiVersion: networking.istio.io/v1beta1
kind: ServiceEntry
metadata:
  name: external-apis
  namespace: asi-system
spec:
  hosts:
  - api.openai.com
  - api.anthropic.com
  - huggingface.co
  ports:
  - number: 443
    name: https
    protocol: HTTPS
  location: MESH_EXTERNAL
  resolution: DNS

---
# Authorization Policy for ASI System
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: asi-authz-policy
  namespace: asi-system
spec:
  rules:
  # Allow dashboard access
  - from:
    - source:
        principals: ["cluster.local/ns/istio-system/sa/istio-ingressgateway-service-account"]
    to:
    - operation:
        methods: ["GET", "POST"]
        paths: ["/dashboard/*", "/inspector/*"]
  
  # Allow API access with authentication
  - from:
    - source:
        principals: ["cluster.local/ns/asi-system/sa/api-service-account"]
    to:
    - operation:
        methods: ["GET", "POST", "PUT", "DELETE"]
        paths: ["/api/*"]
  
  # Allow internal service communication
  - from:
    - source:
        namespaces: ["asi-system"]
    to:
    - operation:
        methods: ["GET", "POST", "PUT", "DELETE"]

---
# Peer Authentication for mTLS
apiVersion: security.istio.io/v1beta1
kind: PeerAuthentication
metadata:
  name: asi-peer-auth
  namespace: asi-system
spec:
  mtls:
    mode: STRICT

---
# Request Authentication for JWT
apiVersion: security.istio.io/v1beta1
kind: RequestAuthentication
metadata:
  name: asi-request-auth
  namespace: asi-system
spec:
  selector:
    matchLabels:
      app: api-gateway
  jwtRules:
  - issuer: "https://asi-auth.local"
    jwksUri: "https://asi-auth.local/.well-known/jwks.json"
    audiences:
    - "asi-system"

---
# Telemetry Configuration
apiVersion: telemetry.istio.io/v1alpha1
kind: Telemetry
metadata:
  name: asi-telemetry
  namespace: asi-system
spec:
  metrics:
  - providers:
    - name: prometheus
  - overrides:
    - match:
        metric: ALL_METRICS
      tagOverrides:
        asi_service:
          value: "%{CLUSTER_NAME}-%{SERVICE_NAME}"
        asi_version:
          value: "%{SERVICE_VERSION}"
  tracing:
  - providers:
    - name: jaeger
  accessLogging:
  - providers:
    - name: otel

---
# Envoy Filter for Custom Headers
apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: asi-custom-headers
  namespace: asi-system
spec:
  configPatches:
  - applyTo: HTTP_FILTER
    match:
      context: SIDECAR_INBOUND
      listener:
        filterChain:
          filter:
            name: "envoy.filters.network.http_connection_manager"
    patch:
      operation: INSERT_BEFORE
      value:
        name: envoy.filters.http.lua
        typed_config:
          "@type": type.googleapis.com/envoy.extensions.filters.http.lua.v3.Lua
          inline_code: |
            function envoy_on_request(request_handle)
              request_handle:headers():add("x-asi-request-id", request_handle:headers():get(":path") .. "-" .. os.time())
              request_handle:headers():add("x-asi-timestamp", os.date("!%Y-%m-%dT%H:%M:%SZ"))
            end
            
            function envoy_on_response(response_handle)
              response_handle:headers():add("x-asi-processed-by", "asi-system")
              response_handle:headers():add("x-asi-version", "1.0.0")
            end

---
# Circuit Breaker Configuration
apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: asi-circuit-breaker
  namespace: asi-system
spec:
  host: "*.asi-system.svc.cluster.local"
  trafficPolicy:
    outlierDetection:
      consecutiveGatewayErrors: 3
      consecutive5xxErrors: 3
      interval: 30s
      baseEjectionTime: 30s
      maxEjectionPercent: 50
      minHealthPercent: 30
    connectionPool:
      tcp:
        maxConnections: 100
      http:
        http1MaxPendingRequests: 10
        http2MaxRequests: 100
        maxRequestsPerConnection: 2
        maxRetries: 3
        consecutiveGatewayErrors: 3
        interval: 30s
        baseEjectionTime: 30s
