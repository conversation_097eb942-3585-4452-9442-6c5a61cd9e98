# ASI System - Prometheus Configuration
# ====================================

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'asi-system'
    environment: 'production'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# Load rules once and periodically evaluate them according to the global 'evaluation_interval'.
rule_files:
  - "rules/*.yml"

# Scrape configuration
scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    metrics_path: /metrics
    scrape_interval: 30s

  # ============================================================================
  # INFRASTRUCTURE SERVICES
  # ============================================================================

  # Kafka metrics
  - job_name: 'kafka'
    static_configs:
      - targets: ['kafka:9101']
    metrics_path: /metrics
    scrape_interval: 30s

  # Redis metrics
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    metrics_path: /metrics
    scrape_interval: 30s

  # PostgreSQL metrics
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    metrics_path: /metrics
    scrape_interval: 30s

  # Neo4j metrics
  - job_name: 'neo4j'
    static_configs:
      - targets: ['neo4j:2004']
    metrics_path: /metrics
    scrape_interval: 30s

  # ============================================================================
  # INGESTION MODULE
  # ============================================================================

  # Go Kafka Producer
  - job_name: 'go-kafka-producer'
    static_configs:
      - targets: ['go-producer:8080']
    metrics_path: /metrics
    scrape_interval: 15s
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: go-producer:8080

  # Rust Kafka Consumer
  - job_name: 'rust-kafka-consumer'
    static_configs:
      - targets: ['rust-consumer:8081']
    metrics_path: /metrics
    scrape_interval: 15s

  # Python Scrapy Service
  - job_name: 'python-scrapy'
    static_configs:
      - targets: ['scrapy:8082']
    metrics_path: /metrics
    scrape_interval: 30s

  # ============================================================================
  # DATA INTEGRATION MODULE
  # ============================================================================

  # Spark Master
  - job_name: 'spark-master'
    static_configs:
      - targets: ['spark-master:8080']
    metrics_path: /metrics/master/prometheus
    scrape_interval: 30s

  # Spark Workers
  - job_name: 'spark-workers'
    static_configs:
      - targets: ['spark-worker:8081']
    metrics_path: /metrics/worker/prometheus
    scrape_interval: 30s

  # Scala Spark Pipeline
  - job_name: 'scala-spark-pipeline'
    static_configs:
      - targets: ['spark-pipeline:8085']
    metrics_path: /metrics
    scrape_interval: 30s

  # Java Protocol Service
  - job_name: 'java-protocol-service'
    static_configs:
      - targets: ['java-protocol:8083']
    metrics_path: /actuator/prometheus
    scrape_interval: 30s

  # Rust Device Integration
  - job_name: 'rust-device-integration'
    static_configs:
      - targets: ['rust-device:8084']
    metrics_path: /metrics
    scrape_interval: 15s

  # ============================================================================
  # LEARNING ENGINE MODULE
  # ============================================================================

  # Python Training Engine
  - job_name: 'python-training-engine'
    static_configs:
      - targets: ['training-engine:8090']
    metrics_path: /metrics
    scrape_interval: 15s
    scrape_timeout: 10s

  # C++ Inference Engine
  - job_name: 'cpp-inference-engine'
    static_configs:
      - targets: ['inference-engine:8091']
    metrics_path: /metrics
    scrape_interval: 10s

  # gRPC Model Server
  - job_name: 'grpc-model-server'
    static_configs:
      - targets: ['model-server:8092']
    metrics_path: /metrics
    scrape_interval: 15s

  # ============================================================================
  # DECISION ENGINE MODULE
  # ============================================================================

  # Python Rule Engine
  - job_name: 'python-rule-engine'
    static_configs:
      - targets: ['rule-engine:8070']
    metrics_path: /metrics
    scrape_interval: 15s

  # Rust Decision Loop
  - job_name: 'rust-decision-loop'
    static_configs:
      - targets: ['decision-loop:8071']
    metrics_path: /metrics
    scrape_interval: 10s

  # C++ Edge Processor
  - job_name: 'cpp-edge-processor'
    static_configs:
      - targets: ['edge-processor:8072']
    metrics_path: /metrics
    scrape_interval: 10s

  # ============================================================================
  # SELF-IMPROVEMENT ENGINE MODULE
  # ============================================================================

  # Python Model Retraining
  - job_name: 'python-model-retraining'
    static_configs:
      - targets: ['model-retraining:8080']
    metrics_path: /metrics
    scrape_interval: 30s

  # Lisp Symbolic Refactor
  - job_name: 'lisp-symbolic-refactor'
    static_configs:
      - targets: ['symbolic-refactor:8081']
    metrics_path: /metrics
    scrape_interval: 30s

  # Julia Performance Analytics
  - job_name: 'julia-performance-analytics'
    static_configs:
      - targets: ['performance-analytics:8082']
    metrics_path: /metrics
    scrape_interval: 15s

  # ============================================================================
  # UI/UX MODULE
  # ============================================================================

  # React Dashboard (if it exposes metrics)
  - job_name: 'react-dashboard'
    static_configs:
      - targets: ['react-dashboard:3000']
    metrics_path: /api/metrics
    scrape_interval: 30s

  # WebSocket Server
  - job_name: 'websocket-server'
    static_configs:
      - targets: ['websocket-server:8080']
    metrics_path: /metrics
    scrape_interval: 30s

  # ============================================================================
  # CORE RUNTIME & CONTROL MODULE
  # ============================================================================

  # Rust Control Kernel
  - job_name: 'rust-control-kernel'
    static_configs:
      - targets: ['control-kernel:9091']
    metrics_path: /metrics
    scrape_interval: 5s  # High frequency for control systems

  # C++ Inference Accelerator
  - job_name: 'cpp-inference-accelerator'
    static_configs:
      - targets: ['inference-accelerator:9092']
    metrics_path: /metrics
    scrape_interval: 10s

  # ============================================================================
  # SYSTEM METRICS
  # ============================================================================

  # Node Exporter (system metrics)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    metrics_path: /metrics
    scrape_interval: 30s

  # cAdvisor (container metrics)
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    metrics_path: /metrics
    scrape_interval: 30s

  # ============================================================================
  # KUBERNETES METRICS (when running on K8s)
  # ============================================================================

  # Kubernetes API server
  - job_name: 'kubernetes-apiservers'
    kubernetes_sd_configs:
      - role: endpoints
    scheme: https
    tls_config:
      ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
    bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
    relabel_configs:
      - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
        action: keep
        regex: default;kubernetes;https

  # Kubernetes nodes
  - job_name: 'kubernetes-nodes'
    kubernetes_sd_configs:
      - role: node
    scheme: https
    tls_config:
      ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
    bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
    relabel_configs:
      - action: labelmap
        regex: __meta_kubernetes_node_label_(.+)
      - target_label: __address__
        replacement: kubernetes.default.svc:443
      - source_labels: [__meta_kubernetes_node_name]
        regex: (.+)
        target_label: __metrics_path__
        replacement: /api/v1/nodes/${1}/proxy/metrics

  # Kubernetes pods
  - job_name: 'kubernetes-pods'
    kubernetes_sd_configs:
      - role: pod
    relabel_configs:
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
        action: keep
        regex: true
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
        action: replace
        target_label: __metrics_path__
        regex: (.+)
      - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
        action: replace
        regex: ([^:]+)(?::\d+)?;(\d+)
        replacement: $1:$2
        target_label: __address__
      - action: labelmap
        regex: __meta_kubernetes_pod_label_(.+)
      - source_labels: [__meta_kubernetes_namespace]
        action: replace
        target_label: kubernetes_namespace
      - source_labels: [__meta_kubernetes_pod_name]
        action: replace
        target_label: kubernetes_pod_name
