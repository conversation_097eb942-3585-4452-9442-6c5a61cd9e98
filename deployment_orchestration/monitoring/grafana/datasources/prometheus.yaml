apiVersion: 1

# ASI System - Grafana Datasources Configuration
# ==============================================

datasources:
  # Prometheus datasource for metrics
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
    jsonData:
      httpMethod: POST
      manageAlerts: true
      prometheusType: Prometheus
      prometheusVersion: 2.45.0
      cacheLevel: 'High'
      disableRecordingRules: false
      incrementalQueryOverlapWindow: 10m
      queryTimeout: 60s
      timeInterval: 15s
    secureJsonData: {}

  # CloudWatch datasource (for AWS deployments)
  - name: CloudWatch
    type: cloudwatch
    access: proxy
    jsonData:
      authType: default
      defaultRegion: us-west-2
      customMetricsNamespaces: 'AWS/EKS,AWS/ApplicationELB,AWS/RDS,AWS/ElastiCache'
      assumeRoleArn: ''
    secureJsonData: {}

  # Jaeger datasource for distributed tracing
  - name: Jaeger
    type: jaeger
    access: proxy
    url: http://jaeger:16686
    editable: true
    jsonData:
      tracesToLogs:
        datasourceUid: 'loki'
        tags: ['job', 'instance', 'pod', 'namespace']
        mappedTags: [
          {
            key: 'service.name',
            value: 'service'
          }
        ]
        mapTagNamesEnabled: true
        spanStartTimeShift: '1h'
        spanEndTimeShift: '1h'
        filterByTraceID: false
        filterBySpanID: false
      tracesToMetrics:
        datasourceUid: 'prometheus'
        tags: [
          {
            key: 'service.name',
            value: 'service'
          },
          {
            key: 'job'
          }
        ]
        queries: [
          {
            name: 'Sample query',
            query: 'sum(rate(traces_spanmetrics_latency_bucket{$$__tags}[5m]))'
          }
        ]
      nodeGraph:
        enabled: true
      search:
        hide: false
      spanBar:
        type: 'Tag'
        tag: 'http.path'

  # Loki datasource for logs (if available)
  - name: Loki
    type: loki
    access: proxy
    url: http://loki:3100
    editable: true
    jsonData:
      derivedFields:
        - datasourceUid: 'jaeger'
          matcherRegex: 'traceID=(\w+)'
          name: 'TraceID'
          url: '$${__value.raw}'
        - datasourceUid: 'jaeger'
          matcherRegex: 'trace_id=(\w+)'
          name: 'TraceID'
          url: '$${__value.raw}'

  # Elasticsearch datasource for logs
  - name: Elasticsearch
    type: elasticsearch
    access: proxy
    url: http://elasticsearch:9200
    database: 'asi-logs-*'
    editable: true
    jsonData:
      interval: Daily
      timeField: '@timestamp'
      esVersion: '8.8.0'
      maxConcurrentShardRequests: 5
      logMessageField: 'message'
      logLevelField: 'level'
      includeFrozen: false

  # TestData datasource for testing
  - name: TestData
    type: testdata
    access: proxy
    editable: true
    jsonData: {}

# Notification channels configuration
notifiers:
  - name: slack-alerts
    type: slack
    uid: slack-alerts
    settings:
      url: '${SLACK_WEBHOOK_URL}'
      channel: '#asi-alerts'
      username: 'Grafana'
      title: 'ASI System Alert'
      text: |
        {{ range .Alerts }}
        *Alert:* {{ .Annotations.summary }}
        *Description:* {{ .Annotations.description }}
        *Runbook:* {{ .Annotations.runbook_url }}
        {{ end }}
      iconEmoji: ':exclamation:'
      iconUrl: ''
      mentionUsers: ''
      mentionGroups: ''
      mentionChannel: ''
      token: ''
    secureSettings: {}

  - name: email-alerts
    type: email
    uid: email-alerts
    settings:
      addresses: '<EMAIL>'
      singleEmail: false
    secureSettings: {}

  - name: pagerduty-alerts
    type: pagerduty
    uid: pagerduty-alerts
    settings:
      integrationKey: '${PAGERDUTY_INTEGRATION_KEY}'
      severity: 'critical'
      customDetails: |
        {
          "firing": {{ .Alerts.Firing | len }},
          "resolved": {{ .Alerts.Resolved | len }}
        }
    secureSettings: {}

# Dashboard provisioning
dashboards:
  - name: 'ASI System Dashboards'
    orgId: 1
    folder: 'ASI System'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /etc/grafana/provisioning/dashboards

# Alert rules provisioning
alerting:
  rules:
    path: /etc/grafana/provisioning/alerting
  contactPoints:
    path: /etc/grafana/provisioning/alerting
  policies:
    path: /etc/grafana/provisioning/alerting
  templates:
    path: /etc/grafana/provisioning/alerting
  muteTimings:
    path: /etc/grafana/provisioning/alerting
