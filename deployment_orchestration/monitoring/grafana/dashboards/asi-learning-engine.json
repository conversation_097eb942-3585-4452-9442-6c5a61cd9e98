{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "ASI Learning Engine Dashboard - Monitoring training, inference, and model performance", "editable": true, "gnetId": null, "graphTooltip": 0, "id": null, "iteration": 1640995200000, "links": [], "panels": [{"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 1}, {"color": "green", "value": 3}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 0}, "id": 1, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.3.3", "targets": [{"expr": "count(up{job=\"asi-learning-engine\"} == 1)", "interval": "", "legendFormat": "Active Models", "refId": "A"}], "title": "Active Models", "type": "stat"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 50}, {"color": "red", "value": 100}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 6, "y": 0}, "id": 2, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.3.3", "targets": [{"expr": "histogram_quantile(0.95, sum(rate(asi_inference_duration_seconds_bucket[5m])) by (le)) * 1000", "interval": "", "legendFormat": "P95 Inference Latency", "refId": "A"}], "title": "Inference Latency (P95)", "type": "stat"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 80}, {"color": "green", "value": 95}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 12, "y": 0}, "id": 3, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.3.3", "targets": [{"expr": "avg(asi_model_accuracy) * 100", "interval": "", "legendFormat": "Average Accuracy", "refId": "A"}], "title": "Model Accuracy", "type": "stat"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 10}, {"color": "green", "value": 100}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 18, "y": 0}, "id": 4, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.3.3", "targets": [{"expr": "sum(rate(asi_inference_requests_total[5m]))", "interval": "", "legendFormat": "Inference RPS", "refId": "A"}], "title": "Inference Rate", "type": "stat"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 4}, "id": 5, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"expr": "asi_training_loss", "interval": "", "legendFormat": "{{model_name}} - {{loss_type}}", "refId": "A"}], "title": "Training Loss", "type": "timeseries"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 4}, "id": 6, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"expr": "asi_model_accuracy * 100", "interval": "", "legendFormat": "{{model_name}}", "refId": "A"}], "title": "Model Accuracy Over Time", "type": "timeseries"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 12}, "id": 7, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"expr": "asi_gpu_memory_used_bytes", "interval": "", "legendFormat": "GPU {{gpu_id}} - {{model_name}}", "refId": "A"}], "title": "GPU Memory Usage", "type": "timeseries"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 12}, "id": 8, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"expr": "asi_gpu_utilization_percent", "interval": "", "legendFormat": "GPU {{gpu_id}}", "refId": "A"}], "title": "GPU Utilization", "type": "timeseries"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 20}, "id": 9, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"expr": "sum(rate(asi_inference_requests_total[5m])) by (model_name)", "interval": "", "legendFormat": "{{model_name}}", "refId": "A"}], "title": "Inference Requests by Model", "type": "timeseries"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 20}, "id": 10, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"expr": "histogram_quantile(0.50, sum(rate(asi_inference_duration_seconds_bucket[5m])) by (le, model_name)) * 1000", "interval": "", "legendFormat": "{{model_name}} P50", "refId": "A"}, {"expr": "histogram_quantile(0.95, sum(rate(asi_inference_duration_seconds_bucket[5m])) by (le, model_name)) * 1000", "interval": "", "legendFormat": "{{model_name}} P95", "refId": "B"}, {"expr": "histogram_quantile(0.99, sum(rate(asi_inference_duration_seconds_bucket[5m])) by (le, model_name)) * 1000", "interval": "", "legendFormat": "{{model_name}} P99", "refId": "C"}], "title": "Inference Latency Percentiles", "type": "timeseries"}], "refresh": "30s", "schemaVersion": 27, "style": "dark", "tags": ["asi", "learning-engine", "ml", "ai"], "templating": {"list": [{"current": {"selected": false, "text": "Prometheus", "value": "Prometheus"}, "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Data Source", "multi": false, "name": "datasource", "options": [], "query": "prometheus", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"allValue": null, "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": "$datasource", "definition": "label_values(asi_model_accuracy, model_name)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": "Model", "multi": true, "name": "model", "options": [], "query": {"query": "label_values(asi_model_accuracy, model_name)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "ASI Learning Engine", "uid": "asi-learning-engine", "version": 1}