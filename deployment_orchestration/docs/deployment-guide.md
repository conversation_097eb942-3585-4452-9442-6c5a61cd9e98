# ASI System - Deployment Guide

## 🚀 Overview

This guide provides comprehensive instructions for deploying the ASI (Artificial Super Intelligence) System using the Deployment & Orchestration module. The module supports multiple deployment scenarios from local development to production cloud environments.

## 📋 Prerequisites

### Required Tools

Ensure you have the following tools installed:

```bash
# Container tools
docker --version          # >= 20.10
docker-compose --version  # >= 2.0

# Kubernetes tools
kubectl version           # >= 1.24
helm version             # >= 3.8

# Infrastructure tools
terraform --version      # >= 1.3

# Cloud CLI tools (choose based on your provider)
aws --version            # AWS CLI v2
gcloud version           # Google Cloud SDK
az --version             # Azure CLI
```

### System Requirements

- **CPU**: Minimum 8 cores (16+ recommended for production)
- **Memory**: Minimum 16GB RAM (32GB+ recommended for production)
- **Storage**: Minimum 100GB free space
- **Network**: Stable internet connection for image pulls and cloud resources

### Cloud Provider Setup

#### AWS Setup
```bash
# Configure AWS credentials
aws configure

# Verify access
aws sts get-caller-identity
```

#### GCP Setup
```bash
# Authenticate with GCP
gcloud auth login
gcloud auth application-default login

# Set project
gcloud config set project YOUR_PROJECT_ID
```

#### Azure Setup
```bash
# Login to Azure
az login

# Set subscription
az account set --subscription YOUR_SUBSCRIPTION_ID
```

## 🏗️ Deployment Scenarios

### 1. Local Development

For local development and testing:

```bash
# Start all services locally with Docker Compose
cd deployment_orchestration
make dev-start

# Check status
make dev-status

# View logs
make dev-logs

# Stop services
make dev-stop
```

### 2. Cloud Development Environment

Deploy to a cloud Kubernetes cluster for development:

```bash
# Deploy infrastructure and application
./scripts/deploy.sh --environment development --cloud-provider aws

# Or step by step
make infra-apply-aws
make docker-build-all
make k8s-deploy-dev
```

### 3. Staging Environment

Deploy to staging for testing:

```bash
# Deploy to staging
./scripts/deploy.sh --environment staging --cloud-provider aws

# Run integration tests
make test-integration
```

### 4. Production Environment

Deploy to production:

```bash
# Deploy to production (with confirmation prompts)
./scripts/deploy.sh --environment production --cloud-provider aws

# Monitor deployment
make k8s-status
make monitoring-deploy
```

## 🔧 Configuration

### Environment Variables

Create a `.env` file based on `.env.example`:

```bash
cp docker-compose/.env.example docker-compose/.env
# Edit the file with your specific values
```

Key variables to configure:

```bash
# Environment
ENVIRONMENT=development|staging|production

# Cloud Provider
CLOUD_PROVIDER=aws|gcp|azure
REGION=us-west-2

# Container Registry
CONTAINER_REGISTRY=your-registry.com
IMAGE_TAG=latest

# Database Credentials
POSTGRES_PASSWORD=secure_password
REDIS_PASSWORD=secure_password
NEO4J_PASSWORD=secure_password

# Monitoring
GRAFANA_ADMIN_PASSWORD=secure_password
```

### Terraform Variables

For infrastructure customization, edit `terraform/{cloud_provider}/terraform.tfvars`:

```hcl
# AWS example
project_name = "asi-system"
environment = "production"
aws_region = "us-west-2"

# Cluster configuration
kubernetes_version = "1.27"
general_node_desired_size = 5
enable_gpu_nodes = true

# Database configuration
postgres_instance_class = "db.r5.xlarge"
redis_node_type = "cache.r5.large"
```

### Helm Values

Customize deployment with Helm values files:

```yaml
# helm/asi-system/values-production.yaml
global:
  environment: production
  imageTag: "v1.0.0"

# Resource limits for production
learningEngine:
  pythonTrainingEngine:
    resources:
      requests:
        cpu: 4
        memory: 8Gi
        nvidia.com/gpu: 1
      limits:
        cpu: 8
        memory: 16Gi
        nvidia.com/gpu: 1

# Enable autoscaling
autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 20
```

## 🚀 Step-by-Step Deployment

### Step 1: Prepare Environment

```bash
# Clone the repository
git clone <repository-url>
cd ASI_SYSTEM/deployment_orchestration

# Check prerequisites
make check-deps

# Setup environment
make setup-dev
```

### Step 2: Configure Infrastructure

```bash
# Initialize Terraform
cd terraform/aws
terraform init

# Plan infrastructure
terraform plan -var-file="terraform.tfvars"

# Apply infrastructure
terraform apply
```

### Step 3: Build Images

```bash
# Build all Docker images
./scripts/build-images.sh

# Or build specific modules
docker build -t asi-training-engine ../learning_engine/python-training-engine

# Push to registry (if needed)
./scripts/build-images.sh --push
```

### Step 4: Deploy to Kubernetes

```bash
# Update kubeconfig
aws eks update-kubeconfig --region us-west-2 --name asi-system-cluster

# Deploy using Helm
helm upgrade --install asi-system helm/asi-system \
  --namespace asi-system \
  --create-namespace \
  --values helm/asi-system/values-production.yaml

# Verify deployment
kubectl get pods -n asi-system
```

### Step 5: Setup Monitoring

```bash
# Deploy monitoring stack
make monitoring-deploy

# Access Grafana
kubectl port-forward svc/grafana 3000:3000 -n asi-system
# Open http://localhost:3000 (admin/admin)
```

## 📊 Monitoring & Observability

### Accessing Dashboards

```bash
# Grafana (Visualization)
kubectl port-forward svc/grafana 3000:3000 -n asi-system

# Prometheus (Metrics)
kubectl port-forward svc/prometheus 9090:9090 -n asi-system

# Jaeger (Tracing)
kubectl port-forward svc/jaeger 16686:16686 -n asi-system

# React Dashboard (ASI UI)
kubectl port-forward svc/react-dashboard 3001:3000 -n asi-system
```

### Key Metrics to Monitor

1. **System Health**
   - Pod status and restarts
   - Resource utilization (CPU, Memory, GPU)
   - Network traffic and latency

2. **Application Metrics**
   - Request rates and response times
   - Error rates and success rates
   - Queue depths and processing times

3. **Business Metrics**
   - Training job completion rates
   - Decision engine accuracy
   - Model performance metrics

### Alerting

Configure alerts in Grafana for:

- High CPU/Memory usage (>80%)
- Pod restart loops
- Service unavailability
- High error rates (>5%)
- GPU utilization issues

## 🔒 Security Considerations

### Network Security

```bash
# Apply network policies
kubectl apply -f k8s/security/network-policies.yaml

# Enable pod security standards
kubectl label namespace asi-system pod-security.kubernetes.io/enforce=restricted
```

### Secrets Management

```bash
# Create secrets
kubectl create secret generic asi-secrets \
  --from-literal=postgres-password=secure_password \
  --from-literal=redis-password=secure_password \
  -n asi-system

# Use external secret management (recommended for production)
# AWS Secrets Manager, Azure Key Vault, or Google Secret Manager
```

### RBAC Configuration

```bash
# Apply RBAC policies
kubectl apply -f k8s/rbac/

# Verify permissions
kubectl auth can-i get pods --as=system:serviceaccount:asi-system:asi-system-sa
```

## 🧪 Testing

### Smoke Tests

```bash
# Run basic connectivity tests
make test-smoke

# Test individual services
kubectl exec -it deployment/go-kafka-producer -n asi-system -- curl localhost:8080/health
```

### Integration Tests

```bash
# Run full integration test suite
make test-integration

# Run specific module tests
cd ../learning_engine && make test
```

### Load Testing

```bash
# Run load tests
make test-load

# Monitor during load testing
watch kubectl top pods -n asi-system
```

## 🚨 Troubleshooting

### Common Issues

1. **Pod Stuck in Pending**
   ```bash
   kubectl describe pod <pod-name> -n asi-system
   # Check for resource constraints or node selector issues
   ```

2. **Image Pull Errors**
   ```bash
   # Check image exists
   docker pull asi-registry.com/training-engine:latest
   
   # Verify registry credentials
   kubectl get secret regcred -n asi-system -o yaml
   ```

3. **Service Connectivity Issues**
   ```bash
   # Test service resolution
   kubectl exec -it deployment/test-pod -n asi-system -- nslookup kafka
   
   # Check service endpoints
   kubectl get endpoints -n asi-system
   ```

4. **Resource Constraints**
   ```bash
   # Check node resources
   kubectl top nodes
   
   # Check resource quotas
   kubectl describe resourcequota -n asi-system
   ```

### Debugging Commands

```bash
# Get pod logs
kubectl logs -f deployment/training-engine -n asi-system

# Execute into container
kubectl exec -it deployment/training-engine -n asi-system -- /bin/bash

# Port forward for debugging
kubectl port-forward pod/<pod-name> 8080:8080 -n asi-system

# Check events
kubectl get events -n asi-system --sort-by='.lastTimestamp'
```

## 🔄 Maintenance

### Updates and Upgrades

```bash
# Update application
helm upgrade asi-system helm/asi-system \
  --namespace asi-system \
  --set global.imageTag=v1.1.0

# Update infrastructure
cd terraform/aws
terraform plan -var-file="terraform.tfvars"
terraform apply
```

### Backup and Recovery

```bash
# Backup persistent data
make backup

# Restore from backup
make restore BACKUP_DATE=2023-12-01
```

### Scaling

```bash
# Scale specific deployments
kubectl scale deployment training-engine --replicas=5 -n asi-system

# Enable horizontal pod autoscaling
kubectl autoscale deployment training-engine --cpu-percent=70 --min=2 --max=10 -n asi-system
```

## 📚 Additional Resources

- [Kubernetes Documentation](https://kubernetes.io/docs/)
- [Helm Documentation](https://helm.sh/docs/)
- [Terraform Documentation](https://www.terraform.io/docs/)
- [Prometheus Monitoring](https://prometheus.io/docs/)
- [Grafana Dashboards](https://grafana.com/docs/)

## 🤝 Support

For issues and questions:

1. Check the troubleshooting section above
2. Review logs and monitoring dashboards
3. Consult the ASI System documentation
4. Contact the ASI development team

## 📄 License

This deployment module is part of the ASI System project. See the main project LICENSE file for details.
