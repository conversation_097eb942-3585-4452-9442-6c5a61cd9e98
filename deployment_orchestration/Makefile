# ASI System - Deployment & Orchestration Makefile
# ================================================

# Color codes for output
RED := \033[0;31m
GREEN := \033[0;32m
YELLOW := \033[0;33m
BLUE := \033[0;34m
PURPLE := \033[0;35m
CYAN := \033[0;36m
WHITE := \033[0;37m
RESET := \033[0m

# Configuration
PROJECT_NAME := asi-system
CONTAINER_REGISTRY ?= asi-registry.com
IMAGE_TAG ?= latest
NAMESPACE ?= asi-system
KUBECONFIG ?= ~/.kube/config
CLOUD_PROVIDER ?= aws
REGION ?= us-west-2

# Directories
DOCKER_COMPOSE_DIR := docker-compose
K8S_DIR := k8s
HELM_DIR := helm/asi-system
TERRAFORM_DIR := terraform
MONITORING_DIR := monitoring
SCRIPTS_DIR := scripts

# Docker Compose files
COMPOSE_FILE := $(DOCKER_COMPOSE_DIR)/docker-compose.yml
COMPOSE_PROD_FILE := $(DOCKER_COMPOSE_DIR)/docker-compose.prod.yml
COMPOSE_TEST_FILE := $(DOCKER_COMPOSE_DIR)/docker-compose.test.yml

# Default target
.DEFAULT_GOAL := help

# Help target
.PHONY: help
help: ## Show this help message
	@echo "$(CYAN)ASI System - Deployment & Orchestration$(RESET)"
	@echo "$(CYAN)=========================================$(RESET)"
	@echo ""
	@echo "$(YELLOW)Available targets:$(RESET)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(GREEN)%-20s$(RESET) %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""

# Prerequisites check
.PHONY: check-deps
check-deps: ## Check required dependencies
	@echo "$(BLUE)Checking dependencies...$(RESET)"
	@command -v docker >/dev/null 2>&1 || { echo "$(RED)Docker is required but not installed$(RESET)"; exit 1; }
	@command -v docker-compose >/dev/null 2>&1 || { echo "$(RED)Docker Compose is required but not installed$(RESET)"; exit 1; }
	@command -v kubectl >/dev/null 2>&1 || { echo "$(RED)kubectl is required but not installed$(RESET)"; exit 1; }
	@command -v helm >/dev/null 2>&1 || { echo "$(RED)Helm is required but not installed$(RESET)"; exit 1; }
	@command -v terraform >/dev/null 2>&1 || { echo "$(RED)Terraform is required but not installed$(RESET)"; exit 1; }
	@echo "$(GREEN)All dependencies are installed$(RESET)"

# Docker targets
.PHONY: docker-build-all
docker-build-all: check-deps ## Build all Docker images
	@echo "$(BLUE)Building all Docker images...$(RESET)"
	@$(SCRIPTS_DIR)/build-images.sh
	@echo "$(GREEN)All images built successfully$(RESET)"

.PHONY: docker-push-all
docker-push-all: ## Push all Docker images to registry
	@echo "$(BLUE)Pushing all images to $(CONTAINER_REGISTRY)...$(RESET)"
	@$(SCRIPTS_DIR)/push-images.sh
	@echo "$(GREEN)All images pushed successfully$(RESET)"

# Development environment
.PHONY: dev-start
dev-start: check-deps ## Start development environment
	@echo "$(BLUE)Starting development environment...$(RESET)"
	@docker-compose -f $(COMPOSE_FILE) up -d
	@echo "$(GREEN)Development environment started$(RESET)"
	@echo "$(YELLOW)Access points:$(RESET)"
	@echo "  - Grafana: http://localhost:3000 (admin/admin)"
	@echo "  - Prometheus: http://localhost:9090"
	@echo "  - Jaeger: http://localhost:16686"
	@echo "  - React Dashboard: http://localhost:3001"

.PHONY: dev-stop
dev-stop: ## Stop development environment
	@echo "$(BLUE)Stopping development environment...$(RESET)"
	@docker-compose -f $(COMPOSE_FILE) down
	@echo "$(GREEN)Development environment stopped$(RESET)"

.PHONY: dev-restart
dev-restart: dev-stop dev-start ## Restart development environment

.PHONY: dev-logs
dev-logs: ## Show logs from development environment
	@docker-compose -f $(COMPOSE_FILE) logs -f

.PHONY: dev-status
dev-status: ## Show status of development services
	@docker-compose -f $(COMPOSE_FILE) ps

# Production environment
.PHONY: prod-start
prod-start: check-deps ## Start production environment (Docker Compose)
	@echo "$(BLUE)Starting production environment...$(RESET)"
	@docker-compose -f $(COMPOSE_FILE) -f $(COMPOSE_PROD_FILE) up -d
	@echo "$(GREEN)Production environment started$(RESET)"

.PHONY: prod-stop
prod-stop: ## Stop production environment
	@echo "$(BLUE)Stopping production environment...$(RESET)"
	@docker-compose -f $(COMPOSE_FILE) -f $(COMPOSE_PROD_FILE) down
	@echo "$(GREEN)Production environment stopped$(RESET)"

# Testing environment
.PHONY: test-start
test-start: check-deps ## Start testing environment
	@echo "$(BLUE)Starting testing environment...$(RESET)"
	@docker-compose -f $(COMPOSE_TEST_FILE) up -d
	@echo "$(GREEN)Testing environment started$(RESET)"

.PHONY: test-stop
test-stop: ## Stop testing environment
	@echo "$(BLUE)Stopping testing environment...$(RESET)"
	@docker-compose -f $(COMPOSE_TEST_FILE) down
	@echo "$(GREEN)Testing environment stopped$(RESET)"

# Kubernetes targets
.PHONY: k8s-create-namespace
k8s-create-namespace: ## Create Kubernetes namespace
	@echo "$(BLUE)Creating Kubernetes namespace...$(RESET)"
	@kubectl apply -f $(K8S_DIR)/namespaces/
	@echo "$(GREEN)Namespace created$(RESET)"

.PHONY: k8s-deploy-dev
k8s-deploy-dev: k8s-create-namespace ## Deploy to Kubernetes (development)
	@echo "$(BLUE)Deploying to Kubernetes (development)...$(RESET)"
	@helm upgrade --install $(PROJECT_NAME) $(HELM_DIR) \
		--namespace $(NAMESPACE) \
		--values $(HELM_DIR)/values-dev.yaml \
		--set image.tag=$(IMAGE_TAG)
	@echo "$(GREEN)Deployed to Kubernetes (development)$(RESET)"

.PHONY: k8s-deploy-prod
k8s-deploy-prod: k8s-create-namespace ## Deploy to Kubernetes (production)
	@echo "$(BLUE)Deploying to Kubernetes (production)...$(RESET)"
	@helm upgrade --install $(PROJECT_NAME) $(HELM_DIR) \
		--namespace $(NAMESPACE) \
		--values $(HELM_DIR)/values-prod.yaml \
		--set image.tag=$(IMAGE_TAG)
	@echo "$(GREEN)Deployed to Kubernetes (production)$(RESET)"

.PHONY: k8s-undeploy
k8s-undeploy: ## Remove deployment from Kubernetes
	@echo "$(BLUE)Removing deployment from Kubernetes...$(RESET)"
	@helm uninstall $(PROJECT_NAME) --namespace $(NAMESPACE)
	@echo "$(GREEN)Deployment removed$(RESET)"

.PHONY: k8s-status
k8s-status: ## Show Kubernetes deployment status
	@echo "$(BLUE)Kubernetes deployment status:$(RESET)"
	@kubectl get pods,svc,ingress -n $(NAMESPACE)

.PHONY: k8s-logs
k8s-logs: ## Show logs from Kubernetes pods
	@echo "$(BLUE)Showing logs from all pods...$(RESET)"
	@kubectl logs -f -l app.kubernetes.io/instance=$(PROJECT_NAME) -n $(NAMESPACE)

# Infrastructure targets
.PHONY: infra-plan-aws
infra-plan-aws: ## Plan AWS infrastructure
	@echo "$(BLUE)Planning AWS infrastructure...$(RESET)"
	@cd $(TERRAFORM_DIR)/aws && terraform init && terraform plan

.PHONY: infra-apply-aws
infra-apply-aws: ## Apply AWS infrastructure
	@echo "$(BLUE)Applying AWS infrastructure...$(RESET)"
	@cd $(TERRAFORM_DIR)/aws && terraform apply

.PHONY: infra-destroy-aws
infra-destroy-aws: ## Destroy AWS infrastructure
	@echo "$(BLUE)Destroying AWS infrastructure...$(RESET)"
	@cd $(TERRAFORM_DIR)/aws && terraform destroy

.PHONY: infra-plan-gcp
infra-plan-gcp: ## Plan GCP infrastructure
	@echo "$(BLUE)Planning GCP infrastructure...$(RESET)"
	@cd $(TERRAFORM_DIR)/gcp && terraform init && terraform plan

.PHONY: infra-apply-gcp
infra-apply-gcp: ## Apply GCP infrastructure
	@echo "$(BLUE)Applying GCP infrastructure...$(RESET)"
	@cd $(TERRAFORM_DIR)/gcp && terraform apply

.PHONY: infra-destroy-gcp
infra-destroy-gcp: ## Destroy GCP infrastructure
	@echo "$(BLUE)Destroying GCP infrastructure...$(RESET)"
	@cd $(TERRAFORM_DIR)/gcp && terraform destroy

# Monitoring targets
.PHONY: monitoring-deploy
monitoring-deploy: ## Deploy monitoring stack
	@echo "$(BLUE)Deploying monitoring stack...$(RESET)"
	@kubectl apply -f $(MONITORING_DIR)/prometheus/
	@kubectl apply -f $(MONITORING_DIR)/grafana/
	@kubectl apply -f $(MONITORING_DIR)/jaeger/
	@echo "$(GREEN)Monitoring stack deployed$(RESET)"

.PHONY: monitoring-undeploy
monitoring-undeploy: ## Remove monitoring stack
	@echo "$(BLUE)Removing monitoring stack...$(RESET)"
	@kubectl delete -f $(MONITORING_DIR)/prometheus/
	@kubectl delete -f $(MONITORING_DIR)/grafana/
	@kubectl delete -f $(MONITORING_DIR)/jaeger/
	@echo "$(GREEN)Monitoring stack removed$(RESET)"

# Testing targets
.PHONY: test-integration
test-integration: ## Run integration tests
	@echo "$(BLUE)Running integration tests...$(RESET)"
	@$(SCRIPTS_DIR)/run-integration-tests.sh
	@echo "$(GREEN)Integration tests completed$(RESET)"

.PHONY: test-load
test-load: ## Run load tests
	@echo "$(BLUE)Running load tests...$(RESET)"
	@$(SCRIPTS_DIR)/run-load-tests.sh
	@echo "$(GREEN)Load tests completed$(RESET)"

.PHONY: test-security
test-security: ## Run security tests
	@echo "$(BLUE)Running security tests...$(RESET)"
	@$(SCRIPTS_DIR)/run-security-tests.sh
	@echo "$(GREEN)Security tests completed$(RESET)"

# Utility targets
.PHONY: clean
clean: ## Clean up resources
	@echo "$(BLUE)Cleaning up resources...$(RESET)"
	@docker system prune -f
	@docker volume prune -f
	@echo "$(GREEN)Cleanup completed$(RESET)"

.PHONY: backup
backup: ## Backup system data
	@echo "$(BLUE)Creating system backup...$(RESET)"
	@$(SCRIPTS_DIR)/backup.sh
	@echo "$(GREEN)Backup completed$(RESET)"

.PHONY: rollback
rollback: ## Rollback to previous version
	@echo "$(BLUE)Rolling back to previous version...$(RESET)"
	@$(SCRIPTS_DIR)/rollback.sh
	@echo "$(GREEN)Rollback completed$(RESET)"

.PHONY: status
status: ## Show overall system status
	@echo "$(BLUE)System Status:$(RESET)"
	@echo "$(YELLOW)Docker Compose Services:$(RESET)"
	@docker-compose -f $(COMPOSE_FILE) ps 2>/dev/null || echo "  No services running"
	@echo ""
	@echo "$(YELLOW)Kubernetes Pods:$(RESET)"
	@kubectl get pods -n $(NAMESPACE) 2>/dev/null || echo "  No pods found"
	@echo ""

.PHONY: logs
logs: ## Show logs from all services
	@echo "$(BLUE)Showing logs from all services...$(RESET)"
	@if docker-compose -f $(COMPOSE_FILE) ps -q | grep -q .; then \
		docker-compose -f $(COMPOSE_FILE) logs -f; \
	else \
		kubectl logs -f -l app.kubernetes.io/instance=$(PROJECT_NAME) -n $(NAMESPACE) 2>/dev/null || echo "No services running"; \
	fi

# Environment setup
.PHONY: setup-dev
setup-dev: check-deps ## Setup development environment
	@echo "$(BLUE)Setting up development environment...$(RESET)"
	@cp $(DOCKER_COMPOSE_DIR)/.env.example $(DOCKER_COMPOSE_DIR)/.env
	@echo "$(GREEN)Development environment setup completed$(RESET)"
	@echo "$(YELLOW)Please edit $(DOCKER_COMPOSE_DIR)/.env with your configuration$(RESET)"

.PHONY: setup-cluster
setup-cluster: ## Setup Kubernetes cluster
	@echo "$(BLUE)Setting up Kubernetes cluster...$(RESET)"
	@$(SCRIPTS_DIR)/setup-cluster.sh
	@echo "$(GREEN)Cluster setup completed$(RESET)"

# Validation targets
.PHONY: validate-k8s
validate-k8s: ## Validate Kubernetes manifests
	@echo "$(BLUE)Validating Kubernetes manifests...$(RESET)"
	@helm lint $(HELM_DIR)
	@kubectl apply --dry-run=client -f $(K8S_DIR)/
	@echo "$(GREEN)Kubernetes manifests are valid$(RESET)"

.PHONY: validate-terraform
validate-terraform: ## Validate Terraform configurations
	@echo "$(BLUE)Validating Terraform configurations...$(RESET)"
	@cd $(TERRAFORM_DIR)/aws && terraform validate
	@cd $(TERRAFORM_DIR)/gcp && terraform validate
	@echo "$(GREEN)Terraform configurations are valid$(RESET)"
