apiVersion: v1
kind: Namespace
metadata:
  name: asi-system
  labels:
    name: asi-system
    component: asi-core
    system: artificial-super-intelligence
    version: v1.0.0
    environment: production
  annotations:
    description: "Main namespace for ASI (Artificial Super Intelligence) System"
    contact: "<EMAIL>"
    documentation: "https://docs.asi-system.com"
    created-by: "deployment-orchestration-module"
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: asi-system-quota
  namespace: asi-system
  labels:
    component: resource-management
    system: asi-system
spec:
  hard:
    # Compute resources
    requests.cpu: "50"
    requests.memory: 100Gi
    requests.nvidia.com/gpu: "8"
    limits.cpu: "100"
    limits.memory: 200Gi
    limits.nvidia.com/gpu: "8"
    
    # Storage resources
    requests.storage: 1Ti
    persistentvolumeclaims: "50"
    
    # Object counts
    pods: "100"
    services: "50"
    secrets: "50"
    configmaps: "50"
    deployments.apps: "30"
    statefulsets.apps: "10"
    jobs.batch: "20"
    cronjobs.batch: "10"
    
    # Network resources
    services.loadbalancers: "5"
    services.nodeports: "10"
---
apiVersion: v1
kind: LimitRange
metadata:
  name: asi-system-limits
  namespace: asi-system
  labels:
    component: resource-management
    system: asi-system
spec:
  limits:
  # Default limits for containers
  - default:
      cpu: "2"
      memory: "4Gi"
      nvidia.com/gpu: "0"
    defaultRequest:
      cpu: "500m"
      memory: "1Gi"
    type: Container
  
  # Limits for pods
  - max:
      cpu: "16"
      memory: "32Gi"
      nvidia.com/gpu: "2"
    min:
      cpu: "100m"
      memory: "128Mi"
    type: Pod
  
  # Limits for persistent volume claims
  - max:
      storage: "100Gi"
    min:
      storage: "1Gi"
    type: PersistentVolumeClaim
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: asi-system-sa
  namespace: asi-system
  labels:
    component: security
    system: asi-system
  annotations:
    description: "Main service account for ASI System components"
automountServiceAccountToken: true
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: asi-system-role
  namespace: asi-system
  labels:
    component: security
    system: asi-system
rules:
# Allow reading pods, services, and endpoints
- apiGroups: [""]
  resources: ["pods", "services", "endpoints", "configmaps", "secrets"]
  verbs: ["get", "list", "watch"]

# Allow creating and updating configmaps (for dynamic configuration)
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["create", "update", "patch"]

# Allow reading deployments and statefulsets
- apiGroups: ["apps"]
  resources: ["deployments", "statefulsets", "replicasets"]
  verbs: ["get", "list", "watch"]

# Allow reading jobs and cronjobs
- apiGroups: ["batch"]
  resources: ["jobs", "cronjobs"]
  verbs: ["get", "list", "watch"]

# Allow reading metrics
- apiGroups: ["metrics.k8s.io"]
  resources: ["pods", "nodes"]
  verbs: ["get", "list"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: asi-system-rolebinding
  namespace: asi-system
  labels:
    component: security
    system: asi-system
subjects:
- kind: ServiceAccount
  name: asi-system-sa
  namespace: asi-system
roleRef:
  kind: Role
  name: asi-system-role
  apiGroup: rbac.authorization.k8s.io
---
apiVersion: v1
kind: NetworkPolicy
metadata:
  name: asi-system-network-policy
  namespace: asi-system
  labels:
    component: security
    system: asi-system
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  
  # Ingress rules
  ingress:
  # Allow traffic from within the namespace
  - from:
    - namespaceSelector:
        matchLabels:
          name: asi-system
  
  # Allow traffic from monitoring namespace
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
  
  # Allow traffic from ingress controllers
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
  
  # Egress rules
  egress:
  # Allow all outbound traffic (can be restricted further)
  - {}
---
apiVersion: v1
kind: Secret
metadata:
  name: asi-system-secrets
  namespace: asi-system
  labels:
    component: security
    system: asi-system
type: Opaque
data:
  # Base64 encoded secrets (replace with actual values)
  postgres-password: YXNpX3Bvc3RncmVzX3Bhc3N3b3JkXzEyMw==  # asi_postgres_password_123
  redis-password: YXNpX3JlZGlzX3Bhc3N3b3JkXzEyMw==        # asi_redis_password_123
  neo4j-password: YXNpX25lbzRqX3Bhc3N3b3JkXzEyMw==        # asi_neo4j_password_123
  grafana-admin-password: YXNpX2dyYWZhbmFfYWRtaW5fMTIz    # asi_grafana_admin_123
  jwt-secret: eW91ci1zdXBlci1zZWNyZXQtand0LWtleS1oZXJl    # your-super-secret-jwt-key-here
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: asi-system-config
  namespace: asi-system
  labels:
    component: configuration
    system: asi-system
data:
  # Global configuration
  environment: "production"
  log-level: "INFO"
  metrics-enabled: "true"
  tracing-enabled: "true"
  
  # Kafka configuration
  kafka-bootstrap-servers: "kafka:9092"
  kafka-topics: "asi-ingestion,asi-decisions,asi-learning,asi-improvements"
  
  # Database configuration
  postgres-host: "postgres"
  postgres-port: "5432"
  postgres-database: "asi_system"
  postgres-user: "asi_user"
  
  redis-host: "redis"
  redis-port: "6379"
  redis-database: "0"
  
  neo4j-host: "neo4j"
  neo4j-port: "7687"
  neo4j-user: "neo4j"
  
  # Monitoring configuration
  prometheus-url: "http://prometheus:9090"
  grafana-url: "http://grafana:3000"
  jaeger-url: "http://jaeger:16686"
  
  # Application-specific configuration
  max-concurrent-jobs: "5"
  model-cache-size: "10GB"
  training-batch-size: "32"
  inference-timeout: "30s"
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: asi-system-pdb
  namespace: asi-system
  labels:
    component: availability
    system: asi-system
spec:
  minAvailable: 1
  selector:
    matchLabels:
      system: asi-system
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: asi-shared-storage
  namespace: asi-system
  labels:
    component: storage
    system: asi-system
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 100Gi
  storageClassName: fast-ssd
